<?xml version="1.0" encoding="UTF-8"?>
<module id="com.polarion.synchronizer" version="1.0.0">

	<service-point id="configDataHandler"
		interface="com.polarion.platform.repository.config.IDataHandler"
		visibility="private">
		<invoke-factory>
			<construct
				class="com.polarion.synchronizer.internal.configuration.ConfigurationDataHandler" />
		</invoke-factory>
	</service-point>

	<contribution configuration-id="com.polarion.platform.repository.config.configuration">
		<config id="com.polarion.synchronizer.configuration"
			cfgFilePath=".polarion/synchronizer/configuration.xml" dataHandler="configDataHandler" inheritable="true"/>
	</contribution>

	<service-point id="synchronizationJobUnitFactory"
		interface="com.polarion.platform.jobs.IJobUnitFactory"
		visibility="private">
		<invoke-factory>
			<construct class="com.polarion.synchronizer.internal.SynchronizationJobUnitFactory">
			</construct>
		</invoke-factory>
	</service-point>
	
	<contribution configuration-id="com.polarion.platform.jobs.configuration">
		<jobUnitFactory name="synchronizer" factory="synchronizationJobUnitFactory" />
	</contribution>
	
	<service-point id="olePreviewGenerator" interface="com.siemens.polarion.previewer.IPreviewer">
		<invoke-factory>
			<construct
				class="com.polarion.synchronizer.internal.ole.preview.OLEPreviewer">
			</construct>
		</invoke-factory>
	</service-point>

	<contribution configuration-id="com.siemens.polarion.previewer.previewers">
		<previewer service="olePreviewGenerator" />
	</contribution>

</module>
