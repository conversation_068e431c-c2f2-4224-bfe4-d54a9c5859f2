/*     */ package com.polaron.synchronizer.macrolink;
/*     */ 
/*     */ import com.polarion.alm.tracker.ITestManagementService;
/*     */ import com.polarion.alm.tracker.model.IWorkItem;
/*     */ import com.polarion.core.util.StringUtils;
/*     */ import com.polarion.platform.i18n.Localization;
/*     */ import com.polarion.synchronizer.ILogger;
/*     */ import com.polarion.synchronizer.model.ITransactionProxy;
/*     */ import java.security.PrivilegedAction;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ import org.jetbrains.annotations.Nullable;
/*     */ import org.jsoup.nodes.Element;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public abstract class AbstractMacroLinkConverter
/*     */ {
/*     */   @NotNull
/*     */   protected ITransactionProxy polarionProxy;
/*     */   @NotNull
/*     */   protected MacroLinkHelper macroLinkHelper;
/*     */   @NotNull
/*     */   private ILogger logger;
/*     */   
/*     */   public AbstractMacroLinkConverter(@NotNull ITransactionProxy polarionProxy, @NotNull ITestManagementService testManagementService, @NotNull String baseUrl, @NotNull ILogger logger) {
/*  49 */     this.polarionProxy = polarionProxy;
/*  50 */     this.macroLinkHelper = new MacroLinkHelper(testManagementService, baseUrl);
/*  51 */     this.logger = logger;
/*     */   }
/*     */   
/*     */   protected abstract void createTestRunMacroLink(@NotNull MacroLinkHelper.TestRunMacroLinkData paramTestRunMacroLinkData, @NotNull Element paramElement, @NotNull StringBuilder paramStringBuilder);
/*     */   
/*     */   protected abstract void createWorkItemMacroLink(@NotNull MacroLinkHelper.WorkItemMacroLinkData paramWorkItemMacroLinkData, @NotNull Element paramElement, @NotNull StringBuilder paramStringBuilder);
/*     */   
/*     */   public boolean processMacroLinks(@NotNull Element element, @NotNull StringBuilder sb) {
/*  59 */     String dataType, str2, attrClass = element.attr("class"); String str1;
/*  60 */     switch ((str1 = attrClass).hashCode()) { case 1958675335: if (!str1.equals("polarion-rte-link"))
/*     */           break; 
/*  62 */         dataType = element.attr("data-type");
/*  63 */         switch ((str2 = dataType).hashCode()) { case -1422467943: if (!str2.equals("testRun")) {
/*     */               break;
/*     */             }
/*     */ 
/*     */ 
/*     */             
/*  69 */             processTestRunMacroLink(element, sb);
/*  70 */             return true;
/*     */           case 34522564:
/*     */             if (!str2.equals("workItem"))
/*     */               break;  processWorkItemMacroLink(element, sb); return true; }
/*     */          break; }
/*  75 */      return false;
/*     */   }
/*     */   
/*     */   private void processWorkItemMacroLink(@NotNull final Element element, @NotNull final StringBuilder sb) {
/*  79 */     final String workItemId = element.attr("data-item-id");
/*  80 */     final String revision = StringUtils.getEmptyIfNullTrimmed(element.attr("data-revision"));
/*  81 */     this.polarionProxy.doInTransaction(new PrivilegedAction<Void>()
/*     */         {
/*     */           public Void run() {
/*  84 */             String projectId = AbstractMacroLinkConverter.this.getProjectId();
/*     */             try {
/*  86 */               IWorkItem workItem = AbstractMacroLinkConverter.this.getWorkItem(workItemId, revision);
/*  87 */               MacroLinkHelper.WorkItemMacroLinkData workItemMacroLinkData = AbstractMacroLinkConverter.this.macroLinkHelper.createWorkItemMacroLinkData(workItemId, revision, projectId, workItem);
/*  88 */               AbstractMacroLinkConverter.this.createWorkItemMacroLink(workItemMacroLinkData, element, sb);
/*  89 */             } catch (IllegalArgumentException e) {
/*  90 */               AbstractMacroLinkConverter.this.logger.warn(Localization.getString("synchronizer.polarion.workItemLoadFailed", new String[] { this.val$workItemId, e.getMessage() }));
/*     */             } 
/*  92 */             return null;
/*     */           }
/*     */         });
/*     */   }
/*     */ 
/*     */   
/*     */   private void processTestRunMacroLink(@NotNull final Element element, @NotNull final StringBuilder sb) {
/*  99 */     final String testRunId = element.attr("data-item-id");
/* 100 */     this.polarionProxy.doInTransaction(new PrivilegedAction<Void>()
/*     */         {
/*     */           public Void run() {
/* 103 */             String projectId = AbstractMacroLinkConverter.this.getProjectId();
/* 104 */             MacroLinkHelper.TestRunMacroLinkData testRunMacroLinkData = AbstractMacroLinkConverter.this.macroLinkHelper.createTestRunMacroLinkData(testRunId, projectId);
/* 105 */             AbstractMacroLinkConverter.this.createTestRunMacroLink(testRunMacroLinkData, element, sb);
/* 106 */             return null;
/*     */           }
/*     */         });
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   protected String getProjectId() {
/* 114 */     return this.polarionProxy.getProject().getId();
/*     */   }
/*     */   
/*     */   @Nullable
/*     */   private IWorkItem getWorkItem(@NotNull String workItemId, @NotNull String revision) {
/* 119 */     IWorkItem workItemTemp = this.polarionProxy.getProject().getWorkItem(workItemId);
/* 120 */     if (workItemTemp != null && !workItemTemp.isUnresolvable() && 
/* 121 */       !revision.isEmpty()) {
/* 122 */       workItemTemp = (IWorkItem)this.polarionProxy.getProject().getDataSvc().getVersionedInstance(workItemTemp.getUri(), revision);
/*     */     }
/*     */     
/* 125 */     return workItemTemp;
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polaron/synchronizer/macrolink/AbstractMacroLinkConverter.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */