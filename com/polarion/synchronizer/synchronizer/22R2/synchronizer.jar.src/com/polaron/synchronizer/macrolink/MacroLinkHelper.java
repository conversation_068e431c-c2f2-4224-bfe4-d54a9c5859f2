/*     */ package com.polaron.synchronizer.macrolink;
/*     */ 
/*     */ import com.polarion.alm.tracker.ITestManagementService;
/*     */ import com.polarion.alm.tracker.model.IStatusOpt;
/*     */ import com.polarion.alm.tracker.model.ITestRun;
/*     */ import com.polarion.alm.tracker.model.ITypeOpt;
/*     */ import com.polarion.alm.tracker.model.IWorkItem;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ import org.jetbrains.annotations.Nullable;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class MacroLinkHelper
/*     */ {
/*     */   @NotNull
/*     */   private ITestManagementService testManagementService;
/*     */   @NotNull
/*     */   private String baseUrl;
/*     */   
/*     */   public MacroLinkHelper(@NotNull ITestManagementService testManagementService, @NotNull String baseUrl) {
/*  43 */     this.testManagementService = testManagementService;
/*  44 */     this.baseUrl = baseUrl;
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   public WorkItemMacroLinkData createWorkItemMacroLinkData(@NotNull String workItemId, @NotNull String revision, @NotNull String projectId, @Nullable IWorkItem workItem) {
/*  49 */     String workItemTitle = null;
/*  50 */     String nameOfType = null;
/*  51 */     String workItemImgPath = null;
/*  52 */     if (workItem != null && !workItem.isUnresolvable() && 
/*  53 */       !workItem.isUnresolvable()) {
/*  54 */       ITypeOpt type = workItem.getType();
/*  55 */       workItemTitle = workItem.getTitle();
/*  56 */       workItemImgPath = getWorkItemImgPath(type);
/*  57 */       nameOfType = getNameOfType(type);
/*     */     } 
/*     */     
/*  60 */     String revisionImgPath = getRevisionImgPath(revision);
/*  61 */     String workTitleStr = getWorkTitleStr(workItemTitle, workItemId);
/*     */     
/*  63 */     String workItemPath = getWorkItemPath(workItemId, revision, projectId);
/*  64 */     return new WorkItemMacroLinkData(nameOfType, revisionImgPath, workItemImgPath, workTitleStr, workItemPath);
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   public TestRunMacroLinkData createTestRunMacroLinkData(@NotNull String testRunId, @NotNull String projectId) {
/*  69 */     ITestRun testRun = this.testManagementService.getTestRun(projectId, testRunId);
/*  70 */     String testRunImgPath = null;
/*  71 */     String testRunLabelStr = null;
/*  72 */     String statusOfType = null;
/*  73 */     if (testRun != null && !testRun.isUnresolvable()) {
/*  74 */       IStatusOpt status = testRun.getStatus();
/*  75 */       statusOfType = getStatusOfType(status);
/*  76 */       testRunImgPath = getTestRunImgPath(status);
/*  77 */       testRunLabelStr = getTestRunLabelStr(testRun);
/*     */     } 
/*     */     
/*  80 */     String testRunPath = getTestRunPath(testRunId, projectId);
/*     */     
/*  82 */     return new TestRunMacroLinkData(statusOfType, testRunImgPath, testRunLabelStr, testRunPath);
/*     */   }
/*     */   
/*     */   @Nullable
/*     */   private String getStatusOfType(@Nullable IStatusOpt status) {
/*  87 */     if (status != null) {
/*  88 */       return status.getName();
/*     */     }
/*  90 */     return null;
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private String getWorkItemPath(@NotNull String workItemId, @NotNull String revision, @NotNull String projectId) {
/*  95 */     StringBuilder sb = new StringBuilder();
/*  96 */     sb.append(this.baseUrl);
/*  97 */     sb.append("/polarion/#/project/");
/*  98 */     sb.append(projectId);
/*  99 */     sb.append("/workitem?id=");
/* 100 */     sb.append(workItemId);
/* 101 */     if (!revision.isEmpty()) {
/* 102 */       sb.append("&revision=");
/* 103 */       sb.append(revision);
/*     */     } 
/* 105 */     return sb.toString();
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private String getWorkTitleStr(@Nullable String workItemTitle, @NotNull String workItemId) {
/* 110 */     StringBuilder sb = new StringBuilder();
/* 111 */     sb.append(workItemId);
/* 112 */     if (workItemTitle != null) {
/* 113 */       sb.append(" - ");
/* 114 */       sb.append(workItemTitle);
/*     */     } 
/* 116 */     return sb.toString();
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private String getWorkItemImgPath(@Nullable ITypeOpt type) {
/* 121 */     StringBuilder sb = new StringBuilder();
/* 122 */     String iconUrl = (type != null) ? type.getProperty("iconURL") : null;
/* 123 */     if (iconUrl == null) {
/* 124 */       iconUrl = "/polarion/ria/images/enums/type_heading.png";
/*     */     }
/* 126 */     sb.append(this.baseUrl);
/* 127 */     sb.append(iconUrl);
/* 128 */     return sb.toString();
/*     */   }
/*     */   
/*     */   @Nullable
/*     */   private String getNameOfType(ITypeOpt type) {
/* 133 */     return (type != null) ? type.getName() : null;
/*     */   }
/*     */   
/*     */   @Nullable
/*     */   private String getRevisionImgPath(@NotNull String revision) {
/* 138 */     if (revision.isEmpty()) {
/* 139 */       return null;
/*     */     }
/* 141 */     StringBuilder sb = new StringBuilder();
/* 142 */     sb.append(this.baseUrl);
/* 143 */     sb.append("/polarion/ria/images/fixed_revision.gif");
/* 144 */     return sb.toString();
/*     */   }
/*     */   
/*     */   @Nullable
/*     */   private String getTestRunImgPath(@Nullable IStatusOpt status) {
/* 149 */     StringBuilder sb = new StringBuilder();
/* 150 */     String iconUrl = (status != null) ? status.getProperty("iconURL") : null;
/* 151 */     if (iconUrl == null) {
/* 152 */       return null;
/*     */     }
/* 154 */     sb.append(this.baseUrl);
/* 155 */     sb.append(iconUrl);
/* 156 */     return sb.toString();
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private String getTestRunLabelStr(@NotNull ITestRun testRun) {
/* 161 */     StringBuilder sb = new StringBuilder();
/* 162 */     sb.append(testRun.getLabel());
/* 163 */     return sb.toString();
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private String getTestRunPath(@NotNull String testRunId, @NotNull String projectId) {
/* 168 */     StringBuilder sb = new StringBuilder();
/* 169 */     sb.append(this.baseUrl);
/* 170 */     sb.append("/polarion/#/project/");
/* 171 */     sb.append(projectId);
/* 172 */     sb.append("/testrun?id=");
/* 173 */     sb.append(testRunId);
/* 174 */     return sb.toString();
/*     */   }
/*     */   
/*     */   public static final class WorkItemMacroLinkData {
/*     */     @Nullable
/*     */     private final String revisionImgPath;
/*     */     @Nullable
/*     */     private final String nameOfStatus;
/*     */     @Nullable
/*     */     private final String workItemImgPath;
/*     */     @NotNull
/*     */     private final String workTitleStr;
/*     */     @NotNull
/*     */     private final String workItemPath;
/*     */     
/*     */     public WorkItemMacroLinkData(String nameOfStatus, String revisionImgPath, String workItemImgPath, String workTitleStr, String workItemPath) {
/* 190 */       this.nameOfStatus = nameOfStatus;
/* 191 */       this.revisionImgPath = revisionImgPath;
/* 192 */       this.workItemImgPath = workItemImgPath;
/* 193 */       this.workTitleStr = workTitleStr;
/* 194 */       this.workItemPath = workItemPath;
/*     */     }
/*     */     
/*     */     @Nullable
/*     */     public String getRevisionImgPath() {
/* 199 */       return this.revisionImgPath;
/*     */     }
/*     */     
/*     */     @Nullable
/*     */     public String getNameOfStatus() {
/* 204 */       return this.nameOfStatus;
/*     */     }
/*     */     
/*     */     @Nullable
/*     */     public String getWorkItemImgPath() {
/* 209 */       return this.workItemImgPath;
/*     */     }
/*     */     
/*     */     @NotNull
/*     */     public String getWorkTitleStr() {
/* 214 */       return this.workTitleStr;
/*     */     }
/*     */     
/*     */     @NotNull
/*     */     public String getWorkItemPath() {
/* 219 */       return this.workItemPath;
/*     */     }
/*     */   }
/*     */   
/*     */   public static class TestRunMacroLinkData
/*     */   {
/*     */     @Nullable
/*     */     private final String statusOfType;
/*     */     @Nullable
/*     */     private final String testRunImgPath;
/*     */     @Nullable
/*     */     private final String testRunLabelStr;
/*     */     @NotNull
/*     */     private final String testRunPath;
/*     */     
/*     */     public TestRunMacroLinkData(String statusOfType, String testRunImgPath, String testRunLabelStr, String testRunPath) {
/* 235 */       this.statusOfType = statusOfType;
/* 236 */       this.testRunImgPath = testRunImgPath;
/* 237 */       this.testRunLabelStr = testRunLabelStr;
/* 238 */       this.testRunPath = testRunPath;
/*     */     }
/*     */     
/*     */     @Nullable
/*     */     public String getStatusOfType() {
/* 243 */       return this.statusOfType;
/*     */     }
/*     */     
/*     */     @Nullable
/*     */     public String getTestRunImgPath() {
/* 248 */       return this.testRunImgPath;
/*     */     }
/*     */     
/*     */     @Nullable
/*     */     public String getTestRunLabelStr() {
/* 253 */       return this.testRunLabelStr;
/*     */     }
/*     */     
/*     */     @NotNull
/*     */     public String getTestRunPath() {
/* 258 */       return this.testRunPath;
/*     */     }
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polaron/synchronizer/macrolink/MacroLinkHelper.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */