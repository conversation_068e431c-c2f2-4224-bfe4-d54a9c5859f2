package com.polarion.synchronizer.proxy.doors.internal;

import com.polarion.synchronizer.proxy.configuration.SpecificationConfiguration;
import java.util.Collection;
import org.jetbrains.annotations.NotNull;

public interface IDoorsExchangeService {
  void createAndUpdateSyncPairs(@NotNull String paramString, @NotNull Collection<SpecificationConfiguration> paramCollection1, @NotNull Collection<SpecificationConfiguration> paramCollection2);
}


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/internal/IDoorsExchangeService.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */