/*     */ package com.polarion.synchronizer.proxy.doors.html2rtftranslator;
/*     */ 
/*     */ import com.polarion.synchronizer.ILogger;
/*     */ import com.polarion.synchronizer.model.Attachment;
/*     */ import com.polarion.synchronizer.ole.EmbeddedObject;
/*     */ import com.polarion.synchronizer.ole.OleExtractor;
/*     */ import com.polarion.synchronizer.proxy.htmltranslator.ElementInfo;
/*     */ import com.polarion.synchronizer.proxy.htmltranslator.ImageSize;
/*     */ import com.polarion.synchronizer.proxy.htmltranslator.Tag;
/*     */ import java.io.IOException;
/*     */ import java.io.InputStream;
/*     */ import java.io.UnsupportedEncodingException;
/*     */ import java.net.URLDecoder;
/*     */ import java.nio.charset.Charset;
/*     */ import java.util.Collection;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import org.apache.commons.io.IOUtils;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ import org.jsoup.nodes.Element;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ImgTag
/*     */   implements Tag
/*     */ {
/*     */   @NotNull
/*     */   private Map<String, Attachment> fileNameToAttachment;
/*     */   @NotNull
/*     */   ILogger logger;
/*     */   
/*     */   public ImgTag(@NotNull Map<String, Attachment> fileNameToAttachment, @NotNull ILogger logger) {
/*  37 */     this.fileNameToAttachment = fileNameToAttachment;
/*  38 */     this.logger = logger;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void open(@NotNull Element element, @NotNull ElementInfo elementInfo, @NotNull StringBuilder sb, @NotNull Collection<Tag> openTags) {}
/*     */ 
/*     */ 
/*     */   
/*     */   public void close(@NotNull Element element, @NotNull ElementInfo elementInfo, @NotNull StringBuilder sb, @NotNull Collection<Tag> openTags) {}
/*     */ 
/*     */ 
/*     */   
/*     */   public void process(@NotNull Element element, @NotNull ElementInfo elementInfo, @NotNull StringBuilder sb, @NotNull Collection<Tag> openTags) {
/*  53 */     String fileName = element.attr("src");
/*  54 */     Attachment attachment = this.fileNameToAttachment.get(decodeFilename(fileName));
/*     */     try {
/*  56 */       if (attachment != null) {
/*  57 */         InputStream attachmentData = attachment.getEmbeddedObject()
/*  58 */           .map(o -> o.getData())
/*  59 */           .orElseGet(() -> {
/*     */               String styleAttribute = paramElement.attr("style");
/*     */               ImageSize size = getDimensions(styleAttribute);
/*     */               Ole1OutputStream ole1OutputStream = new Ole1OutputStream(size.getWidth(), size.getHeight());
/*     */               ole1OutputStream.write(paramAttachment);
/*     */               return ole1OutputStream.getData();
/*     */             });
/*  66 */         sb.append(IOUtils.toString(attachmentData, Charset.defaultCharset()));
/*     */       } else {
/*  68 */         this.logger.warn("Cannot find an attachment for the reference" + fileName + " in img tag. Was an attachment removed?");
/*     */       } 
/*  70 */     } catch (IOException e) {
/*  71 */       sb.append("<Error reading attachment " + attachment.getFileName() + ": " + e.toString() + ">");
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean isContinue() {
/*  77 */     return false;
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private String decodeFilename(@NotNull String fileName) {
/*  82 */     if (fileName.startsWith("workitemimg:")) {
/*  83 */       fileName = fileName.substring("workitemimg:".length());
/*  84 */     } else if (fileName.startsWith("attachment:")) {
/*  85 */       fileName = fileName.substring("attachment:".length());
/*     */     } 
/*  87 */     fileName = fileName.replaceFirst("\\d+\\-", "");
/*  88 */     fileName = fileName.replace("+", "%2B");
/*     */     try {
/*  90 */       fileName = URLDecoder.decode(fileName, "UTF-8");
/*  91 */     } catch (UnsupportedEncodingException e) {
/*  92 */       throw new RuntimeException(e);
/*     */     } 
/*  94 */     return fileName;
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private ImageSize getDimensions(@NotNull String styleAttribute) {
/*  99 */     String[] styles = styleAttribute.split(";");
/* 100 */     Map<String, Integer> styleMap = new HashMap<>(); byte b; int i; String[] arrayOfString1;
/* 101 */     for (i = (arrayOfString1 = styles).length, b = 0; b < i; ) { String style = arrayOfString1[b];
/* 102 */       String[] vals = style.split(":");
/* 103 */       if (vals.length == 2) {
/* 104 */         String key = vals[0];
/* 105 */         int value = ImageSize.getNumValue(vals[1]);
/* 106 */         styleMap.put(key, Integer.valueOf(value));
/*     */       }  b++; }
/*     */     
/* 109 */     if (styleMap.containsKey("max-width")) {
/* 110 */       int j = OleExtractor.pixelsToTwips(((Integer)styleMap.get("max-width")).intValue());
/* 111 */       return new ImageSize(j, j);
/*     */     } 
/* 113 */     int widthInTwips = styleMap.containsKey("width") ? OleExtractor.pixelsToTwips(((Integer)styleMap.get("width")).intValue()) : 0;
/* 114 */     int heightInTwips = styleMap.containsKey("height") ? OleExtractor.pixelsToTwips(((Integer)styleMap.get("height")).intValue()) : 0;
/* 115 */     return new ImageSize(widthInTwips, heightInTwips);
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/html2rtftranslator/ImgTag.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */