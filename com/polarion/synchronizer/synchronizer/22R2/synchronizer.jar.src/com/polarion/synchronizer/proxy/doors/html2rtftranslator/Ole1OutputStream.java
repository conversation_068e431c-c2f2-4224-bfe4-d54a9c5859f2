/*    */ package com.polarion.synchronizer.proxy.doors.html2rtftranslator;
/*    */ 
/*    */ import com.polarion.synchronizer.model.Attachment;
/*    */ import java.io.ByteArrayInputStream;
/*    */ import java.io.ByteArrayOutputStream;
/*    */ import java.io.IOException;
/*    */ import java.io.InputStream;
/*    */ import java.io.SequenceInputStream;
/*    */ import java.util.Arrays;
/*    */ import java.util.Collections;
/*    */ import java.util.List;
/*    */ import org.apache.commons.io.IOUtils;
/*    */ import org.apache.poi.poifs.filesystem.Ole10Native;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class Ole1OutputStream
/*    */ {
/*    */   private static final String LINE_BREAK = "\n";
/*    */   private static final String OLE1_FLAG1 = "01050000\n";
/*    */   private static final String OLE1_FLAG2 = "02000000\n";
/*    */   private static final String OLE1_FLAG3 = "08000000\n";
/*    */   private static final String OLE1_PACKAGE = "5061636b61676500\n";
/*    */   private static final String OLE1_EMPTY = "00000000\n";
/*    */   private static final String RTF_PACKAGE_PREFIX = "{\\object\\objemb{\\*\\objclass Package}";
/*    */   private static final String RTF_OBJDATA_PREFIX = "{\\*\\objdata \n";
/*    */   private static final String RTF_PACKAGE_SUFFIX = "}}\n";
/*    */   private static final int DEFAULT_PACKAGE_WIDTH = 1155;
/*    */   private static final int DEFAULT_PACKAGE_HEIGHT = 810;
/*    */   @NotNull
/* 38 */   private ByteArrayOutputStream oleData = new ByteArrayOutputStream();
/*    */   
/*    */   private int objectWidth;
/*    */   private int objectHeight;
/*    */   
/*    */   public Ole1OutputStream(int width, int height) {
/* 44 */     this.objectWidth = width;
/* 45 */     this.objectHeight = height;
/*    */   }
/*    */   public void write(@NotNull Attachment attachment) {
/*    */     try {
/*    */       Exception exception2;
/* 50 */       String inputFileName = attachment.getFileName();
/* 51 */       Ole10Native ole10Native = new Ole10Native(inputFileName, inputFileName, inputFileName, IOUtils.toByteArray(attachment.getContent()));
/* 52 */       Exception exception1 = null;
/*    */ 
/*    */ 
/*    */     
/*    */     }
/* 57 */     catch (IOException e) {
/* 58 */       throw new RuntimeException("Error writing OLE1 Data" + e.toString());
/*    */     } 
/*    */   }
/*    */   
/*    */   @NotNull
/*    */   public InputStream getData() {
/* 64 */     String objW = (this.objectWidth != 0) ? ("\\objw" + this.objectWidth) : "\\objw1155";
/* 65 */     String objH = (this.objectHeight != 0) ? ("\\objh" + this.objectHeight) : "\\objh810";
/* 66 */     String prefix = "{\\object\\objemb{\\*\\objclass Package}" + objW + objH + "{\\*\\objdata \n" + "01050000\n" + "02000000\n" + "08000000\n" + "5061636b61676500\n" + "00000000\n" + "00000000\n";
/* 67 */     String suffix = "\n01050000\n00000000\n}}\n";
/* 68 */     List<InputStream> streams = Arrays.asList(new InputStream[] { new ByteArrayInputStream(prefix.getBytes()), new ByteArrayInputStream(this.oleData.toByteArray()), new ByteArrayInputStream(suffix.getBytes()) });
/* 69 */     return new SequenceInputStream(Collections.enumeration(streams));
/*    */   }
/*    */   public static boolean isOle1Package(@NotNull InputStream is) {
/*    */     
/* 73 */     try { Exception exception1 = null, exception2 = null;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */       
/*    */       try {  }
/*    */       finally
/* 84 */       { exception2 = null; if (exception1 == null) { exception1 = exception2; } else if (exception1 != exception2) { exception1.addSuppressed(exception2); }  }  } catch (IOException e)
/* 85 */     { throw new RuntimeException("Failed To Read OLE object" + e); }
/*    */     
/* 87 */     return false;
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/html2rtftranslator/Ole1OutputStream.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */