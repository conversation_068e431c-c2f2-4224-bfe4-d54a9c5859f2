/*    */ package com.polarion.synchronizer.proxy.doors.xmlmodel;
/*    */ 
/*    */ import javax.xml.bind.annotation.XmlAttribute;
/*    */ import javax.xml.bind.annotation.XmlRootElement;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @XmlRootElement(name = "deleted-object-publication")
/*    */ public class DoorsDeletedObjectPublication
/*    */ {
/*    */   private String objectId;
/*    */   
/*    */   @NotNull
/*    */   public String getObjectId() {
/* 37 */     return this.objectId;
/*    */   }
/*    */   
/*    */   @XmlAttribute
/*    */   public void setObjectId(String objectId) {
/* 42 */     this.objectId = objectId;
/*    */   }
/*    */ 
/*    */   
/*    */   public int hashCode() {
/* 47 */     int prime = 31;
/* 48 */     int result = 1;
/* 49 */     result = 31 * result + ((this.objectId == null) ? 0 : this.objectId.hashCode());
/* 50 */     return result;
/*    */   }
/*    */ 
/*    */   
/*    */   public boolean equals(Object obj) {
/* 55 */     if (this == obj) {
/* 56 */       return true;
/*    */     }
/* 58 */     if (obj == null) {
/* 59 */       return false;
/*    */     }
/* 61 */     if (getClass() != obj.getClass()) {
/* 62 */       return false;
/*    */     }
/* 64 */     DoorsDeletedObjectPublication other = (DoorsDeletedObjectPublication)obj;
/* 65 */     if (this.objectId == null) {
/* 66 */       if (other.objectId != null) {
/* 67 */         return false;
/*    */       }
/* 69 */     } else if (!this.objectId.equals(other.objectId)) {
/* 70 */       return false;
/*    */     } 
/* 72 */     return true;
/*    */   }
/*    */ 
/*    */   
/*    */   public String toString() {
/* 77 */     return "DoorsDeletedObjectPublication [objectId=" + this.objectId + "]";
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/xmlmodel/DoorsDeletedObjectPublication.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */