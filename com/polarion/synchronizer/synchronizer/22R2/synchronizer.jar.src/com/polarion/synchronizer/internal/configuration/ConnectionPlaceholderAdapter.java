/*    */ package com.polarion.synchronizer.internal.configuration;
/*    */ 
/*    */ import com.polarion.synchronizer.configuration.IConnection;
/*    */ import javax.xml.bind.annotation.adapters.XmlAdapter;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class ConnectionPlaceholderAdapter
/*    */   extends XmlAdapter<String, IConnection>
/*    */ {
/*    */   public String marshal(IConnection v) throws Exception {
/* 32 */     return (v == null) ? null : v.getId();
/*    */   }
/*    */ 
/*    */   
/*    */   public IConnection unmarshal(String v) throws Exception {
/* 37 */     return (v == null) ? null : (IConnection)new ConnectionPlaceholder(v);
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/internal/configuration/ConnectionPlaceholderAdapter.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */