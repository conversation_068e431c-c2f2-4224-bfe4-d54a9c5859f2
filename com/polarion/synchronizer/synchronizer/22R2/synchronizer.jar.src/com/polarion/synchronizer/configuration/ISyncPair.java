package com.polarion.synchronizer.configuration;

import com.fasterxml.jackson.annotation.JsonRootName;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.polarion.synchronizer.IAction;
import com.polarion.synchronizer.IPostProcessingAction;
import com.polarion.synchronizer.IProxyConfiguration;
import com.polarion.synchronizer.internal.configuration.LoadContext;
import com.polarion.synchronizer.internal.configuration.SyncPair;
import com.polarion.synchronizer.model.Direction;
import java.util.Collection;
import javax.xml.bind.annotation.XmlID;
import javax.xml.bind.annotation.XmlTransient;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@JsonRootName("sync_pair")
@JsonDeserialize(as = SyncPair.class)
@XmlJavaTypeAdapter(LoadContext.ProjectAwareAdapter.class)
public interface ISyncPair extends IProjectAware {
  @XmlID
  String getId();
  
  IProxyConfiguration getLeft();
  
  IProxyConfiguration getRight();
  
  MappingConfiguration getMapping();
  
  Direction getNewItemDirection();
  
  Direction getDeleteDirection();
  
  @XmlTransient
  @Nullable
  IAction getLeftDeleteAction();
  
  @XmlTransient
  void setLeftDeleteAction(@Nullable IAction paramIAction);
  
  void setReAddMissingOnRight(boolean paramBoolean);
  
  boolean isReAddMissingOnRight();
  
  @Nullable
  String getProjectId();
  
  @XmlTransient
  Collection<IPostProcessingAction> getPostProcessingActions();
  
  void setPostProcessingActions(@NotNull Collection<IPostProcessingAction> paramCollection);
  
  void setDeleteOutOfScope(boolean paramBoolean);
  
  boolean isDeleteOutOfScope();
}


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/configuration/ISyncPair.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */