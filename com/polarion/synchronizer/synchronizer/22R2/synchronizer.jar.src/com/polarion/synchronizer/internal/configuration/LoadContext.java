/*     */ package com.polarion.synchronizer.internal.configuration;
/*     */ 
/*     */ import com.polarion.synchronizer.IProxyConfiguration;
/*     */ import com.polarion.synchronizer.configuration.IConnection;
/*     */ import com.polarion.synchronizer.configuration.IProjectAware;
/*     */ import com.polarion.synchronizer.configuration.ISyncPair;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Collection;
/*     */ import javax.xml.bind.annotation.adapters.XmlAdapter;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ import org.jetbrains.annotations.Nullable;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class LoadContext
/*     */ {
/*     */   private SyncConfiguration configuration;
/*     */   @Nullable
/*     */   private final String projectId;
/*     */   
/*     */   public class ProxyConfigurationTracker
/*     */     extends XmlAdapter<Object, Object>
/*     */   {
/*     */     public Object marshal(Object v) throws Exception {
/*  43 */       return v;
/*     */     }
/*     */ 
/*     */     
/*     */     public Object unmarshal(Object v) throws Exception {
/*  48 */       if (v instanceof IProxyConfiguration) {
/*  49 */         LoadContext.this.proxyConfigurations.add((IProxyConfiguration)v);
/*     */       } else {
/*  51 */         throw new IllegalArgumentException("Expected an IProxyConfiguration.");
/*     */       } 
/*  53 */       if (v instanceof IProjectAware) {
/*  54 */         ((IProjectAware)v).setProject(LoadContext.this.projectId);
/*     */       }
/*  56 */       return v;
/*     */     }
/*     */   }
/*     */ 
/*     */   
/*     */   public class ProjectAwareAdapter
/*     */     extends XmlAdapter<Object, Object>
/*     */   {
/*     */     public Object unmarshal(Object v) throws Exception {
/*  65 */       setProject(v);
/*  66 */       return v;
/*     */     }
/*     */ 
/*     */     
/*     */     public Object marshal(Object v) throws Exception {
/*  71 */       setProject(v);
/*  72 */       return v;
/*     */     }
/*     */     
/*     */     private void setProject(Object v) {
/*  76 */       if (LoadContext.this.projectId != null && v instanceof IProjectAware) {
/*  77 */         IProjectAware projectAware = (IProjectAware)v;
/*  78 */         projectAware.setProject(LoadContext.this.projectId);
/*     */       } 
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @NotNull
/*  89 */   private final Collection<IProxyConfiguration> proxyConfigurations = new ArrayList<>();
/*     */   
/*     */   LoadContext(@Nullable String projectId) {
/*  92 */     this.projectId = projectId;
/*     */   }
/*     */   
/*     */   public SyncConfiguration getConfiguration() {
/*  96 */     return this.configuration;
/*     */   }
/*     */   
/*     */   public void setConfiguration(SyncConfiguration configuration) {
/* 100 */     this.configuration = configuration;
/*     */   }
/*     */   
/*     */   public ProxyConfigurationTracker getProxyConfigurationTracker() {
/* 104 */     return new ProxyConfigurationTracker();
/*     */   }
/*     */   
/*     */   public ProjectAwareAdapter getProjectAwareAdapter() {
/* 108 */     return new ProjectAwareAdapter();
/*     */   }
/*     */   
/*     */   public void injectConnections(boolean removeMissing) {
/* 112 */     for (IProxyConfiguration proxyConfiguration : this.proxyConfigurations) {
/* 113 */       IConnection connection = proxyConfiguration.getConnection();
/* 114 */       if (connection instanceof ConnectionPlaceholder) {
/* 115 */         IConnection loadConnection = this.configuration.getConnections().get(connection.getId());
/* 116 */         if (removeMissing || loadConnection != null) {
/* 117 */           proxyConfiguration.setConnection(loadConnection);
/*     */         }
/*     */       } 
/*     */     } 
/*     */   }
/*     */   
/*     */   public void merge(LoadContext subLoadContext) {
/* 124 */     for (ISyncPair pair : subLoadContext.getConfiguration().getSyncPairs().values()) {
/* 125 */       this.configuration.addSyncPair(pair);
/* 126 */       IProxyConfiguration left = pair.getLeft();
/* 127 */       if (left != null) {
/* 128 */         this.proxyConfigurations.add(left);
/*     */       }
/* 130 */       IProxyConfiguration right = pair.getRight();
/* 131 */       if (right != null) {
/* 132 */         this.proxyConfigurations.add(right);
/*     */       }
/*     */     } 
/* 135 */     for (IConnection connection : subLoadContext.getConfiguration().getConnections().values())
/* 136 */       this.configuration.addConnection(connection); 
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/internal/configuration/LoadContext.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */