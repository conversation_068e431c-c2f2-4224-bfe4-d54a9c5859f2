/*    */ package com.polarion.synchronizer.proxy.htmltranslator;
/*    */ 
/*    */ import java.util.Collection;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ import org.jsoup.nodes.Element;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public abstract class RecursiveTag
/*    */   implements Tag
/*    */ {
/*    */   @NotNull
/* 31 */   protected String previousText = "";
/*    */ 
/*    */ 
/*    */   
/*    */   public void process(@NotNull Element element, @NotNull ElementInfo elementInfo, @NotNull StringBuilder sb, @NotNull Collection<Tag> openTags) {}
/*    */ 
/*    */ 
/*    */   
/*    */   public boolean isContinue() {
/* 40 */     return true;
/*    */   }
/*    */   
/*    */   public void setPreviousText(@NotNull String previousText) {
/* 44 */     this.previousText = previousText;
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/htmltranslator/RecursiveTag.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */