package com.polarion.synchronizer;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public interface ISynchronizationContext {
  @NotNull
  IConnectionMap getConnectionMap();
  
  @NotNull
  ILogger getLogger();
  
  @NotNull
  IConflictLog getConflictLog();
  
  @NotNull
  IBaselineProvider getBaselineProvider();
  
  @Nullable
  IDependencyManager getDependencyManager();
  
  void currentItem(@Nullable String paramString1, @Nullable String paramString2);
  
  void currentSystem(@NotNull String paramString1, @NotNull String paramString2);
  
  void currentTask(@NotNull ISynchronizationTask paramISynchronizationTask);
  
  void setBaselineProvider(@Nullable IBaselineProvider paramIBaselineProvider);
  
  void setConnectionMap(@Nullable IConnectionMap paramIConnectionMap);
  
  void setLogger(@Nullable ILogger paramILogger);
  
  void initializeDependencyManager();
  
  void releaseDependencyManager();
}


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/ISynchronizationContext.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */