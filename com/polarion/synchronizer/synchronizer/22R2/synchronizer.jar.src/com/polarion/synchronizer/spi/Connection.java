/*     */ package com.polarion.synchronizer.spi;
/*     */ 
/*     */ import com.fasterxml.jackson.annotation.JsonIgnore;
/*     */ import com.fasterxml.jackson.annotation.JsonProperty;
/*     */ import com.polarion.platform.internal.security.UserAccountVault;
/*     */ import com.polarion.synchronizer.configuration.IConnection;
/*     */ import com.polarion.synchronizer.configuration.IProjectAware;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Collection;
/*     */ import java.util.Collections;
/*     */ import java.util.Map;
/*     */ import javax.validation.constraints.Null;
/*     */ import javax.xml.bind.annotation.XmlAttribute;
/*     */ import javax.xml.bind.annotation.XmlID;
/*     */ import javax.xml.bind.annotation.XmlRootElement;
/*     */ import javax.xml.bind.annotation.XmlTransient;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ 
/*     */ @XmlRootElement
/*     */ public abstract class Connection
/*     */   implements IConnection, IProjectAware
/*     */ {
/*     */   @XmlAttribute
/*     */   @XmlID
/*     */   private String id;
/*     */   private String projectId;
/*     */   
/*     */   protected static class ConnectionCredentials {
/*     */     private final String id;
/*     */     private String user;
/*     */     private String password;
/*     */     private String userAccountVaultKey;
/*     */     
/*     */     public ConnectionCredentials(String id) {
/*  35 */       this.id = id;
/*     */     }
/*     */     
/*     */     @XmlTransient
/*     */     @JsonIgnore
/*     */     public String getPassword() {
/*  41 */       return this.password;
/*     */     }
/*     */     
/*     */     @XmlTransient
/*     */     public String getUser() {
/*  46 */       return this.user;
/*     */     }
/*     */     
/*     */     public String getId() {
/*  50 */       return this.id;
/*     */     }
/*     */     
/*     */     private void initialize(String projectId, String connectionId) {
/*  54 */       this.userAccountVaultKey = String.format("connectors.%s.%s.%s", new Object[] { projectId, connectionId, this.id });
/*  55 */       updateCredentials();
/*  56 */       loadCredentials();
/*     */     }
/*     */     
/*     */     private void loadCredentials() {
/*  60 */       UserAccountVault.Credentials credentials = UserAccountVault.getInstance().tryGetCredentialsForKey(this.userAccountVaultKey);
/*  61 */       if (credentials != null) {
/*  62 */         this.user = credentials.getUser();
/*  63 */         this.password = credentials.getPassword();
/*     */       } 
/*     */     }
/*     */     
/*     */     @JsonProperty
/*     */     public void setPassword(String password) {
/*  69 */       if ("".equals(password)) {
/*  70 */         password = null;
/*     */       }
/*  72 */       this.password = password;
/*  73 */       updateCredentials();
/*     */     }
/*     */ 
/*     */     
/*     */     public void setUser(String user) {
/*  78 */       this.user = user;
/*  79 */       updateCredentials();
/*     */     }
/*     */     
/*     */     public boolean isInitialized() {
/*  83 */       return (this.user != null && this.password != null);
/*     */     }
/*     */ 
/*     */     
/*     */     public String toString() {
/*  88 */       return "ConnectionCredentials [id=" + this.id + ", user=" + this.user + "]";
/*     */     }
/*     */     
/*     */     private void updateCredentials() {
/*  92 */       if (this.user != null && this.password != null && this.userAccountVaultKey != null) {
/*  93 */         Map<String, UserAccountVault.Credentials> addedRecords = Collections.singletonMap(this.userAccountVaultKey, new UserAccountVault.Credentials(this.user, this.password));
/*  94 */         UserAccountVault.getInstance().update(addedRecords, null);
/*     */       } 
/*     */     }
/*     */ 
/*     */     
/*     */     public boolean equals(Object obj) {
/* 100 */       if (this == obj) {
/* 101 */         return true;
/*     */       }
/* 103 */       if (obj == null) {
/* 104 */         return false;
/*     */       }
/* 106 */       if (getClass() != obj.getClass()) {
/* 107 */         return false;
/*     */       }
/* 109 */       ConnectionCredentials other = (ConnectionCredentials)obj;
/* 110 */       if (this.id == null) {
/* 111 */         if (other.id != null) {
/* 112 */           return false;
/*     */         }
/* 114 */       } else if (!this.id.equals(other.id)) {
/* 115 */         return false;
/*     */       } 
/* 117 */       if (this.password == null) {
/* 118 */         if (other.password != null) {
/* 119 */           return false;
/*     */         }
/* 121 */       } else if (!this.password.equals(other.password)) {
/* 122 */         return false;
/*     */       } 
/* 124 */       if (this.user == null) {
/* 125 */         if (other.user != null) {
/* 126 */           return false;
/*     */         }
/* 128 */       } else if (!this.user.equals(other.user)) {
/* 129 */         return false;
/*     */       } 
/* 131 */       return true;
/*     */     }
/*     */ 
/*     */     
/*     */     public int hashCode() {
/* 136 */       int prime = 31;
/* 137 */       int result = 1;
/* 138 */       result = 31 * result + ((this.id == null) ? 0 : this.id.hashCode());
/* 139 */       result = 31 * result + ((this.password == null) ? 0 : this.password.hashCode());
/* 140 */       result = 31 * result + ((this.user == null) ? 0 : this.user.hashCode());
/* 141 */       return result;
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/* 153 */   private final Collection<ConnectionCredentials> allCredentials = new ArrayList<>();
/*     */   
/*     */   @Null
/* 156 */   private ConnectionCredentials defaultCredentials = new ConnectionCredentials("default");
/*     */   
/*     */   @Deprecated
/*     */   public Connection() {
/* 160 */     this.allCredentials.add(this.defaultCredentials);
/*     */   }
/*     */   
/*     */   public Connection(String id, String user, String password) {
/* 164 */     this.id = id;
/* 165 */     this.defaultCredentials.user = user;
/* 166 */     this.defaultCredentials.password = password;
/* 167 */     this.allCredentials.add(this.defaultCredentials);
/*     */   }
/*     */   
/*     */   public void addCredentials(ConnectionCredentials credentials) {
/* 171 */     this.allCredentials.add(credentials);
/*     */   }
/*     */ 
/*     */   
/*     */   @JsonProperty
/*     */   public String getId() {
/* 177 */     return this.id;
/*     */   }
/*     */ 
/*     */   
/*     */   @XmlTransient
/*     */   @JsonIgnore
/*     */   public String getPassword() {
/* 184 */     return this.defaultCredentials.getPassword();
/*     */   }
/*     */ 
/*     */   
/*     */   @XmlTransient
/*     */   @JsonProperty
/*     */   public String getUser() {
/* 191 */     return this.defaultCredentials.getUser();
/*     */   }
/*     */ 
/*     */   
/*     */   @JsonProperty
/*     */   public void setPassword(String password) {
/* 197 */     this.defaultCredentials.setPassword(password);
/*     */   }
/*     */ 
/*     */   
/*     */   public void setProject(String projectId) {
/* 202 */     if (projectId != null) {
/* 203 */       initializeCredentials(projectId);
/*     */     }
/* 205 */     this.projectId = projectId;
/*     */   }
/*     */ 
/*     */   
/*     */   public void initializeCredentials(@NotNull String projectId) {
/* 210 */     for (ConnectionCredentials credentials : this.allCredentials) {
/* 211 */       credentials.initialize(projectId, this.id);
/*     */     }
/*     */   }
/*     */   
/*     */   public void setUser(String user) {
/* 216 */     this.defaultCredentials.setUser(user);
/*     */   }
/*     */ 
/*     */   
/*     */   @XmlTransient
/*     */   public String getProjectId() {
/* 222 */     return this.projectId;
/*     */   }
/*     */   
/*     */   public void setProjectId(String projectId) {
/* 226 */     this.projectId = projectId;
/*     */   }
/*     */ 
/*     */   
/*     */   @XmlTransient
/*     */   public boolean isEnabled() {
/* 232 */     return this.defaultCredentials.isInitialized();
/*     */   }
/*     */ 
/*     */   
/*     */   public int hashCode() {
/* 237 */     int prime = 31;
/* 238 */     int result = 1;
/* 239 */     result = 31 * result + ((this.allCredentials == null) ? 0 : this.allCredentials.hashCode());
/* 240 */     result = 31 * result + ((this.defaultCredentials == null) ? 0 : this.defaultCredentials.hashCode());
/* 241 */     result = 31 * result + ((this.id == null) ? 0 : this.id.hashCode());
/* 242 */     result = 31 * result + ((this.projectId == null) ? 0 : this.projectId.hashCode());
/* 243 */     return result;
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean equals(Object obj) {
/* 248 */     if (this == obj) {
/* 249 */       return true;
/*     */     }
/* 251 */     if (obj == null) {
/* 252 */       return false;
/*     */     }
/* 254 */     if (getClass() != obj.getClass()) {
/* 255 */       return false;
/*     */     }
/* 257 */     Connection other = (Connection)obj;
/* 258 */     if (this.allCredentials == null) {
/* 259 */       if (other.allCredentials != null) {
/* 260 */         return false;
/*     */       }
/* 262 */     } else if (!this.allCredentials.equals(other.allCredentials)) {
/* 263 */       return false;
/*     */     } 
/* 265 */     if (this.defaultCredentials == null) {
/* 266 */       if (other.defaultCredentials != null) {
/* 267 */         return false;
/*     */       }
/* 269 */     } else if (!this.defaultCredentials.equals(other.defaultCredentials)) {
/* 270 */       return false;
/*     */     } 
/* 272 */     if (this.id == null) {
/* 273 */       if (other.id != null) {
/* 274 */         return false;
/*     */       }
/* 276 */     } else if (!this.id.equals(other.id)) {
/* 277 */       return false;
/*     */     } 
/* 279 */     if (this.projectId == null) {
/* 280 */       if (other.projectId != null) {
/* 281 */         return false;
/*     */       }
/* 283 */     } else if (!this.projectId.equals(other.projectId)) {
/* 284 */       return false;
/*     */     } 
/* 286 */     return true;
/*     */   }
/*     */ 
/*     */   
/*     */   public String toString() {
/* 291 */     return "Connection [id=" + this.id + ", projectId=" + this.projectId + ", allCredentials=" + this.allCredentials + ", defaultCredentials=" + this.defaultCredentials + "]";
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/spi/Connection.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */