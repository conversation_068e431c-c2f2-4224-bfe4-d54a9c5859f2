package com.polarion.synchronizer;

import com.polarion.synchronizer.model.ItemKey;
import com.polarion.synchronizer.model.Side;

public interface IConnectionMap {
  String getTargetId(String paramString, Side paramSide);
  
  String getTargetId(String paramString, Side paramSide, boolean paramBoolean);
  
  ItemKey getTargetKey(String paramString, Side paramSide);
  
  void addConnection(String paramString1, String paramString2, Side paramSide);
  
  void deleteConnection(String paramString, Side paramSide);
  
  @Deprecated
  boolean existsInScope(String paramString1, Side paramSide, String paramString2);
  
  @Deprecated
  void addToScope(String paramString1, Side paramSide, String paramString2);
}


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/IConnectionMap.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */