package com.polarion.synchronizer;

import com.polarion.synchronizer.proxy.configuration.SpecificationConfiguration;
import java.util.Collection;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public interface IExchangeService {
  @NotNull
  String startImport(@NotNull Collection<SpecificationConfiguration> paramCollection, @NotNull String paramString);
  
  void createPolarionDocument(@NotNull String paramString1, @NotNull String paramString2, @NotNull String paramString3, @Nullable String paramString4);
  
  void createPolarionDocument(@NotNull String paramString1, @NotNull String paramString2, @NotNull String paramString3, @NotNull String paramString4, @Nullable String paramString5);
  
  void saveConfiguration(@NotNull String paramString1, @NotNull String paramString2, @NotNull SpecificationConfiguration paramSpecificationConfiguration);
  
  @Nullable
  SpecificationConfiguration loadConfiguration(@NotNull String paramString1, @NotNull String paramString2);
}


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/IExchangeService.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */