package com.polarion.synchronizer.proxy.doors.internal;

import com.polarion.synchronizer.proxy.doors.IDoorsConnection;
import com.polarion.synchronizer.proxy.doors.IDoorsProject;
import com.polarion.synchronizer.proxy.doors.internal.commands.IDoorsCommand;
import org.jetbrains.annotations.NotNull;

public interface InternalDoorsConnection extends IDoorsConnection {
  @NotNull
  IDoorsProject getProjectByName(@NotNull String paramString);
  
  <T> T execute(IDoorsCommand<T> paramIDoorsCommand);
  
  @NotNull
  IDoorsModule getModule(@NotNull String paramString);
}


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/internal/InternalDoorsConnection.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */