/*     */ package com.polarion.synchronizer.proxy.doors.html2rtftranslator;
/*     */ 
/*     */ import com.polarion.synchronizer.ILogger;
/*     */ import com.polarion.synchronizer.mapping.RtfWrapper;
/*     */ import com.polarion.synchronizer.model.Attachment;
/*     */ import com.polarion.synchronizer.proxy.htmltranslator.ElementInfo;
/*     */ import com.polarion.synchronizer.proxy.htmltranslator.StyleTag;
/*     */ import com.polarion.synchronizer.proxy.htmltranslator.Tag;
/*     */ import java.util.ArrayDeque;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Collections;
/*     */ import java.util.HashMap;
/*     */ import java.util.LinkedHashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ import org.jsoup.Jsoup;
/*     */ import org.jsoup.nodes.Document;
/*     */ import org.jsoup.nodes.Element;
/*     */ import org.jsoup.nodes.Node;
/*     */ import org.jsoup.nodes.TextNode;
/*     */ import org.jsoup.safety.Whitelist;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HtmlToDoorsRtfConverter
/*     */ {
/*     */   @NotNull
/*     */   private final Map<String, Tag> tagMap;
/*     */   @NotNull
/*     */   private final Map<String, StyleTag> styleTagMap;
/*     */   @NotNull
/*  37 */   private Whitelist whitelist = Whitelist.simpleText()
/*  38 */     .addTags(new String[] { "span", "br", "img", "table", "th", "tr", "td"
/*  39 */       }).addProtocols("img", "src", new String[] { "http", "https"
/*  40 */       }).addAttributes(":all", new String[] { "style", "src"
/*  41 */       }).addAttributes("span", new String[] { "class", "data-type", "data-item-id", "data-revision" });
/*     */   
/*     */   public HtmlToDoorsRtfConverter(@NotNull Map<String, Attachment> fileNameToAttachment, @NotNull ILogger logger) {
/*  44 */     this.tagMap = new HashMap<>();
/*  45 */     this.styleTagMap = new HashMap<>();
/*  46 */     this.tagMap.put("img", new ImgTag(fileNameToAttachment, logger));
/*  47 */     init();
/*     */   }
/*     */   
/*     */   private void init() {
/*  51 */     this.tagMap.put("span", new SpanTag());
/*  52 */     this.tagMap.put("br", new BrTag());
/*  53 */     this.styleTagMap.put("font-weight", new FontWeightStyleTag());
/*  54 */     this.styleTagMap.put("font-style", new FontStyleTag());
/*  55 */     this.styleTagMap.put("text-decoration", new TextDecorationStyleTag());
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   public String convert(@NotNull String source) {
/*  60 */     StringBuilder sb = new StringBuilder();
/*  61 */     String cleanHTML = Jsoup.clean(source, "", this.whitelist, (new Document.OutputSettings()).prettyPrint(false));
/*  62 */     Element element = Jsoup.parse(cleanHTML).body();
/*  63 */     parse(element, new ElementInfo(Collections.emptyMap(), element.tag().getName()), sb, new ArrayDeque<>());
/*  64 */     return RtfWrapper.wrapRTF(sb.toString());
/*     */   }
/*     */   
/*     */   private void parse(@NotNull Element parentElement, @NotNull ElementInfo elementInfo, @NotNull StringBuilder sb, @NotNull ArrayDeque<Tag> openTags) {
/*  68 */     Tag previosTag = null;
/*  69 */     for (Node node : parentElement.childNodes()) {
/*  70 */       if (node instanceof Element) {
/*  71 */         Element element = (Element)node;
/*  72 */         Tag tag = this.tagMap.get(element.tag().getName());
/*  73 */         if (tag != null) {
/*  74 */           ElementInfo newElementInfo = newElementInfo(element, elementInfo);
/*  75 */           tag.open(element, elementInfo, sb, Collections.unmodifiableCollection(openTags));
/*  76 */           openTags.push(tag);
/*  77 */           tag.process(element, newElementInfo, sb, Collections.unmodifiableCollection(openTags));
/*  78 */           if (tag.isContinue()) {
/*  79 */             parse(element, newElementInfo, sb, openTags);
/*     */           }
/*  81 */           tag.close(element, elementInfo, sb, Collections.unmodifiableCollection(openTags));
/*  82 */           openTags.pop();
/*  83 */           previosTag = tag; continue;
/*     */         } 
/*  85 */         sb.append(element.text()); continue;
/*     */       } 
/*  87 */       if (node instanceof TextNode) {
/*  88 */         TextNode textNode = (TextNode)node;
/*     */         
/*  90 */         boolean followsSpanTag = previosTag instanceof SpanTag;
/*  91 */         boolean followsBrTag = previosTag instanceof BrTag;
/*     */         
/*  93 */         String textNodeText = textNode.text();
/*  94 */         String spaceCharacter = " ";
/*  95 */         if (!textNodeText.equals(spaceCharacter) || (textNodeText.equals(spaceCharacter) && followsSpanTag)) {
/*  96 */           if (followsBrTag && textNodeText.startsWith(spaceCharacter)) {
/*  97 */             textNodeText = textNodeText.substring(1);
/*     */           }
/*     */           
/* 100 */           String rtfEscapedText = convertToRtf(textNodeText);
/* 101 */           String text = stripText(rtfEscapedText);
/* 102 */           parseStyles(elementInfo, text, sb);
/*     */         } 
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   private void parseStyles(@NotNull ElementInfo elementInfo, @NotNull String text, @NotNull StringBuilder sb) {
/* 110 */     List<StyleTag> styleTagsToClose = new ArrayList<>();
/* 111 */     for (String style : elementInfo.getStyle().keySet()) {
/* 112 */       StyleTag styleTag = this.styleTagMap.get(style);
/* 113 */       if (styleTag != null) {
/* 114 */         styleTag.open(elementInfo, text, sb);
/* 115 */         styleTagsToClose.add(styleTag);
/*     */       } 
/*     */     } 
/* 118 */     sb.append(text);
/* 119 */     Collections.reverse(styleTagsToClose);
/* 120 */     for (StyleTag styleTag : styleTagsToClose) {
/* 121 */       styleTag.close(elementInfo, text, sb);
/*     */     }
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   private ElementInfo newElementInfo(@NotNull Element element, @NotNull ElementInfo oldElementInfo) {
/* 128 */     Map<String, String> styleMap = new LinkedHashMap<>();
/* 129 */     String style = element.attr("style");
/* 130 */     String[] styles = style.split(";"); byte b; int i; String[] arrayOfString1;
/* 131 */     for (i = (arrayOfString1 = styles).length, b = 0; b < i; ) { String s = arrayOfString1[b];
/* 132 */       String[] keyValue = s.split(":");
/* 133 */       if (keyValue.length > 1) {
/* 134 */         String key = keyValue[0].trim();
/* 135 */         String value = keyValue[1].trim();
/* 136 */         if ("font-weight".equals(key) || "font-style".equals(key) || "text-decoration".equals(key)) {
/* 137 */           styleMap.put(key, value);
/*     */         }
/*     */       } 
/*     */       b++; }
/*     */     
/* 142 */     if (styleMap.size() > 0) {
/* 143 */       HashMap<String, String> newMap = new LinkedHashMap<>();
/* 144 */       newMap.putAll(oldElementInfo.getStyle());
/* 145 */       newMap.putAll(styleMap);
/* 146 */       return new ElementInfo(newMap, element.tag().getName());
/*     */     } 
/* 148 */     return oldElementInfo;
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private String stripText(@NotNull String text) {
/* 153 */     int length = text.length();
/* 154 */     char[] oldChars = new char[length + 1];
/* 155 */     text.getChars(0, length, oldChars, 0);
/* 156 */     oldChars[length] = Character.MIN_VALUE;
/* 157 */     int newLen = -1; do {  }
/* 158 */     while (oldChars[++newLen] >= ' ');
/*     */ 
/*     */ 
/*     */     
/* 162 */     for (int j = newLen; j < length; j++) {
/* 163 */       char ch = oldChars[j];
/* 164 */       if (ch >= ' ') {
/* 165 */         oldChars[newLen] = ch;
/* 166 */         newLen++;
/*     */       } 
/*     */     } 
/* 169 */     return new String(oldChars, 0, newLen);
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private String convertToRtf(@NotNull String input) {
/* 174 */     StringBuilder output = new StringBuilder(); byte b; int i; char[] arrayOfChar;
/* 175 */     for (i = (arrayOfChar = input.toCharArray()).length, b = 0; b < i; ) { char c = arrayOfChar[b];
/* 176 */       if (c >= ' ' && c < '') {
/* 177 */         if (c == '\\' || c == '{' || c == '}') {
/* 178 */           output.append('\\');
/*     */         }
/* 180 */         output.append(c);
/* 181 */       } else if (c < ' ' || (c >= '' && c <= 'ÿ')) {
/* 182 */         output.append("\\'");
/* 183 */         output.append(Integer.toHexString(c));
/* 184 */       } else if (c > 'ÿ' && c <= '耀') {
/* 185 */         output.append("\\uc1\\u" + c + "*");
/* 186 */       } else if (c > '耀' && c <= Character.MAX_VALUE) {
/* 187 */         output.append("\\uc1\\u" + (c - 65536) + "*");
/*     */       } else {
/* 189 */         output.append("");
/*     */       }  b++; }
/*     */     
/* 192 */     return output.toString();
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/html2rtftranslator/HtmlToDoorsRtfConverter.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */