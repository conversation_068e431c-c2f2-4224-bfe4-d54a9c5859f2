package com.polarion.synchronizer;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.polarion.synchronizer.internal.configuration.ConnectionPlaceholderAdapter;
import com.polarion.synchronizer.internal.configuration.LoadContext;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import org.jetbrains.annotations.Nullable;

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, property = "proxyType")
@XmlJavaTypeAdapter(LoadContext.ProxyConfigurationTracker.class)
public interface IProxyConfiguration<T extends com.polarion.synchronizer.configuration.IConnection> {
  @XmlAttribute
  @XmlJavaTypeAdapter(ConnectionPlaceholderAdapter.class)
  @Nullable
  T getConnection();
  
  void setConnection(@Nullable T paramT);
  
  @JsonIgnore
  String getSystemIdentifier();
}


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/IProxyConfiguration.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */