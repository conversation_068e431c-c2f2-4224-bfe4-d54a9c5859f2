/*    */ package com.polarion.synchronizer.proxy.doors.xmlmodel;
/*    */ 
/*    */ import java.util.ArrayList;
/*    */ import java.util.List;
/*    */ import javax.xml.bind.annotation.XmlAttribute;
/*    */ import javax.xml.bind.annotation.XmlElement;
/*    */ import javax.xml.bind.annotation.XmlRootElement;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @XmlRootElement(name = "publications")
/*    */ public class DoorsPublications
/*    */ {
/*    */   @NotNull
/* 37 */   private final List<DoorsPublication> publications = new ArrayList<>();
/*    */ 
/*    */   
/*    */   private String id;
/*    */   
/*    */   private Integer expectedNumberOfObjects;
/*    */ 
/*    */   
/*    */   @XmlElement(name = "publication")
/*    */   @NotNull
/*    */   public List<DoorsPublication> getPublications() {
/* 48 */     return this.publications;
/*    */   }
/*    */   
/*    */   public String getId() {
/* 52 */     return this.id;
/*    */   }
/*    */   
/*    */   public void setId(String id) {
/* 56 */     this.id = id;
/*    */   }
/*    */   
/*    */   @NotNull
/*    */   public Integer getExpectedNumberOfObjects() {
/* 61 */     return this.expectedNumberOfObjects;
/*    */   }
/*    */   
/*    */   @XmlAttribute
/*    */   public void setExpectedNumberOfObjects(@NotNull Integer expectedNumberOfObjects) {
/* 66 */     this.expectedNumberOfObjects = expectedNumberOfObjects;
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/xmlmodel/DoorsPublications.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */