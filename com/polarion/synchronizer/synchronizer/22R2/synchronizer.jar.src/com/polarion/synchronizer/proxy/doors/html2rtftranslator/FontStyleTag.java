/*    */ package com.polarion.synchronizer.proxy.doors.html2rtftranslator;
/*    */ 
/*    */ import com.polarion.synchronizer.proxy.htmltranslator.ElementInfo;
/*    */ import com.polarion.synchronizer.proxy.htmltranslator.StyleTag;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class FontStyleTag
/*    */   implements StyleTag
/*    */ {
/*    */   public static final String KEY = "font-style";
/*    */   
/*    */   public void open(@NotNull ElementInfo elementInfo, @NotNull String text, @NotNull StringBuilder sb) {
/* 35 */     if (!text.isEmpty()) {
/* 36 */       String fontStyle = (String)elementInfo.getStyle().get("font-style");
/* 37 */       if ("italic".equals(fontStyle)) {
/* 38 */         sb.append("\\i ");
/*    */       }
/*    */     } 
/*    */   }
/*    */ 
/*    */   
/*    */   public void close(@NotNull ElementInfo elementInfo, @NotNull String text, @NotNull StringBuilder sb) {
/* 45 */     if (!text.isEmpty()) {
/* 46 */       String fontStyle = (String)elementInfo.getStyle().get("font-style");
/* 47 */       if ("italic".equals(fontStyle))
/* 48 */         sb.append("\\i0 "); 
/*    */     } 
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/html2rtftranslator/FontStyleTag.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */