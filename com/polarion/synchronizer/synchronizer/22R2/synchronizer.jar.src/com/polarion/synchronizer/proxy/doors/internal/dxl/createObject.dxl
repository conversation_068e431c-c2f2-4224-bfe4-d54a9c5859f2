Object o
Object refObj = object(%absoluteNumber%, m)
int itemPosition = %itemPosition%

if (null(refObj))
{
	noError()
	Object oFirst = first m;
	if(itemPosition == 0)
		o = create(m)
	else
		o = (null oFirst)?(Object create m):(create last sibling oFirst);
}
else
{
    noError()
    if(itemPosition == 0)
        o = create below(refObj)
    else
        o = create after(refObj)
}
string qualID = qualifiedUniqueID(m)
if(!null(o))
{
	string qualAbsNo = qualID "/" o."Absolute Number"""
	result = qualAbsNo
}

string ErrMess = lastError()
if (!null ErrMess)
{
	result = ErrMess	
}
print(result"\n")