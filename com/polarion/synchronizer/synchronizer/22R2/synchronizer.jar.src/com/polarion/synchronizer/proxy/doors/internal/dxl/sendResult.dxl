OleAutoArgs args = create
OleAutoArgs args2 = create
OleAutoArgs args3 = create
OleAutoObj http = oleCreateAutoObject("WinHttp.WinHttpRequest.5.1")
void sendResult(string result) {
	clear(args)
	clear(args2)
	put(args, "POST")
	put(args, resultUrl)
	OleAutoArgs args2 = create
	put(args2, result  "\nCONTINUE\n")
	put(args3, 0)
	put(args3, 60000)
	put(args3, 30000)
	put(args3, %receiveTimeout%)
	oleMethod(http,"settimeouts",args3)
	oleMethod(http, "open", args)
	string res =  oleMethod(http, "send", args2)
	if(!null(res)){
		print "\n" stringOf(dateAndTime(today)) ": Sending data data Failed"
		print "\n" res
	}
}
