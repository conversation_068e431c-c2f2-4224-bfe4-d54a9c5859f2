/*    */ package com.polarion.synchronizer.internal;
/*    */ 
/*    */ import com.polarion.synchronizer.model.IProxy;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class SystemContext
/*    */ {
/*    */   @NotNull
/*    */   private final String systemId;
/*    */   @NotNull
/*    */   private final IProxy proxy;
/*    */   
/*    */   public SystemContext(String systemId, IProxy proxy) {
/* 36 */     this.systemId = systemId;
/* 37 */     this.proxy = proxy;
/*    */   }
/*    */   
/*    */   public String getSystemId() {
/* 41 */     return this.systemId;
/*    */   }
/*    */   
/*    */   public IProxy getProxy() {
/* 45 */     return this.proxy;
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/internal/SystemContext.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */