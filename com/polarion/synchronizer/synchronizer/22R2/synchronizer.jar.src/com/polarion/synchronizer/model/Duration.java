/*    */ package com.polarion.synchronizer.model;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ 
/*    */ public class Duration
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 8201917784896421330L;
/*    */   private final String stringRepresentation;
/*    */   private final long absoluteDuration;
/*    */   
/*    */   public Duration(String stringRepresentation, long absoluteDuration) {
/* 14 */     this.stringRepresentation = stringRepresentation;
/* 15 */     this.absoluteDuration = absoluteDuration;
/*    */   }
/*    */   
/*    */   public Duration(long absoluteDuration) {
/* 19 */     this.stringRepresentation = null;
/* 20 */     this.absoluteDuration = absoluteDuration;
/*    */   }
/*    */   
/*    */   public String getStringRepresentation() {
/* 24 */     return this.stringRepresentation;
/*    */   }
/*    */   
/*    */   public long getAbsoluteDuration() {
/* 28 */     return this.absoluteDuration;
/*    */   }
/*    */ 
/*    */   
/*    */   public int hashCode() {
/* 33 */     int prime = 31;
/* 34 */     int result = 1;
/* 35 */     result = 31 * result + (int)(this.absoluteDuration ^ this.absoluteDuration >>> 32L);
/* 36 */     return result;
/*    */   }
/*    */ 
/*    */   
/*    */   public boolean equals(Object obj) {
/* 41 */     if (this == obj)
/* 42 */       return true; 
/* 43 */     if (obj == null)
/* 44 */       return false; 
/* 45 */     if (getClass() != obj.getClass())
/* 46 */       return false; 
/* 47 */     Duration other = (Duration)obj;
/* 48 */     if (this.absoluteDuration != other.absoluteDuration)
/* 49 */       return false; 
/* 50 */     return true;
/*    */   }
/*    */ 
/*    */   
/*    */   public String toString() {
/* 55 */     return "Duration [stringRepresentation=" + this.stringRepresentation + ", absoluteDuration=" + this.absoluteDuration + "]";
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/model/Duration.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */