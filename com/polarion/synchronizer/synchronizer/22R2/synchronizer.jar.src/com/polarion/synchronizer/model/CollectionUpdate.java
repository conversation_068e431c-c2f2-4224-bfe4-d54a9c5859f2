/*     */ package com.polarion.synchronizer.model;
/*     */ 
/*     */ import java.util.Collection;
/*     */ import java.util.Collections;
/*     */ import java.util.LinkedHashSet;
/*     */ 
/*     */ public class CollectionUpdate<T>
/*     */ {
/*     */   private Collection<T> added;
/*     */   private Collection<T> removed;
/*     */   private Collection<T> updated;
/*     */   
/*     */   public CollectionUpdate(Collection<T> added, Collection<T> removed) {
/*  14 */     this(added, removed, null);
/*     */   }
/*     */   
/*     */   public CollectionUpdate(Collection<T> added, Collection<T> removed, Collection<T> updated) {
/*  18 */     if (added == null) {
/*  19 */       this.added = Collections.emptySet();
/*     */     } else {
/*  21 */       this.added = new LinkedHashSet<>(added);
/*     */     } 
/*     */     
/*  24 */     if (removed == null) {
/*  25 */       this.removed = Collections.emptySet();
/*     */     } else {
/*  27 */       this.removed = new LinkedHashSet<>(removed);
/*     */     } 
/*     */     
/*  30 */     if (updated == null) {
/*  31 */       this.updated = Collections.emptySet();
/*     */     } else {
/*  33 */       this.updated = new LinkedHashSet<>(updated);
/*     */     } 
/*     */   }
/*     */   
/*     */   public Collection<T> getAdded() {
/*  38 */     return this.added;
/*     */   }
/*     */   
/*     */   public Collection<T> getRemoved() {
/*  42 */     return this.removed;
/*     */   }
/*     */   
/*     */   public Collection<T> getUpdated() {
/*  46 */     return this.updated;
/*     */   }
/*     */   
/*     */   public Collection<T> applyTo(Object value) {
/*     */     Collection<T> newValue;
/*  51 */     if (value instanceof Collection) {
/*  52 */       newValue = new LinkedHashSet<>((Collection<? extends T>)value);
/*  53 */       newValue.removeAll(getRemoved());
/*  54 */       newValue.addAll(getAdded());
/*     */     } else {
/*  56 */       newValue = new LinkedHashSet<>(getAdded());
/*     */     } 
/*  58 */     return newValue;
/*     */   }
/*     */   
/*     */   public boolean isEmpty() {
/*  62 */     return (this.added.isEmpty() && this.removed.isEmpty());
/*     */   }
/*     */ 
/*     */   
/*     */   public int hashCode() {
/*  67 */     int prime = 31;
/*  68 */     int result = 1;
/*  69 */     result = 31 * result + ((this.added == null) ? 0 : this.added.hashCode());
/*  70 */     result = 31 * result + ((this.removed == null) ? 0 : this.removed.hashCode());
/*  71 */     result = 31 * result + ((this.updated == null) ? 0 : this.updated.hashCode());
/*  72 */     return result;
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean equals(Object obj) {
/*  77 */     if (this == obj) {
/*  78 */       return true;
/*     */     }
/*  80 */     if (obj == null) {
/*  81 */       return false;
/*     */     }
/*  83 */     if (getClass() != obj.getClass()) {
/*  84 */       return false;
/*     */     }
/*     */     
/*  87 */     CollectionUpdate other = (CollectionUpdate)obj;
/*  88 */     if (this.added == null) {
/*  89 */       if (other.added != null) {
/*  90 */         return false;
/*     */       }
/*  92 */     } else if (!this.added.equals(other.added)) {
/*  93 */       return false;
/*     */     } 
/*  95 */     if (this.removed == null) {
/*  96 */       if (other.removed != null) {
/*  97 */         return false;
/*     */       }
/*  99 */     } else if (!this.removed.equals(other.removed)) {
/* 100 */       return false;
/*     */     } 
/* 102 */     if (this.updated == null) {
/* 103 */       if (other.updated != null) {
/* 104 */         return false;
/*     */       }
/* 106 */     } else if (!this.updated.equals(other.updated)) {
/* 107 */       return false;
/*     */     } 
/* 109 */     return true;
/*     */   }
/*     */ 
/*     */   
/*     */   public String toString() {
/* 114 */     return "CollectionUpdate [added=" + this.added + ", removed=" + this.removed + ", updated=" + this.updated + "]";
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/model/CollectionUpdate.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */