/*     */ package com.polarion.synchronizer.retryactions;
/*     */ 
/*     */ import com.polarion.core.util.logging.Logger;
/*     */ import java.time.Duration;
/*     */ import java.util.List;
/*     */ import java.util.Objects;
/*     */ import java.util.Optional;
/*     */ import java.util.function.Consumer;
/*     */ import java.util.function.Predicate;
/*     */ import java.util.stream.Collectors;
/*     */ import java.util.stream.Stream;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ 
/*     */ 
/*     */ 
/*     */ public class BackoffRetryActionStrategy
/*     */   implements IRetryActionStrategy
/*     */ {
/*     */   private int maxRetries;
/*     */   @NotNull
/*     */   private Duration delay;
/*     */   private int delayFactor;
/*  23 */   private static final Logger log = Logger.getLogger(BackoffRetryActionStrategy.class);
/*     */   
/*     */   @NotNull
/*     */   public static Builder newBuilder() {
/*  27 */     (new BackoffRetryActionStrategy()).getClass(); return new Builder();
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   public static Builder newBuilder(@NotNull String parameter) {
/*  32 */     (new BackoffRetryActionStrategy(parameter)).getClass(); return new Builder();
/*     */   }
/*     */   
/*     */   private BackoffRetryActionStrategy() {
/*  36 */     this.maxRetries = 2;
/*  37 */     this.delay = Duration.ofSeconds(10L);
/*  38 */     this.delayFactor = 2;
/*     */   }
/*     */   
/*     */   private BackoffRetryActionStrategy(@NotNull String parameter) {
/*  42 */     List<Integer> values = parseConfiguration(parameter);
/*  43 */     this.maxRetries = ((Integer)values.get(0)).intValue();
/*  44 */     this.delay = Duration.ofSeconds(((Integer)values.get(1)).intValue());
/*  45 */     this.delayFactor = ((Integer)values.get(2)).intValue();
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   public static List<Integer> parseConfiguration(@NotNull String parameter) {
/*  50 */     List<Integer> defaultValue = List.of(Integer.valueOf(2), Integer.valueOf(10), Integer.valueOf(2));
/*     */     try {
/*  52 */       List<Integer> values = (List<Integer>)Stream.<String>of(parameter.trim().split(",")).filter(Objects::nonNull).map(value -> Integer.valueOf(Integer.parseInt(value.trim()))).collect(Collectors.toList());
/*  53 */       return (values.size() == 3) ? values : defaultValue;
/*     */     }
/*  55 */     catch (Exception e) {
/*  56 */       log.warn("The Polarion configuration property: com.siemens.polarion.connectors.retryStrategy contains wrong value. The default values <2,10,2> are used");
/*  57 */       return defaultValue;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public class Builder
/*     */   {
/*     */     @NotNull
/*     */     public Builder setMaxRetries(int maxRetries) {
/*  68 */       BackoffRetryActionStrategy.this.maxRetries = maxRetries;
/*  69 */       return this;
/*     */     }
/*     */     
/*     */     @NotNull
/*     */     public Builder setDelay(@NotNull Duration delay) {
/*  74 */       BackoffRetryActionStrategy.this.delay = delay;
/*  75 */       return this;
/*     */     }
/*     */     
/*     */     @NotNull
/*     */     public Builder setDelayFactor(int delayFactor) {
/*  80 */       BackoffRetryActionStrategy.this.delayFactor = delayFactor;
/*  81 */       return this;
/*     */     }
/*     */     
/*     */     @NotNull
/*     */     public BackoffRetryActionStrategy build() {
/*  86 */       return BackoffRetryActionStrategy.this;
/*     */     }
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public <T> Optional<T> execute(@NotNull IRetryAction<T> retryAction, @NotNull Predicate<T> failIfPredicate, @NotNull Consumer<Optional<T>> beforeRetryAction) {
/*  93 */     int triesCount = 0;
/*  94 */     T result = null;
/*     */     while (true) {
/*     */       try {
/*  97 */         result = retryAction.handle();
/*  98 */         if (failIfPredicate.test(result)) {
/*  99 */           if (triesCount >= this.maxRetries) {
/* 100 */             return Optional.ofNullable(result);
/*     */           }
/*     */         } else {
/* 103 */           return Optional.ofNullable(result);
/*     */         } 
/* 105 */       } catch (Exception ex) {
/* 106 */         if (triesCount >= this.maxRetries) {
/* 107 */           throw new RetryActionExecutionException(ex);
/*     */         }
/*     */       } 
/* 110 */       beforeRetryAction.accept(Optional.ofNullable(result));
/*     */       try {
/* 112 */         Thread.sleep((long)Math.pow(this.delayFactor, triesCount) * this.delay.toMillis());
/* 113 */       } catch (InterruptedException e) {
/* 114 */         log.warn("The thread is interrupted!", e);
/* 115 */         Thread.currentThread().interrupt();
/*     */       } 
/* 117 */       triesCount++;
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/retryactions/BackoffRetryActionStrategy.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */