/*    */ package com.polarion.synchronizer.proxy.doors.internal.commands;
/*    */ 
/*    */ import com.polarion.synchronizer.proxy.doors.internal.dxl.DxlScriptBuilder;
/*    */ import com.polarion.synchronizer.proxy.doors.internal.dxl.IDxlScript;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ 
/*    */ public class DoorsCommandCreateObject
/*    */   extends AbstractDoorsCommand<String>
/*    */ {
/*    */   @NotNull
/*    */   private final String absoluteNumber;
/*    */   @NotNull
/*    */   private final String itemPosition;
/*    */   
/*    */   public DoorsCommandCreateObject(@NotNull String absoluteNumber, int itemPosition) {
/* 16 */     this.absoluteNumber = absoluteNumber;
/* 17 */     this.itemPosition = String.valueOf(itemPosition);
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   protected IDxlScript getScript() {
/* 23 */     return (IDxlScript)DxlScriptBuilder.script("createObject.dxl")
/* 24 */       .replaceParameter("absoluteNumber", this.absoluteNumber)
/* 25 */       .replaceParameter("itemPosition", this.itemPosition);
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   protected String processResult(@NotNull String result) throws Exception {
/* 31 */     return result;
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/internal/commands/DoorsCommandCreateObject.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */