/*    */ package com.polarion.synchronizer.spi.translators;
/*    */ 
/*    */ import com.polarion.synchronizer.mapping.TranslationResult;
/*    */ 
/*    */ 
/*    */ public class PassThroughTranslator
/*    */   extends TypesafeTranslator<Object, Object, Object>
/*    */ {
/*    */   public PassThroughTranslator() {
/* 10 */     super(Object.class, Object.class);
/*    */   }
/*    */ 
/*    */   
/*    */   public TranslationResult translateUnidirectionalTypesafe(Object source, Object target) {
/* 15 */     return createUnidirectionalResult(source, target);
/*    */   }
/*    */ 
/*    */   
/*    */   public TranslationResult translateBidirectionalTypesafe(Object sourceBaseline, Object sourceValue, Object targetBaseline, Object targetValue) {
/* 20 */     return createBidirectionalResult(sourceBaseline, sourceValue, sourceValue, targetBaseline, targetValue);
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/spi/translators/PassThroughTranslator.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */