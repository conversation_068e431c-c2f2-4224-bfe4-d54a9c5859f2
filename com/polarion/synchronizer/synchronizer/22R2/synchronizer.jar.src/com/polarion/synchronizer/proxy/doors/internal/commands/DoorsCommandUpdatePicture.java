/*    */ package com.polarion.synchronizer.proxy.doors.internal.commands;
/*    */ 
/*    */ import com.polarion.synchronizer.proxy.doors.internal.dxl.DxlScriptBuilder;
/*    */ import com.polarion.synchronizer.proxy.doors.internal.dxl.IDxlScript;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class DoorsCommandUpdatePicture
/*    */   extends AbstractDoorsCommand<Boolean>
/*    */ {
/*    */   @NotNull
/*    */   private final String pictureAsOle;
/*    */   @NotNull
/*    */   private final String pictureName;
/*    */   @NotNull
/*    */   private final String absoluteNumber;
/*    */   
/*    */   public DoorsCommandUpdatePicture(@NotNull String pictureAsOle, @NotNull String pictureName, @NotNull String absoluteNumber) {
/* 39 */     this.pictureAsOle = pictureAsOle;
/* 40 */     this.pictureName = pictureName;
/* 41 */     this.absoluteNumber = absoluteNumber;
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   protected IDxlScript getScript() {
/* 47 */     return (IDxlScript)DxlScriptBuilder.script("UpdatePicture.dxl")
/* 48 */       .replaceParameter("absoluteNumber", this.absoluteNumber)
/* 49 */       .replaceParameter("pictureAsOle", this.pictureAsOle)
/* 50 */       .replaceParameter("pictureName", this.pictureName);
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   protected Boolean processResult(@NotNull String result) throws Exception {
/* 56 */     return Boolean.valueOf(result.equals("OK"));
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/internal/commands/DoorsCommandUpdatePicture.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */