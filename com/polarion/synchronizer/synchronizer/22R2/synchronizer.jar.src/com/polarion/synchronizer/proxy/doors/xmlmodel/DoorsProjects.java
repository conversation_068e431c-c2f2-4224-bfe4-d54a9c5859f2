/*    */ package com.polarion.synchronizer.proxy.doors.xmlmodel;
/*    */ 
/*    */ import java.util.ArrayList;
/*    */ import java.util.List;
/*    */ import javax.xml.bind.annotation.XmlElement;
/*    */ import javax.xml.bind.annotation.XmlRootElement;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @XmlRootElement(name = "projects")
/*    */ public class DoorsProjects
/*    */ {
/*    */   @NotNull
/* 36 */   private List<String> projects = new ArrayList<>();
/*    */   
/*    */   @XmlElement(name = "project")
/*    */   @NotNull
/*    */   public List<String> getProjects() {
/* 41 */     return this.projects;
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/xmlmodel/DoorsProjects.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */