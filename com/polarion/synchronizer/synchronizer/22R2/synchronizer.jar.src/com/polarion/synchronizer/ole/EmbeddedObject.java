/*     */ package com.polarion.synchronizer.ole;
/*     */ 
/*     */ import com.polarion.synchronizer.mapping.RtfWrapper;
/*     */ import java.io.ByteArrayInputStream;
/*     */ import java.io.IOException;
/*     */ import java.io.InputStream;
/*     */ import java.util.Optional;
/*     */ import org.apache.commons.codec.DecoderException;
/*     */ import org.apache.commons.codec.binary.Hex;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ 
/*     */ public class EmbeddedObject {
/*     */   @NotNull
/*     */   private final byte[] data;
/*     */   private final int objectStart;
/*     */   private int objectEnd;
/*     */   private int binaryStart;
/*     */   private int binaryEnd;
/*     */   private int width;
/*     */   private int height;
/*     */   
/*     */   private final class IgnoreWhitespaceStream
/*     */     extends InputStream {
/*     */     public IgnoreWhitespaceStream(InputStream in) {
/*  25 */       this.in = in;
/*     */     }
/*     */     @NotNull
/*     */     private final InputStream in;
/*     */     public int read() throws IOException {
/*  30 */       int read = this.in.read();
/*  31 */       while (read != -1 && Character.isWhitespace((char)read)) {
/*  32 */         read = this.in.read();
/*     */       }
/*  34 */       return read;
/*     */     }
/*     */   }
/*     */   
/*     */   private final class HexInputStream
/*     */     extends InputStream
/*     */   {
/*     */     @NotNull
/*     */     private final InputStream in;
/*     */     
/*     */     public HexInputStream(InputStream in) {
/*  45 */       this.in = in;
/*     */     }
/*     */ 
/*     */     
/*     */     public int read() throws IOException {
/*  50 */       byte[] bytes = new byte[2];
/*  51 */       int read = this.in.read(bytes);
/*  52 */       if (read < 2) {
/*  53 */         return -1;
/*     */       }
/*     */       
/*     */       try {
/*  57 */         return Hex.decodeHex(new char[] { (char)bytes[0], (char)bytes[1] })[0] & 0xFF;
/*  58 */       } catch (DecoderException e) {
/*  59 */         throw new IOException(e);
/*     */       } 
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public EmbeddedObject(@NotNull byte[] data, int objectStart) {
/*  77 */     this.data = data;
/*  78 */     this.objectStart = objectStart;
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   public InputStream getData() {
/*  83 */     return new ByteArrayInputStream(this.data, this.objectStart, this.objectEnd - this.objectStart + 1);
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   public InputStream getBinaryData() {
/*  88 */     return new HexInputStream(new IgnoreWhitespaceStream(new ByteArrayInputStream(this.data, this.binaryStart, this.binaryEnd - this.binaryStart)));
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public InputStream getDataAsRtf() {
/*  94 */     return RtfWrapper.wrapRTF(getData());
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   public Optional<EmbeddedObject> getThumbnail() {
/*  99 */     return Optional.ofNullable(this);
/*     */   }
/*     */   
/*     */   public int getWidth() {
/* 103 */     return this.width;
/*     */   }
/*     */   
/*     */   public int getHeight() {
/* 107 */     return this.height;
/*     */   }
/*     */   
/*     */   void setWidth(int width) {
/* 111 */     this.width = width;
/*     */   }
/*     */   
/*     */   void setHeight(int height) {
/* 115 */     this.height = height;
/*     */   }
/*     */   
/*     */   void setObjectEnd(int objectEnd) {
/* 119 */     this.objectEnd = objectEnd;
/*     */   }
/*     */   
/*     */   void setBinaryStart(int binaryStart) {
/* 123 */     this.binaryStart = binaryStart;
/*     */   }
/*     */   
/*     */   void setBinaryEnd(int binaryEnd) {
/* 127 */     this.binaryEnd = binaryEnd;
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/ole/EmbeddedObject.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */