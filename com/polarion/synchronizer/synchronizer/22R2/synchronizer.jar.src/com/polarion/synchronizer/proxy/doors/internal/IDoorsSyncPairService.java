package com.polarion.synchronizer.proxy.doors.internal;

import com.polarion.synchronizer.configuration.ISyncPair;
import java.util.Collection;
import org.jetbrains.annotations.NotNull;

public interface IDoorsSyncPairService {
  void createAndUpdateSyncPairs(@NotNull String paramString, @NotNull Collection<ISyncPair> paramCollection1, @NotNull Collection<ISyncPair> paramCollection2);
}


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/internal/IDoorsSyncPairService.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */