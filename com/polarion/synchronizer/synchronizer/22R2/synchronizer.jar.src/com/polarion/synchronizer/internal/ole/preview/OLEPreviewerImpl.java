/*    */ package com.polarion.synchronizer.internal.ole.preview;
/*    */ 
/*    */ import com.polarion.ooxml.oleconverter.IOleConverter;
/*    */ import com.polarion.synchronizer.ole.EmbeddedObject;
/*    */ import com.polarion.synchronizer.ole.OleExtractor;
/*    */ import com.siemens.polarion.previewer.IPreviewResult;
/*    */ import com.siemens.polarion.previewer.IPreviewer;
/*    */ import com.siemens.polarion.previewer.PreviewerParameters;
/*    */ import com.siemens.polarion.previewer.external.AbstractParallelPreviewer;
/*    */ import com.siemens.polarion.previewer.internal.PreviewGeneratorInParallelExecutor;
/*    */ import java.io.InputStream;
/*    */ import java.util.Optional;
/*    */ import java.util.function.Supplier;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ import org.jetbrains.annotations.Nullable;
/*    */ 
/*    */ 
/*    */ 
/*    */ public class OLEPreviewerImpl
/*    */   extends AbstractParallelPreviewer
/*    */ {
/*    */   public OLEPreviewerImpl(@NotNull IOleConverter converter) {
/* 23 */     super(new OLEPreviewGenerator(converter));
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   public IPreviewResult generatePreview(@NotNull InputStream data, @NotNull PreviewerParameters previewParameters, @Nullable Supplier<Boolean> executeGeneration) {
/* 29 */     return OleExtractor.parse(data).getWrappedObject()
/* 30 */       .flatMap(o -> o.getThumbnail())
/* 31 */       .map(t -> (new PreviewGeneratorInParallelExecutor(this.parallelPreviewGenerator, t.getBinaryData(), paramPreviewerParameters, paramSupplier)).execute())
/* 32 */       .orElseGet(() -> (new IPreviewer.EmptyPreviewer()).generatePreview(paramInputStream, paramPreviewerParameters, paramSupplier));
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/internal/ole/preview/OLEPreviewerImpl.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */