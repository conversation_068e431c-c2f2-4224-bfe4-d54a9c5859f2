/*    */ package com.polarion.synchronizer.proxy.doors.internal.commands;
/*    */ 
/*    */ import com.polarion.synchronizer.proxy.doors.internal.dxl.DxlScriptBuilder;
/*    */ import com.polarion.synchronizer.proxy.doors.internal.dxl.IDxlScript;
/*    */ import com.polarion.synchronizer.proxy.doors.xmlmodel.DoorsMetadata;
/*    */ import java.nio.charset.Charset;
/*    */ import javax.xml.bind.JAXB;
/*    */ import org.apache.commons.io.IOUtils;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class DoorsCommandMetadata
/*    */   extends AbstractDoorsCommand<DoorsMetadata>
/*    */ {
/*    */   @NotNull
/*    */   public DoorsMetadata processResult(@NotNull String result) {
/* 40 */     return (DoorsMetadata)JAXB.unmarshal(IOUtils.toInputStream(result, Charset.forName("UTF-8")), DoorsMetadata.class);
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   public IDxlScript getScript() {
/* 46 */     return (IDxlScript)DxlScriptBuilder.create().add((IDxlScript)DxlScriptBuilder.script("cleanName.dxl"))
/* 47 */       .add((IDxlScript)DxlScriptBuilder.script("getDefinedFields.dxl"));
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/internal/commands/DoorsCommandMetadata.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */