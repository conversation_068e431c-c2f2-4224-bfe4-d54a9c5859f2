/*    */ package com.polarion.synchronizer.proxy.doors.internal;
/*    */ 
/*    */ import com.polarion.synchronizer.SynchronizationException;
/*    */ import com.polarion.synchronizer.proxy.doors.internal.commands.DelegatingCommand;
/*    */ import com.polarion.synchronizer.proxy.doors.internal.commands.DoorsCommandCheckModuleLock;
/*    */ import com.polarion.synchronizer.proxy.doors.internal.commands.IDoorsCommand;
/*    */ import com.polarion.synchronizer.proxy.doors.internal.dxl.DxlScriptBuilder;
/*    */ import com.polarion.synchronizer.proxy.doors.internal.dxl.IDxlScript;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class DoorsModule
/*    */   implements IDoorsModule
/*    */ {
/*    */   @NotNull
/*    */   private final String moduleId;
/*    */   @NotNull
/*    */   private final InternalDoorsConnection connection;
/*    */   
/*    */   private final class ModuleCommand<T>
/*    */     extends DelegatingCommand<T>
/*    */   {
/*    */     private final boolean editMode;
/*    */     
/*    */     private ModuleCommand(IDoorsCommand<T> command, boolean editMode) {
/* 40 */       super(command);
/* 41 */       this.editMode = editMode;
/*    */     }
/*    */ 
/*    */     
/*    */     @NotNull
/*    */     public IDxlScript execute() {
/* 47 */       return (IDxlScript)DxlScriptBuilder.create()
/* 48 */         .add((IDxlScript)DxlScriptBuilder.script("ModuleById.dxl")
/* 49 */           .replaceParameter("mode", this.editMode ? "edit" : "read")
/* 50 */           .replaceParameter("module.id", DoorsModule.this.moduleId))
/* 51 */         .add(super.execute());
/*    */     }
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public DoorsModule(@NotNull String moduleId, @NotNull InternalDoorsConnection connection) {
/* 62 */     this.moduleId = moduleId;
/* 63 */     this.connection = connection;
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   public <T> T execute(@NotNull IDoorsCommand<T> command) {
/* 69 */     return execute(command, false);
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   public String getModuleId() {
/* 75 */     return this.moduleId;
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   public <T> T execute(@NotNull IDoorsCommand<T> command, boolean editMode) {
/* 81 */     if (editMode && ((Boolean)this.connection.<Boolean>execute((IDoorsCommand<Boolean>)new DoorsCommandCheckModuleLock(this.moduleId))).booleanValue()) {
/* 82 */       throw new SynchronizationException("Module being synchronized is locked by other Doors user");
/*    */     }
/* 84 */     return this.connection.execute((IDoorsCommand<T>)new ModuleCommand<>(command, editMode));
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/internal/DoorsModule.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */