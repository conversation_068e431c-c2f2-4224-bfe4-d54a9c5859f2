/*     */ package com.polarion.synchronizer.internal.mapping;
/*     */ 
/*     */ import com.polarion.synchronizer.configuration.IAttributeMapper;
/*     */ import com.polarion.synchronizer.model.Side;
/*     */ import com.polarion.synchronizer.model.TransferItem;
/*     */ import java.util.Collection;
/*     */ import java.util.HashMap;
/*     */ import java.util.HashSet;
/*     */ import java.util.Map;
/*     */ 
/*     */ public class MultiTypeMapping implements IAttributeMapper {
/*     */   private final DefaultFieldMappingGroup defaultMapping;
/*     */   
/*     */   private class TypeKey {
/*     */     private final String leftType;
/*     */     private final String rightType;
/*     */     
/*     */     public TypeKey(String leftType, String rightType) {
/*  19 */       this.leftType = leftType;
/*  20 */       this.rightType = rightType;
/*     */     }
/*     */ 
/*     */     
/*     */     public int hashCode() {
/*  25 */       int prime = 31;
/*  26 */       int result = 1;
/*  27 */       result = 31 * result + getOuterType().hashCode();
/*  28 */       result = 31 * result + ((this.leftType == null) ? 0 : this.leftType.hashCode());
/*  29 */       result = 31 * result + ((this.rightType == null) ? 0 : this.rightType.hashCode());
/*  30 */       return result;
/*     */     }
/*     */ 
/*     */     
/*     */     public boolean equals(Object obj) {
/*  35 */       if (this == obj) {
/*  36 */         return true;
/*     */       }
/*  38 */       if (obj == null) {
/*  39 */         return false;
/*     */       }
/*  41 */       if (getClass() != obj.getClass()) {
/*  42 */         return false;
/*     */       }
/*  44 */       TypeKey other = (TypeKey)obj;
/*  45 */       if (!getOuterType().equals(other.getOuterType())) {
/*  46 */         return false;
/*     */       }
/*  48 */       if (this.leftType == null) {
/*  49 */         if (other.leftType != null) {
/*  50 */           return false;
/*     */         }
/*  52 */       } else if (!this.leftType.equals(other.leftType)) {
/*  53 */         return false;
/*     */       } 
/*  55 */       if (this.rightType == null) {
/*  56 */         if (other.rightType != null) {
/*  57 */           return false;
/*     */         }
/*  59 */       } else if (!this.rightType.equals(other.rightType)) {
/*  60 */         return false;
/*     */       } 
/*  62 */       return true;
/*     */     }
/*     */     
/*     */     private MultiTypeMapping getOuterType() {
/*  66 */       return MultiTypeMapping.this;
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  73 */   private final Map<TypeKey, FieldMappingGroup> typeMappings = new HashMap<>();
/*     */   
/*     */   public MultiTypeMapping(DefaultFieldMappingGroup defaultMapping) {
/*  76 */     this.defaultMapping = defaultMapping;
/*     */   }
/*     */ 
/*     */   
/*     */   public Collection<String> getRequiredFields(Side fromSide) {
/*  81 */     Collection<String> requiredFields = new HashSet<>();
/*  82 */     requiredFields.addAll(this.defaultMapping.getRequiredFields(fromSide));
/*  83 */     for (FieldMappingGroup typeSpecificMapping : this.typeMappings.values()) {
/*  84 */       requiredFields.addAll(typeSpecificMapping.getRequiredFields(fromSide));
/*     */     }
/*  86 */     return requiredFields;
/*     */   }
/*     */   
/*     */   public void addTypeMapping(String leftType, String rightType, FieldMappingGroup typeSpecificMapping) {
/*  90 */     TypeKey condition = new TypeKey(leftType, rightType);
/*  91 */     this.typeMappings.put(condition, typeSpecificMapping);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public IAttributeMapper.BidirectionalResult map(TransferItem leftItem, Map<String, Object> leftBaseline, TransferItem rightItem, Map<String, Object> rightBaseline) {
/*  97 */     IAttributeMapper.BidirectionalResult defaultResult = this.defaultMapping.map(leftItem, leftBaseline, rightItem, rightBaseline);
/*     */     
/*  99 */     String typeLeft = loadType(leftItem, defaultResult.getMappedRight());
/* 100 */     String typeRight = loadType(rightItem, defaultResult.getMappedLeft());
/*     */     
/* 102 */     FieldMappingGroup typeSpecific = this.typeMappings.get(new TypeKey(typeLeft, typeRight));
/* 103 */     if (typeSpecific != null) {
/* 104 */       IAttributeMapper.BidirectionalResult typeSpecificResult = typeSpecific.map(leftItem, leftBaseline, rightItem, rightBaseline);
/* 105 */       defaultResult.getMappedLeft().getValues().putAll(typeSpecificResult.getMappedLeft().getValues());
/* 106 */       defaultResult.getMappedRight().getValues().putAll(typeSpecificResult.getMappedRight().getValues());
/* 107 */       defaultResult.getLeftSourceBaseline().getValues().putAll(typeSpecificResult.getLeftSourceBaseline().getValues());
/* 108 */       defaultResult.getRightSourceBaseline().getValues().putAll(typeSpecificResult.getRightSourceBaseline().getValues());
/*     */     } 
/*     */     
/* 111 */     return defaultResult;
/*     */   }
/*     */   
/*     */   private String loadType(TransferItem current, TransferItem mapped) {
/* 115 */     String mappedType = mapped.getType();
/* 116 */     return (mappedType == null) ? current.getType() : mappedType;
/*     */   }
/*     */ 
/*     */   
/*     */   public IAttributeMapper.UnidirectionalResult map(TransferItem sourceItem, Side fromSide) {
/* 121 */     IAttributeMapper.UnidirectionalResult defaultResult = this.defaultMapping.map(sourceItem, fromSide);
/*     */     
/* 123 */     String typeLeft = (fromSide == Side.LEFT) ? sourceItem.getType() : defaultResult.getMapped().getType();
/* 124 */     String typeRight = (fromSide == Side.LEFT) ? defaultResult.getMapped().getType() : sourceItem.getType();
/*     */     
/* 126 */     FieldMappingGroup typeSpecific = this.typeMappings.get(new TypeKey(typeLeft, typeRight));
/* 127 */     if (typeSpecific != null) {
/* 128 */       IAttributeMapper.UnidirectionalResult typeSpecificResult = typeSpecific.map(sourceItem, fromSide);
/*     */       
/* 130 */       defaultResult.getMapped().getValues().putAll(typeSpecificResult.getMapped().getValues());
/* 131 */       defaultResult.getSourceBaseline().getValues().putAll(typeSpecificResult.getSourceBaseline().getValues());
/*     */     } 
/*     */     
/* 134 */     return defaultResult;
/*     */   }
/*     */ 
/*     */   
/*     */   public HierarchyProcessor loadHierarchyProcessor(Collection<TransferItem> leftSourceItems, Collection<TransferItem> rightSourceItems, boolean isPartial) {
/* 139 */     return this.defaultMapping.loadHierarchyProcessor(leftSourceItems, rightSourceItems, isPartial);
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/internal/mapping/MultiTypeMapping.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */