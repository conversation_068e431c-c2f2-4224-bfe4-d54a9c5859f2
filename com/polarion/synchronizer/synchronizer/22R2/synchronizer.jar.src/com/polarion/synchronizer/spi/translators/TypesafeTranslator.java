/*    */ package com.polarion.synchronizer.spi.translators;
/*    */ 
/*    */ import com.polarion.core.util.ObjectUtils;
/*    */ import com.polarion.synchronizer.mapping.ITranslator;
/*    */ import com.polarion.synchronizer.mapping.TranslationResult;
/*    */ import org.jetbrains.annotations.Nullable;
/*    */ 
/*    */ 
/*    */ public abstract class TypesafeTranslator<S, T, R>
/*    */   implements ITranslator<R>
/*    */ {
/*    */   private final Class<?> sourceType;
/*    */   private final Class<?> targetType;
/*    */   
/*    */   public TypesafeTranslator(Class<?> sourceType, Class<?> targetType) {
/* 16 */     this.sourceType = sourceType;
/* 17 */     this.targetType = targetType;
/*    */   }
/*    */ 
/*    */   
/*    */   public TranslationResult<R> translateBidirectional(Object sourceBaseline, Object sourceValue, Object targetBaseline, Object targetValue) {
/* 22 */     if (!this.sourceType.isInstance(sourceBaseline)) {
/* 23 */       sourceBaseline = null;
/*    */     }
/* 25 */     if (!this.sourceType.isInstance(sourceValue)) {
/* 26 */       sourceValue = null;
/*    */     }
/* 28 */     if (!this.targetType.isInstance(targetBaseline)) {
/* 29 */       targetBaseline = null;
/*    */     }
/* 31 */     if (!this.targetType.isInstance(targetValue)) {
/* 32 */       targetValue = null;
/*    */     }
/*    */     
/* 35 */     return translateBidirectionalTypesafe((S)sourceBaseline, (S)sourceValue, (T)targetBaseline, (T)targetValue);
/*    */   }
/*    */ 
/*    */   
/*    */   public TranslationResult<R> translateUnidirectional(Object sourceValue, Object targetValue) {
/* 40 */     if (!this.sourceType.isInstance(sourceValue)) {
/* 41 */       sourceValue = null;
/*    */     }
/* 43 */     if (!this.targetType.isInstance(targetValue)) {
/* 44 */       targetValue = null;
/*    */     }
/*    */     
/* 47 */     return translateUnidirectionalTypesafe((S)sourceValue, (T)targetValue);
/*    */   }
/*    */ 
/*    */   
/*    */   public abstract TranslationResult<R> translateBidirectionalTypesafe(S paramS1, S paramS2, T paramT1, T paramT2);
/*    */   
/*    */   public abstract TranslationResult<R> translateUnidirectionalTypesafe(S paramS, T paramT);
/*    */   
/*    */   protected TranslationResult<R> createUnidirectionalResult(R mappedValue, T targetValue) {
/* 56 */     return new TranslationResult(mappedValue, !ObjectUtils.equalsWithNull(mappedValue, targetValue), false);
/*    */   }
/*    */ 
/*    */   
/*    */   protected TranslationResult<R> createBidirectionalResult(S sourceBaseline, S sourceValue, R mappedValue, T targetBaseline, T targetValue) {
/* 61 */     boolean sourceModified = isSourceModified(sourceValue, sourceBaseline);
/* 62 */     TranslationResult<R> result = new TranslationResult(
/* 63 */         mappedValue, (!ObjectUtils.equalsWithNull(mappedValue, targetValue) && sourceModified), 
/* 64 */         (sourceModified && !ObjectUtils.equalsWithNull(targetValue, targetBaseline)));
/* 65 */     return result;
/*    */   }
/*    */   
/*    */   protected boolean isSourceModified(@Nullable S sourceValue, @Nullable S sourceBaseline) {
/* 69 */     return !ObjectUtils.equalsWithNull(sourceValue, sourceBaseline);
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/spi/translators/TypesafeTranslator.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */