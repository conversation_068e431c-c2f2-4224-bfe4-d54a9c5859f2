/*     */ package com.polarion.synchronizer.proxy.doors.xmlmodel;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.List;
/*     */ import javax.xml.bind.annotation.XmlAttribute;
/*     */ import javax.xml.bind.annotation.XmlElement;
/*     */ import javax.xml.bind.annotation.XmlElementWrapper;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class DoorsAttributeDefinition
/*     */ {
/*     */   @NotNull
/*  36 */   private String name = "";
/*     */   
/*     */   @NotNull
/*  39 */   private String attributeType = "";
/*     */   
/*     */   @NotNull
/*  42 */   private String customAttributeType = "";
/*     */   
/*     */   @NotNull
/*  45 */   private String defaultValue = "";
/*     */   
/*     */   private boolean isMultiValued;
/*     */   
/*     */   @NotNull
/*  50 */   private List<String> availableOptions = new ArrayList<>();
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public DoorsAttributeDefinition() {}
/*     */ 
/*     */   
/*     */   public DoorsAttributeDefinition(@NotNull String name, @NotNull String attributeType, @NotNull String customAttributeType, boolean isMultiValued) {
/*  58 */     this.name = name;
/*  59 */     this.attributeType = attributeType;
/*  60 */     this.customAttributeType = customAttributeType;
/*  61 */     this.isMultiValued = isMultiValued;
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   public String getName() {
/*  66 */     return this.name;
/*     */   }
/*     */   
/*     */   @XmlAttribute(name = "name")
/*     */   public void setName(@NotNull String name) {
/*  71 */     this.name = name;
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   public String getAttributeType() {
/*  76 */     return this.attributeType;
/*     */   }
/*     */   
/*     */   @XmlAttribute(name = "AttributeType")
/*     */   public void setAttributeType(@NotNull String attributeType) {
/*  81 */     this.attributeType = attributeType;
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   public String getCustomAttributeType() {
/*  86 */     return this.customAttributeType;
/*     */   }
/*     */   
/*     */   @XmlAttribute(name = "CustomAttributeType")
/*     */   public void setCustomAttributeType(@NotNull String customAttributeType) {
/*  91 */     this.customAttributeType = customAttributeType;
/*     */   }
/*     */   
/*     */   @XmlAttribute(name = "multiValued")
/*     */   public void setIsMultiValued(boolean isMultiValued) {
/*  96 */     this.isMultiValued = isMultiValued;
/*     */   }
/*     */   
/*     */   public boolean isMultiValued() {
/* 100 */     return this.isMultiValued;
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   public List<String> getAvailableOptions() {
/* 105 */     return this.availableOptions;
/*     */   }
/*     */   
/*     */   @XmlElementWrapper(name = "availableOptions")
/*     */   @XmlElement(name = "availableOption")
/*     */   public void setAvailableOptions(@NotNull List<String> availableOptions) {
/* 111 */     this.availableOptions = availableOptions;
/*     */   }
/*     */   
/*     */   @XmlElement(name = "defaultValue")
/*     */   @NotNull
/*     */   public String getDefaultValue() {
/* 117 */     return this.defaultValue;
/*     */   }
/*     */   
/*     */   public void setDefaultValue(@NotNull String defaultValue) {
/* 121 */     this.defaultValue = defaultValue;
/*     */   }
/*     */ 
/*     */   
/*     */   public int hashCode() {
/* 126 */     int prime = 31;
/* 127 */     int result = 1;
/* 128 */     result = 31 * result + this.attributeType.hashCode();
/* 129 */     result = 31 * result + this.availableOptions.hashCode();
/* 130 */     result = 31 * result + this.customAttributeType.hashCode();
/* 131 */     result = 31 * result + this.defaultValue.hashCode();
/* 132 */     result = 31 * result + (this.isMultiValued ? 1231 : 1237);
/* 133 */     result = 31 * result + this.name.hashCode();
/* 134 */     return result;
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean equals(Object obj) {
/* 139 */     if (this == obj) {
/* 140 */       return true;
/*     */     }
/* 142 */     if (obj == null) {
/* 143 */       return false;
/*     */     }
/* 145 */     if (getClass() != obj.getClass()) {
/* 146 */       return false;
/*     */     }
/* 148 */     DoorsAttributeDefinition other = (DoorsAttributeDefinition)obj;
/* 149 */     if (!this.attributeType.equals(other.attributeType)) {
/* 150 */       return false;
/*     */     }
/* 152 */     if (!this.availableOptions.equals(other.availableOptions)) {
/* 153 */       return false;
/*     */     }
/* 155 */     if (!this.customAttributeType.equals(other.customAttributeType)) {
/* 156 */       return false;
/*     */     }
/* 158 */     if (!this.defaultValue.equals(other.defaultValue)) {
/* 159 */       return false;
/*     */     }
/* 161 */     if (this.isMultiValued != other.isMultiValued) {
/* 162 */       return false;
/*     */     }
/* 164 */     if (!this.name.equals(other.name)) {
/* 165 */       return false;
/*     */     }
/* 167 */     return true;
/*     */   }
/*     */ 
/*     */   
/*     */   public String toString() {
/* 172 */     return "DoorsAttributeDefinition [name=" + this.name + ", attributeType=" + this.attributeType + ", customAttributeType=" + this.customAttributeType + ", defaultValue=" + this.defaultValue + ", isMultiValued=" + this.isMultiValued + ", availableOptions=" + 
/* 173 */       this.availableOptions + "]";
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/xmlmodel/DoorsAttributeDefinition.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */