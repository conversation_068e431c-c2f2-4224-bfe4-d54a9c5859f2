/*    */ package com.polarion.synchronizer.proxy.doors.internal;
/*    */ 
/*    */ import com.google.inject.AbstractModule;
/*    */ import com.google.inject.assistedinject.FactoryModuleBuilder;
/*    */ import com.google.inject.name.Names;
/*    */ import com.polarion.synchronizer.IExchangeService;
/*    */ import com.polarion.synchronizer.IExchangeServiceFactory;
/*    */ import com.polarion.synchronizer.ProxyContribution;
/*    */ import com.polarion.synchronizer.proxy.InternalRepository;
/*    */ import com.polarion.synchronizer.proxy.doors.DoorsConnectionConfiguration;
/*    */ import com.polarion.synchronizer.proxy.doors.DoorsProxyConfiguration;
/*    */ import com.polarion.synchronizer.spi.ProxyBinder;
/*    */ import java.lang.annotation.Annotation;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class DoorsProxyModule
/*    */   extends AbstractModule
/*    */ {
/*    */   protected void configure() {
/* 39 */     String pathToUi = "internal/ui.js";
/*    */     
/* 41 */     ProxyBinder.bindProxy(binder(), 
/* 42 */         new ProxyContribution("Doors", DoorsProxyConfiguration.class, DoorsConnectionConfiguration.class, pathToUi), 
/* 43 */         DoorsProxyFactory.class);
/* 44 */     install((new FactoryModuleBuilder()).implement(IExchangeService.class, DoorsExchangeService.class).build(IExchangeServiceFactory.class));
/*    */     
/* 46 */     bind(InternalRepository.class).annotatedWith((Annotation)Names.named("DoorsRepository")).to(DoorsRepository.class);
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/internal/DoorsProxyModule.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */