/*     */ package com.polarion.synchronizer.proxy;
/*     */ 
/*     */ import com.polarion.core.util.ObjectUtils;
/*     */ import com.polarion.platform.ITransactionService;
/*     */ import com.polarion.platform.context.IContextService;
/*     */ import com.polarion.platform.security.ISecurityService;
/*     */ import com.polarion.platform.service.repository.IRepositoryReadOnlyConnection;
/*     */ import com.polarion.platform.service.repository.IRepositoryService;
/*     */ import com.polarion.platform.spi.security.IAuthenticationSource;
/*     */ import com.polarion.subterra.base.data.identification.ContextId;
/*     */ import com.polarion.subterra.base.data.identification.IContextId;
/*     */ import com.polarion.subterra.base.location.ILocation;
/*     */ import com.polarion.synchronizer.SynchronizationException;
/*     */ import java.io.InputStream;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ import org.jetbrains.annotations.Nullable;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public abstract class AbstractRepository
/*     */   implements InternalRepository
/*     */ {
/*     */   @NotNull
/*     */   protected final ITransactionService transactionService;
/*     */   @NotNull
/*     */   protected final IRepositoryService repositoryService;
/*     */   @NotNull
/*     */   protected final IAuthenticationSource authenticationSource;
/*     */   @NotNull
/*     */   protected final ISecurityService securityService;
/*     */   @NotNull
/*     */   protected final IContextService contextService;
/*     */   
/*     */   public AbstractRepository(@NotNull ITransactionService transactionService, @NotNull IRepositoryService repositoryService, @NotNull IAuthenticationSource authenticationSource, @NotNull ISecurityService securityService, @NotNull IContextService contextService) {
/*  55 */     this.transactionService = transactionService;
/*  56 */     this.repositoryService = repositoryService;
/*  57 */     this.authenticationSource = authenticationSource;
/*  58 */     this.securityService = securityService;
/*  59 */     this.contextService = contextService;
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   protected abstract String getLocationPath();
/*     */   
/*     */   @Nullable
/*     */   public InputStream loadContentForLocation(@NotNull ILocation documentLocation) {
/*  68 */     this.transactionService.beginTx();
/*     */     
/*     */     try {
/*  71 */       IRepositoryReadOnlyConnection connection = this.repositoryService.getReadOnlyConnection(documentLocation);
/*     */       
/*  73 */       if (!connection.exists(documentLocation)) {
/*  74 */         return null;
/*     */       }
/*     */       
/*  77 */       return connection.getContent(documentLocation);
/*     */     }
/*  79 */     catch (SynchronizationException e) {
/*  80 */       throw e;
/*  81 */     } catch (Exception e) {
/*  82 */       throw new SynchronizationException("Failed to load ReqIF document: " + e.getLocalizedMessage(), e);
/*     */     } finally {
/*  84 */       this.transactionService.rollbackTx();
/*     */     } 
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   protected ILocation getDocumentLocationWithoutRevision(@NotNull String projectId, @NotNull String documentName) {
/*  90 */     return getBaseLocation(projectId).append("documents").append(documentName);
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   public String getCurrentRevision(@NotNull String projectId, @NotNull String documentName) {
/*  95 */     ILocation documentLocation = getDocumentLocationWithoutRevision(projectId, documentName);
/*  96 */     return this.repositoryService.getReadOnlyConnection(documentLocation).getLastRevision(documentLocation);
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public ILocation getDocumentLocation(@NotNull String projectId, @NotNull String documentName) {
/* 102 */     ILocation documentLocation = getDocumentLocationWithoutRevision(projectId, documentName);
/* 103 */     String currentRevision = this.repositoryService.getReadOnlyConnection(documentLocation).getLastRevision(documentLocation);
/* 104 */     return documentLocation.setRevision(currentRevision);
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public ILocation getBaseLocation(@NotNull String projectId) {
/* 110 */     return ((ILocation)ObjectUtils.notNull(this.contextService.getContextforId(getContextId(projectId)).getLocation())).append(getLocationPath());
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private IContextId getContextId(@NotNull String projectId) {
/* 115 */     return ContextId.getContextIdFromClusterAndContext("default", projectId);
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public ILocation getGlobalBaseLocation() {
/* 121 */     return ((ILocation)ObjectUtils.notNull(this.contextService.getContextforId(getGlobalContextId()).getLocation())).append(getLocationPath());
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private IContextId getGlobalContextId() {
/* 126 */     return ContextId.getGlobalContextId("default");
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/AbstractRepository.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */