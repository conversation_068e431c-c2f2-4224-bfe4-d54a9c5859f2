/*    */ package com.polarion.synchronizer.internal.configuration;
/*    */ 
/*    */ import com.polarion.synchronizer.model.Direction;
/*    */ import java.util.Collection;
/*    */ import java.util.LinkedHashSet;
/*    */ import javax.xml.bind.annotation.XmlAccessType;
/*    */ import javax.xml.bind.annotation.XmlAccessorType;
/*    */ import javax.xml.bind.annotation.XmlAttribute;
/*    */ import javax.xml.bind.annotation.XmlElement;
/*    */ import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ import org.jetbrains.annotations.Nullable;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @XmlJavaTypeAdapter(FieldMappingConfigurationXmlAdapter.class)
/*    */ @XmlAccessorType(XmlAccessType.FIELD)
/*    */ public class FieldMappingConfiguration
/*    */ {
/*    */   @XmlAttribute
/*    */   private String left;
/*    */   @XmlAttribute
/*    */   private String right;
/*    */   @XmlAttribute
/*    */   private Direction direction;
/*    */   @XmlAttribute
/*    */   private Direction primaryDirection;
/*    */   @XmlAttribute
/*    */   private Direction deleteDirection;
/*    */   @XmlElement(name = "replace")
/* 37 */   private Collection<ValueMappingConfiguration> valueMappings = new LinkedHashSet<>();
/*    */ 
/*    */   
/*    */   public FieldMappingConfiguration() {}
/*    */ 
/*    */   
/*    */   public FieldMappingConfiguration(String leftField, String rightField, Direction direction) {
/* 44 */     this.left = leftField;
/* 45 */     this.right = rightField;
/*    */     
/* 47 */     this.direction = direction;
/*    */   }
/*    */   
/*    */   public FieldMappingConfiguration(String leftField, String rightField, Direction direction, Direction primaryDirection) {
/* 51 */     this(leftField, rightField, direction);
/* 52 */     this.primaryDirection = primaryDirection;
/*    */   }
/*    */   
/*    */   public FieldMappingConfiguration(@NotNull String leftField, @NotNull String rightField, @NotNull Direction direction, @Nullable Direction primaryDirection, @Nullable Direction deleteDirection) {
/* 56 */     this(leftField, rightField, direction, primaryDirection);
/* 57 */     this.deleteDirection = deleteDirection;
/*    */   }
/*    */   
/*    */   public String getLeft() {
/* 61 */     return this.left;
/*    */   }
/*    */   
/*    */   public String getRight() {
/* 65 */     return this.right;
/*    */   }
/*    */   
/*    */   public Direction getDirection() {
/* 69 */     return this.direction;
/*    */   }
/*    */   
/*    */   public Direction getPrimaryDirection() {
/* 73 */     return this.primaryDirection;
/*    */   }
/*    */   
/*    */   public void setDeleteDirection(@Nullable Direction deleteDirection) {
/* 77 */     this.deleteDirection = deleteDirection;
/*    */   }
/*    */   
/*    */   @Nullable
/*    */   public Direction getDeleteDirection() {
/* 82 */     return this.deleteDirection;
/*    */   }
/*    */   
/*    */   public Collection<ValueMappingConfiguration> getValueMappings() {
/* 86 */     return this.valueMappings;
/*    */   }
/*    */   
/*    */   public void setValueMappings(Collection<ValueMappingConfiguration> valueMappings) {
/* 90 */     this.valueMappings = valueMappings;
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/internal/configuration/FieldMappingConfiguration.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */