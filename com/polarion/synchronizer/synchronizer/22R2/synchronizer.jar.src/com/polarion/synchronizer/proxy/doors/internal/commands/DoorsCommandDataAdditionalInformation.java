/*    */ package com.polarion.synchronizer.proxy.doors.internal.commands;
/*    */ 
/*    */ import com.polarion.synchronizer.proxy.doors.internal.dxl.DxlScriptBuilder;
/*    */ import com.polarion.synchronizer.proxy.doors.internal.dxl.IDxlScript;
/*    */ import com.polarion.synchronizer.proxy.doors.xmlmodel.DoorsAdditionalInformationPublications;
/*    */ import java.nio.charset.Charset;
/*    */ import javax.xml.bind.JAXB;
/*    */ import org.apache.commons.io.IOUtils;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class DoorsCommandDataAdditionalInformation
/*    */   extends AbstractDoorsCommand<DoorsAdditionalInformationPublications>
/*    */ {
/*    */   @NotNull
/*    */   public DoorsAdditionalInformationPublications processResult(@NotNull String result) {
/* 40 */     result = result.replaceAll("[\\u0001-\\u0008\\u000B\\u000C\\u000E-\\u001F]", "");
/* 41 */     return (DoorsAdditionalInformationPublications)JAXB.unmarshal(IOUtils.toInputStream(result, Charset.forName("UTF-8")), DoorsAdditionalInformationPublications.class);
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   public IDxlScript getScript() {
/* 47 */     return (IDxlScript)DxlScriptBuilder.create().add((IDxlScript)DxlScriptBuilder.script("sendResult.dxl"))
/* 48 */       .add((IDxlScript)DxlScriptBuilder.script("cleanName.dxl"))
/* 49 */       .add((IDxlScript)DxlScriptBuilder.script("getDoorsObjectsAdditionalInformation.dxl"));
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/internal/commands/DoorsCommandDataAdditionalInformation.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */