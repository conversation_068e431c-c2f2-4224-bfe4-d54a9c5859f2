package com.polarion.synchronizer;

import com.polarion.synchronizer.model.Side;
import java.util.Collection;
import org.jetbrains.annotations.NotNull;

public interface IDependencyManager {
  void setCurrentTask(@NotNull ISynchronizationTask paramISynchronizationTask);
  
  void trackDependency(@NotNull Side paramSide, @NotNull String paramString);
  
  void resolve(@NotNull String paramString1, @NotNull String paramString2);
  
  @NotNull
  Collection<String> getResolvedDependencies(@NotNull ISynchronizationTask paramISynchronizationTask, @NotNull String paramString);
}


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/IDependencyManager.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */