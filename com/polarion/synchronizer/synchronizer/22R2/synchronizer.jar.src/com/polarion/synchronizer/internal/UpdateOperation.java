/*     */ package com.polarion.synchronizer.internal;
/*     */ 
/*     */ import com.polarion.synchronizer.IUpdateOperation;
/*     */ import com.polarion.synchronizer.model.TransferItem;
/*     */ import com.polarion.synchronizer.model.UpdateResult;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class UpdateOperation
/*     */   implements IUpdateOperation
/*     */ {
/*     */   @NotNull
/*     */   private final TransferItem sourceItem;
/*     */   @NotNull
/*     */   private final TransferItem updateContent;
/*     */   @NotNull
/*     */   private final UpdateResult updateResult;
/*     */   
/*     */   public UpdateOperation(TransferItem sourceItem, TransferItem updateContent, UpdateResult updateResult) {
/*  42 */     this.sourceItem = sourceItem;
/*  43 */     this.updateContent = updateContent;
/*  44 */     this.updateResult = updateResult;
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public TransferItem getSourceItem() {
/*  50 */     return this.sourceItem;
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public TransferItem getContent() {
/*  56 */     return this.updateContent;
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public UpdateResult getResult() {
/*  62 */     return this.updateResult;
/*     */   }
/*     */ 
/*     */   
/*     */   public int hashCode() {
/*  67 */     int prime = 31;
/*  68 */     int result = 1;
/*  69 */     result = 31 * result + this.sourceItem.hashCode();
/*  70 */     result = 31 * result + this.updateContent.hashCode();
/*  71 */     result = 31 * result + this.updateResult.hashCode();
/*  72 */     return result;
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean equals(Object obj) {
/*  77 */     if (this == obj) {
/*  78 */       return true;
/*     */     }
/*  80 */     if (obj == null) {
/*  81 */       return false;
/*     */     }
/*  83 */     if (getClass() != obj.getClass()) {
/*  84 */       return false;
/*     */     }
/*  86 */     UpdateOperation other = (UpdateOperation)obj;
/*  87 */     if (!this.sourceItem.equals(other.sourceItem)) {
/*  88 */       return false;
/*     */     }
/*  90 */     if (!this.updateContent.equals(other.updateContent)) {
/*  91 */       return false;
/*     */     }
/*  93 */     if (!this.updateResult.equals(other.updateResult)) {
/*  94 */       return false;
/*     */     }
/*  96 */     return true;
/*     */   }
/*     */ 
/*     */   
/*     */   public String toString() {
/* 101 */     return "UpdateOperation [sourceItem=" + this.sourceItem + ", updateContent=" + this.updateContent + ", updateResult=" + this.updateResult + "]";
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/internal/UpdateOperation.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */