/*     */ package com.polarion.synchronizer.proxy.doors.internal;
/*     */ 
/*     */ import com.polarion.core.util.StringUtils;
/*     */ import com.polarion.synchronizer.ILogger;
/*     */ import com.polarion.synchronizer.SynchronizationException;
/*     */ import com.polarion.synchronizer.model.Attachment;
/*     */ import com.polarion.synchronizer.model.CollectionUpdate;
/*     */ import com.polarion.synchronizer.model.FieldDefinition;
/*     */ import com.polarion.synchronizer.model.Hierarchy;
/*     */ import com.polarion.synchronizer.model.ItemKey;
/*     */ import com.polarion.synchronizer.model.TransferItem;
/*     */ import com.polarion.synchronizer.proxy.doors.html2rtftranslator.HtmlToDoorsRtfConverter;
/*     */ import com.polarion.synchronizer.proxy.doors.internal.commands.DoorsCommandCreateObject;
/*     */ import com.polarion.synchronizer.proxy.doors.internal.commands.DoorsCommandFindPosition;
/*     */ import com.polarion.synchronizer.proxy.doors.internal.commands.DoorsCommandMoveObject;
/*     */ import com.polarion.synchronizer.proxy.doors.internal.commands.DoorsCommandUpdate;
/*     */ import com.polarion.synchronizer.proxy.doors.internal.commands.IDoorsCommand;
/*     */ import java.text.SimpleDateFormat;
/*     */ import java.util.Collection;
/*     */ import java.util.Date;
/*     */ import java.util.HashMap;
/*     */ import java.util.Locale;
/*     */ import java.util.Map;
/*     */ import java.util.function.Function;
/*     */ import java.util.stream.Collectors;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ import org.jetbrains.annotations.Nullable;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class PolarionToDoorsConverter
/*     */   implements IPolarionToDoorsConverter
/*     */ {
/*     */   private static final String COLON = ":";
/*     */   private static final String COMMA = ",";
/*     */   private static final String EMPTY_STRING = "";
/*  40 */   private static SimpleDateFormat doorsDateFormat = new SimpleDateFormat("d MMMM yyyy", Locale.ENGLISH);
/*     */   
/*     */   @NotNull
/*     */   private IDoorsModule module;
/*     */   
/*     */   @NotNull
/*  46 */   private Map<String, String> polarionDoorsParentID = new HashMap<>();
/*     */   @NotNull
/*     */   private ILogger logger;
/*     */   
/*     */   public PolarionToDoorsConverter(@NotNull IDoorsModule module, @NotNull ILogger logger) {
/*  51 */     this.module = module;
/*  52 */     this.logger = logger;
/*     */   }
/*     */ 
/*     */   
/*     */   @Nullable
/*     */   public String getDoorsAttributeValue(@Nullable Object value, @NotNull FieldDefinition fieldDefinition, @NotNull Collection<Attachment> attachments) {
/*  58 */     if (value == null) {
/*  59 */       return null;
/*     */     }
/*  61 */     if (fieldDefinition.getType().equals("string") || fieldDefinition.getType().equals("text"))
/*  62 */       return (String)value; 
/*  63 */     if (fieldDefinition.getType().equals("rich-text")) {
/*  64 */       String result = convert((String)value, attachments);
/*  65 */       return result;
/*  66 */     }  if (fieldDefinition.getType().equals("integer")) {
/*  67 */       Integer tempInt = (Integer)value;
/*  68 */       return tempInt.toString();
/*  69 */     }  if (fieldDefinition.getType().equals("float")) {
/*  70 */       Float tempFloat = (Float)value;
/*  71 */       return tempFloat.toString();
/*  72 */     }  if (fieldDefinition.getType().equals("date")) {
/*  73 */       Date tempDate = (Date)value;
/*  74 */       return doorsDateFormat.format(tempDate);
/*  75 */     }  if (fieldDefinition.getType().equals("option")) {
/*  76 */       if (fieldDefinition.isMultiValued()) {
/*  77 */         return getMultiValueUpdate(value);
/*     */       }
/*  79 */       return (String)value;
/*     */     } 
/*  81 */     if (fieldDefinition.getType().equals("boolean")) {
/*  82 */       Boolean tempBool = (Boolean)value;
/*  83 */       return tempBool.toString();
/*     */     } 
/*     */     
/*  86 */     return null;
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private String convert(@NotNull String source, @NotNull Collection<Attachment> attachments) {
/*  91 */     Map<String, Attachment> fileNameToAttachment = (Map<String, Attachment>)attachments.stream().collect(Collectors.toMap(Attachment::getFileName, Function.identity()));
/*  92 */     HtmlToDoorsRtfConverter conv = new HtmlToDoorsRtfConverter(fileNameToAttachment, this.logger);
/*  93 */     return conv.convert(source);
/*     */   }
/*     */   
/*     */   @Nullable
/*     */   private static String getMultiValueUpdate(@NotNull Object value) {
/*  98 */     CollectionUpdate<String> enumVals = (CollectionUpdate<String>)value;
/*  99 */     Collection<String> addedVals = enumVals.getAdded();
/* 100 */     Collection<String> removedVals = enumVals.getRemoved();
/* 101 */     if (addedVals.isEmpty() && removedVals.isEmpty()) {
/* 102 */       return null;
/*     */     }
/* 104 */     String combinedAddedValsStr = addedVals.isEmpty() ? "" : StringUtils.join(addedVals, ",");
/* 105 */     String combinedRemovedValsStr = removedVals.isEmpty() ? "" : StringUtils.join(removedVals, ",");
/* 106 */     return String.valueOf(combinedAddedValsStr) + ":" + combinedRemovedValsStr;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void putPolarionDoorsParentID(@NotNull String polarionId, @NotNull String doorsId) {
/* 112 */     this.polarionDoorsParentID.put(polarionId, doorsId);
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public String getObjectLocation(@NotNull Hierarchy itemHierarchy) {
/* 118 */     String parentID = itemHierarchy.getParentItemId();
/* 119 */     int itemPosition = itemHierarchy.getPosition();
/*     */     
/* 121 */     String parentAbsNum = "";
/* 122 */     if (this.polarionDoorsParentID.containsKey(parentID)) {
/* 123 */       String parentDoorsID = this.polarionDoorsParentID.get(parentID);
/* 124 */       parentAbsNum = parentDoorsID.split("/")[1];
/* 125 */     } else if (parentID != null && !(itemHierarchy.getParentKey()).isForeign) {
/* 126 */       parentAbsNum = parentID.split("/")[1];
/*     */     } 
/*     */     
/* 129 */     return this.module.<String>execute((IDoorsCommand<String>)new DoorsCommandFindPosition(itemPosition, parentAbsNum), false);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public String handleHierarchy(@NotNull String absoluteNumber, @NotNull Hierarchy hierarchy) {
/* 136 */     String referenceAbsoluteNumber = getObjectLocation(hierarchy);
/* 137 */     return this.module.<String>execute((IDoorsCommand<String>)new DoorsCommandMoveObject(absoluteNumber, referenceAbsoluteNumber.split("/")[1], hierarchy.getPosition()), true);
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public String handleCreateItem(@NotNull TransferItem item, @Nullable Hierarchy hierarchy) {
/*     */     String referenceAbsoluteNumber;
/*     */     int position;
/* 145 */     if (hierarchy == null) {
/* 146 */       referenceAbsoluteNumber = "0";
/* 147 */       position = -1;
/*     */     } else {
/* 149 */       referenceAbsoluteNumber = getObjectLocation(hierarchy).split("/")[1];
/* 150 */       position = hierarchy.getPosition();
/*     */     } 
/* 152 */     String result = this.module.<String>execute((IDoorsCommand<String>)new DoorsCommandCreateObject(referenceAbsoluteNumber, position), true);
/* 153 */     if (!result.startsWith(this.module.getModuleId())) {
/* 154 */       throw new SynchronizationException("Creation of Item Failed : " + result);
/*     */     }
/* 156 */     ItemKey iKey = item.getKey();
/* 157 */     putPolarionDoorsParentID(iKey.id, result);
/* 158 */     return result;
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public String handleStandardAttribute(@NotNull String absoluteNumber, @NotNull String attributeName, @Nullable Object attributeValue, @Nullable FieldDefinition fieldDefinition, @NotNull Collection<Attachment> attachments) {
/* 164 */     String convertedValue = null;
/* 165 */     if (fieldDefinition != null) {
/* 166 */       convertedValue = getDoorsAttributeValue(attributeValue, fieldDefinition, attachments);
/* 167 */       if (fieldDefinition.isMultiValued() && convertedValue == null) {
/* 168 */         return "OK";
/*     */       }
/*     */     } 
/* 171 */     return this.module.<String>execute((IDoorsCommand<String>)new DoorsCommandUpdate((convertedValue != null) ? convertedValue : "", attributeName, absoluteNumber), true);
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/internal/PolarionToDoorsConverter.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */