/*    */ package com.polarion.synchronizer.model;
/*    */ 
/*    */ public class Pair {
/*    */   private String itemId;
/*    */   private String connectToId;
/*    */   
/*    */   public Pair(String itemId, String connectToId) {
/*  8 */     this.itemId = itemId;
/*  9 */     this.connectToId = connectToId;
/*    */   }
/*    */   
/*    */   public String getItemId() {
/* 13 */     return this.itemId;
/*    */   }
/*    */   
/*    */   public String getConnectToId() {
/* 17 */     return this.connectToId;
/*    */   }
/*    */ 
/*    */   
/*    */   public String toString() {
/* 22 */     return "Pair [itemId=" + this.itemId + ", connectToId=" + this.connectToId + "]";
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/model/Pair.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */