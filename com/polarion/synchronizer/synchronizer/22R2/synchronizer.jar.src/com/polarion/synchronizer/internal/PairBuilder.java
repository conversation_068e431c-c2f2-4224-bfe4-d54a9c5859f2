/*     */ package com.polarion.synchronizer.internal;
/*     */ 
/*     */ import com.polarion.synchronizer.IConnectionMap;
/*     */ import com.polarion.synchronizer.model.ItemPair;
/*     */ import com.polarion.synchronizer.model.Side;
/*     */ import com.polarion.synchronizer.model.TransferItem;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Collection;
/*     */ import java.util.HashMap;
/*     */ import java.util.Iterator;
/*     */ import java.util.LinkedList;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ class PairBuilder
/*     */ {
/*     */   @NotNull
/*     */   private final IConnectionMap connectionMap;
/*     */   private final boolean reAddMissing;
/*     */   private final boolean deleteOutOfScope;
/*     */   
/*     */   public PairBuilder(@NotNull IConnectionMap connectionMap, boolean reAddMissing, boolean deleteOutOfScope) {
/*  49 */     this.connectionMap = connectionMap;
/*  50 */     this.reAddMissing = reAddMissing;
/*  51 */     this.deleteOutOfScope = deleteOutOfScope;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public List<TransferItem> loadMissing(@NotNull Collection<ItemPair> allPairs, @NotNull Collection<TransferItem> singleItems, Side fromSide, LoadContext otherLoadContext) {
/*  57 */     List<TransferItem> deletedOnOtherSide = null;
/*     */     
/*  59 */     if (singleItems.isEmpty() || this.deleteOutOfScope) {
/*  60 */       deletedOnOtherSide = new ArrayList<>(singleItems);
/*     */     } else {
/*  62 */       Collection<String> missingPartnerIds = loadTargetIds(singleItems, fromSide);
/*  63 */       Collection<TransferItem> foundItems = otherLoadContext.loadItems(missingPartnerIds);
/*     */       
/*  65 */       if (fromSide == Side.LEFT) {
/*  66 */         LoadPairsResult loadPairs = loadPairs(singleItems, foundItems);
/*  67 */         deletedOnOtherSide = new ArrayList<>(loadPairs.singleLeft);
/*  68 */         allPairs.addAll(loadPairs.pairs);
/*     */       } else {
/*  70 */         LoadPairsResult loadPairs = loadPairs(foundItems, singleItems);
/*  71 */         deletedOnOtherSide = new ArrayList<>(loadPairs.singleRight);
/*  72 */         allPairs.addAll(loadPairs.pairs);
/*     */       } 
/*     */     } 
/*     */     
/*  76 */     if (this.reAddMissing && fromSide == Side.LEFT)
/*     */     {
/*  78 */       for (Iterator<TransferItem> iterator = deletedOnOtherSide.iterator(); iterator.hasNext(); ) {
/*  79 */         TransferItem deleted = iterator.next();
/*  80 */         String targetId = this.connectionMap.getTargetId(deleted.getId(), fromSide);
/*  81 */         if (targetId != null) {
/*  82 */           TransferItem empty = new TransferItem(targetId);
/*  83 */           ItemPair pair = new ItemPair(deleted, empty);
/*  84 */           allPairs.add(pair);
/*  85 */           iterator.remove();
/*     */         } 
/*     */       } 
/*     */     }
/*  89 */     return deletedOnOtherSide;
/*     */   }
/*     */   
/*     */   public LoadPairsResult loadPairs(@NotNull Collection<TransferItem> leftItems, @NotNull Collection<TransferItem> rightItems) {
/*  93 */     Collection<TransferItem> newRight = new LinkedList<>();
/*  94 */     Map<String, TransferItem> rightItemMap = new HashMap<>();
/*  95 */     for (TransferItem item : rightItems) {
/*  96 */       String leftId = this.connectionMap.getTargetId(item.getId(), Side.RIGHT);
/*  97 */       if (leftId == null) {
/*  98 */         newRight.add(item); continue;
/*     */       } 
/* 100 */       rightItemMap.put(leftId, item);
/*     */     } 
/*     */ 
/*     */     
/* 104 */     Collection<TransferItem> newLeft = new LinkedList<>();
/* 105 */     Collection<TransferItem> singleLeft = new LinkedList<>();
/* 106 */     Collection<ItemPair> pairs = new LinkedList<>();
/* 107 */     for (TransferItem leftItem : leftItems) {
/*     */       
/* 109 */       TransferItem rightItem = rightItemMap.remove(leftItem.getId());
/* 110 */       if (rightItem != null) {
/* 111 */         pairs.add(new ItemPair(leftItem, rightItem)); continue;
/* 112 */       }  if (this.connectionMap.getTargetId(leftItem.getId(), Side.LEFT) != null) {
/* 113 */         singleLeft.add(leftItem); continue;
/*     */       } 
/* 115 */       newLeft.add(leftItem);
/*     */     } 
/*     */     
/* 118 */     Collection<TransferItem> singleRight = rightItemMap.values();
/* 119 */     return new LoadPairsResult(singleLeft, singleRight, pairs, newLeft, newRight);
/*     */   }
/*     */   
/*     */   private List<String> loadTargetIds(@NotNull Collection<TransferItem> singleItems, @NotNull Side fromSide) {
/* 123 */     List<String> ids = new ArrayList<>(singleItems.size());
/* 124 */     for (TransferItem singleItem : singleItems) {
/* 125 */       ids.add(this.connectionMap.getTargetId(singleItem.getId(), fromSide));
/*     */     }
/* 127 */     return ids;
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/internal/PairBuilder.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */