/*    */ package com.polarion.synchronizer.proxy.doors.internal.commands;
/*    */ 
/*    */ import com.polarion.synchronizer.proxy.doors.internal.dxl.IDxlScript;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class DelegatingCommand<T>
/*    */   implements IDoorsCommand<T>
/*    */ {
/*    */   @NotNull
/*    */   private final IDoorsCommand<T> delegate;
/*    */   
/*    */   public DelegatingCommand(@NotNull IDoorsCommand<T> delegate) {
/* 34 */     this.delegate = delegate;
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   public IDxlScript execute() {
/* 40 */     return this.delegate.execute();
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   public T getResult() {
/* 46 */     return this.delegate.getResult();
/*    */   }
/*    */ 
/*    */   
/*    */   public boolean setResult(@NotNull String result) {
/* 51 */     return this.delegate.setResult(result);
/*    */   }
/*    */ 
/*    */   
/*    */   public void setError(@NotNull String error) {
/* 56 */     this.delegate.setError(error);
/*    */   }
/*    */ 
/*    */   
/*    */   public boolean canBeExecuted() {
/* 61 */     return this.delegate.canBeExecuted();
/*    */   }
/*    */ 
/*    */   
/*    */   public String toString() {
/* 66 */     return "DelegatingCommand [" + this.delegate + "]";
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/internal/commands/DelegatingCommand.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */