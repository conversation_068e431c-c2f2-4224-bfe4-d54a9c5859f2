/*    */ package com.polarion.synchronizer.proxy.configuration;
/*    */ 
/*    */ import java.util.ArrayList;
/*    */ import java.util.Collection;
/*    */ import javax.xml.bind.annotation.XmlAttribute;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class FieldMapping
/*    */ {
/*    */   private String reqIfAttribute;
/*    */   private String polarionField;
/*    */   private Collection<ValueMapping> valueMappings;
/*    */   
/*    */   @Deprecated
/*    */   public FieldMapping() {}
/*    */   
/*    */   public FieldMapping(String reqIfAttribute, String polarionField) {
/* 20 */     this.reqIfAttribute = reqIfAttribute;
/* 21 */     this.polarionField = polarionField;
/* 22 */     this.valueMappings = new ArrayList<>();
/*    */   }
/*    */   
/*    */   @XmlAttribute
/*    */   public String getReqIfAttribute() {
/* 27 */     return this.reqIfAttribute;
/*    */   }
/*    */   
/*    */   public void setReqIfAttribute(String reqIfAttribute) {
/* 31 */     this.reqIfAttribute = reqIfAttribute;
/*    */   }
/*    */   
/*    */   @XmlAttribute
/*    */   public String getPolarionField() {
/* 36 */     return this.polarionField;
/*    */   }
/*    */   
/*    */   public void setPolarionField(String polarionField) {
/* 40 */     this.polarionField = polarionField;
/*    */   }
/*    */   
/*    */   public Collection<ValueMapping> getValueMappings() {
/* 44 */     return this.valueMappings;
/*    */   }
/*    */   
/*    */   public void setValueMappings(Collection<ValueMapping> valueMappings) {
/* 48 */     this.valueMappings = valueMappings;
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/configuration/FieldMapping.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */