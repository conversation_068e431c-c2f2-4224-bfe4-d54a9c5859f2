/*     */ package com.polarion.synchronizer.proxy.doors.internal;
/*     */ 
/*     */ import com.polarion.core.util.xml.HTMLHelper;
/*     */ import com.polarion.synchronizer.helper.RichTextHelper;
/*     */ import com.polarion.synchronizer.ole.OleExtractor;
/*     */ import com.rtfparserkit.parser.RtfListenerAdaptor;
/*     */ import com.rtfparserkit.rtf.Command;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Arrays;
/*     */ import java.util.Collection;
/*     */ import java.util.LinkedHashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import java.util.stream.Collectors;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ import org.jetbrains.annotations.Nullable;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class RtfToHtmlConverter
/*     */   extends RtfListenerAdaptor
/*     */ {
/*     */   private static final int DEFAULT_DOC_WIDTH = 250;
/*     */   private static final int DEFAULT_IMAGE_WIDTH = 750;
/*     */   @NotNull
/*  27 */   private final StringBuilder htmlStringWithFormatting = new StringBuilder();
/*     */   
/*     */   @NotNull
/*     */   private List<String> attachmentFilenames;
/*     */   
/*     */   private boolean inThumbnail = false;
/*     */   private boolean inResult = false;
/*     */   private boolean pictProcessed = false;
/*     */   private boolean pictContainsData = false;
/*  36 */   private int thumbnailWidth = 0;
/*  37 */   private int thumbnailHeight = 0;
/*     */   
/*  39 */   private int levelCounter = 0;
/*  40 */   private int resultlevel = 0;
/*  41 */   private int nestedPictLevels = 0;
/*  42 */   private int pictsIndex = 0;
/*     */   
/*     */   @NotNull
/*  45 */   private final Collection<HtmlCommand> activeCommands = new ArrayList<>();
/*     */   @NotNull
/*     */   private RichTextHelper richTextHelper;
/*     */   
/*     */   private enum HtmlCommand
/*     */   {
/*  51 */     b(
/*  52 */       (String)Command.b, "font-weight: bold"), i((String)Command.i, "font-style: italic"),
/*  53 */     ul((String)Command.ul, Command.ulnone, (Command)"text-decoration: underline"), strike((String)Command.strike, "text-decoration: line-through"),
/*  54 */     superCmd((String)Command.supercmd, Command.nosupersub, (Command)"vertical-align: super"), sub((String)Command.sub, Command.nosupersub, (Command)"vertical-align: sub"),
/*  55 */     br((String)Command.par, null)
/*     */     {
/*     */       public void appendContent(@NotNull StringBuilder to)
/*     */       {
/*  59 */         to.append("<br>");
/*     */       }
/*     */     };
/*     */ 
/*     */     
/*     */     @NotNull
/*     */     private final Command startCommand;
/*     */     
/*     */     @Nullable
/*     */     private final Command endCommand;
/*     */     @Nullable
/*     */     private final String style;
/*     */     
/*     */     HtmlCommand(@Nullable Command startCommand, Command endCommand, String style) {
/*  73 */       this.startCommand = startCommand;
/*  74 */       this.endCommand = endCommand;
/*  75 */       this.style = style;
/*     */     }
/*     */     
/*     */     HtmlCommand(Command startCommand, String style) {
/*  79 */       this.startCommand = startCommand;
/*  80 */       this.endCommand = null;
/*  81 */       this.style = style;
/*     */     }
/*     */     
/*     */     public boolean matchStart(@NotNull Command command, boolean hasParameters) {
/*  85 */       return (command == this.startCommand && (this.endCommand != null || !hasParameters));
/*     */     }
/*     */     
/*     */     public boolean matchEnd(@NotNull Command command, boolean hasParameter) {
/*  89 */       return !((this.endCommand == null || command != this.endCommand) && (this.endCommand != null || command != this.startCommand || !hasParameter));
/*     */     }
/*     */     
/*     */     public boolean hasStyle() {
/*  93 */       return (this.style != null);
/*     */     }
/*     */     
/*     */     public String getStyle() {
/*  97 */       return this.style;
/*     */     }
/*     */ 
/*     */ 
/*     */     
/*     */     public void appendContent(@NotNull StringBuilder to) {}
/*     */   }
/*     */ 
/*     */   
/*     */   public RtfToHtmlConverter(@NotNull RichTextHelper richTextHelper, @NotNull List<String> attachmentFilenames) {
/* 107 */     this.richTextHelper = richTextHelper;
/* 108 */     this.attachmentFilenames = attachmentFilenames;
/*     */   }
/*     */   
/*     */   private boolean isTopLevel() {
/* 112 */     return (this.levelCounter == 1);
/*     */   }
/*     */ 
/*     */   
/*     */   public void processString(String string) {
/* 117 */     string = string.replaceAll("[\\u0001-\\u0008\\u000B\\u000C\\u000E-\\u001F]", "");
/* 118 */     if (isTopLevel()) {
/* 119 */       if (!this.activeCommands.isEmpty()) {
/* 120 */         appendSpanStart();
/*     */       }
/* 122 */       this.htmlStringWithFormatting.append(HTMLHelper.convertPlainToHTML(string));
/* 123 */       if (!this.activeCommands.isEmpty()) {
/* 124 */         this.htmlStringWithFormatting.append("</span>");
/*     */       }
/* 126 */     } else if (this.inThumbnail && this.nestedPictLevels == 0 && !this.pictProcessed) {
/* 127 */       this.pictContainsData = true;
/* 128 */       processPict();
/* 129 */       if (this.inResult) {
/* 130 */         this.pictProcessed = true;
/*     */       }
/*     */     } 
/*     */   }
/*     */   
/*     */   private void processPict() {
/* 136 */     if (this.attachmentFilenames.size() <= this.pictsIndex) {
/* 137 */       throw new RuntimeException("Error while parsing RTF. Unable to access attachment on position " + String.valueOf(this.pictsIndex + 1) + " in a list of size " + this.attachmentFilenames.size());
/*     */     }
/* 139 */     String name = this.attachmentFilenames.get(this.pictsIndex++);
/* 140 */     appendImage(name);
/* 141 */     this.thumbnailHeight = 0;
/* 142 */     this.thumbnailWidth = 0;
/*     */   }
/*     */   
/*     */   private void appendImage(@NotNull String name) {
/* 146 */     this.htmlStringWithFormatting.append("<img src=\"attachment:");
/* 147 */     this.htmlStringWithFormatting.append(name);
/* 148 */     this.htmlStringWithFormatting.append("\" style=\"");
/* 149 */     if (this.thumbnailHeight == this.thumbnailWidth && (this.thumbnailWidth == 250 || this.thumbnailWidth == 750)) {
/* 150 */       this.htmlStringWithFormatting.append("max-width: ");
/* 151 */       this.htmlStringWithFormatting.append(this.thumbnailWidth);
/* 152 */       this.htmlStringWithFormatting.append("px;");
/*     */     } else {
/* 154 */       this.htmlStringWithFormatting.append("width: ");
/* 155 */       this.htmlStringWithFormatting.append(this.thumbnailWidth);
/* 156 */       this.htmlStringWithFormatting.append("px;height: ");
/* 157 */       this.htmlStringWithFormatting.append(this.thumbnailHeight);
/* 158 */       this.htmlStringWithFormatting.append("px;");
/*     */     } 
/* 160 */     this.htmlStringWithFormatting.append("\"></img>");
/*     */   }
/*     */ 
/*     */   
/*     */   private void appendSpanStart() {
/* 165 */     this.htmlStringWithFormatting.append("<span style=\"");
/* 166 */     appendStyles(this.htmlStringWithFormatting, this.activeCommands);
/* 167 */     this.htmlStringWithFormatting.append("\">");
/*     */   }
/*     */   
/*     */   private void appendStyles(@NotNull StringBuilder to, Collection<HtmlCommand> activeCommands) {
/* 171 */     List<String> stylesList = (List<String>)activeCommands.stream().map(HtmlCommand::getStyle).collect(Collectors.toList());
/* 172 */     Map<String, List<String>> stylesMap = new LinkedHashMap<>();
/* 173 */     for (String styleAttribute : stylesList) {
/* 174 */       String[] style = styleAttribute.split(": ");
/* 175 */       if (stylesMap.containsKey(style[0])) {
/* 176 */         ((List<String>)stylesMap.get(style[0])).add(style[1]); continue;
/*     */       } 
/* 178 */       stylesMap.put(style[0], new ArrayList<>(Arrays.asList(new String[] { style[1] })));
/*     */     } 
/*     */ 
/*     */     
/* 182 */     for (String attribute : stylesMap.keySet()) {
/* 183 */       to.append(String.valueOf(attribute) + ": " + String.join(" ", (Iterable<? extends CharSequence>)stylesMap.get(attribute)) + ";");
/*     */     }
/*     */   }
/*     */ 
/*     */   
/*     */   public void processGroupEnd() {
/* 189 */     this.levelCounter--;
/* 190 */     if (this.inThumbnail && this.nestedPictLevels == 0) {
/* 191 */       this.inThumbnail = false;
/* 192 */       if (!this.pictProcessed && !this.pictContainsData) {
/* 193 */         processPict();
/*     */       }
/*     */     } 
/* 196 */     if (this.inThumbnail) {
/* 197 */       this.nestedPictLevels--;
/*     */     }
/* 199 */     if (!this.inThumbnail && this.inResult && this.resultlevel == this.levelCounter) {
/* 200 */       this.inResult = false;
/* 201 */       this.pictProcessed = false;
/* 202 */       this.resultlevel = 0;
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   public void processGroupStart() {
/* 208 */     this.levelCounter++;
/* 209 */     if (this.inThumbnail) {
/* 210 */       this.nestedPictLevels++;
/*     */     }
/*     */   }
/*     */ 
/*     */   
/*     */   public void processCommand(Command command, int parameter, boolean hasParameter, boolean optional) {
/* 216 */     if (isTopLevel()) {
/* 217 */       byte b; int i; HtmlCommand[] arrayOfHtmlCommand; for (i = (arrayOfHtmlCommand = HtmlCommand.values()).length, b = 0; b < i; ) { HtmlCommand htmlCommand = arrayOfHtmlCommand[b];
/* 218 */         if (htmlCommand.matchStart(command, hasParameter)) {
/* 219 */           if (htmlCommand.hasStyle()) {
/* 220 */             this.activeCommands.add(htmlCommand);
/*     */           } else {
/* 222 */             htmlCommand.appendContent(this.htmlStringWithFormatting);
/*     */           } 
/* 224 */         } else if (htmlCommand.matchEnd(command, hasParameter)) {
/* 225 */           this.activeCommands.remove(htmlCommand);
/*     */         }  b++; }
/*     */     
/*     */     } else {
/* 229 */       processOLE(command, parameter);
/*     */     } 
/*     */   }
/*     */   
/*     */   private void processOLE(@NotNull Command command, int parameter) {
/* 234 */     if (command.getCommandName().equals("pict")) {
/* 235 */       this.inThumbnail = true;
/* 236 */     } else if (command == Command.picwgoal || command == Command.picw) {
/* 237 */       this.thumbnailWidth = OleExtractor.twipsToPixels(parameter);
/* 238 */     } else if (command == Command.pichgoal || command == Command.pich) {
/* 239 */       this.thumbnailHeight = OleExtractor.twipsToPixels(parameter);
/* 240 */     } else if (command == Command.result || command == Command.rtlch) {
/* 241 */       this.inResult = true;
/* 242 */       this.resultlevel = this.levelCounter - 1;
/*     */     } 
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   public String getHtmlStringWithFormatting() {
/* 248 */     return this.richTextHelper.sortAttributes(this.htmlStringWithFormatting.toString());
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/internal/RtfToHtmlConverter.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */