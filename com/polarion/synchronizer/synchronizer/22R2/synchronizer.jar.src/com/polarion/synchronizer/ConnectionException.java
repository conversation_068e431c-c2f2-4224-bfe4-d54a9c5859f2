/*    */ package com.polarion.synchronizer;
/*    */ 
/*    */ public class ConnectionException
/*    */   extends SynchronizationException
/*    */ {
/*    */   public ConnectionException(String cause) {
/*  7 */     super(cause);
/*    */   }
/*    */   
/*    */   public ConnectionException(Throwable cause) {
/* 11 */     super(cause);
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/ConnectionException.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */