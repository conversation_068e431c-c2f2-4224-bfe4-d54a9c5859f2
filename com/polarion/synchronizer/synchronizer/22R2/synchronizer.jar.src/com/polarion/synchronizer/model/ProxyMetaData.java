/*    */ package com.polarion.synchronizer.model;
/*    */ 
/*    */ import com.polarion.synchronizer.mapping.FieldType;
/*    */ import java.util.Collection;
/*    */ import java.util.Map;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class ProxyMetaData
/*    */   implements IProxyMetadata
/*    */ {
/*    */   private final Collection<Option> types;
/*    */   private final boolean isHierarchySupported;
/*    */   private final Collection<FieldDefinition> commonFields;
/*    */   private final Map<String, Collection<FieldDefinition>> fieldDefinitions;
/*    */   private final Map<FieldType, Collection<FieldType>> compatibleFieldsFrom;
/*    */   
/*    */   public ProxyMetaData(Collection<Option> types, boolean isHierarchySupported, Collection<FieldDefinition> commonFields, Map<String, Collection<FieldDefinition>> fieldDefinitions, Map<FieldType, Collection<FieldType>> compatibleFieldsFrom) {
/* 23 */     this.types = types;
/* 24 */     this.isHierarchySupported = isHierarchySupported;
/* 25 */     this.commonFields = commonFields;
/* 26 */     this.fieldDefinitions = fieldDefinitions;
/* 27 */     this.compatibleFieldsFrom = compatibleFieldsFrom;
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   public Collection<Option> getTypes() {
/* 33 */     return this.types;
/*    */   }
/*    */ 
/*    */   
/*    */   public boolean isHierarchySupported() {
/* 38 */     return this.isHierarchySupported;
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   public Collection<FieldDefinition> getCommonFields() {
/* 44 */     return this.commonFields;
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   public Map<String, Collection<FieldDefinition>> getFieldDefinitions() {
/* 50 */     return this.fieldDefinitions;
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   public Map<FieldType, Collection<FieldType>> getCompatibleFieldsFrom() {
/* 56 */     return this.compatibleFieldsFrom;
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/model/ProxyMetaData.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */