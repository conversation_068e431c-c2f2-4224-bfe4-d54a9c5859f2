package com.polarion.synchronizer;

import com.polarion.synchronizer.model.IProxy;
import com.polarion.synchronizer.spi.AnyTypeAdapter;
import java.util.Collection;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import org.jetbrains.annotations.NotNull;

@XmlJavaTypeAdapter(AnyTypeAdapter.class)
public interface IAction {
  void execute(@NotNull IProxy paramIProxy, @NotNull Collection<String> paramCollection);
}


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/IAction.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */