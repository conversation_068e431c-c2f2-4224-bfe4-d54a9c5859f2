package com.polarion.synchronizer;

import com.polarion.synchronizer.configuration.ISyncPair;
import java.util.Collection;
import org.jetbrains.annotations.NotNull;

public interface IPostProcessingActionFactory {
  @NotNull
  Collection<IPostProcessingAction> createActions(@NotNull ISyncPair paramISyncPair);
}


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/IPostProcessingActionFactory.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */