package com.polarion.synchronizer.model;

import com.polarion.synchronizer.IProxyConfiguration;
import com.polarion.synchronizer.configuration.IConnection;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public interface IProxyFactory {
  @NotNull
  IProxy createProxy(@NotNull IProxyConfiguration<? extends IConnection> paramIProxyConfiguration);
  
  @Nullable
  String checkConnection(@NotNull IConnection paramIConnection);
}


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/model/IProxyFactory.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */