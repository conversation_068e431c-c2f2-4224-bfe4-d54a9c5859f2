/*     */ package com.polarion.synchronizer.proxy.configuration;
/*     */ 
/*     */ import com.google.inject.Inject;
/*     */ import com.google.inject.name.Named;
/*     */ import com.polarion.core.util.ObjectUtils;
/*     */ import com.polarion.core.util.logging.Logger;
/*     */ import com.polarion.platform.ITransactionService;
/*     */ import com.polarion.platform.i18n.Localization;
/*     */ import com.polarion.platform.service.repository.IRepositoryConnection;
/*     */ import com.polarion.platform.service.repository.IRepositoryReadOnlyConnection;
/*     */ import com.polarion.platform.service.repository.IRepositoryService;
/*     */ import com.polarion.subterra.base.location.ILocation;
/*     */ import com.polarion.synchronizer.IAction;
/*     */ import com.polarion.synchronizer.SynchronizationException;
/*     */ import com.polarion.synchronizer.proxy.InternalRepository;
/*     */ import com.polarion.synchronizer.proxy.polarion.PolarionProxyConfiguration;
/*     */ import java.io.ByteArrayInputStream;
/*     */ import java.io.ByteArrayOutputStream;
/*     */ import java.io.InputStream;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Collection;
/*     */ import java.util.HashSet;
/*     */ import java.util.Set;
/*     */ import javax.xml.bind.JAXBContext;
/*     */ import javax.xml.bind.JAXBException;
/*     */ import javax.xml.bind.Marshaller;
/*     */ import javax.xml.bind.Unmarshaller;
/*     */ import org.apache.commons.io.IOUtils;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ import org.jetbrains.annotations.Nullable;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ConfigurationService
/*     */   implements IConfigurationService
/*     */ {
/*     */   @NotNull
/*     */   private static final String CONFIG_LOCATION = "configurations";
/*     */   @NotNull
/*     */   private static final String TEMPLATE_LOCATION = "templates";
/*     */   @NotNull
/*  63 */   private static final Logger LOG = Logger.getLogger(ConfigurationService.class);
/*     */   @NotNull
/*     */   private final ITransactionService transactionService;
/*     */   @NotNull
/*     */   private final IRepositoryService repositoryService;
/*     */   @NotNull
/*     */   private final InternalRepository repository;
/*     */   @NotNull
/*     */   private final JAXBContext jaxbContext;
/*     */   
/*     */   @Inject
/*     */   public ConfigurationService(@NotNull ITransactionService transactionService, @NotNull IRepositoryService repositoryService, @Named("DoorsRepository") @NotNull InternalRepository doorsRepository, @NotNull Set<Class<? extends IAction>> actionsClasses) throws JAXBException {
/*  75 */     this.transactionService = transactionService;
/*  76 */     this.repositoryService = repositoryService;
/*  77 */     this.repository = doorsRepository;
/*  78 */     Collection<Class<?>> classes = new ArrayList<>(actionsClasses.size() + 1);
/*  79 */     classes.addAll(actionsClasses);
/*  80 */     classes.add(SpecificationConfiguration.class);
/*  81 */     this.jaxbContext = loadJaxbContext((Class[])classes.<Class<?>[]>toArray((Class<?>[][])new Class[classes.size()]));
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private JAXBContext loadJaxbContext(@NotNull Class[] classes) throws JAXBException {
/*  86 */     ClassLoader oldClassLoader = Thread.currentThread().getContextClassLoader();
/*  87 */     JAXBContext jaxbContext = null;
/*  88 */     Thread.currentThread().setContextClassLoader(getClass().getClassLoader());
/*     */     try {
/*  90 */       jaxbContext = JAXBContext.newInstance(classes);
/*     */     } finally {
/*  92 */       Thread.currentThread().setContextClassLoader(oldClassLoader);
/*     */     } 
/*  94 */     return jaxbContext;
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private SpecificationConfiguration unmarschallConfiguration(@NotNull String projectId, @NotNull String id, @NotNull InputStream content) throws JAXBException {
/*  99 */     Unmarshaller unmarshaller = this.jaxbContext.createUnmarshaller();
/*     */     
/* 101 */     SpecificationConfiguration configuration = (SpecificationConfiguration)unmarshaller.unmarshal(content);
/* 102 */     configuration.getPolarionConfiguration().setProject(projectId);
/* 103 */     configuration.getReqIfConfiguration().setProjectId(projectId);
/* 104 */     configuration.getReqIfConfiguration().setSpecificationId(id);
/* 105 */     return configuration;
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private ILocation getConfigurationLocation(@NotNull String projectId, @NotNull String id, boolean isForTemplate) {
/* 110 */     return getConfigurationsLocation(projectId, isForTemplate ? "templates" : "configurations")
/* 111 */       .append(String.valueOf(id) + ".xml");
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private ILocation getConfigurationsLocation(@NotNull String projectId, @NotNull String location) {
/* 116 */     return this.repository.getBaseLocation(projectId).append(location);
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private ILocation getGlobalTemplateLocation(@NotNull String templateName) {
/* 121 */     return this.repository.getGlobalBaseLocation().append("templates").append(String.valueOf(templateName) + ".xml");
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public Collection<SpecificationConfiguration> findConfigurations(@NotNull String projectId, @NotNull String space, @NotNull String document, boolean isMappedToFile) {
/* 128 */     Collection<SpecificationConfiguration> result, allConfigurations = loadAllConfigurations(projectId);
/* 129 */     Collection<SpecificationConfiguration> configurationsForDocument = findConfigurationsForDocument(space, document, allConfigurations);
/*     */ 
/*     */     
/* 132 */     if (isMappedToFile) {
/* 133 */       result = configurationsForDocument;
/*     */     } else {
/* 135 */       result = findConfiguratonsWithoutDocument(allConfigurations, configurationsForDocument);
/*     */     } 
/*     */     
/* 138 */     return result;
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   private Collection<SpecificationConfiguration> findConfiguratonsWithoutDocument(@NotNull Collection<SpecificationConfiguration> allConfigurations, @NotNull Collection<SpecificationConfiguration> configurationsForDocument) {
/* 144 */     Collection<SpecificationConfiguration> result = new ArrayList<>();
/*     */     
/* 146 */     Collection<String> containingFiles = new HashSet<>();
/* 147 */     for (SpecificationConfiguration configuration : configurationsForDocument) {
/* 148 */       containingFiles.add(configuration.getReqIfConfiguration().getFileName());
/*     */     }
/* 150 */     Collection<String> includedFiles = new HashSet<>();
/* 151 */     for (SpecificationConfiguration configuration : allConfigurations) {
/* 152 */       ReqIfConfiguration reqIfConfiguration = configuration.getReqIfConfiguration();
/* 153 */       String fileName = reqIfConfiguration.getFileName();
/* 154 */       if (!containingFiles.contains(fileName) && !includedFiles.contains(fileName)) {
/* 155 */         includedFiles.add(fileName);
/* 156 */         result.add(configuration);
/*     */       } 
/*     */     } 
/* 159 */     return result;
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private Collection<SpecificationConfiguration> findConfigurationsForDocument(@NotNull String space, @NotNull String document, @NotNull Collection<SpecificationConfiguration> allConfigurations) {
/* 164 */     Collection<SpecificationConfiguration> configurationsForDocument = new ArrayList<>();
/* 165 */     for (SpecificationConfiguration configuration : allConfigurations) {
/* 166 */       PolarionProxyConfiguration polarionConfiguration = configuration.getPolarionConfiguration();
/* 167 */       boolean match = (ObjectUtils.equalsWithNull(polarionConfiguration.getSpace(), space) && 
/* 168 */         ObjectUtils.equalsWithNull(polarionConfiguration.getDocument(), document));
/* 169 */       if (match) {
/* 170 */         configurationsForDocument.add(configuration);
/*     */       }
/*     */     } 
/* 173 */     return configurationsForDocument;
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private Collection<SpecificationConfiguration> loadAllConfigurations(@NotNull String projectId) {
/* 178 */     ILocation configurationsLocation = getConfigurationsLocation(projectId, "configurations");
/* 179 */     boolean isNestedTx = this.transactionService.txExists();
/* 180 */     if (!isNestedTx) {
/* 181 */       this.transactionService.beginTx();
/*     */     }
/* 183 */     Collection<SpecificationConfiguration> allConfigurations = new ArrayList<>();
/*     */     try {
/* 185 */       IRepositoryReadOnlyConnection connection = this.repositoryService.getReadOnlyConnection(configurationsLocation);
/* 186 */       if (connection.exists(configurationsLocation)) {
/* 187 */         Collection<ILocation> configurations = connection.getSubLocations(configurationsLocation, false);
/* 188 */         for (ILocation configurationLocation : configurations) {
/* 189 */           String fileName = configurationLocation.getLastComponent();
/* 190 */           if (fileName.endsWith(".xml")) {
/* 191 */             String configurationId = fileName.substring(0, fileName.length() - 4);
/*     */             try {
/* 193 */               InputStream content = connection.getContent(configurationLocation);
/* 194 */               SpecificationConfiguration configuration = unmarschallConfiguration(projectId, configurationId, content);
/* 195 */               allConfigurations.add(configuration);
/* 196 */             } catch (Exception e) {
/* 197 */               LOG.error("Error loading ReqIF configuration " + configurationLocation.getLocationPath(), e);
/*     */             } 
/*     */           } 
/*     */         } 
/*     */       } 
/*     */     } finally {
/* 203 */       if (!isNestedTx) {
/* 204 */         this.transactionService.rollbackTx();
/*     */       }
/*     */     } 
/* 207 */     return allConfigurations;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   @Nullable
/*     */   public SpecificationConfiguration loadConfiguration(@NotNull String projectId, @NotNull String id) {
/* 214 */     ILocation configurationLocation = getConfigurationLocation(projectId, id, false);
/* 215 */     boolean isNestedTx = this.transactionService.txExists();
/* 216 */     if (!isNestedTx) {
/* 217 */       this.transactionService.beginTx();
/*     */     }
/*     */     
/*     */     try {
/* 221 */       IRepositoryReadOnlyConnection connection = this.repositoryService.getReadOnlyConnection(configurationLocation);
/*     */       
/* 223 */       if (!connection.exists(configurationLocation)) {
/* 224 */         return null;
/*     */       }
/*     */       
/* 227 */       return unmarschallConfiguration(projectId, id, connection.getContent(configurationLocation));
/*     */     }
/* 229 */     catch (Exception e) {
/* 230 */       throw new SynchronizationException("Failed to load configuration " + id + ": " + e.getLocalizedMessage(), e);
/*     */     } finally {
/* 232 */       if (!isNestedTx) {
/* 233 */         this.transactionService.rollbackTx();
/*     */       }
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void saveConfigurationAsTemplate(@Nullable String projectId, @NotNull String templateName, @NotNull SpecificationTemplate configuration) {
/*     */     ILocation configurationLocation;
/* 248 */     if (projectId == null) {
/* 249 */       configurationLocation = getGlobalTemplateLocation(templateName);
/*     */     } else {
/* 251 */       configurationLocation = getConfigurationLocation(projectId, templateName, true);
/*     */     } 
/* 253 */     saveConfigurationInternal(configurationLocation, configuration);
/*     */   }
/*     */ 
/*     */   
/*     */   public void saveConfiguration(@NotNull String projectId, @NotNull String id, @NotNull SpecificationConfiguration configuration) {
/* 258 */     ILocation configurationLocation = getConfigurationLocation(projectId, id, false);
/* 259 */     saveConfigurationInternal(configurationLocation, configuration);
/*     */   }
/*     */   
/*     */   private void saveConfigurationInternal(@NotNull ILocation location, @NotNull SpecificationTemplate configuration) {
/*     */     try {
/* 264 */       boolean isNestedTx = this.transactionService.txExists();
/* 265 */       boolean failed = true;
/* 266 */       if (!isNestedTx) {
/* 267 */         this.transactionService.beginTx();
/*     */       }
/*     */       
/*     */       try {
/* 271 */         IRepositoryConnection connection = this.repositoryService.getConnection(location);
/*     */         
/* 273 */         byte[] configurationContent = marshallConfiguration(configuration);
/*     */         
/* 275 */         boolean updateContent = !(connection.exists(location) && 
/* 276 */           IOUtils.contentEquals(connection.getContent(location), new ByteArrayInputStream(configurationContent)));
/*     */         
/* 278 */         if (updateContent) {
/* 279 */           connection.makeFolders(location.getParentLocation());
/* 280 */           connection.setContent(location, new ByteArrayInputStream(configurationContent));
/*     */         } 
/*     */         
/* 283 */         failed = false;
/*     */       } finally {
/*     */         
/* 286 */         if (!isNestedTx) {
/* 287 */           this.transactionService.endTx(failed);
/*     */         }
/*     */       } 
/* 290 */     } catch (Exception e) {
/* 291 */       throw new SynchronizationException("Failed to save ReqIF configuration.", e);
/*     */     } 
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private byte[] marshallConfiguration(@NotNull SpecificationTemplate template) {
/*     */     ByteArrayOutputStream out;
/*     */     try {
/* 299 */       Marshaller marshaller = this.jaxbContext.createMarshaller();
/* 300 */       marshaller.setProperty("jaxb.formatted.output", Boolean.valueOf(true));
/* 301 */       out = new ByteArrayOutputStream();
/* 302 */       marshaller.marshal(template, out);
/* 303 */     } catch (Exception e) {
/* 304 */       throw new SynchronizationException("Failed to marshall configuration.", e);
/*     */     } 
/*     */     
/* 307 */     return out.toByteArray();
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public Collection<SpecificationTemplate> loadTemplates(@Nullable String projectId) {
/* 313 */     if (projectId != null) {
/* 314 */       return getTemplates(getConfigurationsLocation(projectId, "templates"), false);
/*     */     }
/* 316 */     return getTemplates(this.repository.getGlobalBaseLocation().append("templates"), true);
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   private Collection<SpecificationTemplate> getTemplates(@NotNull ILocation templateLocation, boolean isGlobal) {
/* 322 */     boolean isNestedTx = this.transactionService.txExists();
/* 323 */     if (!isNestedTx) {
/* 324 */       this.transactionService.beginTx();
/*     */     }
/* 326 */     Collection<SpecificationTemplate> templates = new ArrayList<>();
/*     */     try {
/* 328 */       IRepositoryReadOnlyConnection connection = this.repositoryService.getReadOnlyConnection(templateLocation);
/* 329 */       if (connection.exists(templateLocation)) {
/* 330 */         Collection<ILocation> configurations = connection.getSubLocations(templateLocation, false);
/* 331 */         for (ILocation configurationLocation : configurations) {
/* 332 */           String fileName = configurationLocation.getLastComponent();
/* 333 */           if (fileName.endsWith(".xml")) {
/* 334 */             String templateName = fileName.substring(0, fileName.length() - 4);
/* 335 */             if (isGlobal) {
/* 336 */               templateName = String.valueOf(templateName) + Localization.getString("dialog.reqif.mainConfiguration.global");
/*     */             }
/*     */             
/*     */             try {
/* 340 */               InputStream content = connection.getContent(configurationLocation);
/* 341 */               SpecificationTemplate configuration = unmarschallTemplate(templateName, content);
/* 342 */               templates.add(configuration);
/* 343 */             } catch (Exception e) {
/* 344 */               LOG.error("Error loading ReqIF configuration " + configurationLocation.getLocationPath(), e);
/*     */             } 
/*     */           } 
/*     */         } 
/*     */       } 
/*     */     } finally {
/* 350 */       if (!isNestedTx) {
/* 351 */         this.transactionService.rollbackTx();
/*     */       }
/*     */     } 
/* 354 */     return templates;
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private SpecificationTemplate unmarschallTemplate(@NotNull String templateName, @NotNull InputStream content) throws JAXBException {
/* 359 */     Unmarshaller unmarshaller = this.jaxbContext.createUnmarshaller();
/* 360 */     SpecificationTemplate configuration = (SpecificationTemplate)unmarshaller.unmarshal(content);
/* 361 */     configuration.setTemplateName(templateName);
/* 362 */     return configuration;
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/configuration/ConfigurationService.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */