Object o = object(%absoluteNumber%, m)
string attrName = "%attributeName%"
string attrValue = "%attributeValue%" 
AttrDef ad = find(m, attrName)
AttrType at
noError()
void updateMultiValue()
{
	Regexp re = regexp ":"
	Regexp delim = regexp ","
	string addedVals, removedVals, tempIdx, enumVal
	int enumIndex

	if(re attrValue)
	{
		addedVals = attrValue[0:(start 0)-1]
		removedVals = attrValue[(end 0)+1:]

		if(length(addedVals) > 0)
		{
			print "added: "addedVals  "\n"
			while(delim addedVals)
			{
				tempIdx = addedVals[0:(start 0)-1]
				o.attrName += tempIdx
				addedVals = addedVals[(end 0)+1:]
			}

			o.attrName += addedVals
		}

		if(length(removedVals) > 0)
		{
			print "removed: " removedVals 
			while(delim removedVals )
			{
				tempIdx = removedVals [0:(start 0)-1]
				o.attrName -= tempIdx
				removedVals = removedVals [(end 0)+1:]
			}

			o.attrName -= removedVals
		}
	}
}

if(null(attrValue))
{
	o.attrName = null
	result = "OK"
}
else if(!null(ad))
{
	at = ad.type
	
	
	if(at.type == attrString || at.type == attrText)
	{
		//print attrValue "\n"
		o.attrName = richText attrValue
	}
	else if(at.type == attrReal)
	{
		real r = realOf(attrValue)
		
		if(!null(r))
			o.attrName = r
	}
	else if(at.type == attrInteger)
	{
		int i = intOf(attrValue)
		
		if(!null(i))
			o.attrName = i
	}
	else if(at.type == attrDate)
	{
		Date d = date(attrValue)
		Date oldDate = "1 Jan 1970"
		
		if(!null(d))
			o.attrName = d
		else
		{
			d = attrValue
		
			if(!null(d) && d >= oldDate)
				o.attrName = d
		}
	}
	else if(at.type == attrEnumeration)
	{
		if(ad.multi)
		{
			updateMultiValue()
		}
		else
		{
			o.attrName = attrValue
		}
	}
	else if(at.type == attrUsername)
	{
		o.attrName = attrValue
	}
	
	result = "OK"
}
else
	result = "FAIL"
string ErrMess = lastError()
if (!null ErrMess)
{
	result = ErrMess	
}
print(result"\n")