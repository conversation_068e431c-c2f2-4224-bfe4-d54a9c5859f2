/*    */ package com.polarion.synchronizer.configuration;
/*    */ 
/*    */ import com.polarion.synchronizer.internal.configuration.FieldMappingConfiguration;
/*    */ import java.util.LinkedList;
/*    */ import java.util.List;
/*    */ import javax.xml.bind.annotation.XmlAttribute;
/*    */ import javax.xml.bind.annotation.XmlElement;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class FieldMappingGroupConfiguration
/*    */ {
/*    */   @XmlAttribute
/*    */   private String leftType;
/*    */   @XmlAttribute
/*    */   private String rightType;
/*    */   
/*    */   public FieldMappingGroupConfiguration() {}
/*    */   
/*    */   public FieldMappingGroupConfiguration(String leftType, String rightType) {
/* 23 */     this.leftType = leftType;
/* 24 */     this.rightType = rightType;
/*    */   }
/*    */   
/*    */   @XmlElement(name = "fieldMapping")
/* 28 */   private List<FieldMappingConfiguration> fieldMappings = new LinkedList<>();
/*    */   
/*    */   public List<FieldMappingConfiguration> getFieldMappings() {
/* 31 */     return this.fieldMappings;
/*    */   }
/*    */   
/*    */   public String getLeftType() {
/* 35 */     return this.leftType;
/*    */   }
/*    */   
/*    */   public String getRightType() {
/* 39 */     return this.rightType;
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/configuration/FieldMappingGroupConfiguration.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */