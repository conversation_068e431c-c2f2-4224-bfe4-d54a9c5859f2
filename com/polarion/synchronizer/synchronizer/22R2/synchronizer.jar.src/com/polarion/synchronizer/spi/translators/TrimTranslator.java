/*    */ package com.polarion.synchronizer.spi.translators;
/*    */ 
/*    */ import com.polarion.synchronizer.mapping.TranslationResult;
/*    */ import org.jetbrains.annotations.Nullable;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class TrimTranslator
/*    */   extends TypesafeTranslator<String, String, String>
/*    */ {
/*    */   public TrimTranslator() {
/* 31 */     super(String.class, String.class);
/*    */   }
/*    */ 
/*    */   
/*    */   public TranslationResult<String> translateBidirectionalTypesafe(@Nullable String sourceBaseline, @Nullable String sourceValue, @Nullable String targetBaseline, @Nullable String targetValue) {
/* 36 */     return createBidirectionalResult(sourceBaseline, sourceValue, translateValue(sourceValue), targetBaseline, targetValue);
/*    */   }
/*    */ 
/*    */   
/*    */   public TranslationResult<String> translateUnidirectionalTypesafe(@Nullable String sourceValue, @Nullable String targetValue) {
/* 41 */     return createUnidirectionalResult(translateValue(sourceValue), targetValue);
/*    */   }
/*    */   
/*    */   @Nullable
/*    */   private String translateValue(@Nullable String sourceValue) {
/* 46 */     return (sourceValue == null) ? null : sourceValue.trim();
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/spi/translators/TrimTranslator.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */