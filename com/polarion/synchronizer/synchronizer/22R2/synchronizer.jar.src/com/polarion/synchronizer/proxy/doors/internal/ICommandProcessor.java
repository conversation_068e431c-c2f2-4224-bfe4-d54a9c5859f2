package com.polarion.synchronizer.proxy.doors.internal;

import com.google.inject.ImplementedBy;
import com.polarion.synchronizer.proxy.doors.internal.commands.IDoorsCommand;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@ImplementedBy(DoorsCommandProcessor.class)
public interface ICommandProcessor {
  void registerConnection(@NotNull String paramString);
  
  void closeConnection(@NotNull String paramString);
  
  @NotNull
  String getInitScript(@NotNull String paramString);
  
  @NotNull
  String trackExecution(@NotNull String paramString, @NotNull IDoorsCommand<?> paramIDoorsCommand);
  
  void trackResult(@NotNull String paramString1, @NotNull String paramString2, @NotNull String paramString3);
  
  void trackError(@NotNull String paramString1, @NotNull String paramString2, @NotNull String paramString3);
  
  @Nullable
  IDoorsCommand<?> removeCommand(@NotNull String paramString);
  
  <T> IDoorsCommand<T> addCommand(@NotNull String paramString, @NotNull IDoorsCommand<T> paramIDoorsCommand);
  
  <T> T execute(@NotNull String paramString, @NotNull IDoorsCommand<T> paramIDoorsCommand);
}


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/internal/ICommandProcessor.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */