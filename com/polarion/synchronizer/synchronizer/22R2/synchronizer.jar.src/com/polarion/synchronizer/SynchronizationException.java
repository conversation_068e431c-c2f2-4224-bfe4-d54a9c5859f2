/*    */ package com.polarion.synchronizer;
/*    */ 
/*    */ 
/*    */ 
/*    */ public class SynchronizationException
/*    */   extends RuntimeException
/*    */ {
/*    */   public SynchronizationException() {}
/*    */   
/*    */   public SynchronizationException(String message, Throwable cause) {
/* 11 */     super(message, cause);
/*    */   }
/*    */   
/*    */   public SynchronizationException(String message) {
/* 15 */     super(message);
/*    */   }
/*    */   
/*    */   public SynchronizationException(Throwable cause) {
/* 19 */     super(cause);
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/SynchronizationException.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */