/*    */ package com.polarion.synchronizer.spi;
/*    */ 
/*    */ import com.polarion.synchronizer.model.FieldDefinition;
/*    */ import com.polarion.synchronizer.model.IProxy;
/*    */ import com.polarion.synchronizer.model.Option;
/*    */ import com.polarion.synchronizer.model.TransferItem;
/*    */ import com.polarion.synchronizer.model.UpdateResult;
/*    */ import java.util.Collection;
/*    */ import java.util.List;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ import org.jetbrains.annotations.Nullable;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class ProxyFilter
/*    */   extends AbstractProxy
/*    */ {
/*    */   private final IProxy delegate;
/*    */   
/*    */   public ProxyFilter(@NotNull IProxy delegate) {
/* 23 */     this.delegate = delegate;
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   public Collection<TransferItem> getItems(@NotNull Collection<String> ids, @NotNull Collection<String> keys) {
/* 29 */     return this.delegate.getItems(ids, keys);
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   public Collection<TransferItem> getScopeItems(@NotNull Collection<String> keys) {
/* 35 */     return this.delegate.getScopeItems(keys);
/*    */   }
/*    */ 
/*    */   
/*    */   @Nullable
/*    */   public Collection<FieldDefinition> getDefinedFields(@Nullable String typeId) {
/* 41 */     return this.delegate.getDefinedFields(typeId);
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   public List<UpdateResult> update(@NotNull List<TransferItem> items) {
/* 47 */     return this.delegate.update(items);
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   public List<UpdateResult> delete(@NotNull List<String> ids) {
/* 53 */     return this.delegate.delete(ids);
/*    */   }
/*    */ 
/*    */   
/*    */   public boolean isHierarchySupported() {
/* 58 */     return this.delegate.isHierarchySupported();
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   public Collection<Option> getDefinedTypes() {
/* 64 */     return this.delegate.getDefinedTypes();
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   public Collection<Option> getDefinedTypes(@NotNull Collection<String> requiredTypes) {
/* 70 */     return this.delegate.getDefinedTypes(requiredTypes);
/*    */   }
/*    */ 
/*    */   
/*    */   public void close() {
/* 75 */     this.delegate.close();
/*    */   }
/*    */ 
/*    */   
/*    */   public boolean hasNonSynchronizableFields() {
/* 80 */     return this.delegate.hasNonSynchronizableFields();
/*    */   }
/*    */ 
/*    */   
/*    */   @Deprecated
/*    */   public String getContentScope() {
/* 86 */     return this.delegate.getContentScope();
/*    */   }
/*    */ 
/*    */   
/*    */   public String getTargetName() {
/* 91 */     return this.delegate.getTargetName();
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/spi/ProxyFilter.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */