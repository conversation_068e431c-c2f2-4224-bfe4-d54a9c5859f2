/*    */ package com.polarion.synchronizer.configuration;
/*    */ 
/*    */ import com.polarion.synchronizer.model.Direction;
/*    */ import java.util.LinkedList;
/*    */ import java.util.List;
/*    */ import javax.xml.bind.annotation.XmlAccessType;
/*    */ import javax.xml.bind.annotation.XmlAccessorType;
/*    */ import javax.xml.bind.annotation.XmlAttribute;
/*    */ import javax.xml.bind.annotation.XmlElement;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @XmlAccessorType(XmlAccessType.FIELD)
/*    */ public class MappingConfiguration
/*    */ {
/*    */   @XmlAttribute
/*    */   private Direction hierarchyDirection;
/*    */   @XmlAttribute
/*    */   private Direction primaryHierarchyDirection;
/*    */   @XmlElement
/*    */   private FieldMappingGroupConfiguration defaultMappingGroup;
/*    */   @XmlElement(name = "fieldMappingGroup")
/* 26 */   private List<FieldMappingGroupConfiguration> fieldMappingGroups = new LinkedList<>();
/*    */ 
/*    */   
/*    */   @Deprecated
/*    */   public MappingConfiguration() {}
/*    */ 
/*    */   
/*    */   public MappingConfiguration(FieldMappingGroupConfiguration defaultMappingGroup) {
/* 34 */     this.defaultMappingGroup = defaultMappingGroup;
/*    */   }
/*    */   
/*    */   public Direction getHierarchyDirection() {
/* 38 */     return this.hierarchyDirection;
/*    */   }
/*    */   
/*    */   public Direction getPrimaryHierarchyDirection() {
/* 42 */     return this.primaryHierarchyDirection;
/*    */   }
/*    */   
/*    */   public void setHierarchyDirection(Direction hierarchyDirection) {
/* 46 */     this.hierarchyDirection = hierarchyDirection;
/*    */   }
/*    */   
/*    */   public void setPrimaryHierarchyDirection(Direction primaryHierarchyDirection) {
/* 50 */     this.primaryHierarchyDirection = primaryHierarchyDirection;
/*    */   }
/*    */   
/*    */   public List<FieldMappingGroupConfiguration> getFieldMappingGroups() {
/* 54 */     return this.fieldMappingGroups;
/*    */   }
/*    */   
/*    */   public FieldMappingGroupConfiguration getDefaultMappingGroup() {
/* 58 */     return this.defaultMappingGroup;
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/configuration/MappingConfiguration.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */