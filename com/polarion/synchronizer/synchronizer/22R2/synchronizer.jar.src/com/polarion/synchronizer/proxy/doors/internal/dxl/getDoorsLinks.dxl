Buffer b = create

Object o


b += "<links>\n"

for o in all m do {
   
   b += "<object id=\""
   b += o."Absolute Number"""
   b += "\">\n"
   
   Link link 
   for link in o -> "*" do { 
     ModName_ targetmod = target link 
     
     b += "<link id=\""
     b += qualifiedUniqueID(targetmod)
     b += "/"
     b += targetAbsNo(link )""
     b += "\"/>\n"
   }
   
   b += "</object>\n"

} 

b += "</links>"

result = stringOf(b)