/*     */ package com.polarion.synchronizer.internal;
/*     */ 
/*     */ import com.google.inject.name.Named;
/*     */ import com.polarion.core.config.Configuration;
/*     */ import com.polarion.core.util.collection.buffered.ObjectHashMap;
/*     */ import com.polarion.platform.cluster.ClusterService;
/*     */ import com.polarion.synchronizer.IBaselineProvider;
/*     */ import com.polarion.synchronizer.IConnectionMap;
/*     */ import com.polarion.synchronizer.IPersistence;
/*     */ import com.polarion.synchronizer.IPersistenceOperation;
/*     */ import com.polarion.synchronizer.SynchronizationException;
/*     */ import java.io.File;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Collection;
/*     */ import java.util.Collections;
/*     */ import java.util.HashMap;
/*     */ import java.util.HashSet;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import java.util.Set;
/*     */ import java.util.concurrent.TimeUnit;
/*     */ import java.util.concurrent.locks.Lock;
/*     */ import java.util.concurrent.locks.ReentrantLock;
/*     */ import javax.inject.Inject;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ObjectMapPersistence
/*     */   implements IPersistence
/*     */ {
/*     */   @NotNull
/*     */   private final File baseFolder;
/*     */   @NotNull
/*     */   private ClusterService clusterService;
/*     */   @NotNull
/*  37 */   private Map<String, Lock> projectLocks = new HashMap<>();
/*     */   @NotNull
/*  39 */   private Map<String, Lock> connectionMapLocks = new HashMap<>();
/*     */   
/*     */   @Inject
/*     */   public ObjectMapPersistence(@Named("persistenceFolder") @NotNull File baseFolder, @NotNull ClusterService clusterService) {
/*  43 */     this.baseFolder = baseFolder;
/*  44 */     this.clusterService = clusterService;
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public IPersistenceOperation createOperation(@NotNull String projectId) {
/*  50 */     return new PersistenceOperation(projectId, acquireProjectLock(projectId, Configuration.getInstance().connectors().getProjectsLockTimeout()));
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   private Lock getConnectionMapLock(@NotNull String lockPath) {
/*     */     Lock connectionMapLock;
/*  57 */     synchronized (this) {
/*  58 */       connectionMapLock = this.connectionMapLocks.get(lockPath);
/*  59 */       if (connectionMapLock == null) {
/*  60 */         if (!this.clusterService.isCluster()) {
/*  61 */           connectionMapLock = new ReentrantLock();
/*     */         } else {
/*  63 */           connectionMapLock = this.clusterService.locks().newExclusiveLock(this.clusterService.location().cluster(this.clusterService.info().getCluster().getId()).synchronizerLock().append(lockPath));
/*     */         } 
/*  65 */         this.connectionMapLocks.put(lockPath, connectionMapLock);
/*     */       } 
/*     */     } 
/*     */     
/*  69 */     return connectionMapLock;
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   private Lock acquireProjectLock(@NotNull String projectId, long timeout) {
/*     */     Lock projectLock;
/*  76 */     synchronized (this) {
/*  77 */       projectLock = this.projectLocks.get(projectId);
/*  78 */       if (projectLock == null) {
/*  79 */         if (!this.clusterService.isCluster()) {
/*  80 */           projectLock = new ReentrantLock();
/*     */         } else {
/*  82 */           projectLock = this.clusterService.locks().newExclusiveLock(this.clusterService.location().cluster(this.clusterService.info().getCluster().getId()).synchronizerLock().append(projectId));
/*     */         } 
/*  84 */         this.projectLocks.put(projectId, projectLock);
/*     */       } 
/*     */     } 
/*     */     
/*     */     try {
/*  89 */       if (!projectLock.tryLock(timeout, TimeUnit.MILLISECONDS)) {
/*  90 */         throw new RuntimeException("Timeout exceeded while trying to acquire project lock");
/*     */       }
/*  92 */     } catch (InterruptedException e) {
/*  93 */       throw new RuntimeException(e);
/*     */     } 
/*     */     
/*  96 */     return projectLock;
/*     */   }
/*     */   
/*     */   public class PersistenceOperation
/*     */     implements IPersistenceOperation {
/*     */     private final class AutoSaveMap implements Map<String, Object> {
/*     */       @NotNull
/*     */       private final ObjectHashMap objectHashMap;
/*     */       @NotNull
/*     */       private final Lock mapLock;
/*     */       
/*     */       private AutoSaveMap(@NotNull ObjectHashMap objectHashMap, Lock mapLock) {
/* 108 */         this.objectHashMap = objectHashMap;
/* 109 */         this.mapLock = mapLock;
/*     */       }
/*     */ 
/*     */       
/*     */       public Set<Map.Entry<String, Object>> entrySet() {
/* 114 */         return this.objectHashMap.entrySet();
/*     */       }
/*     */ 
/*     */       
/*     */       public int size() {
/* 119 */         this.mapLock.lock();
/*     */         try {
/* 121 */           return this.objectHashMap.size();
/*     */         } finally {
/* 123 */           this.mapLock.unlock();
/*     */         } 
/*     */       }
/*     */ 
/*     */       
/*     */       public boolean isEmpty() {
/* 129 */         throw new UnsupportedOperationException();
/*     */       }
/*     */ 
/*     */       
/*     */       public boolean containsKey(Object key) {
/* 134 */         throw new UnsupportedOperationException();
/*     */       }
/*     */ 
/*     */       
/*     */       public boolean containsValue(Object value) {
/* 139 */         throw new UnsupportedOperationException();
/*     */       }
/*     */ 
/*     */       
/*     */       public Object get(Object key) {
/* 144 */         this.mapLock.lock();
/*     */         try {
/* 146 */           return this.objectHashMap.get(key);
/*     */         } finally {
/* 148 */           this.mapLock.unlock();
/*     */         } 
/*     */       }
/*     */ 
/*     */       
/*     */       public Object put(String key, Object value) {
/* 154 */         this.mapLock.lock();
/*     */         try {
/* 156 */           Object old = this.objectHashMap.put(key, value);
/* 157 */           this.objectHashMap.sleep();
/* 158 */           return old;
/*     */         } finally {
/* 160 */           this.mapLock.unlock();
/*     */         } 
/*     */       }
/*     */ 
/*     */       
/*     */       public Object remove(Object key) {
/* 166 */         this.mapLock.lock();
/*     */         try {
/* 168 */           Object remove = this.objectHashMap.remove(key);
/* 169 */           this.objectHashMap.sleep();
/* 170 */           return remove;
/*     */         } finally {
/* 172 */           this.mapLock.unlock();
/*     */         } 
/*     */       }
/*     */ 
/*     */       
/*     */       public void putAll(Map<? extends String, ? extends Object> m) {
/* 178 */         throw new UnsupportedOperationException();
/*     */       }
/*     */ 
/*     */       
/*     */       public void clear() {
/* 183 */         throw new UnsupportedOperationException();
/*     */       }
/*     */ 
/*     */       
/*     */       public Set<String> keySet() {
/* 188 */         throw new UnsupportedOperationException();
/*     */       }
/*     */ 
/*     */       
/*     */       public Collection<Object> values() {
/* 193 */         throw new UnsupportedOperationException();
/*     */       }
/*     */     }
/*     */     
/*     */     @NotNull
/* 198 */     private final Set<String> connections = new HashSet<>();
/*     */     
/*     */     @NotNull
/* 201 */     private final Map<String, BaselinePersistence> baselineProviders = new HashMap<>();
/*     */     
/*     */     @NotNull
/*     */     private final String projectId;
/*     */     
/*     */     @NotNull
/*     */     private Lock lock;
/*     */     
/*     */     private boolean closed;
/*     */     
/*     */     @NotNull
/* 212 */     private List<ObjectHashMap> createdObjectHashMapList = new ArrayList<>();
/*     */     
/*     */     public PersistenceOperation(@NotNull String projectId, Lock lock) {
/* 215 */       this.projectId = projectId;
/* 216 */       this.lock = lock;
/*     */     }
/*     */ 
/*     */     
/*     */     public void close() {
/* 221 */       for (ObjectHashMap createdObjectHashMap : this.createdObjectHashMapList) {
/* 222 */         createdObjectHashMap.sleep();
/*     */       }
/* 224 */       this.createdObjectHashMapList.clear();
/* 225 */       this.lock.unlock();
/* 226 */       this.closed = true;
/*     */     }
/*     */ 
/*     */     
/*     */     @NotNull
/*     */     public synchronized IBaselineProvider getBaselineProvider(@NotNull String leftSystemId, @NotNull String rightSystemId) {
/* 232 */       if (this.closed) {
/* 233 */         throw new IllegalStateException("Can not access baseline provider: Operation already closed.");
/*     */       }
/* 235 */       if (!"POLARION".equals(leftSystemId)) {
/* 236 */         throw new SynchronizationException("Left system must be Polarion.");
/*     */       }
/*     */       
/* 239 */       String id = String.valueOf(this.projectId) + "/" + rightSystemId;
/*     */       
/* 241 */       BaselinePersistence baselinePersistence = this.baselineProviders.get(id);
/*     */       
/* 243 */       if (baselinePersistence == null) {
/* 244 */         baselinePersistence = new BaselinePersistence(
/* 245 */             createMap(this.projectId, rightSystemId, "baseline-data", "left", false), 
/* 246 */             createMap(this.projectId, rightSystemId, "baseline-data", "right", false));
/* 247 */         this.baselineProviders.put(id, baselinePersistence);
/*     */       } 
/*     */       
/* 250 */       return baselinePersistence;
/*     */     }
/*     */ 
/*     */     
/*     */     @NotNull
/*     */     public synchronized IConnectionMap getConnectionMap(@NotNull String leftSystemId, @NotNull String rightSystemId, @NotNull Collection<String> externalProjectIds) {
/* 256 */       if (this.closed) {
/* 257 */         throw new IllegalStateException("Can not access connection map: Operation already closed.");
/*     */       }
/* 259 */       if (!"POLARION".equals(leftSystemId)) {
/* 260 */         throw new SynchronizationException("Left system must be Polarion.");
/*     */       }
/*     */       
/* 263 */       Collection<IConnectionMap> externalMaps = new ArrayList<>(externalProjectIds.size());
/* 264 */       for (String externalProjectId : externalProjectIds) {
/* 265 */         externalMaps.add(loadConnectionMap(externalProjectId, rightSystemId, Collections.EMPTY_LIST, true));
/*     */       }
/*     */       
/* 268 */       IConnectionMap connectionMap = loadConnectionMap(this.projectId, rightSystemId, externalMaps, false);
/*     */       
/* 270 */       return connectionMap;
/*     */     }
/*     */     
/*     */     @NotNull
/*     */     private IConnectionMap loadConnectionMap(@NotNull String projectId, @NotNull String rightSystemId, @NotNull Collection<IConnectionMap> externalMaps, boolean cloneMaps) {
/* 275 */       String id = String.valueOf(projectId) + "/" + rightSystemId;
/*     */       
/* 277 */       if (this.connections.contains(id)) {
/* 278 */         throw new SynchronizationException(String.format("Can't reload connection map with ID %s .", new Object[] { id }));
/*     */       }
/*     */       
/* 281 */       Map<String, String> leftRight = createMap(projectId, rightSystemId, "connection-data", "left-right", cloneMaps);
/* 282 */       Map<String, String> rightLeft = createMap(projectId, rightSystemId, "connection-data", "right-left", cloneMaps);
/*     */       
/* 284 */       IConnectionMap connectionMap = new ConnectionMap(leftRight, rightLeft, externalMaps);
/*     */       
/* 286 */       this.connections.add(id);
/* 287 */       return connectionMap;
/*     */     }
/*     */     
/*     */     @NotNull
/*     */     private <T> Map<String, T> createMap(@NotNull String projectId, @NotNull String rightSystemId, @NotNull String dataDirectory, @NotNull String directionDataDirectory, boolean cloneMap) {
/* 292 */       File base = new File(getBaseFolder(projectId, rightSystemId), String.valueOf(dataDirectory) + File.separatorChar + directionDataDirectory);
/* 293 */       ObjectHashMap objectHashMap = new ObjectHashMap(base, 100, 100, null, null, 1, false);
/* 294 */       this.createdObjectHashMapList.add(objectHashMap);
/* 295 */       Lock mapLock = ObjectMapPersistence.this.getConnectionMapLock(String.valueOf(projectId) + rightSystemId + dataDirectory + directionDataDirectory);
/* 296 */       Map<String, ?> map = new AutoSaveMap(objectHashMap, mapLock);
/* 297 */       if (!cloneMap) {
/* 298 */         return (Map)map;
/*     */       }
/*     */       
/* 301 */       mapLock.lock();
/*     */       try {
/* 303 */         return (Map)new HashMap<>(map);
/*     */       } finally {
/* 305 */         mapLock.unlock();
/*     */       } 
/*     */     }
/*     */     
/*     */     @NotNull
/*     */     private File getBaseFolder(@NotNull String projectId, @NotNull String rightSystemId) {
/* 311 */       return new File(ObjectMapPersistence.this.baseFolder, String.valueOf(projectId) + File.separatorChar + rightSystemId);
/*     */     }
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/internal/ObjectMapPersistence.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */