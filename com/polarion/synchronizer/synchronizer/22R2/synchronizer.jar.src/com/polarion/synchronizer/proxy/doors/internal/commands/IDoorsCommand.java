package com.polarion.synchronizer.proxy.doors.internal.commands;

import com.polarion.synchronizer.proxy.doors.internal.dxl.IDxlScript;
import org.jetbrains.annotations.NotNull;

public interface IDoorsCommand<T> {
  @NotNull
  IDxlScript execute();
  
  @NotNull
  T getResult();
  
  boolean setResult(@NotNull String paramString);
  
  void setError(@NotNull String paramString);
  
  boolean canBeExecuted();
}


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/internal/commands/IDoorsCommand.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */