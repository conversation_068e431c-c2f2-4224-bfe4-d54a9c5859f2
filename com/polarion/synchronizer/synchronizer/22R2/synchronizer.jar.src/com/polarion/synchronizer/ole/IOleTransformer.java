package com.polarion.synchronizer.ole;

import com.thoughtworks.xstream.converters.ConversionException;
import java.io.InputStream;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public interface IOleTransformer {
  @Nullable
  OLEImage findOleThumbnail(@NotNull InputStream paramInputStream, @NotNull String paramString) throws ConversionException;
}


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/ole/IOleTransformer.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */