/*     */ package com.polarion.synchronizer.proxy.doors.xmlmodel;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.List;
/*     */ import javax.xml.bind.annotation.XmlAttribute;
/*     */ import javax.xml.bind.annotation.XmlElement;
/*     */ import javax.xml.bind.annotation.XmlElementWrapper;
/*     */ import javax.xml.bind.annotation.XmlRootElement;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @XmlRootElement(name = "publication")
/*     */ public class DoorsPublication
/*     */ {
/*     */   private String author;
/*     */   private String version;
/*     */   private String publicationDate;
/*     */   private String publicationTool;
/*     */   private String databaseIdentifier;
/*     */   @NotNull
/*  41 */   private final List<DoorsObject> requirements = new ArrayList<>();
/*     */   
/*     */   @XmlElementWrapper(name = "requirements")
/*     */   @XmlElement(name = "Object")
/*     */   @NotNull
/*     */   public List<DoorsObject> getRequirements() {
/*  47 */     return this.requirements;
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   public String getAuthor() {
/*  52 */     return this.author;
/*     */   }
/*     */   
/*     */   @XmlAttribute
/*     */   public void setAuthor(@NotNull String author) {
/*  57 */     this.author = author;
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   public String getVersion() {
/*  62 */     return this.version;
/*     */   }
/*     */   
/*     */   @XmlAttribute
/*     */   public void setVersion(@NotNull String version) {
/*  67 */     this.version = version;
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   public String getPublicationDate() {
/*  72 */     return this.publicationDate;
/*     */   }
/*     */   
/*     */   @XmlAttribute
/*     */   public void setPublicationDate(@NotNull String publicationDate) {
/*  77 */     this.publicationDate = publicationDate;
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   public String getPublicationTool() {
/*  82 */     return this.publicationTool;
/*     */   }
/*     */   
/*     */   @XmlAttribute
/*     */   public void setPublicationTool(@NotNull String publicationTool) {
/*  87 */     this.publicationTool = publicationTool;
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   public String getDatabaseIdentifier() {
/*  92 */     return this.databaseIdentifier;
/*     */   }
/*     */   
/*     */   @XmlAttribute
/*     */   public void setDatabaseIdentifier(@NotNull String databaseIdentifier) {
/*  97 */     this.databaseIdentifier = databaseIdentifier;
/*     */   }
/*     */ 
/*     */   
/*     */   public int hashCode() {
/* 102 */     int prime = 31;
/* 103 */     int result = 1;
/* 104 */     result = 31 * result + ((this.author == null) ? 0 : this.author.hashCode());
/* 105 */     result = 31 * result + ((this.databaseIdentifier == null) ? 0 : this.databaseIdentifier.hashCode());
/* 106 */     result = 31 * result + ((this.publicationDate == null) ? 0 : this.publicationDate.hashCode());
/* 107 */     result = 31 * result + ((this.publicationTool == null) ? 0 : this.publicationTool.hashCode());
/* 108 */     List<DoorsObject> requirements2 = this.requirements;
/*     */     
/* 110 */     result = 31 * result + requirements2.hashCode();
/* 111 */     result = 31 * result + ((this.version == null) ? 0 : this.version.hashCode());
/* 112 */     return result;
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean equals(Object obj) {
/* 117 */     if (this == obj) {
/* 118 */       return true;
/*     */     }
/* 120 */     if (obj == null) {
/* 121 */       return false;
/*     */     }
/* 123 */     if (getClass() != obj.getClass()) {
/* 124 */       return false;
/*     */     }
/* 126 */     DoorsPublication other = (DoorsPublication)obj;
/* 127 */     if (this.author == null) {
/* 128 */       if (other.author != null) {
/* 129 */         return false;
/*     */       }
/* 131 */     } else if (!this.author.equals(other.author)) {
/* 132 */       return false;
/*     */     } 
/* 134 */     if (this.databaseIdentifier == null) {
/* 135 */       if (other.databaseIdentifier != null) {
/* 136 */         return false;
/*     */       }
/* 138 */     } else if (!this.databaseIdentifier.equals(other.databaseIdentifier)) {
/* 139 */       return false;
/*     */     } 
/* 141 */     if (this.publicationDate == null) {
/* 142 */       if (other.publicationDate != null) {
/* 143 */         return false;
/*     */       }
/* 145 */     } else if (!this.publicationDate.equals(other.publicationDate)) {
/* 146 */       return false;
/*     */     } 
/* 148 */     if (this.publicationTool == null) {
/* 149 */       if (other.publicationTool != null) {
/* 150 */         return false;
/*     */       }
/* 152 */     } else if (!this.publicationTool.equals(other.publicationTool)) {
/* 153 */       return false;
/*     */     } 
/*     */     
/* 156 */     List<DoorsObject> requirements2 = this.requirements;
/*     */     
/* 158 */     if (!requirements2.equals(other.requirements)) {
/* 159 */       return false;
/*     */     }
/*     */     
/* 162 */     if (this.version == null) {
/* 163 */       if (other.version != null) {
/* 164 */         return false;
/*     */       }
/* 166 */     } else if (!this.version.equals(other.version)) {
/* 167 */       return false;
/*     */     } 
/* 169 */     return true;
/*     */   }
/*     */ 
/*     */   
/*     */   public String toString() {
/* 174 */     return "DoorsPublication [author=" + this.author + ", version=" + this.version + ", publicationDate=" + this.publicationDate + 
/* 175 */       ", publicationTool=" + this.publicationTool + ", databaseIdentifier=" + this.databaseIdentifier + ", requirements=" + this.requirements + 
/* 176 */       "]";
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/xmlmodel/DoorsPublication.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */