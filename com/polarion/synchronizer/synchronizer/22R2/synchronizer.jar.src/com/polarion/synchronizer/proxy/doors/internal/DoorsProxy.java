/*     */ package com.polarion.synchronizer.proxy.doors.internal;
/*     */ 
/*     */ import com.polarion.core.util.ObjectUtils;
/*     */ import com.polarion.core.util.StringUtils;
/*     */ import com.polarion.platform.i18n.Localization;
/*     */ import com.polarion.synchronizer.ILogger;
/*     */ import com.polarion.synchronizer.SynchronizationException;
/*     */ import com.polarion.synchronizer.model.Attachment;
/*     */ import com.polarion.synchronizer.model.CollectionUpdate;
/*     */ import com.polarion.synchronizer.model.CreateResult;
/*     */ import com.polarion.synchronizer.model.FieldDefinition;
/*     */ import com.polarion.synchronizer.model.Hierarchy;
/*     */ import com.polarion.synchronizer.model.Option;
/*     */ import com.polarion.synchronizer.model.OptionFieldDefinition;
/*     */ import com.polarion.synchronizer.model.Relation;
/*     */ import com.polarion.synchronizer.model.TransferItem;
/*     */ import com.polarion.synchronizer.model.UpdateResult;
/*     */ import com.polarion.synchronizer.proxy.doors.internal.commands.DoorsCommandCheckModule;
/*     */ import com.polarion.synchronizer.proxy.doors.internal.commands.DoorsCommandCloseModule;
/*     */ import com.polarion.synchronizer.proxy.doors.internal.commands.DoorsCommandData;
/*     */ import com.polarion.synchronizer.proxy.doors.internal.commands.DoorsCommandDataAdditionalInformation;
/*     */ import com.polarion.synchronizer.proxy.doors.internal.commands.DoorsCommandDataForObject;
/*     */ import com.polarion.synchronizer.proxy.doors.internal.commands.DoorsCommandDeleteObject;
/*     */ import com.polarion.synchronizer.proxy.doors.internal.commands.DoorsCommandLinks;
/*     */ import com.polarion.synchronizer.proxy.doors.internal.commands.DoorsCommandMetadata;
/*     */ import com.polarion.synchronizer.proxy.doors.internal.commands.DoorsCommandSave;
/*     */ import com.polarion.synchronizer.proxy.doors.internal.commands.IDoorsCommand;
/*     */ import com.polarion.synchronizer.proxy.doors.xmlmodel.DoorsAdditionalInformationPublications;
/*     */ import com.polarion.synchronizer.proxy.doors.xmlmodel.DoorsAttribute;
/*     */ import com.polarion.synchronizer.proxy.doors.xmlmodel.DoorsAttributeDefinition;
/*     */ import com.polarion.synchronizer.proxy.doors.xmlmodel.DoorsDeletedObjectPublication;
/*     */ import com.polarion.synchronizer.proxy.doors.xmlmodel.DoorsDxlValuePublication;
/*     */ import com.polarion.synchronizer.proxy.doors.xmlmodel.DoorsLinks;
/*     */ import com.polarion.synchronizer.proxy.doors.xmlmodel.DoorsMetadata;
/*     */ import com.polarion.synchronizer.proxy.doors.xmlmodel.DoorsObject;
/*     */ import com.polarion.synchronizer.proxy.doors.xmlmodel.DoorsPublication;
/*     */ import com.polarion.synchronizer.proxy.doors.xmlmodel.DoorsPublications;
/*     */ import com.polarion.synchronizer.spi.AbstractProxy;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Arrays;
/*     */ import java.util.Collection;
/*     */ import java.util.Collections;
/*     */ import java.util.HashSet;
/*     */ import java.util.LinkedHashSet;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import java.util.Optional;
/*     */ import java.util.Set;
/*     */ import java.util.function.Function;
/*     */ import java.util.regex.Matcher;
/*     */ import java.util.regex.Pattern;
/*     */ import java.util.stream.Collector;
/*     */ import java.util.stream.Collectors;
/*     */ import java.util.stream.Stream;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ import org.jetbrains.annotations.Nullable;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class DoorsProxy
/*     */   extends AbstractProxy
/*     */ {
/*     */   private static final String ABSOLUTE_NUMBER = "Absolute Number";
/*     */   public static final String KEY_TABLE_TYPE = "TableType";
/*     */   public static final String KEY_OBJECT_TEXT = "Object Text";
/*  86 */   private static final Collection<String> BASE_ATTRIBUTES = Collections.unmodifiableCollection(new HashSet<>(Arrays.asList(new String[] { "Absolute Number", "TableType", "Object Text" })));
/*     */   
/*     */   @NotNull
/*     */   private IDoorsModule module;
/*     */   
/*     */   @NotNull
/*     */   private final ILogger logger;
/*     */   
/*     */   @NotNull
/*  95 */   private DoorsProxyMetadataHelper doorsProxyMetadataHelper = new DoorsProxyMetadataHelper();
/*     */   
/*     */   @NotNull
/*     */   private DoorsToPolarionConverter doorsToPolarionConverter;
/*     */   
/*     */   @NotNull
/*     */   private IPolarionToDoorsConverter polarionToDoorsConverter;
/*     */   
/*     */   @NotNull
/* 104 */   private Pattern doorsAttributeNameFromPolarionAttachmentFileName = Pattern.compile("(.*) \\d+\\.rtf");
/*     */   
/*     */   public DoorsProxy(@NotNull IDoorsModule module, @NotNull ILogger logger) {
/* 107 */     this.module = module;
/* 108 */     this.doorsToPolarionConverter = new DoorsToPolarionConverter(logger);
/* 109 */     this.polarionToDoorsConverter = new PolarionToDoorsConverter(module, logger);
/* 110 */     this.logger = logger;
/* 111 */     module.execute((IDoorsCommand<?>)new DoorsCommandCloseModule());
/* 112 */     loadMetadata();
/*     */     
/* 114 */     if (!((Boolean)module.<Boolean>execute((IDoorsCommand<Boolean>)new DoorsCommandCheckModule())).booleanValue()) {
/* 115 */       throw new SynchronizationException("Module with ID: " + module.getModuleId() + " does not exist");
/*     */     }
/*     */   }
/*     */   
/*     */   public DoorsProxy(@NotNull IDoorsModule module, @NotNull ILogger logger, @NotNull IPolarionToDoorsConverter polarionToDoorsConverter) {
/* 120 */     this(module, logger);
/* 121 */     this.polarionToDoorsConverter = polarionToDoorsConverter;
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public Collection<Option> getDefinedTypes() {
/* 127 */     return Arrays.asList(new Option[] {
/* 128 */           new Option("heading", Localization.getString("reqif.headingLabel")), 
/* 129 */           new Option("requirement", Localization.getString("reqif.requirementLabel")) });
/*     */   }
/*     */   
/*     */   private void loadMetadata() {
/* 133 */     DoorsMetadata doorsMetadata = this.module.<DoorsMetadata>execute((IDoorsCommand<DoorsMetadata>)new DoorsCommandMetadata());
/*     */     
/* 135 */     for (DoorsAttributeDefinition doorsAttributeDefinition : doorsMetadata.getAttributeDefinitions()) {
/*     */       FieldDefinition fieldDefinition;
/* 137 */       String type = this.doorsToPolarionConverter.convertTypeToPolarionType(doorsAttributeDefinition.getAttributeType(), doorsAttributeDefinition.getName());
/* 138 */       String attributeName = doorsAttributeDefinition.getName();
/*     */ 
/*     */       
/* 141 */       if (type.equals("option")) {
/* 142 */         Set<Option> availableOptions = new LinkedHashSet<>();
/* 143 */         for (String availableOption : doorsAttributeDefinition.getAvailableOptions()) {
/* 144 */           availableOptions.add(new Option(availableOption, availableOption));
/*     */         }
/* 146 */         OptionFieldDefinition optionFieldDefinition = new OptionFieldDefinition(attributeName, attributeName, type, true, doorsAttributeDefinition.isMultiValued(), availableOptions);
/*     */       } else {
/* 148 */         boolean canUpdate = attributeName.equals("Object Heading");
/* 149 */         fieldDefinition = new FieldDefinition(attributeName, attributeName, type, !canUpdate, doorsAttributeDefinition.isMultiValued());
/*     */       } 
/* 151 */       this.doorsProxyMetadataHelper.putFieldDefinition(attributeName, fieldDefinition);
/*     */       
/* 153 */       String defaultValue = doorsAttributeDefinition.getDefaultValue();
/*     */       
/* 155 */       if (!defaultValue.isEmpty()) {
/* 156 */         Object convertedDefault; if (type.equals("option")) {
/* 157 */           if (doorsAttributeDefinition.isMultiValued()) {
/* 158 */             convertedDefault = Arrays.asList(defaultValue.split("(\r)?\n"));
/*     */           } else {
/* 160 */             convertedDefault = defaultValue;
/*     */           } 
/* 162 */         } else if (type.equals("boolean")) {
/* 163 */           convertedDefault = Boolean.valueOf(defaultValue);
/*     */         } else {
/* 165 */           Collection<Attachment> defaultAttachments = new ArrayList<>();
/* 166 */           convertedDefault = this.doorsToPolarionConverter.processSingleValuedAttributes(defaultValue, fieldDefinition, false, defaultAttachments);
/* 167 */           if (!defaultAttachments.isEmpty()) {
/* 168 */             this.logger.warn("Found attachment in default attribute value, attachment will be ignored.");
/*     */           }
/*     */         } 
/* 171 */         this.doorsProxyMetadataHelper.putDefaultValue(attributeName, convertedDefault);
/*     */       } 
/*     */     } 
/* 174 */     this.doorsProxyMetadataHelper.initFields();
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public Collection<FieldDefinition> getDefinedFields(@Nullable String typeId) {
/* 180 */     return this.doorsProxyMetadataHelper.getDefinedFields();
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public Collection<TransferItem> getItems(@NotNull Collection<String> ids, @NotNull Collection<String> keys) {
/* 186 */     Collection<TransferItem> filteredTransferItems = new ArrayList<>();
/* 187 */     for (TransferItem transferItem : getScopeItems(keys)) {
/* 188 */       if (ids.contains(transferItem.getId())) {
/* 189 */         filteredTransferItems.add(transferItem);
/*     */       }
/*     */     } 
/* 192 */     return filteredTransferItems;
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public Collection<TransferItem> getScopeItems(@NotNull Collection<String> keys) {
/* 198 */     String moduleId = this.module.getModuleId();
/* 199 */     Collection<TransferItem> transferItems = new ArrayList<>();
/* 200 */     int topLevelObjectCounter = 0;
/*     */     
/* 202 */     DoorsAdditionalInformationPublications additionalInformation = this.module.<DoorsAdditionalInformationPublications>execute((IDoorsCommand<DoorsAdditionalInformationPublications>)new DoorsCommandDataAdditionalInformation());
/* 203 */     List<DoorsDeletedObjectPublication> deletedObjects = additionalInformation.getDeletedObjectPublications();
/* 204 */     List<String> deletedObjectIds = (List<String>)deletedObjects.stream().map(deletedObject -> deletedObject.getObjectId()).collect(Collectors.toList());
/*     */     
/* 206 */     DoorsLinks doorsLinks = this.module.<DoorsLinks>execute((IDoorsCommand<DoorsLinks>)new DoorsCommandLinks());
/* 207 */     DoorsPublications doorsPublications = this.module.<DoorsPublications>execute((IDoorsCommand<DoorsPublications>)new DoorsCommandData());
/* 208 */     for (DoorsPublication doorsPublication : doorsPublications.getPublications()) {
/* 209 */       List<DoorsObject> requirements = doorsPublication.getRequirements();
/* 210 */       for (DoorsObject doorsObject : requirements) {
/* 211 */         if (doorsObjectIsNotDeleted(doorsObject, deletedObjectIds)) {
/* 212 */           collectTransferItems(doorsObject, moduleId, transferItems, keys, null, topLevelObjectCounter, doorsLinks, deletedObjectIds);
/* 213 */           topLevelObjectCounter++;
/*     */         } 
/*     */       } 
/*     */     } 
/*     */     
/* 218 */     if (topLevelObjectCounter != doorsPublications.getExpectedNumberOfObjects().intValue()) {
/* 219 */       throw new SynchronizationException("Expected " + doorsPublications.getExpectedNumberOfObjects() + 
/* 220 */           " objects (top-level) from Doors but received " + topLevelObjectCounter);
/*     */     }
/*     */     
/* 223 */     List<DoorsDxlValuePublication> doorsDxlPublications = additionalInformation.getDxlValuePublications();
/* 224 */     Map<String, TransferItem> transferItemsById = (Map<String, TransferItem>)transferItems.stream().collect(Collectors.toMap(transferItem -> transferItem.getId(), Function.identity()));
/* 225 */     for (DoorsDxlValuePublication doorsDxlValuePublication : doorsDxlPublications) {
/* 226 */       Map<String, DoorsAttribute> doorsAttributes = (Map<String, DoorsAttribute>)doorsDxlValuePublication.getAttributes().stream().collect(Collectors.toMap(attr -> attr.getName(), Function.identity()));
/* 227 */       Set<String> dxlAttributeKeys = new HashSet<>(doorsAttributes.keySet());
/* 228 */       dxlAttributeKeys.retainAll(keys);
/* 229 */       TransferItem transferItem = transferItemsById.get(String.valueOf(moduleId) + "/" + doorsDxlValuePublication.getObjectId());
/* 230 */       if (transferItem != null) {
/* 231 */         loadContent(dxlAttributeKeys, transferItem, doorsAttributes, true, Collections.EMPTY_LIST);
/*     */       }
/*     */     } 
/*     */     
/* 235 */     return transferItems;
/*     */   }
/*     */   
/*     */   private boolean doorsObjectIsNotDeleted(@NotNull DoorsObject doorsObject, @NotNull List<String> deletedObjectIds) {
/* 239 */     Map<String, DoorsAttribute> attributesByName = getDoorsAttributesByName(doorsObject, Collections.emptySet());
/* 240 */     String absoluteNumber = getAbsoluteNumberFromDoorsAttributes(attributesByName);
/* 241 */     return !deletedObjectIds.contains(absoluteNumber);
/*     */   }
/*     */   
/*     */   private void collectTransferItems(@NotNull DoorsObject doorsObject, @NotNull String moduleId, @NotNull Collection<TransferItem> transferItems, @NotNull Collection<String> keys, @Nullable String parentId, int position, @NotNull DoorsLinks doorsLinks, @NotNull List<String> deletedObjectIds) {
/* 245 */     TransferItem transferItem = loadTransferItem(doorsObject, moduleId, keys, parentId, position, doorsLinks);
/* 246 */     transferItems.add(transferItem);
/* 247 */     processChildren(doorsObject, moduleId, transferItems, keys, transferItem, doorsLinks, deletedObjectIds);
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private TransferItem loadTransferItem(@NotNull DoorsObject doorsObject, @NotNull String moduleId, @NotNull Collection<String> keys, @Nullable String parentId, int position, @NotNull DoorsLinks doorsLinks) {
/* 252 */     Map<String, DoorsAttribute> doorsAttributes = getDoorsAttributesByName(doorsObject, keys);
/*     */     
/* 254 */     String absoluteNumber = getAbsoluteNumberFromDoorsAttributes(doorsAttributes);
/*     */     
/* 256 */     String transferItemId = String.valueOf(moduleId) + "/" + absoluteNumber;
/*     */     
/* 258 */     TransferItem transferItem = new TransferItem(transferItemId);
/* 259 */     Collection<String> regularKeys = new ArrayList<>(keys);
/* 260 */     if (regularKeys.remove("type")) {
/* 261 */       boolean hasObjectText = (getValue("Object Text", doorsAttributes) != null);
/* 262 */       String tableType = getValue("TableType", doorsAttributes);
/* 263 */       boolean isTable = !(!"1".equals(tableType) && !"2".equals(tableType) && !"3".equals(tableType));
/* 264 */       if (hasObjectText || isTable) {
/* 265 */         transferItem.getValues().put("type", "requirement");
/*     */       } else {
/* 267 */         transferItem.getValues().put("type", "heading");
/*     */       } 
/*     */     } 
/* 270 */     if (regularKeys.remove("hierarchy")) {
/* 271 */       transferItem.getValues().put("hierarchy", new Hierarchy(parentId, position));
/*     */     }
/* 273 */     Collection<Attachment> embeddedObjects = new ArrayList<>();
/* 274 */     if (regularKeys.remove("attachments")) {
/* 275 */       transferItem.put("attachments", embeddedObjects);
/*     */     }
/*     */     
/* 278 */     if (regularKeys.remove("relations")) {
/*     */       
/* 280 */       ArrayList<Relation> relations = new ArrayList<>();
/* 281 */       for (String targetId : doorsLinks.getLinkTargets(absoluteNumber)) {
/* 282 */         relations.add(new Relation("relates_to", targetId));
/*     */       }
/* 284 */       transferItem.getValues().put("relations", relations);
/*     */     } 
/*     */     
/* 287 */     loadContent(regularKeys, transferItem, doorsAttributes, false, embeddedObjects);
/* 288 */     return transferItem;
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private String getAbsoluteNumberFromDoorsAttributes(@NotNull Map<String, DoorsAttribute> doorsAttributes) {
/* 293 */     String absoluteNumber = ((DoorsAttribute)doorsAttributes.get("Absolute Number")).getSingleAttributeValue();
/* 294 */     if (absoluteNumber == null) {
/* 295 */       throw new SynchronizationException("Failed to load absolute number for Doors object.");
/*     */     }
/* 297 */     return absoluteNumber;
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private Map<String, DoorsAttribute> getDoorsAttributesByName(@NotNull DoorsObject doorsObject, @NotNull Collection<String> keys) {
/* 302 */     Map<String, DoorsAttribute> doorsAttributes = (Map<String, DoorsAttribute>)doorsObject.getAttributes().stream()
/* 303 */       .filter(attribute -> !(!BASE_ATTRIBUTES.contains(attribute.getName()) && !paramCollection.contains(attribute.getName())))
/* 304 */       .collect(Collectors.toMap(attr -> attr.getName(), Function.identity(), (oldValue, newValue) -> {
/*     */             if (oldValue.equals(newValue)) {
/*     */               this.logger.debug(String.format("Got duplicate attribute from Doors: %s.", new Object[] { oldValue }));
/*     */             } else {
/*     */               this.logger.error(String.format("Got duplicate attribute from Doors: %s was replaced with %s.", new Object[] { oldValue, newValue }));
/*     */             } 
/*     */             return newValue;
/*     */           }));
/* 312 */     return doorsAttributes;
/*     */   }
/*     */   
/*     */   @Nullable
/*     */   private String getValue(@NotNull String key, @NotNull Map<String, DoorsAttribute> doorsAttributes) {
/* 317 */     DoorsAttribute attribute = doorsAttributes.get(key);
/* 318 */     return (attribute == null) ? null : attribute.getSingleAttributeValue();
/*     */   }
/*     */ 
/*     */   
/*     */   private void loadContent(@NotNull Collection<String> keys, @NotNull TransferItem transferItem, @NotNull Map<String, DoorsAttribute> doorsAttributes, boolean forDxl, @Nullable Collection<Attachment> embeddedObjects) {
/* 323 */     for (String key : keys) {
/* 324 */       FieldDefinition fieldDefinition = this.doorsProxyMetadataHelper.getFieldDefinition(key);
/*     */       
/* 326 */       DoorsAttribute doorsAttribute = doorsAttributes.get(key);
/* 327 */       if (fieldDefinition != null) {
/*     */         Object convertedValue;
/* 329 */         if (doorsAttribute == null) {
/* 330 */           if (this.doorsProxyMetadataHelper.getDefaultValue(key) != null) {
/* 331 */             convertedValue = this.doorsProxyMetadataHelper.getDefaultValue(key);
/*     */           } else {
/* 333 */             convertedValue = fieldDefinition.isMultiValued() ? Collections.EMPTY_LIST : null;
/*     */           } 
/* 335 */         } else if (fieldDefinition.isMultiValued()) {
/* 336 */           if (forDxl) {
/* 337 */             convertedValue = Arrays.asList(StringUtils.splitNoEmptyTrimmed(doorsAttribute.getSingleAttributeValue(), "(\r)?\n"));
/*     */           } else {
/* 339 */             convertedValue = this.doorsToPolarionConverter.loadMultiEnum(doorsAttribute.getAttributeValues(), new ArrayList<>(((OptionFieldDefinition)fieldDefinition).getAvailableOptions()));
/*     */           } 
/*     */         } else {
/* 342 */           convertedValue = this.doorsToPolarionConverter.processSingleValuedAttributes(doorsAttribute.getSingleAttributeValue(), fieldDefinition, forDxl, embeddedObjects);
/*     */         } 
/* 344 */         transferItem.getValues().put(key, convertedValue); continue;
/*     */       } 
/* 346 */       throw new SynchronizationException("Tried to load unknown attribute " + key);
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   private void processChildren(@NotNull DoorsObject doorsObject, @NotNull String moduleId, @NotNull Collection<TransferItem> transferItems, @NotNull Collection<String> keys, @NotNull TransferItem transferItem, @NotNull DoorsLinks doorsLinks, @NotNull List<String> deletedObjectIds) {
/* 352 */     List<DoorsObject> children = doorsObject.getChildren();
/*     */     
/* 354 */     if (children != null) {
/* 355 */       int childPosition = 0;
/* 356 */       for (DoorsObject childDoorsObject : children) {
/* 357 */         if (doorsObjectIsNotDeleted(childDoorsObject, deletedObjectIds)) {
/* 358 */           collectTransferItems(childDoorsObject, moduleId, transferItems, keys, transferItem.getId(), childPosition, doorsLinks, deletedObjectIds);
/* 359 */           childPosition++;
/*     */         } 
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean isHierarchySupported() {
/* 368 */     return true;
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public List<UpdateResult> update(@NotNull List<TransferItem> items) {
/* 374 */     List<UpdateResult> results = new ArrayList<>();
/* 375 */     for (TransferItem item : items) {
/*     */       try {
/* 377 */         UpdateResult result; String id = item.getId();
/*     */         
/* 379 */         if (id == null) {
/* 380 */           id = this.polarionToDoorsConverter.handleCreateItem(item, item.getHierarchy());
/* 381 */           CreateResult createResult = new CreateResult(id, null);
/*     */         } else {
/* 383 */           result = UpdateResult.success();
/*     */         } 
/* 385 */         String absoluteNumber = id.split("/")[1];
/* 386 */         updateItem(item, absoluteNumber, result);
/* 387 */         results.add(result);
/* 388 */       } catch (SynchronizationException e) {
/* 389 */         results.add(new UpdateResult(e.getMessage()));
/* 390 */         this.logger.debug("create/update of  item-" + item + "failed." + e.getMessage());
/*     */       } 
/*     */     } 
/* 393 */     this.module.execute((IDoorsCommand<?>)new DoorsCommandSave());
/* 394 */     this.module.execute((IDoorsCommand<?>)new DoorsCommandCloseModule());
/* 395 */     return results;
/*     */   }
/*     */   
/*     */   private void updateItem(@NotNull TransferItem item, @NotNull String absoluteNumber, @NotNull UpdateResult result) {
/*     */     Collection<Attachment> addedAttachments;
/* 400 */     CollectionUpdate<Attachment> attachmentUpdate = (CollectionUpdate<Attachment>)item.getValues().remove("attachments");
/* 401 */     if (attachmentUpdate != null) {
/* 402 */       Collection<String> missingRtfAttributes = (Collection<String>)updatedAttachmentsByAttribute(attachmentUpdate).filter(attribute -> !paramTransferItem.getValues().containsKey(attribute)).collect(Collectors.toList());
/*     */       try {
/* 404 */         addMissingRtfAttributes(absoluteNumber, missingRtfAttributes, item);
/* 405 */       } catch (Exception e) {
/* 406 */         String error = String.format("Failed to update attachments in rich-text attributes: %s", new Object[] { e.toString() });
/* 407 */         missingRtfAttributes.forEach(attribute -> paramUpdateResult.addError(attribute, paramString1));
/*     */       } 
/*     */ 
/*     */       
/* 411 */       addedAttachments = attachmentUpdate.getAdded();
/*     */     } else {
/* 413 */       addedAttachments = new ArrayList<>();
/*     */     } 
/* 415 */     for (Map.Entry<String, Object> pair : (Iterable<Map.Entry<String, Object>>)item.getValues().entrySet()) {
/* 416 */       String attributeName = pair.getKey();
/* 417 */       Hierarchy hierarchy = item.getHierarchy();
/* 418 */       if (hierarchy != null && attributeName.equals("hierarchy")) {
/* 419 */         checkAndUpdateResult(this.polarionToDoorsConverter.handleHierarchy(absoluteNumber, hierarchy), result, attributeName); continue;
/*     */       } 
/* 421 */       FieldDefinition fieldDefinition = this.doorsProxyMetadataHelper.getFieldDefinition(attributeName);
/* 422 */       Object attributeValue = pair.getValue();
/* 423 */       if (!attributeName.equals("type")) {
/* 424 */         if (handleMissingAttachments(absoluteNumber, fieldDefinition, attributeValue, addedAttachments)) {
/* 425 */           checkAndUpdateResult(this.polarionToDoorsConverter.handleStandardAttribute(absoluteNumber, attributeName, attributeValue, fieldDefinition, addedAttachments), result, attributeName); continue;
/*     */         } 
/* 427 */         result.addError(attributeName, "failed to load attachments from doors");
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   private void checkAndUpdateResult(@NotNull String dxlResult, @NotNull UpdateResult result, @NotNull String attributeName) {
/* 435 */     if (!dxlResult.equals("OK")) {
/* 436 */       result.addError(attributeName, dxlResult);
/*     */     }
/*     */   }
/*     */   
/*     */   private void addMissingRtfAttributes(@NotNull String absoluteNumber, @NotNull Collection<String> missingAttributes, @NotNull TransferItem item) {
/* 441 */     if (!missingAttributes.isEmpty()) {
/* 442 */       TransferItem loadedItem = loadSingleItem(absoluteNumber, missingAttributes)
/* 443 */         .<Throwable>orElseThrow(() -> new SynchronizationException("Doors object not found."));
/* 444 */       if (this.logger.isDebugEnabled()) {
/* 445 */         this.logger.debug("Loaded missing rich-text attributes for attachment update: " + loadedItem);
/*     */       }
/* 447 */       item.getValues().putAll(loadedItem.getValues());
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   private Stream<String> updatedAttachmentsByAttribute(@NotNull CollectionUpdate<Attachment> collectionUpdate) {
/* 454 */     if (collectionUpdate.getAdded() == null || collectionUpdate.getRemoved() == null) {
/* 455 */       return Stream.empty();
/*     */     }
/*     */     
/* 458 */     return collectionUpdate.getAdded().stream()
/* 459 */       .map(attachment -> attachment.getFileName())
/* 460 */       .filter(fileName -> paramCollectionUpdate.getRemoved().stream().filter(()).findFirst().isPresent())
/*     */       
/* 462 */       .flatMap(fileName -> {
/*     */           Matcher attributeMatcher = this.doorsAttributeNameFromPolarionAttachmentFileName.matcher(fileName);
/*     */           return attributeMatcher.matches() ? Stream.<String>of(attributeMatcher.group(1)) : Stream.empty();
/*     */         });
/*     */   }
/*     */   
/*     */   private boolean handleMissingAttachments(@NotNull String absoluteNumber, @Nullable FieldDefinition fieldDefinition, @Nullable Object attributeValue, @NotNull Collection<Attachment> attachments) {
/* 469 */     if (isTypeRichText(fieldDefinition) && attributeValue != null) {
/* 470 */       List<String> attachmentNamesFromUpdate = (List<String>)attachments.stream().map(Attachment::getFileName).collect(Collectors.toList());
/* 471 */       Collection<Attachment> attachmentsFromDoors = loadAndParseRtfFromDoors(absoluteNumber, ((FieldDefinition)ObjectUtils.notNull(fieldDefinition)).getKey());
/* 472 */       if (attachmentsFromDoors == null) {
/* 473 */         return false;
/*     */       }
/* 475 */       Stream<Attachment> filteredAttachmentsFromDoors = attachmentsFromDoors.stream().filter(attachment -> !paramList.contains(attachment.getFileName()));
/* 476 */       attachments.addAll(filteredAttachmentsFromDoors.collect((Collector)Collectors.toList()));
/*     */     } 
/* 478 */     return true;
/*     */   }
/*     */   
/*     */   private boolean isTypeRichText(@Nullable FieldDefinition fieldDefinition) {
/* 482 */     return (fieldDefinition != null && fieldDefinition.getType().equals("rich-text"));
/*     */   }
/*     */ 
/*     */   
/*     */   @Nullable
/*     */   private Collection<Attachment> loadAndParseRtfFromDoors(@NotNull String absoluteNumber, @NotNull String fieldKey) {
/* 488 */     Collection<String> keys = new ArrayList<>();
/* 489 */     keys.add(fieldKey);
/* 490 */     keys.add("attachments");
/*     */     
/* 492 */     return loadSingleItem(absoluteNumber, keys)
/* 493 */       .flatMap(transferItem -> Optional.ofNullable(transferItem.getValues().get("attachments")))
/* 494 */       .map(attachments -> (Collection)attachments)
/* 495 */       .orElse(null);
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private Optional<TransferItem> loadSingleItem(@NotNull String absoluteNumber, @NotNull Collection<String> keys) {
/* 500 */     List<DoorsPublication> publications = loadDoorsPublications(absoluteNumber);
/* 501 */     DoorsLinks doorsLinks = new DoorsLinks(Collections.emptyList());
/* 502 */     Optional<TransferItem> item = publications.stream()
/* 503 */       .flatMap(publication -> publication.getRequirements().stream()).findFirst()
/* 504 */       .map(doorsObject -> loadTransferItem(doorsObject, this.module.getModuleId(), paramCollection, null, -1, paramDoorsLinks));
/* 505 */     return item;
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private List<DoorsPublication> loadDoorsPublications(@NotNull String absoluteNumber) {
/* 510 */     return ((DoorsPublications)this.module.<DoorsPublications>execute((IDoorsCommand<DoorsPublications>)new DoorsCommandDataForObject(absoluteNumber))).getPublications();
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public List<UpdateResult> delete(@NotNull List<String> ids) {
/* 516 */     List<UpdateResult> results = new ArrayList<>();
/*     */     
/* 518 */     for (String id : ids) {
/* 519 */       String absoluteNumber = id.split("/")[1];
/* 520 */       String commandResult = this.module.<String>execute((IDoorsCommand<String>)new DoorsCommandDeleteObject(absoluteNumber), true);
/* 521 */       if (commandResult.equals("")) {
/* 522 */         results.add(UpdateResult.success()); continue;
/* 523 */       }  if (commandResult.equals("FAIL")) {
/* 524 */         results.add(new UpdateResult("Failed to delete object with id " + id));
/*     */       }
/*     */     } 
/* 527 */     this.module.execute((IDoorsCommand<?>)new DoorsCommandSave());
/* 528 */     this.module.execute((IDoorsCommand<?>)new DoorsCommandCloseModule());
/* 529 */     return results;
/*     */   }
/*     */ 
/*     */   
/*     */   @Nullable
/*     */   public String getTargetName() {
/* 535 */     return this.module.getModuleId();
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/internal/DoorsProxy.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */