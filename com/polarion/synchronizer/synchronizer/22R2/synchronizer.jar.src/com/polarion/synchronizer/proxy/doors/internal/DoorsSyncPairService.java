/*    */ package com.polarion.synchronizer.proxy.doors.internal;
/*    */ 
/*    */ import com.google.inject.Inject;
/*    */ import com.polarion.synchronizer.configuration.IConfigurationHelper;
/*    */ import com.polarion.synchronizer.configuration.IConnection;
/*    */ import com.polarion.synchronizer.configuration.ISyncPair;
/*    */ import com.polarion.synchronizer.internal.configuration.SyncConfiguration;
/*    */ import com.polarion.synchronizer.proxy.doors.DoorsConnectionConfiguration;
/*    */ import com.polarion.synchronizer.proxy.polarion.PolarionProxyConfiguration;
/*    */ import java.util.Collection;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class DoorsSyncPairService
/*    */   implements IDoorsSyncPairService
/*    */ {
/*    */   @NotNull
/*    */   private final IConfigurationHelper configurationHelper;
/*    */   @NotNull
/*    */   private ICommandProcessor commandProcessor;
/*    */   
/*    */   @Inject
/*    */   public DoorsSyncPairService(@NotNull IConfigurationHelper configurationHelper, @NotNull ICommandProcessor commandProcessor) {
/* 28 */     this.configurationHelper = configurationHelper;
/* 29 */     this.commandProcessor = commandProcessor;
/*    */   }
/*    */ 
/*    */   
/*    */   public void createAndUpdateSyncPairs(@NotNull String projectId, @NotNull Collection<ISyncPair> syncPairsToCreate, @NotNull Collection<ISyncPair> syncPairsToUpdate) {
/* 34 */     SyncConfiguration syncConfiguration = this.configurationHelper.loadConfiguration(projectId, false);
/* 35 */     boolean doorsConnectionExists = syncConfiguration.getConnections().values().stream().anyMatch(connection -> connection instanceof DoorsConnectionConfiguration);
/* 36 */     DoorsConnectionConfiguration doorsConnectionConfiguration = new DoorsConnectionConfiguration("connection-to-doors", "connection-to-doors", this.commandProcessor);
/*    */     
/* 38 */     if (!doorsConnectionExists) {
/* 39 */       this.configurationHelper.saveOrActivateConnection((IConnection)doorsConnectionConfiguration, projectId, false);
/*    */     }
/*    */     
/* 42 */     syncPairsToCreate.stream().forEach(syncPair -> {
/*    */           ((PolarionProxyConfiguration)syncPair.getLeft()).setAccountVaultKey("connector");
/*    */           syncPair.getRight().setConnection(paramIConnection);
/*    */           this.configurationHelper.create(syncPair, paramString);
/*    */         });
/* 47 */     syncPairsToUpdate.stream().forEach(syncPair -> {
/*    */           ((PolarionProxyConfiguration)syncPair.getLeft()).setAccountVaultKey("connector");
/*    */           syncPair.getRight().setConnection(paramIConnection);
/*    */           this.configurationHelper.update(syncPair, paramString, syncPair.getId());
/*    */         });
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/internal/DoorsSyncPairService.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */