/*    */ package com.polarion.synchronizer.internal;
/*    */ 
/*    */ import com.polarion.synchronizer.IConflictLog;
/*    */ import com.polarion.synchronizer.ILogger;
/*    */ import com.polarion.synchronizer.model.Side;
/*    */ 
/*    */ public class ConflictLog
/*    */   implements IConflictLog {
/*    */   private ILogger logger;
/*    */   private String currentLeftItemId;
/*    */   private String currentRightItemId;
/*    */   
/*    */   public ConflictLog(ILogger logger) {
/* 14 */     this.logger = logger;
/*    */   }
/*    */ 
/*    */   
/*    */   public void current(String leftItemId, String rightItemId) {
/* 19 */     this.currentLeftItemId = leftItemId;
/* 20 */     this.currentRightItemId = rightItemId;
/*    */   }
/*    */ 
/*    */   
/*    */   public void reportConflict(String fromField, String toField, Side fromSide) {
/* 25 */     if (this.currentLeftItemId == null || this.currentRightItemId == null) {
/* 26 */       throw new IllegalStateException("Don't know which item is processed.");
/*    */     }
/* 28 */     String fromId = (fromSide == Side.LEFT) ? this.currentLeftItemId : this.currentRightItemId;
/* 29 */     String toId = (fromSide == Side.RIGHT) ? this.currentLeftItemId : this.currentRightItemId;
/*    */     
/* 31 */     this.logger.warn(String.format("Conflict detected: Attribute '%s' of item '%s' was overwirtten with content of attribute '%s' of item '%s' to resolve conflict.", new Object[] {
/* 32 */             toField, toId, fromField, fromId
/*    */           }));
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/internal/ConflictLog.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */