/*    */ package com.polarion.synchronizer.internal.mapping;
/*    */ 
/*    */ import com.google.inject.Inject;
/*    */ import com.google.inject.assistedinject.Assisted;
/*    */ import com.polarion.synchronizer.IDependencyManager;
/*    */ import com.polarion.synchronizer.ISynchronizationContext;
/*    */ import com.polarion.synchronizer.mapping.ValueMapping;
/*    */ import com.polarion.synchronizer.model.Relation;
/*    */ import com.polarion.synchronizer.model.Side;
/*    */ import com.polarion.synchronizer.spi.translators.AbstractCollectionTranslator;
/*    */ import java.util.ArrayList;
/*    */ import java.util.Collection;
/*    */ import java.util.HashSet;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ import org.jetbrains.annotations.Nullable;
/*    */ 
/*    */ 
/*    */ public class RelationTranslator
/*    */   extends AbstractCollectionTranslator<Relation, Relation>
/*    */ {
/*    */   private final ISynchronizationContext context;
/*    */   
/*    */   @Inject
/*    */   public RelationTranslator(@Assisted Collection<ValueMapping> valueMappings, @Assisted Side fromSide, ISynchronizationContext context) {
/* 25 */     super(valueMappings, fromSide);
/* 26 */     this.context = context;
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   protected Collection<Relation> mapCollection(@Nullable Collection<Relation> sourceValue, @Nullable Collection<Relation> otherValue, boolean explode) {
/* 32 */     Collection<Relation> resultCollection = new HashSet<>();
/* 33 */     if (sourceValue != null) {
/* 34 */       for (Relation object : sourceValue) {
/*    */         
/* 36 */         Relation relation = object;
/* 37 */         String targetId = this.context.getConnectionMap().getTargetId(relation.getTargetId(), this.fromSide, true);
/* 38 */         if (targetId == null) {
/* 39 */           IDependencyManager dependencyManager = this.context.getDependencyManager();
/* 40 */           if (dependencyManager != null)
/* 41 */             dependencyManager.trackDependency(this.fromSide, relation.getTargetId()); 
/*    */           continue;
/*    */         } 
/* 44 */         if (explode) {
/* 45 */           Collection<String> matches = loadPotentialMatches(relation.getRole());
/* 46 */           if (matches.isEmpty()) {
/* 47 */             resultCollection.add(new Relation(relation.getRole(), targetId)); continue;
/*    */           } 
/* 49 */           for (String role : matches) {
/* 50 */             resultCollection.add(new Relation(role, targetId));
/*    */           }
/*    */           continue;
/*    */         } 
/* 54 */         Collection<String> otherRoles = new ArrayList<>(1);
/* 55 */         if (otherValue != null) {
/* 56 */           for (Relation otherRelationObj : otherValue) {
/* 57 */             Relation otherRelation = otherRelationObj;
/* 58 */             if (otherRelation.getTargetId().equals(targetId)) {
/* 59 */               otherRoles.add(otherRelation.getRole());
/*    */             }
/*    */           } 
/*    */         }
/*    */         
/* 64 */         String mappedRole = loadBestMatch(otherRoles, relation.getRole());
/* 65 */         resultCollection.add(new Relation(mappedRole, targetId));
/*    */       } 
/*    */     }
/*    */ 
/*    */ 
/*    */     
/* 71 */     return resultCollection;
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/internal/mapping/RelationTranslator.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */