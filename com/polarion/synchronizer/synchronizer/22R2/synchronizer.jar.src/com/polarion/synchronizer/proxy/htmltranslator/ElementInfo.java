/*    */ package com.polarion.synchronizer.proxy.htmltranslator;
/*    */ 
/*    */ import java.util.Map;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class ElementInfo
/*    */ {
/*    */   @NotNull
/*    */   private final Map<String, String> style;
/*    */   @NotNull
/*    */   private final String tagName;
/*    */   
/*    */   public ElementInfo(@NotNull Map<String, String> style, @NotNull String tagName) {
/* 36 */     this.style = style;
/* 37 */     this.tagName = tagName;
/*    */   }
/*    */   
/*    */   @NotNull
/*    */   public Map<String, String> getStyle() {
/* 42 */     return this.style;
/*    */   }
/*    */   
/*    */   @NotNull
/*    */   public String getTagName() {
/* 47 */     return this.tagName;
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/htmltranslator/ElementInfo.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */