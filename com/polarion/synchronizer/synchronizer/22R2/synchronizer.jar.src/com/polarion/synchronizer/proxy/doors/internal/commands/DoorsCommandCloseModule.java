/*    */ package com.polarion.synchronizer.proxy.doors.internal.commands;
/*    */ 
/*    */ import com.polarion.core.config.Configuration;
/*    */ import com.polarion.synchronizer.proxy.doors.internal.dxl.DxlScriptBuilder;
/*    */ import com.polarion.synchronizer.proxy.doors.internal.dxl.IDxlScript;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ 
/*    */ public class DoorsCommandCloseModule
/*    */   extends AbstractDoorsCommand<Boolean>
/*    */ {
/*    */   public DoorsCommandCloseModule() {
/* 12 */     super(Configuration.getInstance().connectors().getDoorsConnectionCheckTimeout() * 1000);
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   protected IDxlScript getScript() {
/* 18 */     return (IDxlScript)DxlScriptBuilder.script("Close.dxl");
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   protected Boolean processResult(@NotNull String result) throws Exception {
/* 24 */     return Boolean.valueOf(result.equals("OK"));
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/internal/commands/DoorsCommandCloseModule.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */