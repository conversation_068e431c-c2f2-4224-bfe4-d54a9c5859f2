/*     */ package com.polarion.synchronizer.spi;
/*     */ 
/*     */ import com.polarion.core.util.logging.Logger;
/*     */ import com.polarion.synchronizer.IProxyConfiguration;
/*     */ import com.polarion.synchronizer.configuration.IConnection;
/*     */ import com.polarion.synchronizer.internal.configuration.ConnectionPlaceholderAdapter;
/*     */ import java.lang.reflect.ParameterizedType;
/*     */ import java.lang.reflect.Type;
/*     */ import javax.xml.bind.annotation.XmlAttribute;
/*     */ import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ import org.jetbrains.annotations.Nullable;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public abstract class AbstractProxyConfiguration<T extends IConnection>
/*     */   implements IProxyConfiguration<T>
/*     */ {
/*  41 */   private static final Logger log = Logger.getLogger(AbstractProxyConfiguration.class);
/*     */ 
/*     */   
/*     */   private T connection;
/*     */ 
/*     */   
/*     */   @XmlAttribute
/*     */   @XmlJavaTypeAdapter(ConnectionPlaceholderAdapter.class)
/*     */   @Nullable
/*     */   public final T getConnection() {
/*  51 */     return this.connection;
/*     */   }
/*     */ 
/*     */   
/*     */   public final void setConnection(@Nullable T connection) {
/*  56 */     if (connection instanceof com.polarion.synchronizer.internal.configuration.ConnectionPlaceholder || connection == null) {
/*  57 */       this.connection = connection;
/*     */     } else {
/*     */       
/*  60 */       Class<? extends IConnection> connectionType = findConnectionType();
/*  61 */       if (connectionType.isAssignableFrom(connection.getClass())) {
/*  62 */         this.connection = connection;
/*     */       } else {
/*  64 */         this.connection = null;
/*  65 */         log.error("Invalid configuration: Tried to assign configuration type " + connection.getClass() + 
/*  66 */             " to proxy configuration that only accepts " + connectionType + ".");
/*     */       } 
/*     */     } 
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private Class<? extends IConnection> findConnectionType() {
/*  73 */     Class<? extends IConnection> connectionType = IConnection.class;
/*  74 */     Type genericSupertype = getClass().getGenericSuperclass();
/*  75 */     if (genericSupertype instanceof ParameterizedType) {
/*  76 */       ParameterizedType parameterizedType = (ParameterizedType)genericSupertype;
/*  77 */       Type[] actualTypeArguments = parameterizedType.getActualTypeArguments();
/*  78 */       if (actualTypeArguments.length == 1) {
/*  79 */         connectionType = (Class<? extends IConnection>)actualTypeArguments[0];
/*     */       }
/*     */     } 
/*  82 */     return connectionType;
/*     */   }
/*     */ 
/*     */   
/*     */   public int hashCode() {
/*  87 */     int prime = 31;
/*  88 */     int result = 1;
/*  89 */     result = 31 * result + ((this.connection == null) ? 0 : this.connection.hashCode());
/*  90 */     return result;
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean equals(Object obj) {
/*  95 */     if (this == obj) {
/*  96 */       return true;
/*     */     }
/*  98 */     if (obj == null) {
/*  99 */       return false;
/*     */     }
/* 101 */     if (getClass() != obj.getClass()) {
/* 102 */       return false;
/*     */     }
/* 104 */     AbstractProxyConfiguration other = (AbstractProxyConfiguration)obj;
/* 105 */     if (this.connection == null) {
/* 106 */       if (other.connection != null) {
/* 107 */         return false;
/*     */       }
/* 109 */     } else if (!this.connection.equals(other.connection)) {
/* 110 */       return false;
/*     */     } 
/* 112 */     return true;
/*     */   }
/*     */ 
/*     */   
/*     */   public String toString() {
/* 117 */     return "AbstractProxyConfiguration [connection=" + this.connection + "]";
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/spi/AbstractProxyConfiguration.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */