/*    */ package com.polarion.synchronizer.mapping;
/*    */ 
/*    */ import com.polarion.synchronizer.model.Side;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ import org.jetbrains.annotations.Nullable;
/*    */ 
/*    */ 
/*    */ public class ValueMapping
/*    */ {
/*    */   private final String left;
/*    */   private final String right;
/*    */   
/*    */   public ValueMapping(String left, String right) {
/* 14 */     this.left = left;
/* 15 */     this.right = right;
/*    */   }
/*    */   
/*    */   public String getLeft() {
/* 19 */     return this.left;
/*    */   }
/*    */   
/*    */   public String getRight() {
/* 23 */     return this.right;
/*    */   }
/*    */   
/*    */   @Nullable
/*    */   public String getFromValue(@NotNull Side fromSide) {
/* 28 */     return (fromSide == Side.LEFT) ? this.left : this.right;
/*    */   }
/*    */   
/*    */   @Nullable
/*    */   public String getToValue(@NotNull Side fromSide) {
/* 33 */     return (fromSide == Side.RIGHT) ? this.left : this.right;
/*    */   }
/*    */ 
/*    */   
/*    */   public int hashCode() {
/* 38 */     int prime = 31;
/* 39 */     int result = 1;
/* 40 */     result = 31 * result + ((this.left == null) ? 0 : this.left.hashCode());
/* 41 */     result = 31 * result + ((this.right == null) ? 0 : this.right.hashCode());
/* 42 */     return result;
/*    */   }
/*    */ 
/*    */   
/*    */   public boolean equals(Object obj) {
/* 47 */     if (this == obj) {
/* 48 */       return true;
/*    */     }
/* 50 */     if (obj == null) {
/* 51 */       return false;
/*    */     }
/* 53 */     if (getClass() != obj.getClass()) {
/* 54 */       return false;
/*    */     }
/* 56 */     ValueMapping other = (ValueMapping)obj;
/* 57 */     if (this.left == null) {
/* 58 */       if (other.left != null) {
/* 59 */         return false;
/*    */       }
/* 61 */     } else if (!this.left.equals(other.left)) {
/* 62 */       return false;
/*    */     } 
/* 64 */     if (this.right == null) {
/* 65 */       if (other.right != null) {
/* 66 */         return false;
/*    */       }
/* 68 */     } else if (!this.right.equals(other.right)) {
/* 69 */       return false;
/*    */     } 
/* 71 */     return true;
/*    */   }
/*    */ 
/*    */   
/*    */   public String toString() {
/* 76 */     return "[" + this.left + "<->" + this.right + "]";
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/mapping/ValueMapping.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */