/*    */ package com.polarion.synchronizer;
/*    */ 
/*    */ import com.polarion.platform.internal.ILicenseSecurityManager;
/*    */ import com.polarion.synchronizer.configuration.IConnection;
/*    */ import java.util.Optional;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ import org.jetbrains.annotations.Nullable;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class ProxyContribution<T extends IProxyConfiguration<U>, U extends IConnection>
/*    */ {
/*    */   @NotNull
/*    */   private String name;
/*    */   @NotNull
/*    */   private final Class<? extends T> configurationClass;
/*    */   @Nullable
/*    */   private final Class<? extends U> connectionClass;
/*    */   @Nullable
/*    */   private String uiContribution;
/*    */   @Nullable
/*    */   private String addonId;
/*    */   
/*    */   public ProxyContribution(@NotNull String name, @NotNull Class<? extends T> proxyConfigurationClass, @Nullable Class<? extends U> connectionConfigurationClass) {
/* 26 */     this(name, proxyConfigurationClass, connectionConfigurationClass, null);
/*    */   }
/*    */ 
/*    */   
/*    */   public ProxyContribution(@NotNull String name, @NotNull Class<? extends T> proxyConfigurationClass, @Nullable Class<? extends U> connectionConfigurationClass, @Nullable String uiContribution) {
/* 31 */     this.name = name;
/* 32 */     this.configurationClass = proxyConfigurationClass;
/* 33 */     this.connectionClass = connectionConfigurationClass;
/* 34 */     this.uiContribution = uiContribution;
/*    */   }
/*    */   @NotNull
/*    */   public ProxyContribution<T, U> requiresLicense(@NotNull String addonId) {
/* 38 */     this.addonId = addonId;
/* 39 */     return this;
/*    */   }
/*    */   @NotNull
/*    */   public ProxyContribution<T, U> withUi(@NotNull String uiContribution) {
/* 43 */     this.uiContribution = uiContribution;
/* 44 */     return this;
/*    */   }
/*    */   
/*    */   @NotNull
/*    */   public String getName() {
/* 49 */     return this.name;
/*    */   }
/*    */   
/*    */   @NotNull
/*    */   public Class<? extends T> getConfigurationClass() {
/* 54 */     return this.configurationClass;
/*    */   }
/*    */   
/*    */   @Nullable
/*    */   public Class<? extends U> getConnectionClass() {
/* 59 */     return this.connectionClass;
/*    */   }
/*    */   
/*    */   @Nullable
/*    */   public String getUiContribution() {
/* 64 */     return this.uiContribution;
/*    */   }
/*    */   
/*    */   public boolean isLicensed(@NotNull ILicenseSecurityManager licenseManager) {
/* 68 */     return ((Boolean)Optional.<String>ofNullable(this.addonId)
/* 69 */       .map(id -> Boolean.valueOf(paramILicenseSecurityManager.hasAddonLicense(id)))
/* 70 */       .orElse(Boolean.valueOf(true))).booleanValue();
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/ProxyContribution.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */