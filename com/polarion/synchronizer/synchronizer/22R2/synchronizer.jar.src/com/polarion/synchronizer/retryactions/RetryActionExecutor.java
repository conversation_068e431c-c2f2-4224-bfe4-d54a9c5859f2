/*    */ package com.polarion.synchronizer.retryactions;
/*    */ 
/*    */ import java.util.Optional;
/*    */ import java.util.function.Consumer;
/*    */ import java.util.function.Predicate;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class RetryActionExecutor<T>
/*    */ {
/*    */   @NotNull
/* 15 */   private IRetryActionStrategy retryStrategy = BackoffRetryActionStrategy.newBuilder().build();
/*    */   
/*    */   @NotNull
/*    */   private Predicate<T> failIfPredicate = p -> false;
/*    */   
/*    */   @NotNull
/*    */   private Consumer<Optional<T>> beforeRetryAction = result -> {
/*    */     
/*    */     };
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   public static <T> Builder newBuilder() {
/* 28 */     (new RetryActionExecutor()).getClass(); return new Builder();
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public class Builder
/*    */   {
/*    */     @NotNull
/*    */     public Builder with(@NotNull IRetryActionStrategy retryStrategy) {
/* 39 */       RetryActionExecutor.this.retryStrategy = retryStrategy;
/* 40 */       return this;
/*    */     }
/*    */     
/*    */     public Builder failIf(@NotNull Predicate<T> failIfPredicate) {
/* 44 */       RetryActionExecutor.this.failIfPredicate = failIfPredicate;
/* 45 */       return this;
/*    */     }
/*    */     
/*    */     public Builder beforeRetry(@NotNull Consumer<Optional<T>> beforeRetryAction) {
/* 49 */       RetryActionExecutor.this.beforeRetryAction = beforeRetryAction;
/* 50 */       return this;
/*    */     }
/*    */     
/*    */     @NotNull
/*    */     public RetryActionExecutor<T> build() {
/* 55 */       return RetryActionExecutor.this;
/*    */     }
/*    */   }
/*    */   
/*    */   @NotNull
/*    */   public T execute(@NotNull IRetryAction<T> retryAction) {
/* 61 */     return this.retryStrategy.<T>execute(retryAction, this.failIfPredicate, this.beforeRetryAction).orElseThrow();
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/retryactions/RetryActionExecutor.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */