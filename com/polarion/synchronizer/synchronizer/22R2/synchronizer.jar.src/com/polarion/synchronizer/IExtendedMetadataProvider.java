package com.polarion.synchronizer;

import com.polarion.synchronizer.model.Option;
import java.util.Collection;

public interface IExtendedMetadataProvider {
  Collection<Option> getAvailableOptions(String paramString1, String paramString2);
}


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/IExtendedMetadataProvider.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */