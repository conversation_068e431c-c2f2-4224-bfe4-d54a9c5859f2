/*    */ package com.polarion.synchronizer.proxy.doors.internal.commands;
/*    */ 
/*    */ import com.polarion.synchronizer.proxy.doors.ProjectNode;
/*    */ import com.polarion.synchronizer.proxy.doors.internal.dxl.DxlScriptBuilder;
/*    */ import com.polarion.synchronizer.proxy.doors.internal.dxl.IDxlScript;
/*    */ import java.io.StringReader;
/*    */ import javax.xml.bind.JAXB;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class LoadProjectContentCommand
/*    */   extends AbstractDoorsCommand<ProjectNode>
/*    */ {
/*    */   @NotNull
/*    */   private final String projectName;
/*    */   
/*    */   public LoadProjectContentCommand(@NotNull String projectName) {
/* 41 */     this.projectName = projectName;
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   protected ProjectNode processResult(@NotNull String result) throws Exception {
/* 47 */     return (ProjectNode)JAXB.unmarshal(new StringReader(result), ProjectNode.class);
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   public IDxlScript getScript() {
/* 53 */     return (IDxlScript)DxlScriptBuilder.script("LoadProjectStructureCommand.dxl").replaceParameter("project.name", this.projectName);
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/internal/commands/LoadProjectContentCommand.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */