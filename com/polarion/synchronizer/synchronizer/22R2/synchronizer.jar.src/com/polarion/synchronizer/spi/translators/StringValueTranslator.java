/*    */ package com.polarion.synchronizer.spi.translators;
/*    */ 
/*    */ import com.google.inject.assistedinject.Assisted;
/*    */ import com.polarion.synchronizer.mapping.ValueMapping;
/*    */ import com.polarion.synchronizer.model.Side;
/*    */ import java.util.Collection;
/*    */ import java.util.HashSet;
/*    */ import java.util.LinkedHashSet;
/*    */ import javax.inject.Inject;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ import org.jetbrains.annotations.Nullable;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class StringValueTranslator
/*    */   extends AbstractStringTranslator
/*    */ {
/*    */   private Side from;
/* 20 */   private Collection<ValueMapping> valueMappings = new HashSet<>();
/*    */   
/*    */   @Inject
/*    */   public StringValueTranslator(@Assisted Side from, @Assisted Collection<ValueMapping> valueMappings) {
/* 24 */     this.from = from;
/* 25 */     this.valueMappings = valueMappings;
/*    */   }
/*    */ 
/*    */   
/*    */   protected String mapValue(@Nullable String stringValue, @Nullable String targetValue) {
/* 30 */     stringValue = (stringValue == null) ? null : stringValue.trim();
/* 31 */     targetValue = (targetValue == null) ? null : targetValue.trim();
/* 32 */     String result = stringValue;
/* 33 */     Collection<String> potentialMatches = loadPotentialMatches(stringValue, this.from);
/* 34 */     if (potentialMatches.contains(targetValue)) {
/* 35 */       result = targetValue;
/* 36 */     } else if (potentialMatches.size() > 0) {
/* 37 */       result = potentialMatches.iterator().next();
/*    */     } 
/* 39 */     return result;
/*    */   }
/*    */   
/*    */   private Collection<String> loadPotentialMatches(@Nullable String stringValue, @NotNull Side from) {
/* 43 */     Collection<String> potentialMatch = new LinkedHashSet<>();
/* 44 */     for (ValueMapping valueMapping : this.valueMappings) {
/* 45 */       String match = (from == Side.LEFT) ? valueMapping.getLeft() : valueMapping.getRight();
/* 46 */       String replace = (from == Side.RIGHT) ? valueMapping.getLeft() : valueMapping.getRight();
/* 47 */       if (match.equals(stringValue)) {
/* 48 */         potentialMatch.add(replace);
/*    */       }
/*    */     } 
/* 51 */     return potentialMatch;
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/spi/translators/StringValueTranslator.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */