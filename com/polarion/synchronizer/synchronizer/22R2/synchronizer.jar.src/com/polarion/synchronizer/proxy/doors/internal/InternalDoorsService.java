package com.polarion.synchronizer.proxy.doors.internal;

import com.google.inject.ImplementedBy;
import com.polarion.synchronizer.proxy.doors.IDoorsConnection;
import com.polarion.synchronizer.proxy.doors.IDoorsService;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@ImplementedBy(DoorsService.class)
public interface InternalDoorsService extends IDoorsService {
  @NotNull
  InternalDoorsConnection createConnection();
  
  @NotNull
  InternalDoorsConnection getOrCreateConnection(@NotNull String paramString);
  
  @Nullable
  InternalDoorsConnection getConnection(@NotNull String paramString);
  
  void removeConnection(@NotNull String paramString);
}


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/internal/InternalDoorsService.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */