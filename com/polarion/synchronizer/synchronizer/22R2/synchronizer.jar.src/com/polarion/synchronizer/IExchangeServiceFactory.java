package com.polarion.synchronizer;

import com.polarion.synchronizer.proxy.doors.IDoorsConnection;
import org.jetbrains.annotations.NotNull;

public interface IExchangeServiceFactory {
  @NotNull
  IExchangeService create(@NotNull IDoorsConnection paramIDoorsConnection);
}


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/IExchangeServiceFactory.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */