/*     */ package com.polarion.synchronizer.proxy.doors.internal.tables;
/*     */ 
/*     */ import com.polarion.synchronizer.IConnectionMap;
/*     */ import com.polarion.synchronizer.ILogger;
/*     */ import com.polarion.synchronizer.model.Side;
/*     */ import com.polarion.synchronizer.model.TransferItem;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Collection;
/*     */ import java.util.HashMap;
/*     */ import java.util.HashSet;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class TableProcessor
/*     */ {
/*     */   private static final String TABLE = "<table id=\"polarion_wiki macro name=table\" class=\"polarion-Document-table\" style=\"width: 80%;margin-left: auto;margin-right: auto;border: 1px solid #CCCCCC;empty-cells: show;border-collapse: collapse;\">\n";
/*  25 */   private final String TH = "<th style=\"font-weight: bold;font-size: 9pt;background-color: #F0F0F0;line-height: 1.5;text-align: left;vertical-align: top;height: 12px;border: 1px solid #CCCCCC;padding: 5px;\">\n";
/*     */ 
/*     */   
/*  28 */   private final String TD = "<td style=\"font-size: 9pt;line-height: 1.5;text-align: left;vertical-align: top;height: 12px;border: 1px solid #CCCCCC;padding: 5px;\">\n";
/*     */   private final ILogger log;
/*     */   
/*     */   public final class Table implements ITable {
/*  32 */     private final Map<String, TableProcessor.Row> rowMap = new HashMap<>();
/*  33 */     private final List<TableProcessor.Row> rows = new ArrayList<>();
/*  34 */     private final Collection<String> containedItemIds = new HashSet<>();
/*     */     
/*     */     private final String id;
/*     */     
/*     */     public Table(String id) {
/*  39 */       this.id = id;
/*     */     }
/*     */ 
/*     */     
/*     */     @NotNull
/*     */     public String getId() {
/*  45 */       return this.id;
/*     */     }
/*     */     
/*     */     public List<TableProcessor.Row> getRows() {
/*  49 */       return this.rows;
/*     */     }
/*     */     
/*     */     public void addRow(String id, int position) {
/*  53 */       TableProcessor.Row row = new TableProcessor.Row(id, (position == 0), this);
/*  54 */       this.rowMap.put(id, row);
/*  55 */       TableProcessor.this.allRows.put(id, row);
/*  56 */       while (this.rows.size() <= position) {
/*  57 */         this.rows.add(null);
/*     */       }
/*  59 */       this.rows.set(position, row);
/*     */       
/*  61 */       String polarionId = TableProcessor.this.connectionMap.getTargetId(id, Side.RIGHT);
/*  62 */       if (polarionId == null) {
/*  63 */         TableProcessor.this.log.error("Failed to load Work Item ID for Doors table row " + id);
/*     */       } else {
/*  65 */         this.containedItemIds.add(polarionId);
/*     */       } 
/*     */     }
/*     */ 
/*     */     
/*     */     public String toString() {
/*  71 */       return "Table [rows=" + this.rows + "]";
/*     */     }
/*     */ 
/*     */     
/*     */     @NotNull
/*     */     public String toHtml() {
/*  77 */       StringBuilder builder = new StringBuilder();
/*  78 */       builder.append("<table id=\"polarion_wiki macro name=table\" class=\"polarion-Document-table\" style=\"width: 80%;margin-left: auto;margin-right: auto;border: 1px solid #CCCCCC;empty-cells: show;border-collapse: collapse;\">\n");
/*  79 */       builder.append("<tbody>\n");
/*  80 */       for (TableProcessor.Row row : this.rows) {
/*  81 */         if (row != null) {
/*  82 */           builder.append(row.toHtml()); continue;
/*     */         } 
/*  84 */         TableProcessor.this.log.error(String.format("No content was set for a row in table %s.", new Object[] { getId() }));
/*  85 */         builder.append("<tr/>");
/*     */       } 
/*     */ 
/*     */       
/*  89 */       builder.append("</tbody>\n");
/*  90 */       builder.append("</table>\n");
/*  91 */       return builder.toString();
/*     */     }
/*     */ 
/*     */     
/*     */     @NotNull
/*     */     public Collection<String> getContainedItemIds() {
/*  97 */       return this.containedItemIds;
/*     */     }
/*     */   }
/*     */ 
/*     */   
/*     */   public final class Row
/*     */   {
/* 104 */     private final List<TableProcessor.Cell> cells = new ArrayList<>();
/*     */     
/*     */     private final String id;
/*     */     
/*     */     private final boolean first;
/*     */     
/*     */     private final TableProcessor.Table table;
/*     */     
/*     */     private Row(String doorsId, boolean first, TableProcessor.Table table) {
/* 113 */       this.id = TableProcessor.this.connectionMap.getTargetId(doorsId, Side.RIGHT);
/* 114 */       this.first = first;
/* 115 */       this.table = table;
/*     */     }
/*     */     
/*     */     public String getId() {
/* 119 */       return this.id;
/*     */     }
/*     */     
/*     */     public List<TableProcessor.Cell> getCells() {
/* 123 */       return this.cells;
/*     */     }
/*     */     
/*     */     public void addCell(TableProcessor.Cell cell, int position) {
/* 127 */       this.table.containedItemIds.add(cell.polarionId);
/* 128 */       while (this.cells.size() <= position) {
/* 129 */         this.cells.add(null);
/*     */       }
/* 131 */       this.cells.set(position, cell);
/*     */     }
/*     */ 
/*     */ 
/*     */     
/*     */     public String toString() {
/* 137 */       return "Row [cells=" + this.cells + "]";
/*     */     }
/*     */     
/*     */     public String toHtml() {
/* 141 */       StringBuilder builder = new StringBuilder();
/* 142 */       builder.append("<tr>\n");
/* 143 */       for (TableProcessor.Cell cell : this.cells) {
/* 144 */         if (this.first) {
/* 145 */           builder.append("<th style=\"font-weight: bold;font-size: 9pt;background-color: #F0F0F0;line-height: 1.5;text-align: left;vertical-align: top;height: 12px;border: 1px solid #CCCCCC;padding: 5px;\">\n");
/*     */         } else {
/* 147 */           builder.append("<td style=\"font-size: 9pt;line-height: 1.5;text-align: left;vertical-align: top;height: 12px;border: 1px solid #CCCCCC;padding: 5px;\">\n");
/*     */         } 
/* 149 */         if (cell != null) {
/* 150 */           builder.append(cell.toHtml());
/*     */         } else {
/* 152 */           TableProcessor.this.log.error(String.format("Missing content for cell in row %s.", new Object[] { getId() }));
/*     */         } 
/* 154 */         if (this.first) {
/* 155 */           builder.append("</th>\n"); continue;
/*     */         } 
/* 157 */         builder.append("</td>\n");
/*     */       } 
/*     */       
/* 160 */       builder.append("</tr>\n");
/* 161 */       return builder.toString();
/*     */     }
/*     */   }
/*     */ 
/*     */   
/*     */   private final class Cell
/*     */   {
/*     */     private final String polarionId;
/*     */     private final String title;
/*     */     
/*     */     public Cell(String id, String title) {
/* 172 */       String polarionId = TableProcessor.this.connectionMap.getTargetId(id, Side.RIGHT);
/* 173 */       if (polarionId == null) {
/* 174 */         TableProcessor.this.log.error("Failed to load Work Item ID for Doors table cell " + id);
/* 175 */         polarionId = "unknown/unknown";
/*     */       } 
/* 177 */       this.polarionId = polarionId;
/* 178 */       this.title = title;
/*     */     }
/*     */     
/*     */     public String getProjectId() {
/* 182 */       return this.polarionId.split("/")[0];
/*     */     }
/*     */     
/*     */     public String getWorkItemId() {
/* 186 */       return this.polarionId.split("/")[1];
/*     */     }
/*     */     
/*     */     public String toHtml() {
/* 190 */       return String.format(
/* 191 */           "<a href=\"/polarion/#/project/%s/workitem?id=%s\" target=\"_top\" class=\"descriptionLink\">%s</a>\n", new Object[] {
/* 192 */             getProjectId(), getWorkItemId(), this.title
/*     */           });
/*     */     }
/*     */   }
/*     */ 
/*     */   
/* 198 */   private final Map<String, Table> tables = new HashMap<>();
/*     */   
/* 200 */   private final Map<String, Row> allRows = new HashMap<>();
/*     */   
/*     */   @NotNull
/*     */   private final IConnectionMap connectionMap;
/*     */   
/*     */   public TableProcessor(@NotNull IConnectionMap connectionMap, @NotNull ILogger log) {
/* 206 */     this.connectionMap = connectionMap;
/* 207 */     this.log = log;
/*     */   }
/*     */ 
/*     */   
/*     */   public Collection<ITable> loadTables(Collection<TransferItem> allItems) {
/* 212 */     Collection<TransferItem> cells = new ArrayList<>();
/*     */     
/* 214 */     Collection<TransferItem> rows = new ArrayList<>();
/*     */     
/* 216 */     for (TransferItem item : allItems) {
/* 217 */       String tableType = (String)item.getValue("TableType");
/* 218 */       if (tableType != null) {
/* 219 */         String str; switch ((str = tableType).hashCode()) { case -830787764: if (!str.equals("TableRow")) {
/*     */               continue;
/*     */             }
/*     */ 
/*     */             
/* 224 */             rows.add(item);case 14892959: if (!str.equals("TableBase"))
/*     */               continue;  this.tables.put(item.getId(), new Table(this.connectionMap.getTargetId(item.getId(), Side.RIGHT)));
/*     */           case 14926384: if (!str.equals("TableCell"))
/* 227 */               continue;  cells.add(item); }
/*     */ 
/*     */ 
/*     */       
/*     */       } 
/*     */     } 
/* 233 */     for (TransferItem rowItem : rows) {
/* 234 */       Table table = this.tables.get(rowItem.getHierarchy().getParentItemId());
/* 235 */       if (table != null) {
/* 236 */         table.addRow(rowItem.getId(), rowItem.getHierarchy().getPosition()); continue;
/*     */       } 
/* 238 */       this.log.error(String.format("Table with ID %s for row %s is not known.", new Object[] { rowItem.getHierarchy().getParentItemId(), rowItem.getId() }));
/*     */     } 
/*     */ 
/*     */     
/* 242 */     for (TransferItem cellItem : cells) {
/* 243 */       String parent = cellItem.getHierarchy().getParentItemId();
/* 244 */       Row row = this.allRows.get(parent);
/* 245 */       if (row != null) {
/* 246 */         Object content = cellItem.getValue("Object Text");
/* 247 */         row.addCell(new Cell(cellItem.getId(), (content == null) ? "" : content.toString()), cellItem.getHierarchy().getPosition()); continue;
/*     */       } 
/* 249 */       this.log.error(String.format("Row with ID %s for cell %s is not known.", new Object[] { parent, cellItem.getId() }));
/*     */     } 
/*     */ 
/*     */     
/* 253 */     Collection<ITable> result = new ArrayList<>(this.tables.size());
/* 254 */     for (Map.Entry<String, Table> entry : this.tables.entrySet()) {
/* 255 */       result.add(entry.getValue());
/*     */     }
/*     */     
/* 258 */     return result;
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/internal/tables/TableProcessor.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */