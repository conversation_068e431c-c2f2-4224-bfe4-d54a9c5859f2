void processItems(Item item) {
  string itemType = item type
  bool isModule = itemType == "Formal"
  bool include = itemType == "Project" || itemType == "Folder" || isModule
  if(include) {
    result = result "<item id=\"" (item qualifiedUniqueID) "\" name=\"" (item name) "\""
    if(isModule) {
      result = result " isModule=\"true\""
    }
    result = result ">"
    Folder f = folder(item)
    if(f != null) {
      Item subItem
      for subItem in f do {
        processItems(subItem)
      }
    }
    result = result "</item>"
  }
}
processItems(item("/%project.name%"))