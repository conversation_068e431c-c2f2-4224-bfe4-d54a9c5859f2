/*     */ package com.polarion.synchronizer;
/*     */ 
/*     */ import com.google.inject.Inject;
/*     */ import com.polarion.alm.projects.model.IFolderManager;
/*     */ import com.polarion.alm.projects.model.IProject;
/*     */ import com.polarion.alm.tracker.model.ILinkRoleOpt;
/*     */ import com.polarion.alm.tracker.model.IModule;
/*     */ import com.polarion.alm.tracker.model.ITrackerProject;
/*     */ import com.polarion.alm.tracker.model.IWorkItem;
/*     */ import com.polarion.core.util.ObjectUtils;
/*     */ import com.polarion.core.util.types.Text;
/*     */ import com.polarion.platform.i18n.Localization;
/*     */ import com.polarion.platform.jobs.GenericJobException;
/*     */ import com.polarion.platform.jobs.IJob;
/*     */ import com.polarion.platform.jobs.IJobStateListener;
/*     */ import com.polarion.platform.jobs.JobState;
/*     */ import com.polarion.subterra.base.data.identification.ContextId;
/*     */ import com.polarion.subterra.base.data.identification.IContextId;
/*     */ import com.polarion.subterra.base.location.Location;
/*     */ import com.polarion.synchronizer.configuration.FieldMappingGroupConfiguration;
/*     */ import com.polarion.synchronizer.configuration.ISyncPair;
/*     */ import com.polarion.synchronizer.configuration.MappingConfiguration;
/*     */ import com.polarion.synchronizer.internal.configuration.FieldMappingConfiguration;
/*     */ import com.polarion.synchronizer.internal.configuration.SyncPair;
/*     */ import com.polarion.synchronizer.internal.configuration.ValueMappingConfiguration;
/*     */ import com.polarion.synchronizer.model.Direction;
/*     */ import com.polarion.synchronizer.proxy.configuration.FieldMapping;
/*     */ import com.polarion.synchronizer.proxy.configuration.IConfigurationService;
/*     */ import com.polarion.synchronizer.proxy.configuration.MappingGroup;
/*     */ import com.polarion.synchronizer.proxy.configuration.SpecificationConfiguration;
/*     */ import com.polarion.synchronizer.proxy.configuration.ValueMapping;
/*     */ import com.polarion.synchronizer.proxy.polarion.FlagModifiedWorkItemsAction;
/*     */ import com.polarion.synchronizer.proxy.polarion.PolarionProxyConfiguration;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Collection;
/*     */ import java.util.Set;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ import org.jetbrains.annotations.Nullable;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public abstract class AbstractExchangeService
/*     */   implements IExchangeService
/*     */ {
/*     */   @NotNull
/*     */   protected final ICommonServices commonServices;
/*     */   @NotNull
/*     */   private final IConfigurationService configurationService;
/*     */   @Nullable
/*     */   private Collection<IPostProcessingActionFactory> importPostProcessingFactories;
/*     */   
/*     */   public AbstractExchangeService(@NotNull ICommonServices commonServices, @NotNull IConfigurationService configurationService) {
/*  76 */     this.commonServices = commonServices;
/*  77 */     this.configurationService = configurationService;
/*     */   }
/*     */   
/*     */   @Inject
/*     */   public void setImportPostProcessingFactories(@NotNull Set<IPostProcessingActionFactory> postProcessingFactories) {
/*  82 */     this.importPostProcessingFactories = postProcessingFactories;
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public String startImport(@NotNull Collection<SpecificationConfiguration> configurations, @NotNull String projectId) {
/*     */     try {
/*  89 */       IJob job = createImportJob(configurations, projectId);
/*     */       
/*  91 */       job.schedule();
/*  92 */       return job.getId();
/*  93 */     } catch (GenericJobException e) {
/*  94 */       throw new SynchronizationException("Failed to start import: " + e.getLocalizedMessage(), e);
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public IJob createImportJob(@NotNull Collection<SpecificationConfiguration> configurations, @NotNull String projectId) throws GenericJobException {
/* 101 */     Collection<ISyncPair> syncPairs = loadSyncPairs(configurations, false);
/* 102 */     ISyncJobUnit syncJobUnit = (ISyncJobUnit)this.commonServices.getJobService().getJobUnitRepository().getJobUnitFactory("synchronizer")
/* 103 */       .createJobUnit(getImportJobName(configurations));
/* 104 */     syncJobUnit.setConfiguration(syncPairs);
/* 105 */     syncJobUnit.setScope(this.commonServices.getContextService().getContextforId(getContextId(projectId)));
/* 106 */     IJob job = this.commonServices.getJobService().getJobManager().spawnJob(syncJobUnit, null);
/* 107 */     job.addJobStateListener(new IJobStateListener()
/*     */         {
/*     */           public void stateChanged(IJob job, JobState state)
/*     */           {
/* 111 */             if (state == JobState.STATE_FINISHED || state == JobState.STATE_ABORTED) {
/* 112 */               AbstractExchangeService.this.onImportFinished();
/*     */             }
/*     */           }
/*     */         });
/* 116 */     return job;
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   protected String loadDocumentList(@NotNull Collection<SpecificationConfiguration> configurations) {
/* 121 */     StringBuilder documents = new StringBuilder();
/* 122 */     String commaSpace = Localization.getString("definition.commaSpace");
/* 123 */     for (SpecificationConfiguration configuration : configurations) {
/* 124 */       PolarionProxyConfiguration polarionConfiguration = configuration.getPolarionConfiguration();
/* 125 */       documents.append(polarionConfiguration.getSpace());
/* 126 */       documents.append(Localization.getString("definition.slash"));
/* 127 */       documents.append(polarionConfiguration.getDocument());
/* 128 */       documents.append(commaSpace);
/*     */     } 
/* 130 */     String documentList = documents.substring(0, documents.length() - commaSpace.length());
/* 131 */     return documentList;
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   protected abstract String getImportJobName(@NotNull Collection<SpecificationConfiguration> paramCollection);
/*     */   
/*     */   @NotNull
/*     */   protected abstract IProxyConfiguration getProxyConfiguration(@NotNull SpecificationConfiguration paramSpecificationConfiguration);
/*     */   
/*     */   protected void onImportFinished() {}
/*     */   
/*     */   @NotNull
/*     */   protected Collection<ISyncPair> loadSyncPairs(@NotNull Collection<SpecificationConfiguration> configurations, boolean export) {
/* 144 */     Collection<ISyncPair> syncPairs = new ArrayList<>();
/* 145 */     for (SpecificationConfiguration configuration : configurations) {
/* 146 */       syncPairs.add(createSyncPair(configuration, export));
/*     */     }
/* 148 */     return syncPairs;
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   private ISyncPair createSyncPair(@NotNull SpecificationConfiguration configuration, boolean export) {
/* 154 */     Direction direction = export ? Direction.LEFT_TO_RIGHT : Direction.RIGHT_TO_LEFT;
/*     */     
/* 156 */     PolarionProxyConfiguration polarionConfiguration = convertPolarionConfiguration(configuration);
/*     */     
/* 158 */     SyncPair syncPair = new SyncPair((String)ObjectUtils.notNull(configuration.getReqIfConfiguration().getSpecificationId()), (IProxyConfiguration)polarionConfiguration, getProxyConfiguration(configuration));
/*     */     
/* 160 */     FieldMappingGroupConfiguration defaultMappingGroup = new FieldMappingGroupConfiguration();
/*     */     
/* 162 */     MappingConfiguration targetMappingConfiguration = new MappingConfiguration(defaultMappingGroup);
/*     */     
/* 164 */     FieldMappingConfiguration typeMapping = new FieldMappingConfiguration("type", "type", direction);
/* 165 */     defaultMappingGroup.getFieldMappings().add(typeMapping);
/* 166 */     FieldMappingConfiguration attachmentMapping = new FieldMappingConfiguration("attachments", "attachments", direction);
/* 167 */     defaultMappingGroup.getFieldMappings().add(attachmentMapping);
/*     */     
/* 169 */     for (MappingGroup mappingGroup : configuration.getMappingConfiguration().getMappingGroups()) {
/* 170 */       if (export == mappingGroup.getDirection().isExport()) {
/* 171 */         typeMapping.getValueMappings().add(new ValueMappingConfiguration(mappingGroup.getPolarionType(), mappingGroup.getReqIfType()));
/* 172 */         targetMappingConfiguration.getFieldMappingGroups().add(convertFieldMappingGroup(mappingGroup, direction));
/*     */       } 
/*     */     } 
/*     */     
/* 176 */     targetMappingConfiguration.setHierarchyDirection((export && !configuration.isExportNew()) ? null : direction);
/* 177 */     syncPair.setMapping(targetMappingConfiguration);
/* 178 */     syncPair.setNewItemDirection((export && !configuration.isExportNew()) ? null : direction);
/* 179 */     syncPair.setLeftDeleteAction(configuration.getPolarionDeleteAction());
/* 180 */     if (export && configuration.isExportNew()) {
/* 181 */       syncPair.setDeleteDirection(Direction.LEFT_TO_RIGHT);
/* 182 */       syncPair.setReAddMissingOnRight(true);
/* 183 */       syncPair.setDeleteOutOfScope(true);
/*     */     } 
/*     */     
/* 186 */     if (!export) {
/* 187 */       Collection<IPostProcessingAction> postProcessingActions = new ArrayList<>();
/* 188 */       if (configuration.getFlagModifiedWorkItemsField() != null) {
/* 189 */         postProcessingActions.add(new FlagModifiedWorkItemsAction(configuration.getFlagModifiedWorkItemsField(), configuration.getFlagModifiedWorkItemsIgnoreFields()));
/*     */       }
/* 191 */       if (this.importPostProcessingFactories != null) {
/* 192 */         for (IPostProcessingActionFactory factory : this.importPostProcessingFactories) {
/* 193 */           postProcessingActions.addAll(factory.createActions((ISyncPair)syncPair));
/*     */         }
/*     */       }
/*     */       
/* 197 */       syncPair.setPostProcessingActions(postProcessingActions);
/*     */     } 
/*     */     
/* 200 */     return (ISyncPair)syncPair;
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private PolarionProxyConfiguration convertPolarionConfiguration(@NotNull SpecificationConfiguration configuration) {
/* 205 */     PolarionProxyConfiguration polarionConfiguration = configuration.getPolarionConfiguration();
/* 206 */     polarionConfiguration.setCanDefineLayouts(true);
/* 207 */     polarionConfiguration.setIgnoreStructureLinks(true);
/* 208 */     polarionConfiguration.setUseCurrentUser(true);
/* 209 */     polarionConfiguration.setDeleteOnlyRichTextReferencedAttachments(true);
/* 210 */     return polarionConfiguration;
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private FieldMappingGroupConfiguration convertFieldMappingGroup(@NotNull MappingGroup mappingGroup, @NotNull Direction direction) {
/* 215 */     FieldMappingGroupConfiguration fieldMappingGroupConfiguration = new FieldMappingGroupConfiguration(mappingGroup.getPolarionType(), mappingGroup.getReqIfType());
/* 216 */     for (FieldMapping fieldMapping : mappingGroup.getFieldMappings()) {
/* 217 */       FieldMappingConfiguration fieldMappingConfiguration = convertFieldMapping(fieldMapping, direction);
/* 218 */       if (fieldMappingConfiguration != null) {
/* 219 */         fieldMappingGroupConfiguration.getFieldMappings().add(fieldMappingConfiguration);
/*     */       }
/*     */     } 
/* 222 */     return fieldMappingGroupConfiguration;
/*     */   }
/*     */   
/*     */   @Nullable
/*     */   private FieldMappingConfiguration convertFieldMapping(@NotNull FieldMapping fieldMapping, @NotNull Direction direction) {
/* 227 */     FieldMappingConfiguration fieldMappingConfiguration = null;
/* 228 */     String polarionField = fieldMapping.getPolarionField();
/* 229 */     if (polarionField != null) {
/* 230 */       fieldMappingConfiguration = new FieldMappingConfiguration(polarionField, fieldMapping.getReqIfAttribute(), direction);
/* 231 */       Collection<ValueMapping> valueMappings = fieldMapping.getValueMappings();
/* 232 */       if (valueMappings != null) {
/* 233 */         for (ValueMapping valueMapping : valueMappings) {
/* 234 */           fieldMappingConfiguration.getValueMappings().add(
/* 235 */               new ValueMappingConfiguration(valueMapping.getPolarionValue(), valueMapping.getReqIfValue()));
/*     */         }
/*     */       }
/*     */     } 
/* 239 */     return fieldMappingConfiguration;
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   protected IContextId getContextId(@NotNull String projectId) {
/* 244 */     return ContextId.getContextIdFromClusterAndContext("default", projectId);
/*     */   }
/*     */ 
/*     */   
/*     */   public void createPolarionDocument(@NotNull String projectId, @NotNull String space, @NotNull String documentName, @Nullable String documentTypeId) {
/* 249 */     createPolarionDocument(projectId, space, documentName, documentName, documentTypeId);
/*     */   }
/*     */ 
/*     */   
/*     */   public void createPolarionDocument(@NotNull String projectId, @NotNull String space, @NotNull String documentName, @NotNull String documentTitle, @Nullable String documentTypeId) {
/* 254 */     ITrackerProject trackerProject = this.commonServices.getTrackerService().getTrackerProject(projectId);
/*     */     
/* 256 */     createNotExistingSpace(space, projectId);
/*     */     
/* 258 */     IModule document = this.commonServices.getTrackerService().getModuleManager().createModule(
/* 259 */         (IProject)trackerProject, Location.getLocation(space), documentName, 
/* 260 */         null, (ILinkRoleOpt)trackerProject.getWorkItemLinkRoleEnum().wrapOption("parent"), false);
/*     */     
/* 262 */     if (documentTypeId != null) {
/* 263 */       document.setEnumerationValue("type", documentTypeId);
/*     */     }
/* 265 */     document.setHomePageContent(Text.html(""));
/* 266 */     document.setTitle(documentTitle);
/* 267 */     document.save();
/*     */     
/* 269 */     IWorkItem title = document.createWorkItem(document.getHeadingWorkItemType().getId());
/* 270 */     title.setTitle(documentName);
/* 271 */     title.save();
/*     */     
/* 273 */     document.save();
/*     */   }
/*     */   
/*     */   private void createNotExistingSpace(@NotNull String space, @NotNull String projectId) {
/* 277 */     IFolderManager folderManager = this.commonServices.getTrackerService().getFolderManager();
/* 278 */     if (!folderManager.existFolder(projectId, space)) {
/* 279 */       folderManager.createFolder(projectId, space, null);
/*     */     }
/*     */   }
/*     */ 
/*     */   
/*     */   @Nullable
/*     */   public SpecificationConfiguration loadConfiguration(@NotNull String projectId, @NotNull String id) {
/* 286 */     return this.configurationService.loadConfiguration(projectId, id);
/*     */   }
/*     */ 
/*     */   
/*     */   public void saveConfiguration(@NotNull String projectId, @NotNull String id, @NotNull SpecificationConfiguration configuration) {
/* 291 */     this.configurationService.saveConfiguration(projectId, id, configuration);
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/AbstractExchangeService.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */