package com.polarion.synchronizer;

import com.polarion.synchronizer.model.IProxy;
import com.polarion.synchronizer.model.IProxyMetadata;
import java.util.Collection;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public interface IMetadataService {
  @NotNull
  IProxy createTemporaryProxy(@Nullable String paramString, @NotNull IProxyConfiguration paramIProxyConfiguration);
  
  @NotNull
  IProxyMetadata loadMetadata(@Nullable String paramString, @NotNull IProxyConfiguration paramIProxyConfiguration);
  
  @NotNull
  IProxyMetadata loadMetadata(@Nullable String paramString, @NotNull IProxyConfiguration paramIProxyConfiguration, @Nullable Collection<String> paramCollection);
}


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/IMetadataService.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */