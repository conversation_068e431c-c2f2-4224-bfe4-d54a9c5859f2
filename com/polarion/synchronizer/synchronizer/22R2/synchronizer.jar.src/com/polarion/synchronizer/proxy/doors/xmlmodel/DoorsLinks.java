/*     */ package com.polarion.synchronizer.proxy.doors.xmlmodel;
/*     */ 
/*     */ import com.polarion.core.util.ObjectUtils;
/*     */ import java.util.Collection;
/*     */ import java.util.Collections;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import java.util.stream.Collectors;
/*     */ import javax.xml.bind.annotation.XmlAccessType;
/*     */ import javax.xml.bind.annotation.XmlAccessorType;
/*     */ import javax.xml.bind.annotation.XmlAttribute;
/*     */ import javax.xml.bind.annotation.XmlElement;
/*     */ import javax.xml.bind.annotation.XmlRootElement;
/*     */ import javax.xml.bind.annotation.XmlTransient;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ import org.jetbrains.annotations.Nullable;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @XmlAccessorType(XmlAccessType.FIELD)
/*     */ @XmlRootElement(name = "links")
/*     */ public class DoorsLinks
/*     */ {
/*     */   @XmlElement(name = "object")
/*     */   @Nullable
/*     */   private Collection<Object> objects;
/*     */   @XmlTransient
/*     */   @Nullable
/*     */   private Map<String, Collection<String>> idToLinkMap;
/*     */   
/*     */   @XmlAccessorType(XmlAccessType.FIELD)
/*     */   public static class Object
/*     */   {
/*     */     @XmlAttribute
/*     */     private String id;
/*     */     @XmlElement(name = "link")
/*     */     @Nullable
/*     */     private Collection<DoorsLinks.Link> links;
/*     */     
/*     */     @Deprecated
/*     */     public Object() {}
/*     */     
/*     */     public Object(@NotNull String id, @NotNull Collection<DoorsLinks.Link> links) {
/*  61 */       this.id = id;
/*  62 */       this.links = links;
/*     */     }
/*     */   }
/*     */ 
/*     */   
/*     */   @XmlAccessorType(XmlAccessType.FIELD)
/*     */   public static class Link
/*     */   {
/*     */     @XmlAttribute
/*     */     @Nullable
/*     */     public String id;
/*     */ 
/*     */     
/*     */     @Deprecated
/*     */     public Link() {}
/*     */     
/*     */     public Link(@NotNull String id) {
/*  79 */       this.id = id;
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public DoorsLinks() {}
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public DoorsLinks(@NotNull Collection<Object> objects) {
/*  98 */     this.objects = objects;
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private Map<String, Collection<String>> getIdToLinkMap() {
/* 103 */     if (this.idToLinkMap == null) {
/* 104 */       this.idToLinkMap = new HashMap<>();
/* 105 */       if (this.objects != null) {
/* 106 */         this.objects.forEach(object -> {
/*     */               Collection<Link> links = object.links;
/*     */               if (links != null) {
/*     */                 ((Map<String, Collection>)ObjectUtils.notNull(this.idToLinkMap)).put(object.id, (Collection)links.stream().map(()).collect(Collectors.toList()));
/*     */               }
/*     */             });
/*     */       }
/*     */     } 
/* 114 */     return (Map<String, Collection<String>>)ObjectUtils.notNull(this.idToLinkMap);
/*     */   }
/*     */   
/*     */   public Collection<String> getLinkTargets(String sourceId) {
/* 118 */     return getIdToLinkMap().getOrDefault(sourceId, Collections.emptyList());
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/xmlmodel/DoorsLinks.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */