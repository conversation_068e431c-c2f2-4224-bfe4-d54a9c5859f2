package com.polarion.synchronizer.model;

import com.polarion.synchronizer.mapping.FieldType;
import java.util.Collection;
import java.util.Map;
import org.jetbrains.annotations.NotNull;

public interface IProxyMetadata {
  @NotNull
  Collection<Option> getTypes();
  
  boolean isHierarchySupported();
  
  @NotNull
  Collection<FieldDefinition> getCommonFields();
  
  @NotNull
  Map<String, Collection<FieldDefinition>> getFieldDefinitions();
  
  @NotNull
  Map<FieldType, Collection<FieldType>> getCompatibleFieldsFrom();
}


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/model/IProxyMetadata.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */