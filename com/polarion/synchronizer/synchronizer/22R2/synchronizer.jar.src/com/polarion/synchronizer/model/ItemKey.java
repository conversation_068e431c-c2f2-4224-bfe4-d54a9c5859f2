/*    */ package com.polarion.synchronizer.model;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class ItemKey
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 8680441580874082821L;
/*    */   public final String id;
/*    */   public final boolean isForeign;
/*    */   
/*    */   public ItemKey(String id, boolean isForeign) {
/* 13 */     this.id = id;
/* 14 */     this.isForeign = isForeign;
/*    */   }
/*    */ 
/*    */   
/*    */   public int hashCode() {
/* 19 */     int prime = 31;
/* 20 */     int result = 1;
/* 21 */     result = 31 * result + ((this.id == null) ? 0 : this.id.hashCode());
/* 22 */     result = 31 * result + (this.isForeign ? 1231 : 1237);
/* 23 */     return result;
/*    */   }
/*    */ 
/*    */   
/*    */   public boolean equals(Object obj) {
/* 28 */     if (this == obj) {
/* 29 */       return true;
/*    */     }
/* 31 */     if (obj == null) {
/* 32 */       return false;
/*    */     }
/* 34 */     if (getClass() != obj.getClass()) {
/* 35 */       return false;
/*    */     }
/* 37 */     ItemKey other = (ItemKey)obj;
/* 38 */     if (this.id == null) {
/* 39 */       if (other.id != null) {
/* 40 */         return false;
/*    */       }
/* 42 */     } else if (!this.id.equals(other.id)) {
/* 43 */       return false;
/*    */     } 
/* 45 */     if (this.isForeign != other.isForeign) {
/* 46 */       return false;
/*    */     }
/* 48 */     return true;
/*    */   }
/*    */ 
/*    */   
/*    */   public String toString() {
/* 53 */     return String.valueOf(this.id) + (this.isForeign ? " (foreign)" : "");
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/model/ItemKey.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */