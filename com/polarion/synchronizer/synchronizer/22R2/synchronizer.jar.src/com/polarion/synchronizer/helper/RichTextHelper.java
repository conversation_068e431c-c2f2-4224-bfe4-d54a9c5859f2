/*     */ package com.polarion.synchronizer.helper;
/*     */ 
/*     */ import com.polarion.core.util.types.Text;
/*     */ import com.polarion.core.util.xml.HTMLHelper;
/*     */ import com.polarion.synchronizer.spi.translators.RichTextUtils;
/*     */ import javax.xml.transform.Templates;
/*     */ import javax.xml.transform.TransformerConfigurationException;
/*     */ import javax.xml.transform.TransformerException;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ import org.jetbrains.annotations.Nullable;
/*     */ import org.w3c.dom.Document;
/*     */ import org.w3c.dom.Element;
/*     */ import org.w3c.dom.Node;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class RichTextHelper
/*     */ {
/*     */   @NotNull
/*     */   private Templates templates;
/*     */   
/*     */   public RichTextHelper() {
/*     */     try {
/*  31 */       Exception exception2, exception1 = null;
/*     */ 
/*     */     
/*     */     }
/*  35 */     catch (TransformerException|java.io.IOException e) {
/*  36 */       throw new RuntimeException(e);
/*     */     } 
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   public String sortAttributes(@NotNull String html) {
/*  42 */     Document document = HTMLHelper.parseHTMLAsDOM(html, false);
/*  43 */     return sortAttributes(document);
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   public String sortAttributes(@NotNull Document document) {
/*     */     Text text;
/*     */     try {
/*  50 */       text = HTMLHelper.writeBodyContentToText(document, this.templates.newTransformer());
/*  51 */     } catch (TransformerConfigurationException e) {
/*  52 */       throw new RuntimeException(e);
/*     */     } 
/*  54 */     return text.getContent().trim();
/*     */   }
/*     */   
/*     */   public static void alignImageStyleAttributes(@Nullable Element element) {
/*  58 */     if (element != null) {
/*  59 */       Node child = element.getFirstChild();
/*  60 */       while (child != null) {
/*  61 */         if (child.getNodeType() == 1) {
/*  62 */           if (child.getNodeName().equalsIgnoreCase("img")) {
/*  63 */             normalizeStyle(child);
/*  64 */             normalizeSrc(child);
/*     */           } 
/*  66 */           alignImageStyleAttributes((Element)child);
/*     */         } 
/*  68 */         child = child.getNextSibling();
/*     */       } 
/*     */     } 
/*     */   }
/*     */   
/*     */   private static void normalizeSrc(@NotNull Node child) {
/*  74 */     Node src = child.getAttributes().getNamedItem("src");
/*  75 */     if (src != null) {
/*  76 */       String fileName = RichTextUtils.nameFromUrl(src.getNodeValue());
/*  77 */       if (fileName != null) {
/*  78 */         src.setNodeValue("attachment:" + HTMLHelper.encodeURLPart(fileName));
/*     */       }
/*     */     } 
/*     */   }
/*     */   
/*     */   private static void normalizeStyle(@NotNull Node child) {
/*  84 */     Node style = child.getAttributes().getNamedItem("style");
/*  85 */     if (style != null) {
/*  86 */       String aligned = alignImageStyleAttribute(style.getNodeValue());
/*  87 */       if (!aligned.isEmpty() && !aligned.contains("height")) {
/*  88 */         aligned = addMissingHeight(aligned, child);
/*     */       }
/*  90 */       style.setNodeValue(aligned);
/*     */     } 
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private static String addMissingHeight(@NotNull String aligned, @NotNull Node child) {
/*  96 */     Node heightAttribute = child.getAttributes().getNamedItem("height");
/*  97 */     if (heightAttribute != null) {
/*  98 */       String height = heightAttribute.getNodeValue();
/*  99 */       return String.valueOf(aligned) + "height: " + height + "px;";
/*     */     } 
/* 101 */     return aligned;
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private static String alignImageStyleAttribute(@NotNull String style) {
/* 106 */     StringBuffer result = new StringBuffer(); byte b; int i; String[] arrayOfString;
/* 107 */     for (i = (arrayOfString = style.split(";")).length, b = 0; b < i; ) { String cssDeclaration = arrayOfString[b];
/* 108 */       String trimmedCssDeclaration = cssDeclaration.trim();
/* 109 */       if (!trimmedCssDeclaration.isEmpty())
/* 110 */         if (trimmedCssDeclaration.contains(":")) {
/* 111 */           String[] split = trimmedCssDeclaration.split(":", 2);
/* 112 */           result.append(String.valueOf(split[0].trim()) + ": " + split[1].trim() + ";");
/*     */         } else {
/* 114 */           result.append(String.valueOf(trimmedCssDeclaration) + ";");
/*     */         }  
/*     */       b++; }
/*     */     
/* 118 */     return result.toString();
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   public static String cleanupImageAttributes(@NotNull String input) {
/* 123 */     return removeAttribute(removeAttribute(input, "title"), "data-lastmodified");
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private static String removeAttribute(@NotNull String input, @NotNull String attribute) {
/* 128 */     return input.replaceAll("(<img[^>]*) " + attribute + "=\"[^\"]*\"", "$1");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public String normalizeForPolarion(@NotNull String richText) {
/* 137 */     Document document = HTMLHelper.parseHTMLAsDOM(richText);
/* 138 */     alignImageStyleAttributes(document.getDocumentElement());
/* 139 */     return sortAttributes(document);
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/helper/RichTextHelper.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */