/*     */ package com.polarion.synchronizer.proxy.doors;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.Collection;
/*     */ import javax.xml.bind.annotation.XmlAccessType;
/*     */ import javax.xml.bind.annotation.XmlAccessorType;
/*     */ import javax.xml.bind.annotation.XmlAttribute;
/*     */ import javax.xml.bind.annotation.XmlElement;
/*     */ import javax.xml.bind.annotation.XmlRootElement;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @XmlAccessorType(XmlAccessType.FIELD)
/*     */ @XmlRootElement(name = "item")
/*     */ public class ProjectNode
/*     */ {
/*     */   @XmlAttribute
/*     */   private String id;
/*     */   @XmlAttribute
/*     */   private String name;
/*     */   @XmlAttribute
/*     */   private boolean isModule;
/*     */   @XmlElement(name = "item")
/*     */   @NotNull
/*  52 */   private final Collection<ProjectNode> childNodes = new ArrayList<>();
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public ProjectNode() {}
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public static ProjectNode folder(@NotNull String id, @NotNull String name) {
/*  61 */     return new ProjectNode(id, name, false);
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   public static ProjectNode module(@NotNull String id, @NotNull String name) {
/*  66 */     return new ProjectNode(id, name, true);
/*     */   }
/*     */   
/*     */   public ProjectNode(@NotNull String id, @NotNull String name, boolean isModule) {
/*  70 */     this.id = id;
/*  71 */     this.name = name;
/*  72 */     this.isModule = isModule;
/*     */   }
/*     */   
/*     */   public String getId() {
/*  76 */     return this.id;
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   public String getName() {
/*  81 */     return this.name;
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   public Collection<ProjectNode> getChildNodes() {
/*  86 */     return this.childNodes;
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   public ProjectNode add(@NotNull ProjectNode node) {
/*  91 */     getChildNodes().add(node);
/*  92 */     return this;
/*     */   }
/*     */   
/*     */   public boolean isModule() {
/*  96 */     return this.isModule;
/*     */   }
/*     */ 
/*     */   
/*     */   public int hashCode() {
/* 101 */     int prime = 31;
/* 102 */     int result = 1;
/* 103 */     result = 31 * result + this.childNodes.hashCode();
/* 104 */     result = 31 * result + ((this.id == null) ? 0 : this.id.hashCode());
/* 105 */     result = 31 * result + (this.isModule ? 1231 : 1237);
/* 106 */     result = 31 * result + ((this.name == null) ? 0 : this.name.hashCode());
/* 107 */     return result;
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean equals(Object obj) {
/* 112 */     if (this == obj) {
/* 113 */       return true;
/*     */     }
/* 115 */     if (obj == null) {
/* 116 */       return false;
/*     */     }
/* 118 */     if (getClass() != obj.getClass()) {
/* 119 */       return false;
/*     */     }
/* 121 */     ProjectNode other = (ProjectNode)obj;
/* 122 */     if (!this.childNodes.equals(other.childNodes)) {
/* 123 */       return false;
/*     */     }
/* 125 */     if (this.id == null) {
/* 126 */       if (other.id != null) {
/* 127 */         return false;
/*     */       }
/* 129 */     } else if (!this.id.equals(other.id)) {
/* 130 */       return false;
/*     */     } 
/* 132 */     if (this.isModule != other.isModule) {
/* 133 */       return false;
/*     */     }
/* 135 */     if (this.name == null) {
/* 136 */       if (other.name != null) {
/* 137 */         return false;
/*     */       }
/* 139 */     } else if (!this.name.equals(other.name)) {
/* 140 */       return false;
/*     */     } 
/* 142 */     return true;
/*     */   }
/*     */ 
/*     */   
/*     */   public String toString() {
/* 147 */     return "ProjectNode [id=" + this.id + ", name=" + this.name + ", isModule=" + this.isModule + "]\n\t" + this.childNodes;
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/ProjectNode.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */