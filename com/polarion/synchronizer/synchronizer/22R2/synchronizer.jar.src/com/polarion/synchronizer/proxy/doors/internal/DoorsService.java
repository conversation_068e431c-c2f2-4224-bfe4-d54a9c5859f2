/*    */ package com.polarion.synchronizer.proxy.doors.internal;
/*    */ 
/*    */ import com.google.inject.Inject;
/*    */ import com.google.inject.Singleton;
/*    */ import com.polarion.core.util.UUID;
/*    */ import com.polarion.synchronizer.IExchangeServiceFactory;
/*    */ import com.polarion.synchronizer.IMetadataService;
/*    */ import com.polarion.synchronizer.proxy.doors.IDoorsConnection;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ import org.jetbrains.annotations.Nullable;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @Singleton
/*    */ public class DoorsService
/*    */   implements InternalDoorsService
/*    */ {
/*    */   @NotNull
/* 40 */   private Map<String, DoorsConnection> connections = new HashMap<>();
/*    */   
/*    */   @NotNull
/*    */   private final ICommandProcessor commandProcessor;
/*    */   
/*    */   @NotNull
/*    */   private final IMetadataService metadataService;
/*    */   
/*    */   @NotNull
/*    */   private final IExchangeServiceFactory exchangeServiceFactory;
/*    */   
/*    */   @Inject
/*    */   public DoorsService(@NotNull ICommandProcessor commandProcessor, @NotNull IMetadataService metadataService, @NotNull IExchangeServiceFactory exchangeServiceFactory) {
/* 53 */     this.commandProcessor = commandProcessor;
/* 54 */     this.metadataService = metadataService;
/* 55 */     this.exchangeServiceFactory = exchangeServiceFactory;
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   public synchronized InternalDoorsConnection createConnection() {
/* 61 */     DoorsConnection connection = new DoorsConnection(UUID.nextUUID(), this.commandProcessor, this, this.metadataService, this.exchangeServiceFactory);
/* 62 */     this.connections.put(connection.getId(), connection);
/* 63 */     return connection;
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   public synchronized InternalDoorsConnection getOrCreateConnection(@NotNull String id) {
/* 69 */     DoorsConnection connection = this.connections.get(id);
/* 70 */     if (connection == null) {
/* 71 */       connection = new DoorsConnection(id, this.commandProcessor, this, this.metadataService, this.exchangeServiceFactory);
/* 72 */       this.connections.put(id, connection);
/*    */     } 
/* 74 */     return connection;
/*    */   }
/*    */ 
/*    */   
/*    */   @Nullable
/*    */   public synchronized InternalDoorsConnection getConnection(@NotNull String id) {
/* 80 */     return this.connections.get(id);
/*    */   }
/*    */ 
/*    */   
/*    */   public synchronized void removeConnection(@NotNull String connectionId) {
/* 85 */     this.connections.remove(connectionId);
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/internal/DoorsService.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */