/*     */ package com.polarion.synchronizer.proxy.doors;
/*     */ 
/*     */ import com.fasterxml.jackson.annotation.JsonIgnore;
/*     */ import com.polarion.core.util.ObjectUtils;
/*     */ import com.polarion.synchronizer.SynchronizationException;
/*     */ import com.polarion.synchronizer.spi.AbstractProxyConfiguration;
/*     */ import javax.xml.bind.annotation.XmlAccessType;
/*     */ import javax.xml.bind.annotation.XmlAccessorType;
/*     */ import javax.xml.bind.annotation.XmlAttribute;
/*     */ import javax.xml.bind.annotation.XmlTransient;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ import org.jetbrains.annotations.Nullable;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @XmlAccessorType(XmlAccessType.FIELD)
/*     */ public class DoorsProxyConfiguration
/*     */   extends AbstractProxyConfiguration<DoorsConnectionConfiguration>
/*     */ {
/*     */   @XmlAttribute
/*     */   @Nullable
/*     */   private String moduleId;
/*     */   @XmlAttribute
/*     */   @Nullable
/*     */   private String typeAttribute;
/*     */   @XmlTransient
/*     */   @Nullable
/*     */   private String remoteConnectionId;
/*     */   private static final String PROXY_ID = "DOORS";
/*     */   
/*     */   @Deprecated
/*     */   public DoorsProxyConfiguration() {}
/*     */   
/*     */   public DoorsProxyConfiguration(@NotNull String remoteConnectionId, @NotNull String moduleId) {
/*  60 */     this.remoteConnectionId = remoteConnectionId;
/*  61 */     this.moduleId = moduleId;
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private DoorsConnectionConfiguration getDoorsConnection() {
/*  66 */     DoorsConnectionConfiguration connection = (DoorsConnectionConfiguration)getConnection();
/*  67 */     if (connection == null) {
/*  68 */       throw new SynchronizationException("Missing Doors connection!");
/*     */     }
/*  70 */     return connection;
/*     */   }
/*     */ 
/*     */   
/*     */   @JsonIgnore
/*     */   @NotNull
/*     */   public String getRemoteConnectionId() {
/*  77 */     String remoteConnectionId = this.remoteConnectionId;
/*  78 */     return (remoteConnectionId == null) ? getDoorsConnection().getRemoteConnectionId() : remoteConnectionId;
/*     */   }
/*     */   
/*     */   public String getModuleId() {
/*  82 */     return this.moduleId;
/*     */   }
/*     */ 
/*     */   
/*     */   @XmlTransient
/*     */   @Nullable
/*     */   public String getModuleIdFromURL() {
/*  89 */     if (this.moduleId != null && this.moduleId.startsWith("doors://")) {
/*  90 */       return parseDoorsUrl((String)ObjectUtils.notNull(this.moduleId));
/*     */     }
/*  92 */     return this.moduleId;
/*     */   }
/*     */   
/*     */   public void setModuleId(@NotNull String moduleId) {
/*  96 */     this.moduleId = moduleId;
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private String parseDoorsUrl(@NotNull String moduleId) {
/* 101 */     String[] moduleIdParts = moduleId.split("::");
/* 102 */     if (moduleIdParts.length == 2) {
/* 103 */       String idPart = moduleIdParts[1];
/* 104 */       return idPart.replaceAll("(^\\w-{1})", "").replaceAll("-\\w-", "-");
/*     */     } 
/* 106 */     return moduleId;
/*     */   }
/*     */   
/*     */   @Nullable
/*     */   public String getTypeAttribute() {
/* 111 */     return this.typeAttribute;
/*     */   }
/*     */   
/*     */   public void setTypeAttribute(@Nullable String typeAttribute) {
/* 115 */     this.typeAttribute = typeAttribute;
/*     */   }
/*     */ 
/*     */   
/*     */   public String getSystemIdentifier() {
/* 120 */     return "DOORS";
/*     */   }
/*     */   
/*     */   @Nullable
/*     */   public String checkConfiguration() {
/* 125 */     if (this.moduleId == null) {
/* 126 */       return "Missing Module ID";
/*     */     }
/* 128 */     return null;
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/DoorsProxyConfiguration.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */