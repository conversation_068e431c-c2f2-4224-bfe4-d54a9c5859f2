/*    */ package com.polarion.synchronizer.model;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ import java.util.ArrayList;
/*    */ import java.util.Arrays;
/*    */ import java.util.List;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class TestSteps
/*    */   implements Serializable
/*    */ {
/*    */   protected static final long serialVersionUID = -26738649950541441L;
/*    */   protected List<TestStep> testSteps;
/*    */   
/*    */   public TestSteps(TestStep... steps) {
/* 35 */     this.testSteps = new ArrayList<>(Arrays.asList(steps));
/*    */   }
/*    */   
/*    */   public List<TestStep> getTestSteps() {
/* 39 */     return this.testSteps;
/*    */   }
/*    */   
/*    */   public List<String> getKeys() {
/* 43 */     if (!this.testSteps.isEmpty()) {
/* 44 */       TestStep step = this.testSteps.get(0);
/* 45 */       return step.getKeys();
/*    */     } 
/* 47 */     return null;
/*    */   }
/*    */   
/*    */   public void addTestStep(TestStep step) {
/* 51 */     this.testSteps.add(step);
/*    */   }
/*    */   
/*    */   public void replaceKey(String oldKey, String newKey) {
/* 55 */     if (!getKeys().contains(oldKey)) {
/*    */       return;
/*    */     }
/* 58 */     for (TestStep step : this.testSteps) {
/* 59 */       step.replaceKey(oldKey, newKey);
/*    */     }
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public String toString() {
/* 66 */     return "TestSteps [testSteps=" + this.testSteps + "]";
/*    */   }
/*    */ 
/*    */   
/*    */   public int hashCode() {
/* 71 */     int prime = 31;
/* 72 */     int result = 1;
/* 73 */     result = 31 * result + ((this.testSteps == null) ? 0 : this.testSteps.hashCode());
/* 74 */     return result;
/*    */   }
/*    */ 
/*    */   
/*    */   public boolean equals(Object obj) {
/* 79 */     if (this == obj) {
/* 80 */       return true;
/*    */     }
/* 82 */     if (obj == null) {
/* 83 */       return false;
/*    */     }
/* 85 */     if (!(obj instanceof TestSteps)) {
/* 86 */       return false;
/*    */     }
/* 88 */     TestSteps other = (TestSteps)obj;
/* 89 */     if (this.testSteps == null) {
/* 90 */       if (other.testSteps != null) {
/* 91 */         return false;
/*    */       }
/* 93 */     } else if (!this.testSteps.equals(other.testSteps)) {
/* 94 */       return false;
/*    */     } 
/* 96 */     return true;
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/model/TestSteps.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */