/*    */ package com.polarion.synchronizer.model;
/*    */ 
/*    */ import java.util.Collection;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class OptionFieldDefinition
/*    */   extends FieldDefinition
/*    */ {
/*    */   private final Collection<Option> availableOptions;
/*    */   
/*    */   public OptionFieldDefinition(String key, String label, String type, boolean readOnly, boolean multiValued, Collection<Option> availableOptions) {
/* 34 */     super(key, label, type, readOnly, multiValued);
/* 35 */     this.availableOptions = availableOptions;
/*    */   }
/*    */   
/*    */   public Collection<Option> getAvailableOptions() {
/* 39 */     return this.availableOptions;
/*    */   }
/*    */ 
/*    */   
/*    */   public String toString() {
/* 44 */     return "OptionFieldDefinition [availableOptions=" + this.availableOptions + ", toString()=" + super.toString() + "]";
/*    */   }
/*    */ 
/*    */   
/*    */   public int hashCode() {
/* 49 */     int prime = 31;
/* 50 */     int result = super.hashCode();
/* 51 */     result = 31 * result + ((this.availableOptions == null) ? 0 : this.availableOptions.hashCode());
/* 52 */     return result;
/*    */   }
/*    */ 
/*    */   
/*    */   public boolean equals(Object obj) {
/* 57 */     if (!equalsIgnoreOptions(obj)) {
/* 58 */       return false;
/*    */     }
/* 60 */     OptionFieldDefinition other = (OptionFieldDefinition)obj;
/* 61 */     if (this.availableOptions == null) {
/* 62 */       if (other.availableOptions != null) {
/* 63 */         return false;
/*    */       }
/* 65 */     } else if (!this.availableOptions.equals(other.availableOptions)) {
/* 66 */       return false;
/*    */     } 
/* 68 */     return true;
/*    */   }
/*    */   
/*    */   public boolean equalsIgnoreOptions(Object obj) {
/* 72 */     if (this == obj) {
/* 73 */       return true;
/*    */     }
/* 75 */     if (!super.equals(obj)) {
/* 76 */       return false;
/*    */     }
/* 78 */     return true;
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/model/OptionFieldDefinition.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */