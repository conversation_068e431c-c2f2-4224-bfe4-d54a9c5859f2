/*    */ package com.polarion.synchronizer.proxy.doors.internal.commands;
/*    */ 
/*    */ import com.polarion.synchronizer.proxy.doors.internal.dxl.DxlScriptBuilder;
/*    */ import com.polarion.synchronizer.proxy.doors.internal.dxl.IDxlScript;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class DoorsCommandCheckModule
/*    */   extends AbstractDoorsCommand<Boolean>
/*    */ {
/*    */   @NotNull
/*    */   protected IDxlScript getScript() {
/* 34 */     return (IDxlScript)DxlScriptBuilder.script("checkModule.dxl");
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   protected Boolean processResult(@NotNull String result) throws Exception {
/* 40 */     return Boolean.valueOf(result.equals("OK"));
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/internal/commands/DoorsCommandCheckModule.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */