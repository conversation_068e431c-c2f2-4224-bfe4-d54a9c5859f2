/*    */ package com.polarion.synchronizer.proxy.doors.html2rtftranslator;
/*    */ 
/*    */ import com.polarion.synchronizer.proxy.htmltranslator.ElementInfo;
/*    */ import com.polarion.synchronizer.proxy.htmltranslator.StyleTag;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class TextDecorationStyleTag
/*    */   implements StyleTag
/*    */ {
/*    */   public static final String KEY = "text-decoration";
/*    */   
/*    */   public void open(@NotNull ElementInfo elementInfo, @NotNull String text, @NotNull StringBuilder sb) {
/* 35 */     if (!text.isEmpty()) {
/* 36 */       String textDecoration = (String)elementInfo.getStyle().get("text-decoration");
/* 37 */       if ("underline".equals(textDecoration)) {
/* 38 */         sb.append("\\ul ");
/* 39 */       } else if ("line-through".equals(textDecoration)) {
/* 40 */         sb.append("\\strike ");
/* 41 */       } else if (textDecoration.contains("line-through") && textDecoration.contains("underline")) {
/* 42 */         sb.append("\\ul \\strike ");
/*    */       } 
/*    */     } 
/*    */   }
/*    */ 
/*    */   
/*    */   public void close(@NotNull ElementInfo elementInfo, @NotNull String text, @NotNull StringBuilder sb) {
/* 49 */     if (!text.isEmpty()) {
/* 50 */       String textDecoration = (String)elementInfo.getStyle().get("text-decoration");
/* 51 */       if ("underline".equals(textDecoration)) {
/* 52 */         sb.append("\\ulnone ");
/* 53 */       } else if ("line-through".equals(textDecoration)) {
/* 54 */         sb.append("\\strike0 ");
/* 55 */       } else if (textDecoration.contains("line-through") && textDecoration.contains("underline")) {
/* 56 */         sb.append("\\strike0 \\ulnone ");
/*    */       } 
/*    */     } 
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/html2rtftranslator/TextDecorationStyleTag.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */