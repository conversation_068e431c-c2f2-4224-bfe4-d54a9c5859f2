/*     */ package com.polarion.synchronizer.proxy.polarion;
/*     */ 
/*     */ import com.polarion.synchronizer.IPostProcessingAction;
/*     */ import com.polarion.synchronizer.ISynchronizationTask;
/*     */ import com.polarion.synchronizer.IUpdateOperation;
/*     */ import com.polarion.synchronizer.model.FieldDefinition;
/*     */ import com.polarion.synchronizer.model.IProxy;
/*     */ import com.polarion.synchronizer.model.Option;
/*     */ import com.polarion.synchronizer.model.TransferItem;
/*     */ import com.polarion.synchronizer.model.UpdateResult;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Collection;
/*     */ import java.util.Collections;
/*     */ import java.util.Date;
/*     */ import java.util.HashSet;
/*     */ import java.util.List;
/*     */ import java.util.Set;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ import org.jetbrains.annotations.Nullable;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class FlagModifiedWorkItemsAction
/*     */   implements IPostProcessingAction
/*     */ {
/*     */   private final String flagModifiedWorkItemsField;
/*     */   private final Collection<String> flagModifiedWorkItemsIgnoreFields;
/*     */   
/*     */   public FlagModifiedWorkItemsAction(@NotNull String flagModifiedWorkItemsField, @Nullable Collection<String> flagModifiedWorkItemsIgnoreFields) {
/*  52 */     this.flagModifiedWorkItemsField = flagModifiedWorkItemsField;
/*  53 */     this.flagModifiedWorkItemsIgnoreFields = flagModifiedWorkItemsIgnoreFields;
/*     */   }
/*     */ 
/*     */   
/*     */   public void execute(@NotNull ISynchronizationTask executedTask) {
/*  58 */     Object valueForFlagField = getValueForFlagField(executedTask.getLeftProxy());
/*     */     
/*  60 */     List<TransferItem> itemsToUpdate = new ArrayList<>();
/*  61 */     for (IUpdateOperation update : executedTask.getLeftResult().getUpdates()) {
/*  62 */       if (update.getResult().isFailed()) {
/*     */         continue;
/*     */       }
/*  65 */       TransferItem content = update.getContent();
/*  66 */       if (this.flagModifiedWorkItemsIgnoreFields != null && this.flagModifiedWorkItemsIgnoreFields.containsAll(content.getValues().keySet())) {
/*     */         continue;
/*     */       }
/*     */       
/*  70 */       TransferItem updateFlagFieldItem = new TransferItem((content.getKey()).id);
/*  71 */       updateFlagFieldItem.put(this.flagModifiedWorkItemsField, valueForFlagField);
/*  72 */       itemsToUpdate.add(updateFlagFieldItem);
/*     */     } 
/*     */     
/*  75 */     for (UpdateResult result : executedTask.getLeftProxy().update(itemsToUpdate)) {
/*  76 */       if (result.hasError()) {
/*  77 */         executedTask.getLogger().error(result);
/*     */       }
/*     */     } 
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private Object getValueForFlagField(@NotNull IProxy proxy) {
/*  84 */     Set<FieldDefinition> fieldDefinitions = new HashSet<>();
/*  85 */     for (Option type : proxy.getDefinedTypes()) {
/*  86 */       fieldDefinitions.addAll(proxy.getDefinedFields(type.getId()));
/*     */     }
/*     */     
/*  89 */     for (FieldDefinition fd : fieldDefinitions) {
/*  90 */       if (fd.getKey().equals(this.flagModifiedWorkItemsField)) {
/*  91 */         if (fd.getType().equals("boolean"))
/*  92 */           return Boolean.valueOf(true); 
/*  93 */         if (fd.getType().equals("date-time"))
/*  94 */           return new Date(); 
/*  95 */         if (fd.getType().equals("date")) {
/*  96 */           return new Date();
/*     */         }
/*  98 */         throw new RuntimeException(String.format("Field [%s] with type [%s] is not supported as flag field", new Object[] { this.flagModifiedWorkItemsField, fd.getType() }));
/*     */       } 
/*     */     } 
/*     */     
/* 102 */     throw new RuntimeException(String.format("Undefined flag field [%s]", new Object[] { this.flagModifiedWorkItemsField }));
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public Collection<String> getAdditionalKeysLeft(@NotNull IProxy proxy) {
/* 108 */     return Collections.EMPTY_LIST;
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public Collection<String> getAdditionalKeysRight(@NotNull IProxy proxy) {
/* 114 */     return Collections.EMPTY_LIST;
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/polarion/FlagModifiedWorkItemsAction.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */