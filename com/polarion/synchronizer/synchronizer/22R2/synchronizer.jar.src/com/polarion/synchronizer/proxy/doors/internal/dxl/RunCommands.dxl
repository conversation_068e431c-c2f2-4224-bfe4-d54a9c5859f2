pragma runLim,0
OleAutoArgs args = create
OleAutoArgs args2 = create
OleAutoObj http = oleCreateAutoObject("WinHttp.WinHttpRequest.5.1")
void sendResult(string commandId, string result) {
	clear(args)
	clear(args2)
	put(args, "POST")
	put(args, "%polarion.base.url%/polarion/doorsconnector/rest/connections/%connection.id%/commands/" commandId "/result")
	OleAutoArgs args2 = create
	put(args2, result)
	oleMethod(http, "open", args)
	string res = oleMethod(http, "send", args2)
	if(!null(res)){
				print "\n" stringOf(dateAndTime(today)) ": Sending data data Failed"
				print "\n" res
	}
}
Regexp lines = regexp ".*"
bool connected = true
while(connected) {
	clear(args)
	put(args, "GET")
	put(args, "%polarion.base.url%/polarion/doorsconnector/rest/connections/%connection.id%/nextCommand")
	oleMethod(http, "open", args)
	oleMethod(http, "send")
	int status
	oleGet(http, "status", status)
	connected = status == 200
	string response
	oleGet(http, "responseText", response)
	print "RESPONSE:" response "\n"
	lines response
	string commandId = response[0:end 0]
	string resultUrl = "%polarion.base.url%/polarion/doorsconnector/rest/connections/%connection.id%/commands/" commandId "/result"
	string command = "pragma runLim,0\nstring resultUrl = \"" resultUrl "\"\n" response[end 0+2:]
	sendResult(commandId, eval_(command))
}