/*    */ package com.polarion.synchronizer.proxy.doors.internal.commands;
/*    */ 
/*    */ import com.polarion.synchronizer.proxy.doors.internal.dxl.DxlScriptBuilder;
/*    */ import com.polarion.synchronizer.proxy.doors.internal.dxl.IDxlScript;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ 
/*    */ public class DoorsCommandDeleteObject
/*    */   extends AbstractDoorsCommand<String>
/*    */ {
/*    */   @NotNull
/*    */   private final String absoluteNumber;
/*    */   
/*    */   public DoorsCommandDeleteObject(@NotNull String absoluteNumber) {
/* 14 */     this.absoluteNumber = absoluteNumber;
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   protected IDxlScript getScript() {
/* 20 */     return (IDxlScript)DxlScriptBuilder.script("deleteObject.dxl")
/* 21 */       .replaceParameter("absoluteNumber", this.absoluteNumber);
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   protected String processResult(@NotNull String result) throws Exception {
/* 27 */     return result;
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/internal/commands/DoorsCommandDeleteObject.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */