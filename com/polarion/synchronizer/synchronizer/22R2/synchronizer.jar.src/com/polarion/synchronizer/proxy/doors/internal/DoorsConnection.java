/*     */ package com.polarion.synchronizer.proxy.doors.internal;
/*     */ 
/*     */ import com.polarion.synchronizer.IExchangeService;
/*     */ import com.polarion.synchronizer.IExchangeServiceFactory;
/*     */ import com.polarion.synchronizer.IMetadataService;
/*     */ import com.polarion.synchronizer.IProxyConfiguration;
/*     */ import com.polarion.synchronizer.SynchronizationException;
/*     */ import com.polarion.synchronizer.model.IProxyMetadata;
/*     */ import com.polarion.synchronizer.proxy.configuration.SpecificationConfiguration;
/*     */ import com.polarion.synchronizer.proxy.doors.DoorsProxyConfiguration;
/*     */ import com.polarion.synchronizer.proxy.doors.IDoorsProject;
/*     */ import com.polarion.synchronizer.proxy.doors.internal.commands.AbstractDoorsCommand;
/*     */ import com.polarion.synchronizer.proxy.doors.internal.commands.IDoorsCommand;
/*     */ import com.polarion.synchronizer.proxy.doors.internal.commands.LoadProjectsCommand;
/*     */ import com.polarion.synchronizer.proxy.doors.internal.dxl.DxlScriptBuilder;
/*     */ import com.polarion.synchronizer.proxy.doors.internal.dxl.IDxlScript;
/*     */ import com.polarion.synchronizer.proxy.doors.xmlmodel.DoorsProjects;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Collection;
/*     */ import java.util.Optional;
/*     */ import java.util.stream.Collectors;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ import org.jetbrains.annotations.Nullable;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class DoorsConnection
/*     */   implements InternalDoorsConnection
/*     */ {
/*     */   @NotNull
/*     */   private final String id;
/*     */   @NotNull
/*     */   private final ICommandProcessor commandProcessor;
/*     */   @NotNull
/*     */   private final IMetadataService metadataService;
/*     */   @NotNull
/*     */   private final InternalDoorsService doorsService;
/*     */   @NotNull
/*     */   private final IExchangeService exchangeService;
/*     */   
/*     */   public DoorsConnection(@NotNull String id, @NotNull ICommandProcessor commandProcessor, @NotNull InternalDoorsService doorsService, @NotNull IMetadataService metadataService, @NotNull IExchangeServiceFactory exchangeServiceFactory) {
/*  65 */     this.id = id;
/*  66 */     this.commandProcessor = commandProcessor;
/*  67 */     this.doorsService = doorsService;
/*  68 */     this.metadataService = metadataService;
/*  69 */     this.exchangeService = exchangeServiceFactory.create(this);
/*  70 */     commandProcessor.registerConnection(id);
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public String getId() {
/*  76 */     return this.id;
/*     */   }
/*     */ 
/*     */   
/*     */   public void waitForClientConnection() {
/*  81 */     execute((IDoorsCommand<String>)new AbstractDoorsCommand<String>()
/*     */         {
/*     */           @NotNull
/*     */           public IDxlScript getScript()
/*     */           {
/*  86 */             return (IDxlScript)DxlScriptBuilder.scriptWithContent("print \"Anybody listening?\"");
/*     */           }
/*     */ 
/*     */           
/*     */           @NotNull
/*     */           protected String processResult(@NotNull String result) {
/*  92 */             return "";
/*     */           }
/*     */         });
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public Collection<String> loadProjects() {
/* 100 */     return ((DoorsProjects)execute((IDoorsCommand<DoorsProjects>)new LoadProjectsCommand())).getProjects();
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public String getConnectionScript() {
/* 106 */     return this.commandProcessor.getInitScript(this.id);
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public IDoorsProject getProjectByName(@NotNull String name) {
/* 112 */     return new ActiveDoorsProject(name, this);
/*     */   }
/*     */ 
/*     */   
/*     */   public void close() {
/* 117 */     this.commandProcessor.closeConnection(this.id);
/* 118 */     this.doorsService.removeConnection(this.id);
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public <T> T execute(IDoorsCommand<T> command) {
/* 124 */     return this.commandProcessor.execute(this.id, command);
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public IProxyMetadata loadMetadata(@NotNull String moduleId) {
/* 130 */     return this.metadataService.loadMetadata(moduleId, (IProxyConfiguration)new DoorsProxyConfiguration(this.id, moduleId));
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public IDoorsModule getModule(@NotNull String uuid) {
/* 136 */     return new DoorsModule(uuid, this);
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public String startImport(@NotNull String projectId, @NotNull Collection<String> specificationIds) {
/* 142 */     return this.exchangeService.startImport(
/* 143 */         (Collection)specificationIds.stream()
/* 144 */         .map(id -> (SpecificationConfiguration)Optional.<SpecificationConfiguration>ofNullable(this.exchangeService.loadConfiguration(paramString1, id)).orElseThrow(()))
/* 145 */         .collect(Collectors.toList()), 
/* 146 */         projectId);
/*     */   }
/*     */ 
/*     */   
/*     */   public void createPolarionDocument(@NotNull String projectId, @NotNull String space, @NotNull String documentName, @Nullable String documentTypeId) {
/* 151 */     this.exchangeService.createPolarionDocument(projectId, space, documentName, documentTypeId);
/*     */   }
/*     */ 
/*     */   
/*     */   public void createPolarionDocument(@NotNull String projectId, @NotNull String space, @NotNull String documentName, @NotNull String documentTitle, @Nullable String documentTypeId) {
/* 156 */     this.exchangeService.createPolarionDocument(projectId, space, documentName, documentTitle, documentTypeId);
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public Collection<SpecificationConfiguration> loadConfigurations(@NotNull String projectId, @NotNull Collection<String> ids) {
/* 162 */     Collection<SpecificationConfiguration> configurations = new ArrayList<>(ids.size());
/* 163 */     for (String id : ids) {
/* 164 */       SpecificationConfiguration configuration = this.exchangeService.loadConfiguration(projectId, id);
/* 165 */       if (configuration != null) {
/* 166 */         configurations.add(configuration);
/*     */       }
/*     */     } 
/* 169 */     return configurations;
/*     */   }
/*     */ 
/*     */   
/*     */   public void createAndUpdateSyncPairs(@NotNull String projectId, @NotNull Collection<SpecificationConfiguration> configurationsCreate, @NotNull Collection<SpecificationConfiguration> configurationsUpdate) {
/* 174 */     ((IDoorsExchangeService)this.exchangeService).createAndUpdateSyncPairs(projectId, configurationsCreate, configurationsUpdate);
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/internal/DoorsConnection.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */