/*    */ package com.polarion.synchronizer.spi.translators;
/*    */ 
/*    */ import com.google.inject.TypeLiteral;
/*    */ import com.google.inject.util.Types;
/*    */ import com.polarion.synchronizer.mapping.ITranslator;
/*    */ import com.polarion.synchronizer.mapping.ITranslatorFactory;
/*    */ import java.lang.reflect.ParameterizedType;
/*    */ import java.lang.reflect.Type;
/*    */ 
/*    */ 
/*    */ 
/*    */ public abstract class TranslatorModule<R>
/*    */   extends BaseTranslatorModule<R>
/*    */ {
/*    */   private final TypeLiteral<ITranslator<R>> translatorType;
/*    */   private final TypeLiteral<ITranslatorFactory<R>> factoryType;
/*    */   
/*    */   public TranslatorModule(String sourceItemType, String targetItemType, Class<? extends ITranslator<R>> translatorClass, boolean sourceMulti, boolean targetMulti) {
/* 19 */     super(sourceItemType, targetItemType, sourceMulti, targetMulti, translatorClass);
/*    */     
/* 21 */     ParameterizedType type = (ParameterizedType)getClass().getGenericSuperclass();
/* 22 */     Type[] parameterTypes = type.getActualTypeArguments();
/*    */     
/* 24 */     this.translatorType = 
/* 25 */       TypeLiteral.get(Types.newParameterizedType(ITranslator.class, new Type[] { parameterTypes[0] }));
/* 26 */     this.factoryType = 
/* 27 */       TypeLiteral.get(Types.newParameterizedType(ITranslatorFactory.class, new Type[] { parameterTypes[0] }));
/*    */   }
/*    */ 
/*    */   
/*    */   public TranslatorModule(String sourceItemType, String targetItemType, Class<? extends ITranslator<R>> translatorClass) {
/* 32 */     this(sourceItemType, targetItemType, translatorClass, false, false);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   protected TypeLiteral<ITranslator<R>> getTranslatorType() {
/* 39 */     return this.translatorType;
/*    */   }
/*    */ 
/*    */   
/*    */   protected TypeLiteral<ITranslatorFactory<R>> getFactoryType() {
/* 44 */     return this.factoryType;
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/spi/translators/TranslatorModule.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */