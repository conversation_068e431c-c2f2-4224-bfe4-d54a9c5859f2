/*     */ package com.polarion.synchronizer.model;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.Collection;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ 
/*     */ public class UpdateResult
/*     */ {
/*  10 */   private Map<String, Collection<String>> fieldErrorMap = new HashMap<>();
/*     */   
/*     */   public static final UpdateResult success() {
/*  13 */     return new UpdateResult(null);
/*     */   }
/*     */ 
/*     */   
/*     */   private String error;
/*     */   private boolean hasWarning;
/*     */   private final Exception exception;
/*     */   
/*     */   public UpdateResult(String error) {
/*  22 */     this.error = error;
/*  23 */     this.exception = null;
/*     */   }
/*     */   
/*     */   public UpdateResult(String message, Exception exception) {
/*  27 */     this.error = String.valueOf(message) + exception.toString();
/*  28 */     this.exception = exception;
/*     */   }
/*     */   
/*     */   public String getError() {
/*  32 */     return String.valueOf(this.error) + (this.fieldErrorMap.isEmpty() ? "" : ("\nFailed to update fields: " + this.fieldErrorMap));
/*     */   }
/*     */   
/*     */   public Exception getException() {
/*  36 */     return this.exception;
/*     */   }
/*     */   
/*     */   public boolean hasError() {
/*  40 */     return !(this.error == null && this.fieldErrorMap.isEmpty());
/*     */   }
/*     */   
/*     */   public boolean hasWarning() {
/*  44 */     return this.hasWarning;
/*     */   }
/*     */   
/*     */   public boolean isFailed() {
/*  48 */     return (this.error != null);
/*     */   }
/*     */   
/*     */   public void setHasWarning() {
/*  52 */     this.hasWarning = true;
/*     */   }
/*     */   
/*     */   public void addError(String field, String message) {
/*  56 */     Collection<String> fieldErrors = this.fieldErrorMap.get(field);
/*  57 */     if (fieldErrors == null) {
/*  58 */       fieldErrors = new ArrayList<>();
/*  59 */       this.fieldErrorMap.put(field, fieldErrors);
/*     */     } 
/*  61 */     fieldErrors.add(message);
/*     */   }
/*     */ 
/*     */   
/*     */   public String toString() {
/*  66 */     if (!hasError()) {
/*  67 */       return "Success.";
/*     */     }
/*  69 */     if (this.error != null) {
/*  70 */       return this.error;
/*     */     }
/*  72 */     return "Failed to update fields :" + this.fieldErrorMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Collection<String> getFailedFields() {
/*  77 */     return this.fieldErrorMap.keySet();
/*     */   }
/*     */ 
/*     */   
/*     */   public int hashCode() {
/*  82 */     int prime = 31;
/*  83 */     int result = 1;
/*  84 */     result = 31 * result + ((this.error == null) ? 0 : this.error.hashCode());
/*  85 */     result = 31 * result + ((this.exception == null) ? 0 : this.exception.hashCode());
/*  86 */     result = 31 * result + ((this.fieldErrorMap == null) ? 0 : this.fieldErrorMap.hashCode());
/*  87 */     return result;
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean equals(Object obj) {
/*  92 */     if (this == obj) {
/*  93 */       return true;
/*     */     }
/*  95 */     if (obj == null) {
/*  96 */       return false;
/*     */     }
/*  98 */     if (getClass() != obj.getClass()) {
/*  99 */       return false;
/*     */     }
/* 101 */     UpdateResult other = (UpdateResult)obj;
/* 102 */     if (this.error == null) {
/* 103 */       if (other.error != null) {
/* 104 */         return false;
/*     */       }
/* 106 */     } else if (!this.error.equals(other.error)) {
/* 107 */       return false;
/*     */     } 
/* 109 */     if (this.exception == null) {
/* 110 */       if (other.exception != null) {
/* 111 */         return false;
/*     */       }
/* 113 */     } else if (!this.exception.equals(other.exception)) {
/* 114 */       return false;
/*     */     } 
/* 116 */     if (this.fieldErrorMap == null) {
/* 117 */       if (other.fieldErrorMap != null) {
/* 118 */         return false;
/*     */       }
/* 120 */     } else if (!this.fieldErrorMap.equals(other.fieldErrorMap)) {
/* 121 */       return false;
/*     */     } 
/* 123 */     return true;
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/model/UpdateResult.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */