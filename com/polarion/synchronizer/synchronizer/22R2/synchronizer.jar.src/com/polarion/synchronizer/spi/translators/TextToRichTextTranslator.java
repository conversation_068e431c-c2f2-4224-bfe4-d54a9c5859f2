/*   */ package com.polarion.synchronizer.spi.translators;
/*   */ 
/*   */ import com.polarion.core.util.types.Text;
/*   */ 
/*   */ public class TextToRichTextTranslator
/*   */   extends AbstractStringTranslator
/*   */ {
/*   */   protected String mapValue(String sourceValue, String targetValue) {
/* 9 */     return Text.plain(sourceValue).convertToHTML().getContent().replaceAll("\\r", "");
/*   */   }
/*   */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/spi/translators/TextToRichTextTranslator.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */