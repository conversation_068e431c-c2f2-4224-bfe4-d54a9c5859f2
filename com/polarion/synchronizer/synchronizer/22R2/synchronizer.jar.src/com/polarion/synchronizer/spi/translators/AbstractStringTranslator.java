/*    */ package com.polarion.synchronizer.spi.translators;
/*    */ 
/*    */ import com.polarion.synchronizer.mapping.TranslationResult;
/*    */ import org.jetbrains.annotations.Nullable;
/*    */ 
/*    */ public abstract class AbstractStringTranslator
/*    */   extends TypesafeTranslator<String, String, String>
/*    */ {
/*    */   public AbstractStringTranslator() {
/* 10 */     super(String.class, String.class);
/*    */   }
/*    */ 
/*    */   
/*    */   public TranslationResult<String> translateBidirectionalTypesafe(@Nullable String sourceBaseline, @Nullable String sourceValue, @Nullable String targetBaseline, @Nullable String targetValue) {
/* 15 */     String mappedValue = (sourceValue == null) ? null : mapValue(sourceValue, targetValue);
/* 16 */     return createBidirectionalResult(sourceBaseline, sourceValue, mappedValue, targetBaseline, targetValue);
/*    */   }
/*    */ 
/*    */   
/*    */   public TranslationResult<String> translateUnidirectionalTypesafe(@Nullable String sourceValue, @Nullable String targetValue) {
/* 21 */     String mappedValue = (sourceValue == null) ? null : mapValue(sourceValue, targetValue);
/* 22 */     return createUnidirectionalResult(mappedValue, targetValue);
/*    */   }
/*    */   
/*    */   protected abstract String mapValue(String paramString1, String paramString2);
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/spi/translators/AbstractStringTranslator.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */