/*    */ package com.polarion.synchronizer.internal.configuration;
/*    */ 
/*    */ import javax.xml.bind.annotation.XmlAccessType;
/*    */ import javax.xml.bind.annotation.XmlAccessorType;
/*    */ import javax.xml.bind.annotation.XmlAttribute;
/*    */ import javax.xml.bind.annotation.XmlRootElement;
/*    */ 
/*    */ @XmlAccessorType(XmlAccessType.FIELD)
/*    */ @XmlRootElement(name = "replace")
/*    */ public class ValueMappingConfiguration
/*    */ {
/*    */   @XmlAttribute
/*    */   private String left;
/*    */   
/*    */   public ValueMappingConfiguration(String left, String right) {
/* 16 */     this.right = right;
/* 17 */     this.left = left;
/*    */   }
/*    */ 
/*    */   
/*    */   @XmlAttribute
/*    */   private String right;
/*    */   
/*    */   public ValueMappingConfiguration() {}
/*    */   
/*    */   public String getRight() {
/* 27 */     return this.right;
/*    */   }
/*    */   
/*    */   public String getLeft() {
/* 31 */     return this.left;
/*    */   }
/*    */ 
/*    */   
/*    */   public int hashCode() {
/* 36 */     int prime = 31;
/* 37 */     int result = 1;
/* 38 */     result = 31 * result + ((this.left == null) ? 0 : this.left.hashCode());
/* 39 */     result = 31 * result + ((this.right == null) ? 0 : this.right.hashCode());
/* 40 */     return result;
/*    */   }
/*    */ 
/*    */   
/*    */   public boolean equals(Object obj) {
/* 45 */     if (this == obj) {
/* 46 */       return true;
/*    */     }
/* 48 */     if (obj == null) {
/* 49 */       return false;
/*    */     }
/* 51 */     if (getClass() != obj.getClass()) {
/* 52 */       return false;
/*    */     }
/* 54 */     ValueMappingConfiguration other = (ValueMappingConfiguration)obj;
/* 55 */     if (this.left == null) {
/* 56 */       if (other.left != null) {
/* 57 */         return false;
/*    */       }
/* 59 */     } else if (!this.left.equals(other.left)) {
/* 60 */       return false;
/*    */     } 
/* 62 */     if (this.right == null) {
/* 63 */       if (other.right != null) {
/* 64 */         return false;
/*    */       }
/* 66 */     } else if (!this.right.equals(other.right)) {
/* 67 */       return false;
/*    */     } 
/* 69 */     return true;
/*    */   }
/*    */ 
/*    */   
/*    */   public String toString() {
/* 74 */     return "ValueMappingConfiguration [right=" + this.right + ", left=" + this.left + "]";
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/internal/configuration/ValueMappingConfiguration.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */