/*     */ package com.polarion.synchronizer.spi;
/*     */ 
/*     */ import com.polarion.synchronizer.model.FieldDefinition;
/*     */ import com.polarion.synchronizer.model.IProxy;
/*     */ import com.polarion.synchronizer.model.Option;
/*     */ import com.polarion.synchronizer.model.TransferItem;
/*     */ import com.polarion.synchronizer.model.UpdateResult;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Collection;
/*     */ import java.util.Collections;
/*     */ import java.util.List;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ import org.jetbrains.annotations.Nullable;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public abstract class AbstractProxy
/*     */   implements IProxy
/*     */ {
/*     */   @NotNull
/*     */   public List<UpdateResult> update(@NotNull List<TransferItem> items) {
/*  43 */     return createErrorResults(items, "Unsupported operation: Update.");
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public List<UpdateResult> delete(@NotNull List<String> ids) {
/*  49 */     return createErrorResults(ids, "Unsupported operation: Delete.");
/*     */   }
/*     */   
/*     */   public List<UpdateResult> createErrorResults(List<?> values, String error) {
/*  53 */     List<UpdateResult> updateResults = new ArrayList<>(values.size());
/*     */     
/*  55 */     for (Object value : values) {
/*  56 */       updateResults.add(new UpdateResult(error));
/*     */     }
/*  58 */     return updateResults;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void close() {}
/*     */ 
/*     */ 
/*     */   
/*     */   @Nullable
/*     */   public String getContentScope() {
/*  69 */     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public Collection<TransferItem> getItems(@NotNull Collection<String> ids, @NotNull Collection<String> keys) {
/*  75 */     return Collections.EMPTY_LIST;
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public Collection<TransferItem> getScopeItems(@NotNull Collection<String> keys) {
/*  81 */     return Collections.EMPTY_LIST;
/*     */   }
/*     */ 
/*     */   
/*     */   @Nullable
/*     */   public Collection<FieldDefinition> getDefinedFields(@Nullable String typeId) {
/*  87 */     return Collections.EMPTY_LIST;
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean isHierarchySupported() {
/*  92 */     return false;
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public Collection<Option> getDefinedTypes() {
/*  98 */     return Collections.EMPTY_LIST;
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public Collection<Option> getDefinedTypes(@NotNull Collection<String> requiredTypes) {
/* 104 */     return getDefinedTypes();
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean hasNonSynchronizableFields() {
/* 109 */     return false;
/*     */   }
/*     */ 
/*     */   
/*     */   @Nullable
/*     */   public String getTargetName() {
/* 115 */     return null;
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/spi/AbstractProxy.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */