/*    */ package com.polarion.synchronizer.spi;
/*    */ 
/*    */ import com.polarion.core.util.logging.Logger;
/*    */ import com.polarion.synchronizer.ILogger;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ 
/*    */ public final class Log4jLoggerAdapter
/*    */   implements ILogger
/*    */ {
/*    */   @NotNull
/*    */   private final Logger delegate;
/*    */   
/*    */   public Log4jLoggerAdapter() {
/* 14 */     this(Logger.getLogger("com.polarion.synchronizer"));
/*    */   }
/*    */   
/*    */   public Log4jLoggerAdapter(@NotNull Logger delegate) {
/* 18 */     this.delegate = delegate;
/*    */   }
/*    */ 
/*    */   
/*    */   public void error(Object message, Throwable t) {
/* 23 */     this.delegate.error(message, t);
/*    */   }
/*    */ 
/*    */   
/*    */   public void debug(Object message, Throwable t) {
/* 28 */     this.delegate.debug(message, t);
/*    */   }
/*    */ 
/*    */   
/*    */   public void debug(Object message) {
/* 33 */     this.delegate.debug(message);
/*    */   }
/*    */ 
/*    */   
/*    */   public void error(Object message) {
/* 38 */     this.delegate.error(message);
/*    */   }
/*    */ 
/*    */   
/*    */   public void fatal(Object message, Throwable t) {
/* 43 */     this.delegate.fatal(message, t);
/*    */   }
/*    */ 
/*    */   
/*    */   public void fatal(Object message) {
/* 48 */     this.delegate.fatal(message);
/*    */   }
/*    */ 
/*    */   
/*    */   public void info(Object message, Throwable t) {
/* 53 */     this.delegate.info(message, t);
/*    */   }
/*    */ 
/*    */   
/*    */   public void info(Object message) {
/* 58 */     this.delegate.info(message);
/*    */   }
/*    */ 
/*    */   
/*    */   public boolean isDebugEnabled() {
/* 63 */     return this.delegate.isDebugEnabled();
/*    */   }
/*    */ 
/*    */   
/*    */   public boolean isInfoEnabled() {
/* 68 */     return this.delegate.isInfoEnabled();
/*    */   }
/*    */ 
/*    */   
/*    */   public void warn(Object message, Throwable t) {
/* 73 */     this.delegate.warn(message, t);
/*    */   }
/*    */ 
/*    */   
/*    */   public void warn(Object message) {
/* 78 */     this.delegate.warn(message);
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/spi/Log4jLoggerAdapter.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */