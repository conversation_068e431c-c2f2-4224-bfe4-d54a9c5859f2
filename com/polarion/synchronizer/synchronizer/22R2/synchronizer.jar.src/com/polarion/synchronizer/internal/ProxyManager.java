/*    */ package com.polarion.synchronizer.internal;
/*    */ 
/*    */ import com.polarion.platform.internal.ILicenseSecurityManager;
/*    */ import com.polarion.synchronizer.IProxyConfiguration;
/*    */ import com.polarion.synchronizer.ProxyContribution;
/*    */ import com.polarion.synchronizer.SynchronizationException;
/*    */ import com.polarion.synchronizer.configuration.IConnection;
/*    */ import com.polarion.synchronizer.model.IProxy;
/*    */ import com.polarion.synchronizer.model.IProxyFactory;
/*    */ import com.polarion.synchronizer.spi.IProxyExtension;
/*    */ import java.util.Collection;
/*    */ import java.util.HashMap;
/*    */ import java.util.LinkedList;
/*    */ import java.util.Map;
/*    */ import java.util.Set;
/*    */ import javax.inject.Inject;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ import org.jetbrains.annotations.Nullable;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class ProxyManager
/*    */ {
/*    */   @NotNull
/*    */   private final Map<ProxyContribution<?, ?>, IProxyFactory> proxyContributions;
/*    */   @NotNull
/*    */   private final ILicenseSecurityManager licenseManager;
/*    */   @NotNull
/*    */   private final Collection<IProxyExtension> extensionContributions;
/*    */   @NotNull
/* 33 */   private final Map<Class<? extends IProxyConfiguration<? extends IConnection>>, IProxyFactory> proxyFactories = new HashMap<>(); @NotNull
/* 34 */   private final Map<Class<? extends IConnection>, IProxyFactory> proxyFactoriesByConnection = new HashMap<>(); @NotNull
/* 35 */   private final Collection<Class<? extends IConnection>> connectionClasses = new LinkedList<>();
/*    */   
/*    */   @Inject
/*    */   public ProxyManager(@NotNull Map<ProxyContribution<?, ?>, IProxyFactory> proxyContributions, @NotNull Set<IProxyExtension> extensionContributions, @NotNull ILicenseSecurityManager licenseManager) {
/* 39 */     this.proxyContributions = proxyContributions;
/* 40 */     this.extensionContributions = extensionContributions;
/* 41 */     this.licenseManager = licenseManager;
/* 42 */     for (Map.Entry<ProxyContribution<?, ?>, IProxyFactory> contributionEntry : proxyContributions.entrySet()) {
/* 43 */       this.proxyFactories.put(((ProxyContribution)contributionEntry.getKey()).getConfigurationClass(), contributionEntry.getValue());
/* 44 */       Class<? extends IConnection> connectionClass = ((ProxyContribution)contributionEntry.getKey()).getConnectionClass();
/* 45 */       if (connectionClass != null) {
/* 46 */         this.connectionClasses.add(connectionClass);
/* 47 */         this.proxyFactoriesByConnection.put(connectionClass, contributionEntry.getValue());
/*    */       } 
/*    */     } 
/*    */   }
/*    */   
/*    */   @NotNull
/*    */   public IProxy newProxy(@NotNull IProxyConfiguration<? extends IConnection> proxyConfiguration) {
/* 54 */     IProxyConfiguration<?> extendedConfiguration = proxyConfiguration;
/* 55 */     for (IProxyExtension extension : this.extensionContributions) {
/* 56 */       extendedConfiguration = extension.applyToConfiguration(extendedConfiguration);
/*    */     }
/*    */     
/* 59 */     IProxyFactory factory = this.proxyFactories.get(proxyConfiguration.getClass());
/* 60 */     boolean isLicensed = ((Boolean)this.proxyContributions.entrySet().stream()
/* 61 */       .filter(entry -> (entry.getValue() == paramIProxyFactory))
/* 62 */       .map(Map.Entry::getKey)
/* 63 */       .map(contribution -> Boolean.valueOf(contribution.isLicensed(this.licenseManager)))
/* 64 */       .findFirst().orElseThrow(() -> new RuntimeException("Internal Error: Failed to locate contribution for factory " + paramIProxyFactory))).booleanValue();
/*    */     
/* 66 */     if (!isLicensed) {
/* 67 */       throw new SynchronizationException("Connector is not licensed.");
/*    */     }
/* 69 */     IProxy extended = factory.createProxy(extendedConfiguration);
/* 70 */     for (IProxyExtension extension : this.extensionContributions) {
/* 71 */       extended = extension.applyToProxy(extended, proxyConfiguration);
/*    */     }
/* 73 */     return extended;
/*    */   }
/*    */   
/*    */   @Nullable
/*    */   public String checkConnection(@NotNull IConnection connection) {
/* 78 */     IProxyFactory proxyFactory = this.proxyFactoriesByConnection.get(connection.getClass());
/* 79 */     return proxyFactory.checkConnection(connection);
/*    */   }
/*    */   
/*    */   @NotNull
/*    */   public Collection<Class<? extends IProxyConfiguration<? extends IConnection>>> getConfigurationClasses() {
/* 84 */     return this.proxyFactories.keySet();
/*    */   }
/*    */   
/*    */   @NotNull
/*    */   public Collection<Class<? extends IConnection>> getConnectionClasses() {
/* 89 */     return this.connectionClasses;
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/internal/ProxyManager.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */