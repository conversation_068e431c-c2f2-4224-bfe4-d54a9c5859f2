/*    */ package com.polarion.synchronizer.spi.translators;
/*    */ 
/*    */ import com.google.inject.AbstractModule;
/*    */ import com.google.inject.Key;
/*    */ import com.google.inject.TypeLiteral;
/*    */ import com.google.inject.assistedinject.FactoryModuleBuilder;
/*    */ import com.google.inject.multibindings.MapBinder;
/*    */ import com.google.inject.name.Names;
/*    */ import com.polarion.synchronizer.mapping.ITranslator;
/*    */ import com.polarion.synchronizer.mapping.ITranslatorFactory;
/*    */ import com.polarion.synchronizer.mapping.TranslatorRegistry;
/*    */ import java.lang.annotation.Annotation;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public abstract class BaseTranslatorModule<R>
/*    */   extends AbstractModule
/*    */ {
/*    */   private final String sourceItemType;
/*    */   private final String targetItemType;
/*    */   private final boolean sourceMulti;
/*    */   private final boolean targetMulti;
/*    */   private final Class<? extends ITranslator<R>> translatorClass;
/*    */   
/*    */   public BaseTranslatorModule(String sourceItemType, String targetItemType, boolean sourceMulti, boolean targetMulti, Class<? extends ITranslator<R>> translatorClass) {
/* 30 */     this.sourceItemType = sourceItemType;
/* 31 */     this.targetItemType = targetItemType;
/* 32 */     this.sourceMulti = sourceMulti;
/* 33 */     this.targetMulti = targetMulti;
/* 34 */     this.translatorClass = translatorClass;
/*    */   }
/*    */ 
/*    */   
/*    */   protected abstract TypeLiteral<ITranslator<R>> getTranslatorType();
/*    */ 
/*    */   
/*    */   protected abstract TypeLiteral<ITranslatorFactory<R>> getFactoryType();
/*    */   
/*    */   protected void configure() {
/* 44 */     MapBinder<TranslatorRegistry.Key, ITranslatorFactory> translatorBinder = 
/* 45 */       MapBinder.newMapBinder(binder(), TranslatorRegistry.Key.class, ITranslatorFactory.class);
/*    */     
/* 47 */     TranslatorRegistry.Key key = new TranslatorRegistry.Key(this.sourceItemType, this.targetItemType, this.sourceMulti, this.targetMulti);
/*    */     
/* 49 */     install((new FactoryModuleBuilder())
/* 50 */         .implement(getTranslatorType(), this.translatorClass)
/* 51 */         .build(Key.get(getFactoryType(), (Annotation)Names.named(key.toString()))));
/*    */     
/* 53 */     translatorBinder.addBinding(key)
/* 54 */       .to(Key.get(getFactoryType(), (Annotation)Names.named(key.toString())));
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/spi/translators/BaseTranslatorModule.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */