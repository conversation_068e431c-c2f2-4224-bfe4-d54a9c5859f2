package com.polarion.synchronizer.proxy.polarion;

import com.polarion.synchronizer.IAction;

public interface ISetAttributeAction extends IAction {
  String getValue();
  
  String getId();
}


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/polarion/ISetAttributeAction.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */