/*    */ package com.polarion.synchronizer.proxy.doors.internal;
/*    */ 
/*    */ import com.rtfparserkit.parser.IRtfSource;
/*    */ import com.rtfparserkit.parser.RtfStreamSource;
/*    */ import java.io.ByteArrayInputStream;
/*    */ import java.io.IOException;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class RtfSourceWithLastPosition
/*    */   implements IRtfSource
/*    */ {
/*    */   @NotNull
/*    */   private final byte[] data;
/*    */   @NotNull
/*    */   private IRtfSource delegate;
/* 22 */   private int lastPositionRead = -1;
/*    */   
/*    */   public RtfSourceWithLastPosition(@NotNull byte[] data) {
/* 25 */     this.data = data;
/* 26 */     this.delegate = (IRtfSource)new RtfStreamSource(new ByteArrayInputStream(data));
/*    */   }
/*    */ 
/*    */   
/*    */   public int read() throws IOException {
/* 31 */     this.lastPositionRead++;
/* 32 */     return this.delegate.read();
/*    */   }
/*    */ 
/*    */   
/*    */   public void unread(int c) throws IOException {
/* 37 */     this.delegate.unread(c);
/* 38 */     this.lastPositionRead--;
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public int read(byte[] b) throws IOException {
/* 44 */     return this.delegate.read(b);
/*    */   }
/*    */   
/*    */   public int getLastPositionRead() {
/* 48 */     return this.lastPositionRead;
/*    */   }
/*    */   
/*    */   @NotNull
/*    */   public byte[] getData() {
/* 53 */     return this.data;
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/internal/RtfSourceWithLastPosition.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */