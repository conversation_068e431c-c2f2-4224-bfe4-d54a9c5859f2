package com.polarion.synchronizer.proxy.doors;

import com.polarion.synchronizer.model.IProxyMetadata;
import com.polarion.synchronizer.proxy.configuration.SpecificationConfiguration;
import java.util.Collection;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public interface IDoorsConnection {
  @NotNull
  String getId();
  
  void waitForClientConnection();
  
  @NotNull
  Collection<String> loadProjects();
  
  @NotNull
  String getConnectionScript();
  
  @NotNull
  IDoorsProject getProjectByName(@NotNull String paramString);
  
  void close();
  
  @NotNull
  IProxyMetadata loadMetadata(@NotNull String paramString);
  
  @NotNull
  String startImport(@NotNull String paramString, @NotNull Collection<String> paramCollection);
  
  void createPolarionDocument(@NotNull String paramString1, @NotNull String paramString2, @NotNull String paramString3, @Nullable String paramString4);
  
  void createPolarionDocument(@NotNull String paramString1, @NotNull String paramString2, @NotNull String paramString3, @NotNull String paramString4, @Nullable String paramString5);
  
  void createAndUpdateSyncPairs(@NotNull String paramString, @NotNull Collection<SpecificationConfiguration> paramCollection1, @NotNull Collection<SpecificationConfiguration> paramCollection2);
  
  @NotNull
  Collection<SpecificationConfiguration> loadConfigurations(@NotNull String paramString, @NotNull Collection<String> paramCollection);
}


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/IDoorsConnection.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */