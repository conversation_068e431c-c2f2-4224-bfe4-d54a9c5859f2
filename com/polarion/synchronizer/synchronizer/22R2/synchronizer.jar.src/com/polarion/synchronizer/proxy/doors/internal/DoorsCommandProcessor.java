/*     */ package com.polarion.synchronizer.proxy.doors.internal;
/*     */ 
/*     */ import com.google.inject.Singleton;
/*     */ import com.polarion.core.config.Configuration;
/*     */ import com.polarion.core.util.UUID;
/*     */ import com.polarion.core.util.logging.Logger;
/*     */ import com.polarion.synchronizer.proxy.doors.internal.commands.IDoorsCommand;
/*     */ import com.polarion.synchronizer.proxy.doors.internal.dxl.DxlScriptBuilder;
/*     */ import java.util.ArrayDeque;
/*     */ import java.util.Collection;
/*     */ import java.util.Collections;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import java.util.Queue;
/*     */ import javax.ws.rs.NotFoundException;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ import org.jetbrains.annotations.Nullable;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @Singleton
/*     */ public class DoorsCommandProcessor
/*     */   implements ICommandProcessor
/*     */ {
/*  46 */   private static final Logger log = Logger.getLogger(DoorsCommandProcessor.class);
/*     */   
/*     */   @NotNull
/*  49 */   private Map<String, Queue<IDoorsCommand<?>>> commandQueues = new HashMap<>();
/*     */   
/*     */   @NotNull
/*  52 */   private Map<String, Map<String, IDoorsCommand<?>>> executedCommands = new HashMap<>();
/*     */   
/*     */   @NotNull
/*     */   private final String baseUrl;
/*     */   
/*     */   public DoorsCommandProcessor() {
/*  58 */     this.baseUrl = Configuration.getInstance().getBaseURL().toString();
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public String trackExecution(@NotNull String connectionId, @NotNull IDoorsCommand<?> command) {
/*  64 */     Map<String, IDoorsCommand<?>> commands = getExecutedCommands(connectionId);
/*  65 */     String commandId = UUID.nextUUID();
/*  66 */     commands.put(commandId, command);
/*  67 */     if (log.isDebugEnabled()) {
/*  68 */       log.debug(String.format("Executing command %s with ID %s in connection %s.", new Object[] { command, commandId, connectionId }));
/*     */     }
/*  70 */     if (log.isTraceEnabled()) {
/*  71 */       log.trace(command.toString());
/*     */     }
/*  73 */     return commandId;
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private Map<String, IDoorsCommand<?>> getExecutedCommands(String connectionId) {
/*  78 */     Map<String, IDoorsCommand<?>> commands = this.executedCommands.get(connectionId);
/*  79 */     if (commands == null) {
/*  80 */       throw createMissingConnectionException(connectionId);
/*     */     }
/*  82 */     return commands;
/*     */   }
/*     */ 
/*     */   
/*     */   public synchronized void trackResult(@NotNull String connectionId, @NotNull String commandId, @NotNull String result) {
/*  87 */     if (log.isDebugEnabled()) {
/*  88 */       log.debug(String.format("Received result for command %s.", new Object[] { commandId }));
/*     */     }
/*  90 */     if (log.isTraceEnabled()) {
/*  91 */       log.trace(result);
/*     */     }
/*  93 */     IDoorsCommand<?> command = getCommand(connectionId, commandId);
/*  94 */     if (command.setResult(result)) {
/*  95 */       getExecutedCommands(connectionId).remove(commandId);
/*     */     }
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private IDoorsCommand<?> removeCommand(@NotNull String connectionId, @NotNull String commandId) {
/* 101 */     IDoorsCommand<?> command = getExecutedCommands(connectionId).remove(commandId);
/* 102 */     if (command == null) {
/* 103 */       throw new NotFoundException(String.format("Connection with id %s has no command with %s waiting for a result.", new Object[] { connectionId, commandId }));
/*     */     }
/* 105 */     return command;
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private IDoorsCommand<?> getCommand(@NotNull String connectionId, @NotNull String commandId) {
/* 110 */     IDoorsCommand<?> command = getExecutedCommands(connectionId).get(commandId);
/* 111 */     if (command == null) {
/* 112 */       throw new NotFoundException(String.format("Connection with id %s has no command with %s waiting for a result.", new Object[] { connectionId, commandId }));
/*     */     }
/* 114 */     return command;
/*     */   }
/*     */ 
/*     */   
/*     */   public void trackError(@NotNull String connectionId, @NotNull String commandId, @NotNull String error) {
/* 119 */     if (log.isDebugEnabled()) {
/* 120 */       log.debug(String.format("Received error for command %s:\n%s", new Object[] { commandId, error }));
/*     */     }
/* 122 */     removeCommand(connectionId, commandId).setError(error);
/*     */   }
/*     */ 
/*     */   
/*     */   @Nullable
/*     */   public IDoorsCommand<?> removeCommand(@NotNull String connectionId) {
/* 128 */     Queue<IDoorsCommand<?>> queue = getQueue(connectionId);
/* 129 */     synchronized (queue) {
/* 130 */       while (!queue.isEmpty()) {
/* 131 */         IDoorsCommand<?> command = queue.remove();
/* 132 */         if (command.canBeExecuted()) {
/* 133 */           return command;
/*     */         }
/*     */       } 
/*     */     } 
/* 137 */     return null;
/*     */   }
/*     */   
/*     */   private Queue<IDoorsCommand<?>> getQueue(String connectionId) {
/* 141 */     Queue<IDoorsCommand<?>> queue = this.commandQueues.get(connectionId);
/* 142 */     if (queue != null) {
/* 143 */       return queue;
/*     */     }
/* 145 */     throw createMissingConnectionException(connectionId);
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   private NotFoundException createMissingConnectionException(@NotNull String connectionId) {
/* 151 */     return new NotFoundException(String.format("Connection %s does not exist.", new Object[] { connectionId }));
/*     */   }
/*     */ 
/*     */   
/*     */   public <T> IDoorsCommand<T> addCommand(@NotNull String connectionId, @NotNull IDoorsCommand<T> command) {
/* 156 */     if (log.isDebugEnabled()) {
/* 157 */       log.debug(String.format("Added command %s to connection %s.", new Object[] { command, connectionId }));
/*     */     }
/* 159 */     Queue<IDoorsCommand<?>> queue = getQueue(connectionId);
/* 160 */     synchronized (queue) {
/* 161 */       queue.add(command);
/*     */     } 
/* 163 */     return command;
/*     */   }
/*     */ 
/*     */   
/*     */   public void registerConnection(@NotNull String connectionId) {
/* 168 */     this.commandQueues.put(connectionId, new ArrayDeque<>());
/* 169 */     this.executedCommands.put(connectionId, Collections.synchronizedMap(new HashMap<>()));
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public String getInitScript(@NotNull String connectionId) {
/* 175 */     return DxlScriptBuilder.script("RunCommands.dxl")
/* 176 */       .replaceParameter("polarion.base.url", this.baseUrl)
/* 177 */       .replaceParameter("connection.id", connectionId)
/* 178 */       .toString();
/*     */   }
/*     */ 
/*     */   
/*     */   public void closeConnection(@NotNull String connectionId) {
/* 183 */     Queue<IDoorsCommand<?>> queue = getQueue(connectionId);
/* 184 */     synchronized (queue) {
/* 185 */       for (IDoorsCommand<?> command : queue) {
/* 186 */         command.setError("Connection closed.");
/*     */       }
/*     */     } 
/* 189 */     this.commandQueues.remove(connectionId);
/*     */     
/* 191 */     Collection<IDoorsCommand<?>> waitingCommands = getExecutedCommands(connectionId).values();
/* 192 */     for (IDoorsCommand<?> command : waitingCommands) {
/* 193 */       command.setError("Connection closed.");
/*     */     }
/* 195 */     waitingCommands.remove(connectionId);
/*     */   }
/*     */ 
/*     */   
/*     */   @Nullable
/*     */   public <T> T execute(@NotNull String connectionId, @NotNull IDoorsCommand<T> command) {
/* 201 */     return (T)addCommand(connectionId, command).getResult();
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/internal/DoorsCommandProcessor.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */