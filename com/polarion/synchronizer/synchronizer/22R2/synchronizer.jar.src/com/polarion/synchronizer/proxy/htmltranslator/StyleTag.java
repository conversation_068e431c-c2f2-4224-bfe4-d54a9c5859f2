package com.polarion.synchronizer.proxy.htmltranslator;

import org.jetbrains.annotations.NotNull;

public interface StyleTag {
  void open(@NotNull ElementInfo paramElementInfo, @NotNull String paramString, @NotNull StringBuilder paramStringBuilder);
  
  void close(@NotNull ElementInfo paramElementInfo, @NotNull String paramString, @NotNull StringBuilder paramStringBuilder);
}


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/htmltranslator/StyleTag.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */