/*    */ package com.polarion.synchronizer.proxy.doors.internal;
/*    */ 
/*    */ import com.google.inject.Inject;
/*    */ import com.polarion.core.util.ObjectUtils;
/*    */ import com.polarion.synchronizer.IProxyConfiguration;
/*    */ import com.polarion.synchronizer.ISynchronizationContext;
/*    */ import com.polarion.synchronizer.SynchronizationException;
/*    */ import com.polarion.synchronizer.configuration.IConnection;
/*    */ import com.polarion.synchronizer.model.IProxy;
/*    */ import com.polarion.synchronizer.model.IProxyFactory;
/*    */ import com.polarion.synchronizer.proxy.doors.DoorsProxyConfiguration;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ import org.jetbrains.annotations.Nullable;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class DoorsProxyFactory
/*    */   implements IProxyFactory
/*    */ {
/*    */   @NotNull
/*    */   private final InternalDoorsService doorsService;
/*    */   @NotNull
/*    */   private final ISynchronizationContext context;
/*    */   
/*    */   @Inject
/*    */   public DoorsProxyFactory(@NotNull InternalDoorsService doorsService, @NotNull ISynchronizationContext context) {
/* 47 */     this.doorsService = doorsService;
/* 48 */     this.context = context;
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   public IProxy createProxy(@NotNull IProxyConfiguration<? extends IConnection> configuration) {
/* 54 */     DoorsProxyConfiguration doorsConfiguration = (DoorsProxyConfiguration)configuration;
/* 55 */     String configurationError = doorsConfiguration.checkConfiguration();
/* 56 */     if (configurationError != null) {
/* 57 */       throw new SynchronizationException("Doors configuration error:" + configurationError);
/*    */     }
/*    */     
/* 60 */     InternalDoorsConnection connection = this.doorsService.getOrCreateConnection(doorsConfiguration.getRemoteConnectionId());
/* 61 */     IDoorsModule module = connection.getModule((String)ObjectUtils.notNull(doorsConfiguration.getModuleIdFromURL()));
/* 62 */     return (IProxy)new DoorsProxy(module, this.context.getLogger());
/*    */   }
/*    */ 
/*    */   
/*    */   @Nullable
/*    */   public String checkConnection(@NotNull IConnection connection) {
/* 68 */     return null;
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/internal/DoorsProxyFactory.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */