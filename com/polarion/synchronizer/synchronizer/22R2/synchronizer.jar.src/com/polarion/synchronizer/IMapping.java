package com.polarion.synchronizer;

import com.polarion.synchronizer.configuration.IAttributeMapper;
import com.polarion.synchronizer.model.Direction;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public interface IMapping {
  @NotNull
  IAttributeMapper getAttributeMapper();
  
  @Nullable
  Direction getCreateDirection();
  
  @Nullable
  Direction getDeleteDirection();
}


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/IMapping.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */