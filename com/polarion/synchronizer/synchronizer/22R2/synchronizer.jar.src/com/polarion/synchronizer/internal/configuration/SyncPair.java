/*     */ package com.polarion.synchronizer.internal.configuration;
/*     */ 
/*     */ import com.fasterxml.jackson.annotation.JsonIgnore;
/*     */ import com.polarion.synchronizer.IAction;
/*     */ import com.polarion.synchronizer.IPostProcessingAction;
/*     */ import com.polarion.synchronizer.IProxyConfiguration;
/*     */ import com.polarion.synchronizer.configuration.ISyncPair;
/*     */ import com.polarion.synchronizer.configuration.MappingConfiguration;
/*     */ import com.polarion.synchronizer.model.Direction;
/*     */ import java.util.Collection;
/*     */ import javax.xml.bind.annotation.XmlAccessType;
/*     */ import javax.xml.bind.annotation.XmlAccessorType;
/*     */ import javax.xml.bind.annotation.XmlAttribute;
/*     */ import javax.xml.bind.annotation.XmlElement;
/*     */ import javax.xml.bind.annotation.XmlTransient;
/*     */ import javax.xml.bind.annotation.XmlType;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ import org.jetbrains.annotations.Nullable;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @XmlAccessorType(XmlAccessType.FIELD)
/*     */ @XmlType(propOrder = {"left", "right", "mapping"})
/*     */ public class SyncPair
/*     */   implements ISyncPair
/*     */ {
/*     */   @XmlElement
/*     */   private MappingConfiguration mapping;
/*     */   @XmlAttribute
/*     */   private String id;
/*     */   @XmlAttribute
/*     */   private Direction newItemDirection;
/*     */   @XmlAttribute
/*     */   private Direction deleteDirection;
/*     */   @XmlTransient
/*     */   @Nullable
/*     */   private IAction leftDeleteAction;
/*     */   private IProxyConfiguration left;
/*     */   private IProxyConfiguration right;
/*     */   @XmlAttribute
/*     */   private boolean reAddMissingOnRight;
/*     */   @XmlTransient
/*     */   private String projectId;
/*     */   @XmlTransient
/*     */   @JsonIgnore
/*     */   @Nullable
/*     */   private Collection<IPostProcessingAction> postProcessingActions;
/*     */   @XmlTransient
/*     */   private boolean deleteOutOfScope;
/*     */   
/*     */   @Deprecated
/*     */   public SyncPair() {}
/*     */   
/*     */   public SyncPair(@NotNull String id, @NotNull IProxyConfiguration<?> left, @NotNull IProxyConfiguration<?> right) {
/*  65 */     this.id = id;
/*  66 */     this.left = left;
/*  67 */     this.right = right;
/*     */   }
/*     */ 
/*     */   
/*     */   public IProxyConfiguration getLeft() {
/*  72 */     return this.left;
/*     */   }
/*     */ 
/*     */   
/*     */   public IProxyConfiguration getRight() {
/*  77 */     return this.right;
/*     */   }
/*     */ 
/*     */   
/*     */   public MappingConfiguration getMapping() {
/*  82 */     return this.mapping;
/*     */   }
/*     */   
/*     */   public void setMapping(MappingConfiguration mapping) {
/*  86 */     this.mapping = mapping;
/*     */   }
/*     */ 
/*     */   
/*     */   public String getId() {
/*  91 */     return this.id;
/*     */   }
/*     */ 
/*     */   
/*     */   public Direction getNewItemDirection() {
/*  96 */     return this.newItemDirection;
/*     */   }
/*     */   
/*     */   public void setNewItemDirection(Direction newItemDirection) {
/* 100 */     this.newItemDirection = newItemDirection;
/*     */   }
/*     */ 
/*     */   
/*     */   public Direction getDeleteDirection() {
/* 105 */     return this.deleteDirection;
/*     */   }
/*     */   
/*     */   public void setDeleteDirection(Direction deleteDirection) {
/* 109 */     this.deleteDirection = deleteDirection;
/*     */   }
/*     */ 
/*     */   
/*     */   @Nullable
/*     */   public IAction getLeftDeleteAction() {
/* 115 */     return this.leftDeleteAction;
/*     */   }
/*     */ 
/*     */   
/*     */   public void setLeftDeleteAction(@Nullable IAction leftDeleteAction) {
/* 120 */     this.leftDeleteAction = leftDeleteAction;
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean isReAddMissingOnRight() {
/* 125 */     return this.reAddMissingOnRight;
/*     */   }
/*     */ 
/*     */   
/*     */   public void setReAddMissingOnRight(boolean reAddMissingOnLeft) {
/* 130 */     this.reAddMissingOnRight = reAddMissingOnLeft;
/*     */   }
/*     */ 
/*     */   
/*     */   @Nullable
/*     */   public String getProjectId() {
/* 136 */     return this.projectId;
/*     */   }
/*     */ 
/*     */   
/*     */   public void setProject(String projectId) {
/* 141 */     this.projectId = projectId;
/*     */   }
/*     */ 
/*     */   
/*     */   @XmlTransient
/*     */   public Collection<IPostProcessingAction> getPostProcessingActions() {
/* 147 */     return this.postProcessingActions;
/*     */   }
/*     */ 
/*     */   
/*     */   public void setPostProcessingActions(@NotNull Collection<IPostProcessingAction> actions) {
/* 152 */     this.postProcessingActions = actions;
/*     */   }
/*     */ 
/*     */   
/*     */   public void setDeleteOutOfScope(boolean delete) {
/* 157 */     this.deleteOutOfScope = delete;
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean isDeleteOutOfScope() {
/* 162 */     return this.deleteOutOfScope;
/*     */   }
/*     */ 
/*     */   
/*     */   public String toString() {
/* 167 */     return "SyncPair [id=" + this.id + ", projectId=" + this.projectId + ", left=" + this.left + ", right=" + this.right + ", newItemDirection=" + this.newItemDirection + ", deleteDirection=" + this.deleteDirection + ", deleteOutOfScope=" + this.deleteOutOfScope + 
/* 168 */       ", leftDeleteAction=" + this.leftDeleteAction + ", reAddMissingOnRight=" + this.reAddMissingOnRight + ", postProcessingActions=" + this.postProcessingActions + ", mapping=" + this.mapping + "]";
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/internal/configuration/SyncPair.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */