/*    */ package com.polarion.synchronizer.internal.ole.preview;
/*    */ 
/*    */ import com.polarion.ooxml.oleconverter.IOleConverter;
/*    */ import com.siemens.polarion.previewer.PreviewGenerationCanceled;
/*    */ import com.siemens.polarion.previewer.internal.IPreviewTask;
/*    */ import com.siemens.polarion.previewer.internal.IPreviewTaskResult;
/*    */ import java.io.ByteArrayInputStream;
/*    */ import java.io.InputStream;
/*    */ import java.nio.ByteBuffer;
/*    */ import org.apache.commons.io.IOUtils;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ import org.jetbrains.annotations.Nullable;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ final class OLEConverterPreviewTask
/*    */   implements IPreviewTask
/*    */ {
/*    */   @NotNull
/*    */   private final InputStream inputData;
/*    */   @NotNull
/*    */   private IOleConverter converter;
/*    */   
/*    */   public OLEConverterPreviewTask(@NotNull InputStream inputData, @NotNull IOleConverter converter) {
/* 29 */     this.inputData = inputData;
/* 30 */     this.converter = converter;
/*    */   }
/*    */ 
/*    */   
/*    */   public IPreviewTaskResult call() throws Exception, PreviewGenerationCanceled {
/* 35 */     final byte[] data = this.converter.convertWmf(ByteBuffer.wrap(IOUtils.toByteArray(this.inputData)), "Convert OLE thumbnail in ReqIF document");
/* 36 */     if (data.length == 0) {
/* 37 */       return (IPreviewTaskResult)new IPreviewTaskResult.EmptyPreviewTaskResult();
/*    */     }
/* 39 */     return new IPreviewTaskResult()
/*    */       {
/*    */         @Nullable
/*    */         public InputStream stream()
/*    */         {
/* 44 */           return new ByteArrayInputStream(data);
/*    */         }
/*    */       };
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/internal/ole/preview/OLEConverterPreviewTask.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */