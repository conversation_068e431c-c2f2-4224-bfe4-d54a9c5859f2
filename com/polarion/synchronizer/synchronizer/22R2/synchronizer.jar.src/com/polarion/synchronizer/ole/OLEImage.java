/*    */ package com.polarion.synchronizer.ole;
/*    */ 
/*    */ import java.io.ByteArrayInputStream;
/*    */ import java.io.InputStream;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class OLEImage
/*    */ {
/*    */   @NotNull
/*    */   private final byte[] data;
/*    */   private final int width;
/*    */   private final int height;
/*    */   @NotNull
/*    */   private final String name;
/*    */   
/*    */   public OLEImage(@NotNull byte[] data, int width, int height, @NotNull String name) {
/* 38 */     this.data = data;
/* 39 */     this.width = width;
/* 40 */     this.height = height;
/* 41 */     this.name = name;
/*    */   }
/*    */   
/*    */   @NotNull
/*    */   public InputStream getImageData() {
/* 46 */     return new ByteArrayInputStream(this.data);
/*    */   }
/*    */   
/*    */   @NotNull
/*    */   public byte[] getData() {
/* 51 */     return this.data;
/*    */   }
/*    */   
/*    */   public int getWidth() {
/* 55 */     return this.width;
/*    */   }
/*    */   
/*    */   public int getHeight() {
/* 59 */     return this.height;
/*    */   }
/*    */   
/*    */   @NotNull
/*    */   public String getName() {
/* 64 */     return this.name;
/*    */   }
/*    */ 
/*    */   
/*    */   public String toString() {
/* 69 */     return String.valueOf(this.name) + ";" + this.width + ";" + this.height;
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/ole/OLEImage.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */