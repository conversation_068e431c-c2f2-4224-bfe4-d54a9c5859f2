/*     */ package com.polarion.synchronizer.proxy.doors.internal.commands;
/*     */ 
/*     */ import com.polarion.core.config.Configuration;
/*     */ import com.polarion.core.util.ObjectUtils;
/*     */ import com.polarion.core.util.logging.Logger;
/*     */ import com.polarion.synchronizer.SynchronizationException;
/*     */ import com.polarion.synchronizer.proxy.doors.internal.dxl.IDxlScript;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ import org.jetbrains.annotations.Nullable;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public abstract class AbstractDoorsCommand<T>
/*     */   implements IDoorsCommand<T>
/*     */ {
/*     */   @NotNull
/*  36 */   private static final Logger log = Logger.getLogger(AbstractDoorsCommand.class);
/*     */   @NotNull
/*     */   private static final String CONTINUE = "\nCONTINUE\n";
/*     */   
/*     */   protected enum Status
/*     */   {
/*  42 */     NEW(false), EXECUTED(false), SUCCESS(true), FAILED(true), TIMEOUT(true);
/*     */     
/*     */     boolean done;
/*     */     
/*     */     Status(boolean done) {
/*  47 */       this.done = done;
/*     */     }
/*     */   }
/*     */ 
/*     */   
/*  52 */   protected static final int DEFAULT_TIMEOUT = Configuration.getInstance().connectors().getDoorsCommandTimeout() * 1000;
/*     */   
/*     */   private final int timeout;
/*     */   
/*     */   private RuntimeException exception;
/*     */   
/*     */   @Nullable
/*     */   private T result;
/*     */   
/*     */   @NotNull
/*  62 */   private Status status = Status.NEW;
/*     */   
/*  64 */   private long created = System.currentTimeMillis();
/*     */   
/*  66 */   private final StringBuilder responseBuffer = new StringBuilder();
/*     */   
/*     */   public AbstractDoorsCommand() {
/*  69 */     this(DEFAULT_TIMEOUT);
/*     */   }
/*     */ 
/*     */   
/*     */   public AbstractDoorsCommand(int timeout) {
/*  74 */     this.timeout = timeout;
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public IDxlScript execute() {
/*  80 */     if (this.status != Status.NEW) {
/*  81 */       throw new IllegalStateException("Command already has been executed!");
/*     */     }
/*  83 */     this.status = Status.EXECUTED;
/*  84 */     return getScript().replaceParameter("receiveTimeout", String.valueOf(this.timeout));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public T getResult() {
/*  93 */     waitForResult();
/*  94 */     switch (this.status) {
/*     */       case FAILED:
/*  96 */         throw new SynchronizationException("Excecution of command failed.", this.exception);
/*     */       case SUCCESS:
/*  98 */         return (T)ObjectUtils.notNull(this.result);
/*     */       case TIMEOUT:
/* 100 */         throw new SynchronizationException("Waiting for result timed out.");
/*     */     } 
/* 102 */     throw new RuntimeException(String.format("Internal Error: Unexpected status: %s.", new Object[] { this.status }));
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   private synchronized void waitForResult() {
/*     */     try {
/* 109 */       while (!this.status.done) {
/* 110 */         long wait = this.timeout - System.currentTimeMillis() - this.created;
/* 111 */         if (wait > 0L) {
/* 112 */           wait(wait); continue;
/*     */         } 
/* 114 */         setStatus(Status.TIMEOUT);
/*     */       }
/*     */     
/* 117 */     } catch (InterruptedException e) {
/* 118 */       throw new SynchronizationException("Interrupted waiting for result.", e);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public final boolean setResult(@NotNull String result) {
/* 127 */     if (this.status.done) {
/* 128 */       throw new IllegalStateException(String.format("Can't receive result in state %s.", new Object[] { this.status }));
/*     */     }
/*     */     try {
/* 131 */       if (result.endsWith("\nCONTINUE\n")) {
/* 132 */         String data = result.substring(0, result.length() - "\nCONTINUE\n".length());
/* 133 */         log.debug(String.format("Received partial response for %s.", new Object[] { getClass() }));
/* 134 */         log.trace(String.format("Data:\n%s", new Object[] { data }));
/* 135 */         this.responseBuffer.append(data);
/* 136 */         this.created = System.currentTimeMillis();
/*     */       } else {
/* 138 */         log.debug(String.format("Received final response for %s.", new Object[] { getClass() }));
/* 139 */         log.trace(String.format("Data:\n%s", new Object[] { result }));
/* 140 */         this.responseBuffer.append(result);
/* 141 */         this.result = processResult(this.responseBuffer.toString());
/* 142 */         setStatus(Status.SUCCESS);
/*     */       } 
/* 144 */     } catch (Exception e) {
/* 145 */       this.exception = (RuntimeException)new SynchronizationException(String.format("Failed to process result: %s\n%s", new Object[] { e.toString(), this.responseBuffer }), e);
/* 146 */       setStatus(Status.FAILED);
/*     */     } 
/* 148 */     return this.status.done;
/*     */   }
/*     */ 
/*     */   
/*     */   public final void setError(@NotNull String error) {
/* 153 */     this.exception = (RuntimeException)new SynchronizationException(error);
/* 154 */     setStatus(Status.FAILED);
/*     */   }
/*     */   
/*     */   protected synchronized void setStatus(@NotNull Status status) {
/* 158 */     this.status = status;
/* 159 */     notifyAll();
/*     */   }
/*     */ 
/*     */   
/*     */   public String toString() {
/* 164 */     return getClass().getSimpleName();
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean canBeExecuted() {
/* 169 */     return (this.status == Status.NEW);
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   protected abstract IDxlScript getScript();
/*     */   
/*     */   @NotNull
/*     */   protected abstract T processResult(@NotNull String paramString) throws Exception;
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/internal/commands/AbstractDoorsCommand.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */