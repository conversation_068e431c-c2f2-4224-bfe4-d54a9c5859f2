/*    */ package com.polarion.synchronizer.spi.translators;
/*    */ 
/*    */ import com.polarion.synchronizer.mapping.ITranslator;
/*    */ import com.polarion.synchronizer.mapping.TranslationResult;
/*    */ 
/*    */ public final class ChainedTranslator<S, T> extends TypesafeTranslator<S, T, T> {
/*    */   private final ITranslator<?> translator;
/*    */   private final ITranslator<T> nextTranslator;
/*    */   private final ITranslator<?> reverseTranslator;
/*    */   
/*    */   public ChainedTranslator(ITranslator<?> translator, ITranslator<T> nextTranslator, ITranslator<?> reverseTranslator) {
/* 12 */     super(Object.class, Object.class);
/* 13 */     this.translator = translator;
/* 14 */     this.nextTranslator = nextTranslator;
/* 15 */     this.reverseTranslator = reverseTranslator;
/*    */   }
/*    */ 
/*    */   
/*    */   public TranslationResult<T> translateBidirectionalTypesafe(S sourceBaseline, S sourceValue, T targetBaseline, T targetValue) {
/* 20 */     Object translatedTarget = this.reverseTranslator.translateUnidirectional(targetValue, null).getResultValue();
/*    */     
/* 22 */     Object firstTranslationResult = this.translator.translateUnidirectional(sourceValue, translatedTarget).getResultValue();
/*    */     
/* 24 */     T nextTranslationResult = (T)this.nextTranslator.translateUnidirectional(firstTranslationResult, targetValue).getResultValue();
/*    */     
/* 26 */     return createBidirectionalResult(sourceBaseline, sourceValue, nextTranslationResult, targetBaseline, targetValue);
/*    */   }
/*    */   
/*    */   public TranslationResult<T> translateUnidirectionalTypesafe(S sourceValue, T targetValue) {
/* 30 */     Object firstTarget = this.reverseTranslator.translateUnidirectional(targetValue, null).getResultValue();
/* 31 */     Object firstResult = this.translator.translateUnidirectional(sourceValue, firstTarget).getResultValue();
/* 32 */     T result = (T)this.nextTranslator.translateUnidirectional(firstResult, targetValue).getResultValue();
/* 33 */     return createUnidirectionalResult(result, targetValue);
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/spi/translators/ChainedTranslator.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */