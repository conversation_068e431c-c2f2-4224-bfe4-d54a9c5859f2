/*   */ package com.polarion.synchronizer.spi.translators;
/*   */ 
/*   */ import com.polarion.core.util.types.Text;
/*   */ 
/*   */ public class RichTextToTextTranslator
/*   */   extends AbstractStringTranslator {
/*   */   protected String mapValue(String sourceValue, String targetValue) {
/* 8 */     return Text.html(sourceValue).convertToPlainText().getContent();
/*   */   }
/*   */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/spi/translators/RichTextToTextTranslator.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */