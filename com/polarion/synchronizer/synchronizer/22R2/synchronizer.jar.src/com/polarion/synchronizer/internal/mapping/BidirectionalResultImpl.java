/*    */ package com.polarion.synchronizer.internal.mapping;
/*    */ 
/*    */ import com.polarion.synchronizer.configuration.IAttributeMapper;
/*    */ import com.polarion.synchronizer.model.Side;
/*    */ import com.polarion.synchronizer.model.TransferItem;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ 
/*    */ public class BidirectionalResultImpl
/*    */   implements IAttributeMapper.BidirectionalResult
/*    */ {
/*    */   @NotNull
/*    */   private final IAttributeMapper.UnidirectionalResult leftResult;
/*    */   @NotNull
/*    */   private final IAttributeMapper.UnidirectionalResult rightResult;
/*    */   
/*    */   public BidirectionalResultImpl(@NotNull IAttributeMapper.UnidirectionalResult leftResult, @NotNull IAttributeMapper.UnidirectionalResult rightResult) {
/* 17 */     this.leftResult = leftResult;
/* 18 */     this.rightResult = rightResult;
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   public TransferItem getMappedLeft() {
/* 24 */     return getResultFor(Side.LEFT).getMapped();
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   public TransferItem getMappedRight() {
/* 30 */     return getResultFor(Side.RIGHT).getMapped();
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   public TransferItem getLeftSourceBaseline() {
/* 36 */     return getResultFor(Side.LEFT).getSourceBaseline();
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   public TransferItem getRightSourceBaseline() {
/* 42 */     return getResultFor(Side.RIGHT).getSourceBaseline();
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   public IAttributeMapper.UnidirectionalResult getResultFor(Side from) {
/* 48 */     return (from == Side.LEFT) ? this.leftResult : this.rightResult;
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/internal/mapping/BidirectionalResultImpl.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */