string attrName
AttrType t

result = "<metadata><attributes>"

for attrName in m do {
	
	AttrDef ad = find(m, attrName)

	t = ad.type
	
	AttrType at = find(m Module, t.name)

	string customTypeName = t.name 

	string systemTypeName = stringOf at.type

	if(t.name == "Boolean"){
		systemTypeName = "Boolean"
	}

	string possibleValues = ""
	string defaultValue = ""

	if(systemTypeName == "Enumeration"){
		
		possibleValues = "<availableOptions>"
	
		for (i = 0; i < at.size; i++){
			string cleanedTypeName = cleanName(at.strings[i])
			possibleValues = possibleValues "<availableOption>" cleanedTypeName "</availableOption>"
		}
		
		possibleValues = possibleValues "</availableOptions>"
		
	}
	
	
	if(ad.defval){
       string def = ad.defval
       string cleanDefault = cleanName(def)
	   defaultValue = defaultValue "<defaultValue>"  cleanDefault "</defaultValue>"
	}

	string cleanAttrName = cleanName(attrName)

	string cleanTypeName = cleanName(systemTypeName)

	string cleanCustomTypeName = cleanName(customTypeName)

	result = result "<attribute name=\"" cleanAttrName"\" AttributeType=\"" cleanTypeName "\" CustomAttributeType=\"" cleanCustomTypeName  "\" multiValued=\"" ad.multi "\">" "<attributeValue> noValue </attributeValue>" possibleValues defaultValue  "</attribute>"
}

result = result "</attributes></metadata>"