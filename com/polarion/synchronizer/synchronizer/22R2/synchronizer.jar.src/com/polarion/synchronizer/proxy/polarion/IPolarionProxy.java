package com.polarion.synchronizer.proxy.polarion;

import com.polarion.alm.tracker.model.IModule;
import com.polarion.alm.tracker.model.ITrackerProject;
import com.polarion.synchronizer.model.ITransactionProxy;
import java.security.PrivilegedAction;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public interface IPolarionProxy extends ITransactionProxy {
  @Nullable
  IModule getDocument();
  
  @NotNull
  ITrackerProject getProject();
  
  @Nullable
  <T> T doInTransaction(@NotNull PrivilegedAction<T> paramPrivilegedAction);
}


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/polarion/IPolarionProxy.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */