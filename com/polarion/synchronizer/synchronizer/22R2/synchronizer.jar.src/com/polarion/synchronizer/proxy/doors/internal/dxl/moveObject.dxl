Object myObj = object(%absoluteNumber%, m)
Object refObj = object(%referenceAbsoluteNumber%, m)
Object prevObj
int itemPosition = %itemPosition%
int prevAbs, refAbs
noError()
if(!null(refObj) && !null(myObj))
{
	if(itemPosition == 0)
		move(myObj, below(refObj))
	else
		move(myObj, refObj)
	result = "OK"
}
else if(null(refObj) && itemPosition == 0) //move item to position 1 on level 1
{
	Object oFirst = first(m);
	if (myObj != oFirst)
	{
		move(oFirst, myObj);
		result = "OK"
	}
	else
	{ //item is already in position 1
		result = "OK"
	}
}
else
	result = "FAIL"
string ErrMess = lastError()
if (!null ErrMess)
{
	result = ErrMess	
}
print(result"\n")