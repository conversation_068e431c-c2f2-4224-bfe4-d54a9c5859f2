/*    */ package com.polarion.synchronizer;
/*    */ 
/*    */ import com.polarion.platform.jobs.IJobStatus;
/*    */ import com.polarion.subterra.base.location.ILocation;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public final class ExportResult
/*    */ {
/*    */   @NotNull
/*    */   private ILocation location;
/*    */   
/*    */   public enum XMLValidationStatus
/*    */   {
/* 31 */     NOT_EXECUTED, SUCCESS, FAILED;
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   @NotNull
/* 37 */   private XMLValidationStatus validationStatus = XMLValidationStatus.NOT_EXECUTED;
/*    */   @NotNull
/*    */   private IJobStatus jobStatus;
/*    */   
/*    */   public ExportResult(@NotNull IJobStatus jobStatus, @NotNull ILocation location) {
/* 42 */     this.jobStatus = jobStatus;
/* 43 */     this.location = location;
/*    */   }
/*    */   
/*    */   @NotNull
/*    */   public IJobStatus getJobStatus() {
/* 48 */     return this.jobStatus;
/*    */   }
/*    */   
/*    */   @NotNull
/*    */   public XMLValidationStatus getValidationStatus() {
/* 53 */     return this.validationStatus;
/*    */   }
/*    */   
/*    */   public void setValidationStatus(@NotNull XMLValidationStatus validationStatus) {
/* 57 */     this.validationStatus = validationStatus;
/*    */   }
/*    */   
/*    */   @NotNull
/*    */   public ILocation getLocation() {
/* 62 */     return this.location;
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/ExportResult.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */