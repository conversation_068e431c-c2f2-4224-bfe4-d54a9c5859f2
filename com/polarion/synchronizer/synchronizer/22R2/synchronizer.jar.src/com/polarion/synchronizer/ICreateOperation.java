package com.polarion.synchronizer;

import com.polarion.synchronizer.model.CreateResult;
import com.polarion.synchronizer.model.UpdateResult;
import org.jetbrains.annotations.NotNull;

public interface ICreateOperation extends IUpdateOperation {
  @NotNull
  CreateResult getResult();
}


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/ICreateOperation.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */