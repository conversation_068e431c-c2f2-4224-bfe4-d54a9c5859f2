/*    */ package com.polarion.synchronizer.proxy.doors.internal.commands;
/*    */ 
/*    */ import com.polarion.synchronizer.proxy.doors.internal.dxl.DxlScriptBuilder;
/*    */ import com.polarion.synchronizer.proxy.doors.internal.dxl.IDxlScript;
/*    */ import com.polarion.synchronizer.proxy.doors.xmlmodel.DoorsLinks;
/*    */ import java.nio.charset.Charset;
/*    */ import javax.xml.bind.JAXB;
/*    */ import org.apache.commons.io.IOUtils;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class DoorsCommandLinks
/*    */   extends AbstractDoorsCommand<DoorsLinks>
/*    */ {
/*    */   @NotNull
/*    */   protected IDxlScript getScript() {
/* 40 */     return (IDxlScript)DxlScriptBuilder.script("getDoorsLinks.dxl");
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   public DoorsLinks processResult(@NotNull String result) throws Exception {
/* 46 */     return (DoorsLinks)JAXB.unmarshal(IOUtils.toInputStream(result, Charset.defaultCharset()), DoorsLinks.class);
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/internal/commands/DoorsCommandLinks.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */