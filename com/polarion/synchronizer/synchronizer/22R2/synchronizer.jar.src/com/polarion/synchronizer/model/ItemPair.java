/*    */ package com.polarion.synchronizer.model;
/*    */ 
/*    */ 
/*    */ public class ItemPair
/*    */ {
/*    */   private TransferItem left;
/*    */   private TransferItem right;
/*    */   
/*    */   public ItemPair(TransferItem left, TransferItem right) {
/* 10 */     this.left = left;
/* 11 */     this.right = right;
/*    */   }
/*    */   
/*    */   public TransferItem getLeft() {
/* 15 */     return this.left;
/*    */   }
/*    */   
/*    */   public TransferItem getRight() {
/* 19 */     return this.right;
/*    */   }
/*    */   
/*    */   public TransferItem getFrom(Side fromSide) {
/* 23 */     if (fromSide == Side.LEFT) {
/* 24 */       return this.left;
/*    */     }
/* 26 */     return this.right;
/*    */   }
/*    */ 
/*    */   
/*    */   public TransferItem getTo(Side fromSide) {
/* 31 */     if (fromSide == Side.LEFT) {
/* 32 */       return this.right;
/*    */     }
/* 34 */     return this.left;
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public String toString() {
/* 40 */     return "Pair [left=" + this.left + ", right=" + this.right + "]";
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/model/ItemPair.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */