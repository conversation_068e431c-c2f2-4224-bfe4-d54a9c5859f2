/*    */ package com.polarion.synchronizer;
/*    */ 
/*    */ import com.google.inject.Inject;
/*    */ import com.polarion.alm.tracker.ITrackerService;
/*    */ import com.polarion.platform.context.IContextService;
/*    */ import com.polarion.platform.jobs.IJobService;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class CommonServices
/*    */   implements ICommonServices
/*    */ {
/*    */   @NotNull
/*    */   private final IContextService contextService;
/*    */   @NotNull
/*    */   private final IJobService jobService;
/*    */   @NotNull
/*    */   private final ISynchronizationService synchronizationService;
/*    */   @NotNull
/*    */   private final ITrackerService trackerService;
/*    */   
/*    */   @Inject
/*    */   public CommonServices(@NotNull IContextService contextService, @NotNull IJobService jobService, @NotNull ISynchronizationService synchronizationService, @NotNull ITrackerService trackerService) {
/* 43 */     this.contextService = contextService;
/* 44 */     this.jobService = jobService;
/* 45 */     this.synchronizationService = synchronizationService;
/* 46 */     this.trackerService = trackerService;
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   public IContextService getContextService() {
/* 52 */     return this.contextService;
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   public IJobService getJobService() {
/* 58 */     return this.jobService;
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   public ISynchronizationService getSynchronizationService() {
/* 64 */     return this.synchronizationService;
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   public ITrackerService getTrackerService() {
/* 70 */     return this.trackerService;
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/CommonServices.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */