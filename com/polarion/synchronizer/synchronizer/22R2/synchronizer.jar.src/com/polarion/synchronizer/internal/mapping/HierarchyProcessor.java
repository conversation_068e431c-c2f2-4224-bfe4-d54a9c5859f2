/*     */ package com.polarion.synchronizer.internal.mapping;
/*     */ 
/*     */ import com.polarion.core.util.ObjectUtils;
/*     */ import com.polarion.synchronizer.IBaselineProvider;
/*     */ import com.polarion.synchronizer.IConflictLog;
/*     */ import com.polarion.synchronizer.IConnectionMap;
/*     */ import com.polarion.synchronizer.ILogger;
/*     */ import com.polarion.synchronizer.ISynchronizationContext;
/*     */ import com.polarion.synchronizer.configuration.IAttributeMapper;
/*     */ import com.polarion.synchronizer.internal.hierarchy.ItemTree;
/*     */ import com.polarion.synchronizer.model.CreateResult;
/*     */ import com.polarion.synchronizer.model.Direction;
/*     */ import com.polarion.synchronizer.model.Hierarchy;
/*     */ import com.polarion.synchronizer.model.ItemKey;
/*     */ import com.polarion.synchronizer.model.Side;
/*     */ import com.polarion.synchronizer.model.TransferItem;
/*     */ import com.polarion.synchronizer.model.UpdateResult;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Collection;
/*     */ import java.util.HashMap;
/*     */ import java.util.LinkedHashMap;
/*     */ import java.util.LinkedList;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ import org.jetbrains.annotations.Nullable;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HierarchyProcessor
/*     */ {
/*     */   @NotNull
/*  36 */   private final LinkedHashMap<ItemKey, TransferItem> leftItemMap = new LinkedHashMap<>();
/*     */   @NotNull
/*  38 */   private final LinkedHashMap<ItemKey, TransferItem> rightItemMap = new LinkedHashMap<>();
/*     */   
/*     */   @NotNull
/*  41 */   private final Map<String, Collection<IAttributeMapper.UnidirectionalResult>> leftForeignParentItems = new HashMap<>();
/*     */   @NotNull
/*  43 */   private final Map<String, Collection<IAttributeMapper.UnidirectionalResult>> rightForeignParentItems = new HashMap<>();
/*     */ 
/*     */   
/*     */   private final ItemTree leftTree;
/*     */ 
/*     */   
/*     */   private final ItemTree rightTree;
/*     */   
/*     */   private final ItemTree primaryMergedTree;
/*     */   
/*     */   private final ItemTree secondaryMergedTree;
/*     */   
/*     */   private final Direction direction;
/*     */   
/*     */   private final Direction primaryDirection;
/*     */   
/*     */   private final boolean isDisabled;
/*     */   
/*     */   private final boolean isBidirectional;
/*     */   
/*     */   @NotNull
/*     */   private final IConnectionMap connectionMap;
/*     */   
/*     */   @NotNull
/*     */   private final ILogger logger;
/*     */   
/*     */   @NotNull
/*     */   private final IConflictLog conflictLog;
/*     */ 
/*     */   
/*     */   public HierarchyProcessor(@NotNull Collection<TransferItem> leftItems, @NotNull Collection<TransferItem> rightItems, @Nullable Direction direction, @Nullable Direction primaryDirection, @NotNull ISynchronizationContext context) {
/*  74 */     this.direction = direction;
/*     */     
/*  76 */     this.primaryDirection = primaryDirection;
/*     */     
/*  78 */     this.connectionMap = context.getConnectionMap();
/*  79 */     this.logger = context.getLogger();
/*  80 */     this.conflictLog = context.getConflictLog();
/*     */     
/*  82 */     this.isDisabled = (direction == null);
/*     */     
/*  84 */     this.isBidirectional = (direction == Direction.BIDIRECTIONAL);
/*     */     
/*  86 */     if (this.isDisabled) {
/*  87 */       this.leftTree = null;
/*  88 */       this.rightTree = null;
/*  89 */       this.primaryMergedTree = null;
/*  90 */       this.secondaryMergedTree = null;
/*     */     } else {
/*  92 */       this.leftTree = new ItemTree(leftItems, this.logger);
/*  93 */       this.rightTree = new ItemTree(rightItems, this.logger);
/*  94 */       loadMap(leftItems, this.leftItemMap);
/*  95 */       loadMap(rightItems, this.rightItemMap);
/*     */       
/*  97 */       if (this.isBidirectional) {
/*  98 */         if (primaryDirection == Direction.BIDIRECTIONAL) {
/*  99 */           throw new IllegalArgumentException("primaryDirection can't be BIDIRECTIONAL.");
/*     */         }
/*     */         
/* 102 */         Side primarySource = (primaryDirection == Direction.LEFT_TO_RIGHT) ? Side.LEFT : Side.RIGHT;
/* 103 */         Side secondarySource = primarySource.getOtherSide();
/*     */         
/* 105 */         ItemTree primarySourceTree = (primarySource == Side.LEFT) ? this.leftTree : this.rightTree;
/* 106 */         ItemTree secondarySourceTree = (secondarySource == Side.LEFT) ? this.leftTree : this.rightTree;
/*     */         
/* 108 */         this.logger.debug("Marking changes in primary tree.");
/* 109 */         markChanged(primarySourceTree, primarySource, context.getBaselineProvider());
/* 110 */         this.logger.debug("Marking changes in secondary tree.");
/* 111 */         markChanged(secondarySourceTree, primarySource.getOtherSide(), context.getBaselineProvider());
/*     */         
/* 113 */         this.primaryMergedTree = new ItemTree(primarySourceTree);
/* 114 */         this.secondaryMergedTree = new ItemTree(this.primaryMergedTree);
/*     */         
/* 116 */         insertSecondaryChanges(secondarySourceTree.getRoot(), secondarySource);
/*     */       } else {
/*     */         
/* 119 */         this.primaryMergedTree = (direction == Direction.LEFT_TO_RIGHT) ? this.leftTree : this.rightTree;
/* 120 */         this.secondaryMergedTree = null;
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   private void insertSecondaryChanges(ItemTree.Node root, Side secondarySource) {
/* 127 */     String parentId = root.getId();
/* 128 */     ItemKey targetParentId = (parentId == null) ? null : this.connectionMap.getTargetKey(parentId, secondarySource);
/*     */     
/* 130 */     ItemTree.Node primaryParent = this.primaryMergedTree.getNode(targetParentId);
/*     */     
/* 132 */     List<ItemTree.Node> children = root.getChildren();
/* 133 */     for (int position = 0; position < children.size(); position++) {
/*     */       
/* 135 */       ItemTree.Node node = children.get(position);
/*     */       
/* 137 */       if (node == null) {
/* 138 */         if (primaryParent.areChildrenModified()) {
/* 139 */           if (primaryParent.countUnknownChildren() == root.countUnknownChildren()) {
/* 140 */             this.logger.debug("Same unknwon item count for " + primaryParent + " and " + root);
/*     */           } else {
/* 142 */             this.logger.debug("Removing unknown items for " + primaryParent + " and " + root);
/* 143 */             primaryParent.removeUnknownChildren();
/* 144 */             root.removeUnknownChildren();
/*     */           } 
/*     */         } else {
/*     */           
/* 148 */           this.logger.debug("Unknown item reinsertesd: " + primaryParent);
/* 149 */           primaryParent.addNode(null, position);
/*     */         }
/*     */       
/*     */       }
/*     */       else {
/*     */         
/* 155 */         if (node.isModified()) {
/*     */           
/* 157 */           ItemTree.Node targetNode = getTargetNode(node, this.primaryMergedTree, secondarySource);
/*     */           
/* 159 */           if (targetNode == null) {
/* 160 */             this.logger.error("Target node for update " + node.getKey() + " not found, update will be ignored.");
/*     */           } else {
/*     */             
/* 163 */             ItemKey leftId = (secondarySource == Side.LEFT) ? node.getKey() : targetNode.getKey();
/* 164 */             ItemKey rightId = (secondarySource == Side.RIGHT) ? node.getKey() : targetNode.getKey();
/*     */             
/* 166 */             this.conflictLog.current(leftId.toString(), rightId.toString());
/*     */             
/* 168 */             if (!primaryParent.addNode(targetNode, position)) {
/* 169 */               this.conflictLog.reportConflict("hierarchy", "hierarchy", secondarySource.getOtherSide());
/*     */             }
/*     */             
/* 172 */             ItemTree.Node secondaryTargetParent = this.secondaryMergedTree.getNode(targetParentId);
/* 173 */             ItemTree.Node secondaryTargetNode = getTargetNode(node, this.secondaryMergedTree, secondarySource);
/* 174 */             secondaryTargetParent.addNode(secondaryTargetNode, position);
/*     */           } 
/*     */         } 
/*     */ 
/*     */         
/* 179 */         insertSecondaryChanges(node, secondarySource);
/*     */       } 
/*     */     } 
/*     */   } private ItemTree.Node getTargetNode(ItemTree.Node sourceNode, ItemTree tree, Side secondarySource) {
/*     */     ItemTree.Node targetNode;
/* 184 */     String sourceId = sourceNode.getId();
/*     */ 
/*     */     
/* 187 */     if (sourceId == null) {
/* 188 */       targetNode = tree.getNode(null);
/*     */     } else {
/* 190 */       ItemKey targetKey = this.connectionMap.getTargetKey(sourceId, secondarySource);
/* 191 */       if (targetKey.isForeign) {
/* 192 */         targetNode = tree.createForeignNode(sourceId);
/*     */       } else {
/* 194 */         targetNode = tree.getNode(targetKey);
/*     */       } 
/*     */     } 
/*     */     
/* 198 */     return targetNode;
/*     */   }
/*     */   
/*     */   private void markChanged(ItemTree tree, Side primarySource, IBaselineProvider baselineProvider) {
/* 202 */     for (ItemTree.Node node : tree.getAllNodes()) {
/* 203 */       if (node.getId() != null) {
/* 204 */         boolean isModified; Map<String, Object> baseline = baselineProvider.loadBaseline(node.getId(), primarySource);
/*     */         
/* 206 */         Hierarchy baselineHierarchy = null;
/* 207 */         if (baseline != null) {
/* 208 */           Object baselineObj = baseline.get("hierarchy");
/* 209 */           if (baselineObj instanceof Hierarchy) {
/* 210 */             baselineHierarchy = (Hierarchy)baselineObj;
/*     */           }
/*     */         } 
/*     */ 
/*     */ 
/*     */         
/* 216 */         if (baselineHierarchy == null) {
/* 217 */           isModified = true;
/*     */         } else {
/* 219 */           ItemKey parentKey = baselineHierarchy.getParentKey();
/* 220 */           if (parentKey != null && parentKey.isForeign) {
/* 221 */             this.logger.error("Found foreign parent key in baseline for item " + node.getId() + ".");
/*     */           }
/*     */           
/* 224 */           boolean positionChanged = (baselineHierarchy.getPosition() != node.getPosition());
/*     */           
/* 226 */           String parentId = (node.getParent() == null) ? null : node.getParent().getId();
/* 227 */           boolean parentChanged = !ObjectUtils.equalsWithNull(baselineHierarchy.getParentItemId(), parentId);
/* 228 */           isModified = !(!parentChanged && !positionChanged);
/*     */           
/* 230 */           if (positionChanged) {
/* 231 */             this.logger.debug("Position: " + baselineHierarchy.getPosition());
/* 232 */             this.logger.debug("Baseline Position: " + node.getPosition());
/*     */           } 
/* 234 */           if (parentChanged) {
/* 235 */             this.logger.debug("Parent: " + baselineHierarchy.getParentItemId());
/* 236 */             this.logger.debug("Baseline Parent: " + parentId);
/*     */           } 
/*     */         } 
/*     */ 
/*     */         
/* 241 */         if (isModified) {
/* 242 */           this.logger.debug("Modified: " + node);
/* 243 */           node.markModified();
/*     */         } 
/*     */       } 
/*     */     } 
/*     */   }
/*     */   
/*     */   private void loadMap(Collection<TransferItem> items, Map<ItemKey, TransferItem> target) {
/* 250 */     for (TransferItem item : items) {
/* 251 */       target.put(item.getKey(), item);
/*     */     }
/*     */   }
/*     */ 
/*     */   
/*     */   public List<IAttributeMapper.UnidirectionalResult> insertHierarchyUpdates(Collection<IAttributeMapper.UnidirectionalResult> updates, Side forSide) {
/* 257 */     this.logger.debug("Inserting hierarchy for " + forSide);
/* 258 */     Map<ItemKey, IAttributeMapper.UnidirectionalResult> updateMap = loadUpdateMap(updates);
/* 259 */     List<IAttributeMapper.UnidirectionalResult> result = new ArrayList<>(updates.size());
/* 260 */     if (!this.isDisabled && this.direction.isTo(forSide)) {
/* 261 */       insertHierarchyUpdates(forSide, updateMap, result);
/*     */     }
/* 263 */     result.addAll(updateMap.values());
/* 264 */     return result;
/*     */   }
/*     */   
/*     */   private void insertHierarchyUpdates(@NotNull Side forSide, @NotNull Map<ItemKey, IAttributeMapper.UnidirectionalResult> updateMap, @NotNull List<IAttributeMapper.UnidirectionalResult> result) {
/* 268 */     boolean isSecondaryDirection = (this.isBidirectional && this.primaryDirection.isFrom(forSide));
/*     */     
/* 270 */     ItemTree sourceTree = isSecondaryDirection ? this.secondaryMergedTree : this.primaryMergedTree;
/* 271 */     cleanUnknownItems(sourceTree, forSide, isSecondaryDirection);
/*     */     
/* 273 */     if (this.direction.isTo(forSide)) {
/* 274 */       for (ItemTree.Node sourceNode : sourceTree.getAllNodes()) {
/*     */         
/* 276 */         if (sourceNode.getId() == null) {
/*     */           continue;
/*     */         }
/* 279 */         insertHierarchyUpdate(sourceNode, forSide, isSecondaryDirection, updateMap);
/*     */       } 
/*     */       
/* 282 */       collectUpdates(forSide.getOtherSide(), updateMap, result, sourceTree.getRoot(), isSecondaryDirection);
/* 283 */       logDebugInformation(updateMap, result);
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   private void insertHierarchyUpdate(@NotNull ItemTree.Node sourceNode, @NotNull Side forSide, boolean isSecondaryDirection, @NotNull Map<ItemKey, IAttributeMapper.UnidirectionalResult> updateMap) {
/* 289 */     ItemKey targetKey = getKeyForSide(sourceNode.getKey(), forSide.getOtherSide(), isSecondaryDirection);
/* 290 */     ItemKey sourceKey = getKeyForSide(sourceNode.getKey(), forSide, !isSecondaryDirection);
/* 291 */     ItemKey sourceParentKey = sourceNode.getParent().getKey();
/* 292 */     ItemKey targetParentKey = (sourceParentKey == null) ? null : getKeyForSide(sourceParentKey, forSide.getOtherSide(), isSecondaryDirection);
/*     */ 
/*     */     
/* 295 */     Hierarchy updateHierarchy = getUpdateHierarchy(sourceNode, targetParentKey);
/*     */     
/* 297 */     Hierarchy targetHierarchy = getTargetHierarchy(forSide, targetKey);
/*     */     
/* 299 */     boolean hierarchyUpdated = !ObjectUtils.equalsWithNull(targetHierarchy, updateHierarchy);
/* 300 */     IAttributeMapper.UnidirectionalResult unidirectionlResult = updateMap.get(targetKey);
/* 301 */     boolean isSourceNew = sourceKey.isForeign;
/*     */     
/* 303 */     if (isSourceNew) {
/* 304 */       if (unidirectionlResult != null) {
/* 305 */         this.logger.error("Unexpected: Found mapped data for new item " + sourceKey);
/*     */       } else {
/*     */         
/* 308 */         if (hierarchyUpdated && isSecondaryDirection) {
/* 309 */           this.logger.error("Unexpected: Item " + targetKey + " created on primary side was updated from secondary side.");
/*     */         }
/*     */         
/* 312 */         this.logger.debug("Creating mapped data for " + targetKey + ".");
/*     */         
/* 314 */         if (targetKey.isForeign) {
/* 315 */           this.logger.error("Unexpected: Target " + targetKey + " and source " + sourceKey + "are foreign items.");
/*     */         }
/*     */         
/* 318 */         if (hierarchyUpdated) {
/* 319 */           unidirectionlResult = createUpdateForNew(targetKey, updateMap);
/*     */         }
/*     */       } 
/* 322 */     } else if (unidirectionlResult == null) {
/* 323 */       unidirectionlResult = createUpdateForExisting(sourceKey, targetKey, updateMap);
/*     */     } 
/*     */     
/* 326 */     if (!isSourceNew) {
/* 327 */       updateSourceBaseline(unidirectionlResult, updateHierarchy, forSide);
/*     */     }
/*     */     
/* 330 */     if (hierarchyUpdated && unidirectionlResult != null) {
/* 331 */       this.logger.debug(String.format("Hierarchy of %s was changed from %s to %s.", new Object[] {
/* 332 */               targetKey, targetHierarchy, updateHierarchy
/*     */             }));
/* 334 */       trackForeignParent(targetParentKey, unidirectionlResult, forSide);
/* 335 */       unidirectionlResult.getMapped().setHierarchy(updateHierarchy);
/*     */     } 
/*     */   }
/*     */   
/*     */   private void logDebugInformation(@NotNull Map<ItemKey, IAttributeMapper.UnidirectionalResult> updateMap, @NotNull List<IAttributeMapper.UnidirectionalResult> result) {
/* 340 */     if (this.logger.isDebugEnabled()) {
/* 341 */       this.logger.debug("Hierarchy Updates: " + result);
/* 342 */       this.logger.debug("Updates w/o Hierarchy: " + updateMap.values());
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   private IAttributeMapper.UnidirectionalResult createUpdateForExisting(@NotNull ItemKey sourceKey, @NotNull ItemKey targetKey, @NotNull Map<ItemKey, IAttributeMapper.UnidirectionalResult> updateMap) {
/* 348 */     this.logger.error("Unexpected: Unable to find mapped data for " + sourceKey + " creating mapped data.");
/*     */     
/* 350 */     TransferItem source = new TransferItem(sourceKey.id);
/* 351 */     IAttributeMapper.UnidirectionalResult unidirectionlResult = new UnidirectionalResultImpl(source, new TransferItem(targetKey.id, targetKey.isForeign), source);
/*     */     
/* 353 */     updateMap.put(targetKey, unidirectionlResult);
/* 354 */     return unidirectionlResult;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   private IAttributeMapper.UnidirectionalResult createUpdateForNew(@NotNull ItemKey targetKey, @NotNull Map<ItemKey, IAttributeMapper.UnidirectionalResult> updateMap) {
/* 361 */     TransferItem source = new TransferItem(null);
/* 362 */     IAttributeMapper.UnidirectionalResult unidirectionlResult = new UnidirectionalResultImpl(source, new TransferItem(targetKey.id, false), source);
/* 363 */     updateMap.put(targetKey, unidirectionlResult);
/* 364 */     return unidirectionlResult;
/*     */   }
/*     */   
/*     */   private void updateSourceBaseline(IAttributeMapper.UnidirectionalResult unidirectionlResult, Hierarchy update, Side forSide) {
/*     */     Hierarchy sourceHierarchy;
/* 369 */     ItemKey targetParentKey = update.getParentKey();
/* 370 */     if (targetParentKey == null) {
/* 371 */       sourceHierarchy = new Hierarchy(null, update.getPosition());
/*     */     } else {
/* 373 */       sourceHierarchy = 
/* 374 */         new Hierarchy((getKeyForSide(targetParentKey, forSide, false)).id, update.getPosition());
/*     */     } 
/*     */     
/* 377 */     unidirectionlResult.getSourceBaseline().setHierarchy(sourceHierarchy);
/*     */   }
/*     */   
/*     */   private Hierarchy getUpdateHierarchy(ItemTree.Node sourceNode, ItemKey targetParentKey) {
/* 381 */     Hierarchy updateHierarchy = Hierarchy.withParentKey(targetParentKey, sourceNode.getPosition());
/* 382 */     return updateHierarchy;
/*     */   }
/*     */   
/*     */   private Hierarchy getTargetHierarchy(Side forSide, ItemKey targetKey) {
/* 386 */     Map<ItemKey, TransferItem> targetItemMap = (forSide == Side.LEFT) ? this.leftItemMap : this.rightItemMap;
/* 387 */     TransferItem targetItem = targetItemMap.get(targetKey);
/* 388 */     if (targetItem == null) {
/* 389 */       if (!targetKey.isForeign) {
/* 390 */         this.logger.error("Missing target item found for key " + targetKey);
/*     */       }
/* 392 */       return null;
/*     */     } 
/* 394 */     return (Hierarchy)targetItem.getValue("hierarchy");
/*     */   }
/*     */ 
/*     */   
/*     */   private void cleanUnknownItems(ItemTree sourceTree, Side forSide, boolean isSecondaryDirection) {
/* 399 */     ItemTree targetTree = (forSide == Side.LEFT) ? this.leftTree : this.rightTree;
/*     */     
/* 401 */     for (ItemTree.Node targetNode : targetTree.getAllNodes()) {
/*     */       ItemKey sourceKey;
/* 403 */       if (targetNode.getId() == null) {
/* 404 */         sourceKey = null;
/*     */       } else {
/* 406 */         sourceKey = getKeyForSide(targetNode.getKey(), forSide, isSecondaryDirection);
/*     */       } 
/*     */ 
/*     */       
/* 410 */       ItemTree.Node sourceNode = sourceTree.getNode(sourceKey);
/*     */       
/* 412 */       if (sourceNode == null) {
/* 413 */         this.logger.debug("No item found for in source tree for target item " + targetNode.getKey());
/*     */         
/*     */         continue;
/*     */       } 
/* 417 */       if (sourceNode.countUnknownChildren() != targetNode.countUnknownChildren()) {
/* 418 */         this.logger.debug("Removing unknown from " + sourceNode);
/* 419 */         sourceNode.removeUnknownChildren();
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   private void trackForeignParent(ItemKey targetParentKey, IAttributeMapper.UnidirectionalResult unidirectionlResult, Side forSide) {
/* 426 */     if (targetParentKey != null && targetParentKey.isForeign) {
/* 427 */       Map<String, Collection<IAttributeMapper.UnidirectionalResult>> foreignParentItemsMap = getForeignParentItems(forSide);
/* 428 */       Collection<IAttributeMapper.UnidirectionalResult> childItems = foreignParentItemsMap.get(targetParentKey.id);
/* 429 */       if (childItems == null) {
/* 430 */         childItems = new LinkedList<>();
/* 431 */         foreignParentItemsMap.put(targetParentKey.id, childItems);
/*     */       } 
/* 433 */       childItems.add(unidirectionlResult);
/*     */     } 
/*     */   }
/*     */   
/*     */   private Map<ItemKey, IAttributeMapper.UnidirectionalResult> loadUpdateMap(Collection<IAttributeMapper.UnidirectionalResult> updates) {
/* 438 */     Map<ItemKey, IAttributeMapper.UnidirectionalResult> updateMap = new LinkedHashMap<>();
/* 439 */     for (IAttributeMapper.UnidirectionalResult unidirectionlResult : updates) {
/* 440 */       TransferItem item = unidirectionlResult.getMapped();
/* 441 */       updateMap.put(item.getKey(), unidirectionlResult);
/*     */     } 
/* 443 */     return updateMap;
/*     */   }
/*     */   
/*     */   private void collectUpdates(Side sourceSide, Map<ItemKey, IAttributeMapper.UnidirectionalResult> updateMap, List<IAttributeMapper.UnidirectionalResult> sortedResult, ItemTree.Node root, boolean isSecodaryTree) {
/* 447 */     if (root == null) {
/* 448 */       this.logger.debug("Keep unknonw item.");
/*     */       return;
/*     */     } 
/* 451 */     for (ItemTree.Node node : root.getChildren()) {
/* 452 */       if (node == null) {
/* 453 */         this.logger.debug("Null node: " + root);
/*     */         continue;
/*     */       } 
/* 456 */       ItemKey targetItemKey = getKeyForSide(node.getKey(), sourceSide, isSecodaryTree);
/* 457 */       IAttributeMapper.UnidirectionalResult update = updateMap.get(targetItemKey);
/*     */       
/* 459 */       if (update == null) {
/* 460 */         this.logger.debug(String.format("Found item %s in tree but not in list of updates. Could be a new item.", new Object[] { targetItemKey })); continue;
/*     */       } 
/* 462 */       if (update.getMapped().getHierarchy() != null) {
/* 463 */         sortedResult.add(update);
/* 464 */         updateMap.remove(targetItemKey);
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/* 469 */     for (ItemTree.Node node : root.getChildren()) {
/* 470 */       collectUpdates(sourceSide, updateMap, sortedResult, node, isSecodaryTree);
/*     */     }
/*     */   }
/*     */   
/*     */   private ItemKey getKeyForSide(ItemKey sourceKey, Side sourceSide, boolean isSameSide) {
/*     */     ItemKey targetKey;
/* 476 */     if (isSameSide) {
/* 477 */       targetKey = sourceKey;
/*     */     }
/* 479 */     else if (sourceKey.isForeign) {
/* 480 */       targetKey = new ItemKey(sourceKey.id, false);
/*     */     } else {
/* 482 */       targetKey = this.connectionMap.getTargetKey(sourceKey.id, sourceSide);
/*     */     } 
/*     */     
/* 485 */     return targetKey;
/*     */   }
/*     */   
/*     */   public void handleResult(String sourceId, UpdateResult result, Side targetSide) {
/* 489 */     if (sourceId == null) {
/*     */       return;
/*     */     }
/*     */     
/* 493 */     if (result instanceof CreateResult) {
/* 494 */       CreateResult createResult = (CreateResult)result;
/* 495 */       String createdId = createResult.getCreatedId();
/* 496 */       if (createdId != null) {
/* 497 */         Map<String, Collection<IAttributeMapper.UnidirectionalResult>> foreignParentItems = getForeignParentItems(targetSide);
/* 498 */         Collection<IAttributeMapper.UnidirectionalResult> dependentItems = foreignParentItems.get(sourceId);
/* 499 */         if (dependentItems != null) {
/* 500 */           for (IAttributeMapper.UnidirectionalResult unidirectionlResult : dependentItems) {
/* 501 */             TransferItem item = unidirectionlResult.getMapped();
/* 502 */             Hierarchy hierarchy = item.getHierarchy();
/* 503 */             this.logger.debug("Fixing hierarchy for " + item + " new parent: " + createdId);
/* 504 */             item.setHierarchy(new Hierarchy(createdId, hierarchy.getPosition()));
/*     */           } 
/*     */         }
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   private Map<String, Collection<IAttributeMapper.UnidirectionalResult>> getForeignParentItems(Side targetSide) {
/* 514 */     Map<String, Collection<IAttributeMapper.UnidirectionalResult>> foreignParentItems = 
/* 515 */       (targetSide == Side.LEFT) ? this.leftForeignParentItems : this.rightForeignParentItems;
/* 516 */     return foreignParentItems;
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/internal/mapping/HierarchyProcessor.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */