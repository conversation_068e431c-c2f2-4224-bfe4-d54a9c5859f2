/*    */ package com.polarion.synchronizer.internal;
/*    */ 
/*    */ import com.google.inject.Inject;
/*    */ import com.google.inject.Singleton;
/*    */ import com.polarion.synchronizer.IPersistence;
/*    */ import com.polarion.synchronizer.ISynchronizationContext;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @Singleton
/*    */ public class SingletonContext
/*    */ {
/*    */   @NotNull
/*    */   private final IPersistence persistence;
/*    */   @NotNull
/*    */   private final ISynchronizationContext context;
/*    */   
/*    */   @Inject
/*    */   public SingletonContext(IPersistence persistence, ISynchronizationContext context) {
/* 41 */     this.persistence = persistence;
/* 42 */     this.context = context;
/*    */   }
/*    */   
/*    */   @NotNull
/*    */   public IPersistence getPersistence() {
/* 47 */     return this.persistence;
/*    */   }
/*    */   
/*    */   @NotNull
/*    */   public ISynchronizationContext getContext() {
/* 52 */     return this.context;
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/internal/SingletonContext.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */