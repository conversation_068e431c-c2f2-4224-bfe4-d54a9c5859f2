package com.polarion.synchronizer;

import com.polarion.synchronizer.model.TransferItem;
import java.util.Collection;
import org.jetbrains.annotations.NotNull;

public interface ISynchronizationResult {
  @NotNull
  Collection<IUpdateOperation> getUpdates();
  
  @NotNull
  Collection<TransferItem> getUnchanged();
  
  @NotNull
  Collection<TransferItem> getSourceItems();
}


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/ISynchronizationResult.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */