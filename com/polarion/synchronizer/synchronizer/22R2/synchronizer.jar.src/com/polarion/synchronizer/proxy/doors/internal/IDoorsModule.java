package com.polarion.synchronizer.proxy.doors.internal;

import com.polarion.synchronizer.proxy.doors.internal.commands.IDoorsCommand;
import org.jetbrains.annotations.NotNull;

public interface IDoorsModule {
  @NotNull
  <T> T execute(@NotNull IDoorsCommand<T> paramIDoorsCommand);
  
  @NotNull
  <T> T execute(@NotNull IDoorsCommand<T> paramIDoorsCommand, boolean paramBoolean);
  
  @NotNull
  String getModuleId();
}


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/internal/IDoorsModule.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */