/*    */ package com.polarion.synchronizer.internal.mapping;
/*    */ 
/*    */ import com.google.inject.assistedinject.Assisted;
/*    */ import com.polarion.synchronizer.mapping.ValueMapping;
/*    */ import com.polarion.synchronizer.model.Attachment;
/*    */ import com.polarion.synchronizer.model.Side;
/*    */ import com.polarion.synchronizer.spi.translators.AbstractCollectionTranslator;
/*    */ import java.util.Collection;
/*    */ import java.util.Collections;
/*    */ import javax.inject.Inject;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ import org.jetbrains.annotations.Nullable;
/*    */ 
/*    */ 
/*    */ 
/*    */ public class AttachmentTranslator
/*    */   extends AbstractCollectionTranslator<Attachment, Attachment>
/*    */ {
/*    */   @Inject
/*    */   public AttachmentTranslator(@Assisted Collection<ValueMapping> valueMappings, @Assisted Side fromSide) {
/* 21 */     super(valueMappings, fromSide);
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   protected Collection<Attachment> mapCollection(@Nullable Collection<Attachment> sourceValue, @Nullable Collection<Attachment> otherValue, boolean explode) {
/* 27 */     return (sourceValue == null) ? Collections.EMPTY_LIST : sourceValue;
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/internal/mapping/AttachmentTranslator.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */