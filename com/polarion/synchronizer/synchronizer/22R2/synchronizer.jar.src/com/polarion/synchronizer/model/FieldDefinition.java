/*     */ package com.polarion.synchronizer.model;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class FieldDefinition
/*     */ {
/*     */   private final String key;
/*     */   private final String label;
/*     */   private final String type;
/*     */   private final boolean readOnly;
/*     */   private final boolean multiValued;
/*     */   
/*     */   public String getKey() {
/*  16 */     return this.key;
/*     */   }
/*     */   
/*     */   public String getLabel() {
/*  20 */     return this.label;
/*     */   }
/*     */   
/*     */   public String getType() {
/*  24 */     return this.type;
/*     */   }
/*     */   
/*     */   public boolean isReadOnly() {
/*  28 */     return this.readOnly;
/*     */   }
/*     */   
/*     */   public boolean isMultiValued() {
/*  32 */     return this.multiValued;
/*     */   }
/*     */   
/*     */   public FieldDefinition(String key, String label, String type, boolean readOnly, boolean multiValued) {
/*  36 */     this.key = key;
/*  37 */     this.label = (label == null) ? key : label;
/*  38 */     this.type = type;
/*  39 */     this.readOnly = readOnly;
/*  40 */     this.multiValued = multiValued;
/*     */   }
/*     */ 
/*     */   
/*     */   public int hashCode() {
/*  45 */     int prime = 31;
/*  46 */     int result = 1;
/*  47 */     result = 31 * result + ((this.key == null) ? 0 : this.key.hashCode());
/*  48 */     result = 31 * result + ((this.label == null) ? 0 : this.label.hashCode());
/*  49 */     result = 31 * result + (this.multiValued ? 1231 : 1237);
/*  50 */     result = 31 * result + (this.readOnly ? 1231 : 1237);
/*  51 */     result = 31 * result + ((this.type == null) ? 0 : this.type.hashCode());
/*  52 */     return result;
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean equals(Object obj) {
/*  57 */     if (this == obj) {
/*  58 */       return true;
/*     */     }
/*  60 */     if (obj == null) {
/*  61 */       return false;
/*     */     }
/*  63 */     if (getClass() != obj.getClass()) {
/*  64 */       return false;
/*     */     }
/*  66 */     FieldDefinition other = (FieldDefinition)obj;
/*  67 */     if (this.key == null) {
/*  68 */       if (other.key != null) {
/*  69 */         return false;
/*     */       }
/*  71 */     } else if (!this.key.equals(other.key)) {
/*  72 */       return false;
/*     */     } 
/*  74 */     if (this.label == null) {
/*  75 */       if (other.label != null) {
/*  76 */         return false;
/*     */       }
/*  78 */     } else if (!this.label.equals(other.label)) {
/*  79 */       return false;
/*     */     } 
/*  81 */     if (this.multiValued != other.multiValued) {
/*  82 */       return false;
/*     */     }
/*  84 */     if (this.readOnly != other.readOnly) {
/*  85 */       return false;
/*     */     }
/*  87 */     if (this.type == null) {
/*  88 */       if (other.type != null) {
/*  89 */         return false;
/*     */       }
/*  91 */     } else if (!this.type.equals(other.type)) {
/*  92 */       return false;
/*     */     } 
/*  94 */     return true;
/*     */   }
/*     */ 
/*     */   
/*     */   public String toString() {
/*  99 */     return "FieldDefinition [key=" + this.key + ", label=" + this.label + ", type=" + 
/* 100 */       this.type + ", readOnly=" + this.readOnly + ", multiValued=" + 
/* 101 */       this.multiValued + "]";
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/model/FieldDefinition.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */