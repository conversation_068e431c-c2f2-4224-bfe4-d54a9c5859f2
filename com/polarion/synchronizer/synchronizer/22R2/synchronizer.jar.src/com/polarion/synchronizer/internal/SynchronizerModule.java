/*     */ package com.polarion.synchronizer.internal;
/*     */ 
/*     */ import com.google.inject.AbstractModule;
/*     */ import com.google.inject.Module;
/*     */ import com.google.inject.Provider;
/*     */ import com.google.inject.Provides;
/*     */ import com.google.inject.Scopes;
/*     */ import com.google.inject.TypeLiteral;
/*     */ import com.google.inject.multibindings.Multibinder;
/*     */ import com.google.inject.name.Named;
/*     */ import com.polarion.core.util.logging.Logger;
/*     */ import com.polarion.platform.cluster.ClusterService;
/*     */ import com.polarion.platform.guice.internal.hivemind.HiveMindProvider;
/*     */ import com.polarion.platform.service.repository.IRepositoryService;
/*     */ import com.polarion.synchronizer.CommonServices;
/*     */ import com.polarion.synchronizer.IAction;
/*     */ import com.polarion.synchronizer.ICommonServices;
/*     */ import com.polarion.synchronizer.IMetadataService;
/*     */ import com.polarion.synchronizer.IPersistence;
/*     */ import com.polarion.synchronizer.IPostProcessingActionFactory;
/*     */ import com.polarion.synchronizer.ISynchronizationContext;
/*     */ import com.polarion.synchronizer.ISynchronizationService;
/*     */ import com.polarion.synchronizer.TransactionalExecutor;
/*     */ import com.polarion.synchronizer.configuration.ConfigurationHelper;
/*     */ import com.polarion.synchronizer.configuration.IConfigurationHelper;
/*     */ import com.polarion.synchronizer.internal.mapping.AttachmentTranslator;
/*     */ import com.polarion.synchronizer.internal.mapping.DateOnlyTranslator;
/*     */ import com.polarion.synchronizer.internal.mapping.DateTimeTranslator;
/*     */ import com.polarion.synchronizer.internal.mapping.RelationTranslator;
/*     */ import com.polarion.synchronizer.mapping.FieldType;
/*     */ import com.polarion.synchronizer.mapping.ITranslator;
/*     */ import com.polarion.synchronizer.mapping.MappingFactory;
/*     */ import com.polarion.synchronizer.mapping.TranslatorRegistry;
/*     */ import com.polarion.synchronizer.model.Attachment;
/*     */ import com.polarion.synchronizer.model.CollectionUpdate;
/*     */ import com.polarion.synchronizer.model.Relation;
/*     */ import com.polarion.synchronizer.proxy.configuration.ConfigurationService;
/*     */ import com.polarion.synchronizer.proxy.configuration.IConfigurationService;
/*     */ import com.polarion.synchronizer.proxy.doors.internal.DoorsSyncPairService;
/*     */ import com.polarion.synchronizer.proxy.doors.internal.IDoorsSyncPairService;
/*     */ import com.polarion.synchronizer.proxy.doors.internal.TypeAttributeExtension;
/*     */ import com.polarion.synchronizer.proxy.doors.internal.tables.DoorsPostProcessingActionFactory;
/*     */ import com.polarion.synchronizer.proxy.doors.internal.tables.ITableBuilder;
/*     */ import com.polarion.synchronizer.proxy.doors.internal.tables.TableBuilder;
/*     */ import com.polarion.synchronizer.spi.IProxyExtension;
/*     */ import com.polarion.synchronizer.spi.ProxyBinder;
/*     */ import com.polarion.synchronizer.spi.translators.CollectionTranslatorModule;
/*     */ import com.polarion.synchronizer.spi.translators.MultiToSingleTranslator;
/*     */ import com.polarion.synchronizer.spi.translators.PassThroughTranslator;
/*     */ import com.polarion.synchronizer.spi.translators.RichTextToRichTextTranslator;
/*     */ import com.polarion.synchronizer.spi.translators.RichTextToTextTranslator;
/*     */ import com.polarion.synchronizer.spi.translators.SingleToMultiTranslator;
/*     */ import com.polarion.synchronizer.spi.translators.StringCollectionTranslator;
/*     */ import com.polarion.synchronizer.spi.translators.StringValueTranslator;
/*     */ import com.polarion.synchronizer.spi.translators.TextToRichTextTranslator;
/*     */ import com.polarion.synchronizer.spi.translators.TextToStringTranslator;
/*     */ import com.polarion.synchronizer.spi.translators.TranslatorModule;
/*     */ import com.polarion.synchronizer.spi.translators.TrimTranslator;
/*     */ import java.io.File;
/*     */ import java.io.FileFilter;
/*     */ import java.io.IOException;
/*     */ import java.util.Date;
/*     */ import org.apache.commons.io.FileUtils;
/*     */ import org.apache.commons.io.filefilter.DirectoryFileFilter;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ import org.jetbrains.annotations.Nullable;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class SynchronizerModule
/*     */   extends AbstractModule
/*     */ {
/*     */   private static final String TYPE_ID_STRING = "synchronizer:idstring";
/*  95 */   private static final Logger logger = Logger.getLogger(SynchronizerModule.class);
/*     */ 
/*     */   
/*     */   protected void configure() {
/*  99 */     bind(ISynchronizationService.class).to(SynchronizationService.class);
/* 100 */     bind(TransactionalExecutor.class);
/*     */     
/* 102 */     bind(ThreadSynchronizationContext.class).in(Scopes.SINGLETON);
/* 103 */     bind(ISynchronizationContext.class).to(ThreadSynchronizationContext.class);
/*     */     
/* 105 */     bind(ObjectMapPersistence.class).in(Scopes.SINGLETON);
/* 106 */     bind(IPersistence.class).to(ObjectMapPersistence.class);
/*     */     
/* 108 */     bind(SingletonContext.class);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 116 */     install((Module)new TranslatorModule<String>("synchronizer:idstring", "synchronizer:idstring", StringValueTranslator.class) {
/*     */         
/*     */         });
/* 119 */     install((Module)new TranslatorModule<Date>("date-time", "date-time", DateTimeTranslator.class) {  }
/*     */       );
/* 121 */     install((Module)new TranslatorModule<Date>("date-time", "date", DateOnlyTranslator.class) {
/*     */         
/*     */         });
/* 124 */     install((Module)new TranslatorModule<Date>("date", "date", DateOnlyTranslator.class) {  }
/*     */       );
/* 126 */     install((Module)new TranslatorModule<Date>("date", "date-time", DateOnlyTranslator.class) {
/*     */         
/*     */         });
/* 129 */     install((Module)new CollectionTranslatorModule<String>("synchronizer:idstring", "synchronizer:idstring", StringCollectionTranslator.class) {
/*     */         
/*     */         });
/* 132 */     install((Module)new TranslatorModule<String>("synchronizer:idstring", "synchronizer:idstring", MultiToSingleTranslator.class, true, false) {  }
/*     */       );
/* 134 */     install((Module)new TranslatorModule<CollectionUpdate<String>>("synchronizer:idstring", "synchronizer:idstring", SingleToMultiTranslator.class, false, true) {
/*     */         
/*     */         });
/* 137 */     install((Module)new CollectionTranslatorModule<Relation>("relation", "relation", RelationTranslator.class) {  }
/*     */       );
/* 139 */     install((Module)new CollectionTranslatorModule<Attachment>("attachment", "attachment", AttachmentTranslator.class) {
/*     */         
/*     */         });
/* 142 */     install((Module)new TranslatorModule<String>("rich-text", "text", RichTextToTextTranslator.class) {  }
/*     */       );
/* 144 */     install((Module)new TranslatorModule<String>("text", "rich-text", TextToRichTextTranslator.class) {
/*     */         
/*     */         });
/* 147 */     install((Module)new TranslatorModule<String>("text", "string", TextToStringTranslator.class) {  }
/*     */       );
/* 149 */     install((Module)new TranslatorModule<String>("string", "text", TrimTranslator.class) {
/*     */         
/*     */         });
/* 152 */     install((Module)new TranslatorModule<String>("unknown", "text", TrimTranslator.class) {
/*     */         
/*     */         });
/* 155 */     install((Module)new TranslatorModule<String>("rich-text", "rich-text", RichTextToRichTextTranslator.class) {
/*     */         
/*     */         });
/* 158 */     install((Module)new TranslatorModule<Object>("boolean", "boolean", PassThroughTranslator.class) {
/*     */         
/*     */         });
/* 161 */     Multibinder<TranslatorRegistry.CompatibleType> compatibleTypesBinder = Multibinder.newSetBinder(binder(), TranslatorRegistry.CompatibleType.class);
/*     */     
/* 163 */     registerReplaceableType(compatibleTypesBinder, "string", "synchronizer:idstring", false);
/* 164 */     registerReplaceableType(compatibleTypesBinder, "option", "synchronizer:idstring", false);
/* 165 */     registerReplaceableType(compatibleTypesBinder, "user", "synchronizer:idstring", false);
/*     */     
/* 167 */     registerReplaceableType(compatibleTypesBinder, "string", "synchronizer:idstring", true);
/* 168 */     registerReplaceableType(compatibleTypesBinder, "option", "synchronizer:idstring", true);
/* 169 */     registerReplaceableType(compatibleTypesBinder, "user", "synchronizer:idstring", true);
/*     */     
/* 171 */     bind(TranslatorRegistry.class);
/*     */     
/* 173 */     bind(MappingFactory.class);
/*     */     
/* 175 */     bind(IRepositoryService.class).toProvider((Provider)new HiveMindProvider(IRepositoryService.class));
/*     */     
/* 177 */     bind(IMetadataService.class).to(MetadataService.class);
/*     */     
/* 179 */     Multibinder.newSetBinder(binder(), new TypeLiteral<Class<? extends IAction>>()
/*     */         {
/*     */         
/*     */         });
/* 183 */     ProxyBinder.prepareBinding(binder());
/*     */     
/* 185 */     bind(IConfigurationService.class).to(ConfigurationService.class);
/*     */     
/* 187 */     bind(ICommonServices.class).to(CommonServices.class);
/*     */     
/* 189 */     bind(ITableBuilder.class).to(TableBuilder.class);
/*     */     
/* 191 */     Multibinder<IPostProcessingActionFactory> postProcessingFactories = Multibinder.newSetBinder(binder(), IPostProcessingActionFactory.class);
/* 192 */     postProcessingFactories.addBinding().to(DoorsPostProcessingActionFactory.class);
/*     */     
/* 194 */     bind(IConfigurationHelper.class).to(ConfigurationHelper.class);
/*     */     
/* 196 */     bind(IDoorsSyncPairService.class).to(DoorsSyncPairService.class);
/*     */     
/* 198 */     Multibinder<IProxyExtension> proxyExtensions = Multibinder.newSetBinder(binder(), IProxyExtension.class);
/* 199 */     proxyExtensions.addBinding().to(TypeAttributeExtension.class);
/*     */   }
/*     */   
/*     */   private void registerReplaceableType(Multibinder<TranslatorRegistry.CompatibleType> compatibleTypesBinder, String typeA, String typeB, boolean multi) {
/* 203 */     compatibleTypesBinder.addBinding().toInstance(new TranslatorRegistry.CompatibleType(new FieldType(typeA, multi), new FieldType(typeB, multi)));
/*     */   }
/*     */ 
/*     */   
/*     */   @Provides
/*     */   @Named("persistenceFolder")
/*     */   public File getPersistenceFolder(@NotNull ClusterService clusterService) {
/* 210 */     String sharedDir = System.getProperty("com.polarion.shared");
/* 211 */     File persistenceFolder = (sharedDir == null || sharedDir.isEmpty()) ? new File(String.valueOf(System.getProperty("com.polarion.data")) + "/synchronizer") : 
/* 212 */       new File(String.valueOf(sharedDir) + "/data/synchronizer");
/*     */ 
/*     */     
/* 215 */     File brokenFolder = new File("null/data/synchronizer");
/* 216 */     if (brokenFolder.exists()) {
/* 217 */       if (clusterService.isCluster()) {
/* 218 */         logger.info("Found broken connector persistence folder created by DPP-79896. Folder will be moved to correct location.");
/* 219 */         recover(persistenceFolder, brokenFolder);
/*     */       } else {
/* 221 */         logger.info("Found broken connector persistence folder created by DPP-79896  " + brokenFolder.getAbsolutePath() + 
/* 222 */             ". Folder is not in use according to current settings, no recovery will be performed.");
/*     */       } 
/*     */     }
/*     */     
/* 226 */     return persistenceFolder;
/*     */   }
/*     */ 
/*     */   
/*     */   private void recover(@NotNull File persistenceFolder, @NotNull File brokenFolder) {
/* 231 */     File backup = backupPersistenceFolder(persistenceFolder);
/* 232 */     recoverFromBrokenFolder(persistenceFolder, brokenFolder);
/* 233 */     if (backup != null)
/*     */       try {
/* 235 */         mergeFromBackup(persistenceFolder, backup);
/* 236 */       } catch (IOException e) {
/* 237 */         throw new RuntimeException("Unexpected error merging connector persistence after recovery of broken folder.", e);
/*     */       }  
/*     */   }
/*     */   private void mergeFromBackup(@NotNull File persistenceFolder, @NotNull File backup) throws IOException { byte b;
/*     */     int i;
/*     */     File[] arrayOfFile;
/* 243 */     for (i = (arrayOfFile = backup.listFiles((FileFilter)DirectoryFileFilter.INSTANCE)).length, b = 0; b < i; ) { File projectDirectory = arrayOfFile[b];
/* 244 */       File targetProjectDirectory = new File(persistenceFolder, projectDirectory.getName());
/* 245 */       if (targetProjectDirectory.exists()) {
/* 246 */         mergeConnections(projectDirectory, targetProjectDirectory);
/*     */       } else {
/* 248 */         FileUtils.copyDirectory(projectDirectory, targetProjectDirectory);
/*     */       } 
/*     */       b++; }
/*     */      } private void mergeConnections(@NotNull File projectDirectory, @NotNull File targetProjectDirectory) throws IOException { byte b;
/*     */     int i;
/*     */     File[] arrayOfFile;
/* 254 */     for (i = (arrayOfFile = projectDirectory.listFiles((FileFilter)DirectoryFileFilter.INSTANCE)).length, b = 0; b < i; ) { File connectionDirectory = arrayOfFile[b];
/* 255 */       File targetConnectionDirectory = new File(targetProjectDirectory, connectionDirectory.getName());
/* 256 */       if (!targetConnectionDirectory.exists())
/* 257 */         FileUtils.copyDirectory(connectionDirectory, targetConnectionDirectory); 
/*     */       b++; }
/*     */      }
/*     */ 
/*     */   
/*     */   private void recoverFromBrokenFolder(@NotNull File persistenceFolder, @NotNull File brokenFile) {
/*     */     try {
/* 264 */       FileUtils.moveDirectory(brokenFile, persistenceFolder);
/* 265 */     } catch (IOException e) {
/* 266 */       throw new RuntimeException("Unexpected error restoring connector persistence.", e);
/*     */     } 
/*     */   }
/*     */   
/*     */   @Nullable
/*     */   private File backupPersistenceFolder(@NotNull File persistenceFolder) {
/* 272 */     File backupDestination = null;
/*     */     
/* 274 */     if (persistenceFolder.exists()) {
/* 275 */       backupDestination = new File(persistenceFolder.getParentFile(), "synchronizer-backup");
/* 276 */       for (int i = 0; backupDestination.exists(); i++) {
/* 277 */         backupDestination = new File(persistenceFolder.getParentFile(), String.format("synchronizer-backup-%s", new Object[] { Integer.valueOf(i) }));
/*     */       } 
/* 279 */       logger.warn("Connector persistence folder already exists, backing up existing folder to " + backupDestination);
/*     */       try {
/* 281 */         FileUtils.moveDirectory(persistenceFolder, backupDestination);
/* 282 */       } catch (IOException e) {
/* 283 */         throw new RuntimeException("Unexpected error creating backup of connector persistence.", e);
/*     */       } 
/*     */     } 
/* 286 */     return backupDestination;
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/internal/SynchronizerModule.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */