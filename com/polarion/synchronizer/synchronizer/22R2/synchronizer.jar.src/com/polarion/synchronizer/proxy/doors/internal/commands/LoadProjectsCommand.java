/*    */ package com.polarion.synchronizer.proxy.doors.internal.commands;
/*    */ 
/*    */ import com.polarion.synchronizer.proxy.doors.internal.dxl.DxlScriptBuilder;
/*    */ import com.polarion.synchronizer.proxy.doors.internal.dxl.IDxlScript;
/*    */ import com.polarion.synchronizer.proxy.doors.xmlmodel.DoorsProjects;
/*    */ import java.io.StringReader;
/*    */ import javax.xml.bind.JAXB;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class LoadProjectsCommand
/*    */   extends AbstractDoorsCommand<DoorsProjects>
/*    */ {
/*    */   @NotNull
/*    */   public IDxlScript getScript() {
/* 39 */     return (IDxlScript)DxlScriptBuilder.script("LoadProjects.dxl");
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   protected DoorsProjects processResult(@NotNull String result) {
/* 45 */     return (DoorsProjects)JAXB.unmarshal(new StringReader(result), DoorsProjects.class);
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/internal/commands/LoadProjectsCommand.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */