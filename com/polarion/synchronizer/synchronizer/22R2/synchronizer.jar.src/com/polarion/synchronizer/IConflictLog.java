package com.polarion.synchronizer;

import com.polarion.synchronizer.model.Side;

public interface IConflictLog {
  void current(String paramString1, String paramString2);
  
  void reportConflict(String paramString1, String paramString2, Side paramSide);
}


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/IConflictLog.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */