/*    */ package com.polarion.synchronizer.model;
/*    */ 
/*    */ import com.polarion.platform.i18n.Localization;
/*    */ import java.util.Collection;
/*    */ import java.util.List;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ import org.jetbrains.annotations.Nullable;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public interface IProxy
/*    */ {
/*    */   public static final String KEY_ITEM_URL = "com.polarion.synchronizer:item-url";
/*    */   public static final String TYPE_OPTION = "option";
/*    */   public static final String TYPE_STRING = "string";
/*    */   public static final String TYPE_TEXT = "text";
/*    */   public static final String TYPE_USER = "user";
/*    */   public static final String TYPE_DATE = "date";
/*    */   public static final String TYPE_DATE_TIME = "date-time";
/*    */   public static final String TYPE_RICH_TEXT = "rich-text";
/*    */   public static final String TYPE_INTEGER = "integer";
/*    */   public static final String TYPE_FLOAT = "float";
/*    */   public static final String TYPE_CURRENCY = "currency";
/*    */   public static final String TYPE_BOOLEAN = "boolean";
/*    */   public static final String TYPE_DURATION = "duration";
/*    */   public static final String TYPE_UNKNOWN = "unknown";
/*    */   public static final String TYPE_COMMENT = "comment";
/*    */   public static final String TYPE_RELATION = "relation";
/*    */   public static final String TYPE_ATTACHMENT = "attachment";
/*    */   public static final String TYPE_TESTSTEPS = "teststeps";
/*    */   public static final String TYPE_ITEM_REF_HYPERLINK_ITEMREFURL = "item-ref-hyperlink:itemrefurl";
/*    */   public static final String TYPE_ITEM_REF_HYPERLINK_HYPERLINK = "item-ref-hyperlink:hyperlink";
/*    */   public static final String KEY_RELATIONS = "relations";
/*    */   public static final String KEY_ATTACHMENTS = "attachments";
/*    */   public static final String KEY_COMMENTS = "comments";
/*    */   public static final String KEY_HIERARCHY = "hierarchy";
/*    */   public static final String KEY_TYPE = "type";
/*    */   public static final String KEY_TESTSTEPS = "testSteps";
/*    */   public static final String KEY_PLANNEDIN = "plannedIn";
/*    */   public static final String KEY_ITEM_REF_HYPERLINK_URL = "urlForItemRefHyperlink";
/*    */   public static final String KEY_ITEM_REF_HYPERLINK_HYPERLINK = "itemRefHyperlinkHyperlink";
/* 79 */   public static final FieldDefinition FIELD_RELATION = new FieldDefinition("relations", "relations", "relation", false, true);
/* 80 */   public static final FieldDefinition FIELD_ITEM_URL = new FieldDefinition("com.polarion.synchronizer:item-url", Localization.getString("synchronizer.key.itemUrl"), "string", true, false);
/*    */   
/* 82 */   public static final FieldDefinition FIELD_ITEM_REF_HYPERLINK_URL = new FieldDefinition("urlForItemRefHyperlink", "URL for Item Reference Hyperlink", "item-ref-hyperlink:itemrefurl", true, false);
/* 83 */   public static final FieldDefinition FIELD_ITEM_REF_HYPERLINK = new FieldDefinition("itemRefHyperlinkHyperlink", "Item Reference Hyperlink", "item-ref-hyperlink:hyperlink", false, true);
/*    */   
/* 85 */   public static final FieldDefinition FIELD_ITEM_PLANNED_IN = new FieldDefinition("plannedIn", "Planned In", "string", false, true);
/*    */   
/*    */   @NotNull
/*    */   Collection<TransferItem> getItems(@NotNull Collection<String> paramCollection1, @NotNull Collection<String> paramCollection2);
/*    */   
/*    */   @NotNull
/*    */   Collection<TransferItem> getScopeItems(@NotNull Collection<String> paramCollection);
/*    */   
/*    */   @Nullable
/*    */   Collection<FieldDefinition> getDefinedFields(@Nullable String paramString);
/*    */   
/*    */   @NotNull
/*    */   List<UpdateResult> update(@NotNull List<TransferItem> paramList);
/*    */   
/*    */   @NotNull
/*    */   List<UpdateResult> delete(@NotNull List<String> paramList);
/*    */   
/*    */   boolean isHierarchySupported();
/*    */   
/*    */   @NotNull
/*    */   Collection<Option> getDefinedTypes();
/*    */   
/*    */   @NotNull
/*    */   Collection<Option> getDefinedTypes(@NotNull Collection<String> paramCollection);
/*    */   
/*    */   void close();
/*    */   
/*    */   boolean hasNonSynchronizableFields();
/*    */   
/*    */   @Deprecated
/*    */   @Nullable
/*    */   String getContentScope();
/*    */   
/*    */   @Nullable
/*    */   String getTargetName();
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/model/IProxy.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */