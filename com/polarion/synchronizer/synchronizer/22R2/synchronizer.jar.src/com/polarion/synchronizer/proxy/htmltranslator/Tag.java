package com.polarion.synchronizer.proxy.htmltranslator;

import java.util.Collection;
import org.jetbrains.annotations.NotNull;
import org.jsoup.nodes.Element;

public interface Tag {
  void open(@NotNull Element paramElement, @NotNull ElementInfo paramElementInfo, @NotNull StringBuilder paramStringBuilder, @NotNull Collection<Tag> paramCollection);
  
  void close(@NotNull Element paramElement, @NotNull ElementInfo paramElementInfo, @NotNull StringBuilder paramStringBuilder, @NotNull Collection<Tag> paramCollection);
  
  void process(@NotNull Element paramElement, @NotNull ElementInfo paramElementInfo, @NotNull StringBuilder paramStringBuilder, @NotNull Collection<Tag> paramCollection);
  
  boolean isContinue();
}


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/htmltranslator/Tag.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */