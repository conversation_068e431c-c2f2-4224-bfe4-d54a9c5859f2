/*     */ package com.polarion.synchronizer.internal;
/*     */ 
/*     */ import com.polarion.synchronizer.IBaselineProvider;
/*     */ import com.polarion.synchronizer.ILogger;
/*     */ import com.polarion.synchronizer.ISynchronizationContext;
/*     */ import com.polarion.synchronizer.configuration.IAttributeMapper;
/*     */ import com.polarion.synchronizer.internal.mapping.HierarchyProcessor;
/*     */ import com.polarion.synchronizer.model.Direction;
/*     */ import com.polarion.synchronizer.model.ItemPair;
/*     */ import com.polarion.synchronizer.model.Side;
/*     */ import com.polarion.synchronizer.model.TransferItem;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Collection;
/*     */ import java.util.Iterator;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ import org.jetbrains.annotations.Nullable;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ class MultiItemMapping
/*     */ {
/*     */   @NotNull
/*     */   private final IAttributeMapper mapping;
/*     */   @NotNull
/*     */   private final ISynchronizationContext context;
/*     */   @NotNull
/*     */   private final ILogger logger;
/*     */   
/*     */   private static class SideResult
/*     */   {
/*     */     @NotNull
/*  49 */     private List<IAttributeMapper.UnidirectionalResult> updatesFor = new ArrayList<>();
/*     */     @NotNull
/*     */     private final List<TransferItem> sourceItems;
/*     */     @NotNull
/*  53 */     private final List<TransferItem> filledSourceBaselines = new ArrayList<>();
/*     */     @NotNull
/*  55 */     private final List<TransferItem> emptySourceBaselines = new ArrayList<>();
/*     */     @NotNull
/*  57 */     private final List<TransferItem> emptySourceItems = new ArrayList<>();
/*     */     @NotNull
/*  59 */     private final List<TransferItem> mappedItems = new ArrayList<>();
/*     */     
/*     */     private SideResult(List<TransferItem> sourceItems) {
/*  62 */       this.sourceItems = sourceItems;
/*     */     }
/*     */   }
/*     */ 
/*     */   
/*     */   public static class MultiItemMappingResult
/*     */   {
/*     */     @NotNull
/*     */     private final MultiItemMapping.SideResult leftResult;
/*     */     
/*     */     @NotNull
/*     */     private final MultiItemMapping.SideResult rightResult;
/*     */     
/*     */     private HierarchyProcessor hierarchyProcessor;
/*     */     
/*     */     @NotNull
/*     */     private final LoadPairsResult loadPairs;
/*     */     
/*     */     private MultiItemMappingResult(@NotNull LoadPairsResult loadResult) {
/*  81 */       this.loadPairs = loadResult;
/*  82 */       this.leftResult = new MultiItemMapping.SideResult(initializeSourceItems(Side.LEFT));
/*  83 */       this.rightResult = new MultiItemMapping.SideResult(initializeSourceItems(Side.RIGHT));
/*  84 */       addPairs();
/*     */     }
/*     */ 
/*     */     
/*     */     private MultiItemMapping.SideResult get(Side side) {
/*  89 */       return (side == Side.LEFT) ? this.leftResult : this.rightResult;
/*     */     }
/*     */     
/*     */     @NotNull
/*     */     private List<TransferItem> initializeSourceItems(@NotNull Side side) {
/*  94 */       List<TransferItem> result = new ArrayList<>(this.loadPairs.pairs.size() + getNewItemsFrom(side).size());
/*  95 */       result.addAll(getNewItemsFrom(side));
/*  96 */       return result;
/*     */     }
/*     */     
/*     */     private void addPairs() {
/* 100 */       for (ItemPair pair : this.loadPairs.pairs) {
/* 101 */         (get(Side.LEFT)).sourceItems.add(pair.getLeft());
/* 102 */         (get(Side.RIGHT)).sourceItems.add(pair.getRight());
/*     */       } 
/*     */     }
/*     */     
/*     */     @NotNull
/*     */     public List<IAttributeMapper.UnidirectionalResult> getUpdatesFor(@NotNull Side side) {
/* 108 */       return (get(side)).updatesFor;
/*     */     }
/*     */     
/*     */     @NotNull
/*     */     private Collection<TransferItem> getNewItemsFrom(@NotNull Side side) {
/* 113 */       return (side == Side.LEFT) ? this.loadPairs.newLeft : this.loadPairs.newRight;
/*     */     }
/*     */     
/*     */     private void initializeMappedData() {
/* 117 */       initializeMappedData(Side.LEFT);
/* 118 */       initializeMappedData(Side.RIGHT);
/*     */     }
/*     */     
/*     */     private void initializeMappedData(@NotNull Side fromSide) {
/* 122 */       for (Iterator<IAttributeMapper.UnidirectionalResult> iterator = getUpdatesFor(fromSide.getOtherSide()).iterator(); iterator.hasNext(); ) {
/* 123 */         IAttributeMapper.UnidirectionalResult mapped = iterator.next();
/* 124 */         if (mapped.getMapped().getValues().size() > 0) {
/* 125 */           getFilledSourceBaselines(fromSide).add(mapped.getSourceBaseline());
/* 126 */           getUpdateItemsFor(fromSide.getOtherSide()).add(mapped.getMapped()); continue;
/*     */         } 
/* 128 */         getEmptySourceBaselines(fromSide).add(mapped.getSourceBaseline());
/* 129 */         getEmptySourceItems(fromSide).add(mapped.getSource());
/* 130 */         iterator.remove();
/*     */       } 
/*     */     }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*     */     @NotNull
/*     */     public List<TransferItem> getFilledSourceBaselines(@NotNull Side side) {
/* 140 */       return (get(side)).filledSourceBaselines;
/*     */     }
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*     */     @NotNull
/*     */     public List<TransferItem> getEmptySourceBaselines(@NotNull Side side) {
/* 148 */       return (get(side)).emptySourceBaselines;
/*     */     }
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*     */     @NotNull
/*     */     public List<TransferItem> getEmptySourceItems(@NotNull Side side) {
/* 156 */       return (get(side)).emptySourceItems;
/*     */     }
/*     */     
/*     */     @NotNull
/*     */     public List<TransferItem> getUpdateItemsFor(@NotNull Side side) {
/* 161 */       return (get(side)).mappedItems;
/*     */     }
/*     */     
/*     */     @NotNull
/*     */     public List<TransferItem> getSourceItems(@NotNull Side side) {
/* 166 */       return (get(side)).sourceItems;
/*     */     }
/*     */     
/*     */     public HierarchyProcessor getHierarchyProcessor() {
/* 170 */       return this.hierarchyProcessor;
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public MultiItemMapping(@NotNull ISynchronizationContext context, @NotNull IAttributeMapper mapping) {
/* 183 */     this.context = context;
/* 184 */     this.mapping = mapping;
/* 185 */     this.logger = context.getLogger();
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   public MultiItemMappingResult executeMapping(LoadPairsResult loadPairs, @Nullable Direction newItemDirection, boolean isPartial) {
/* 190 */     Collection<ItemPair> allPairs = loadPairs.pairs;
/*     */     
/* 192 */     MultiItemMappingResult result = new MultiItemMappingResult(loadPairs);
/*     */     
/* 194 */     mapCreates(newItemDirection, result);
/* 195 */     mapUpdates(allPairs, result);
/* 196 */     processHierarchy(result, isPartial);
/* 197 */     result.initializeMappedData();
/* 198 */     return result;
/*     */   }
/*     */   
/*     */   private void processHierarchy(@NotNull MultiItemMappingResult result, boolean isPartial) {
/* 202 */     result.hierarchyProcessor = this.mapping.loadHierarchyProcessor(result.getSourceItems(Side.LEFT), result.getSourceItems(Side.RIGHT), isPartial);
/*     */     
/* 204 */     (result.get(Side.RIGHT)).updatesFor = result.hierarchyProcessor.insertHierarchyUpdates(result.getUpdatesFor(Side.RIGHT), Side.RIGHT);
/* 205 */     (result.get(Side.LEFT)).updatesFor = result.hierarchyProcessor.insertHierarchyUpdates(result.getUpdatesFor(Side.LEFT), Side.LEFT);
/*     */   }
/*     */   
/*     */   private void mapCreates(@Nullable Direction newItemDirection, @NotNull MultiItemMappingResult result) {
/* 209 */     if (newItemDirection != null) {
/* 210 */       mapCreates(newItemDirection, Side.LEFT, result);
/* 211 */       mapCreates(newItemDirection, Side.RIGHT, result);
/*     */     } 
/*     */   }
/*     */   
/*     */   private void mapCreates(@NotNull Direction newItemDirection, @NotNull Side fromSide, @NotNull MultiItemMappingResult result) {
/* 216 */     if (newItemDirection.isFrom(fromSide)) {
/* 217 */       Collection<TransferItem> newItems = result.getNewItemsFrom(fromSide);
/* 218 */       List<IAttributeMapper.UnidirectionalResult> mappingResults = new ArrayList<>(newItems.size());
/* 219 */       for (TransferItem item : newItems) {
/* 220 */         this.context.currentItem(
/* 221 */             (fromSide == Side.LEFT) ? item.getId() : null, 
/* 222 */             (fromSide == Side.RIGHT) ? item.getId() : null);
/*     */         
/* 224 */         IAttributeMapper.UnidirectionalResult mappingResult = this.mapping.map(item, fromSide);
/* 225 */         mappingResults.add(mappingResult);
/*     */       } 
/* 227 */       result.getUpdatesFor(fromSide.getOtherSide()).addAll(mappingResults);
/*     */     } 
/*     */   }
/*     */   
/*     */   private void mapUpdates(@NotNull Collection<ItemPair> allPairs, @NotNull MultiItemMappingResult result) {
/* 232 */     if (!allPairs.isEmpty()) {
/*     */       
/* 234 */       List<IAttributeMapper.BidirectionalResult> mappingResults = new ArrayList<>();
/*     */       
/* 236 */       for (ItemPair pair : allPairs) {
/* 237 */         TransferItem leftItem = pair.getLeft();
/* 238 */         TransferItem rightItem = pair.getRight();
/*     */         
/* 240 */         this.logger.info("Mapping left item - " + leftItem.getId() + " to right item - " + rightItem.getId());
/*     */         
/* 242 */         IBaselineProvider baselineProvider = this.context.getBaselineProvider();
/* 243 */         Map<String, Object> leftBaseline = baselineProvider.loadBaseline(leftItem.getId(), Side.LEFT);
/* 244 */         Map<String, Object> rightBaseline = baselineProvider.loadBaseline(rightItem.getId(), Side.RIGHT);
/*     */         
/* 246 */         this.context.currentItem(pair.getLeft().getId(), pair.getRight().getId());
/* 247 */         IAttributeMapper.BidirectionalResult mappingResult = this.mapping.map(leftItem, leftBaseline, rightItem, rightBaseline);
/* 248 */         mappingResults.add(mappingResult);
/* 249 */         result.getUpdatesFor(Side.LEFT).add(mappingResult.getResultFor(Side.RIGHT));
/* 250 */         result.getUpdatesFor(Side.RIGHT).add(mappingResult.getResultFor(Side.LEFT));
/*     */       } 
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/internal/MultiItemMapping.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */