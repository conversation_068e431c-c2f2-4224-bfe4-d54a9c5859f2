/*     */ package com.polarion.synchronizer;
/*     */ 
/*     */ import com.polarion.platform.context.ContextNature;
/*     */ import com.polarion.platform.i18n.Localization;
/*     */ import com.polarion.platform.jobs.GenericJobException;
/*     */ import com.polarion.platform.jobs.IJobStatus;
/*     */ import com.polarion.platform.jobs.IJobUnitFactory;
/*     */ import com.polarion.platform.jobs.IProgressMonitor;
/*     */ import com.polarion.platform.jobs.spi.AbstractJobUnit;
/*     */ import com.polarion.synchronizer.configuration.ISyncPair;
/*     */ import com.polarion.synchronizer.internal.JobLoggerAdapter;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Collection;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ import org.jetbrains.annotations.Nullable;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class SynchronizationJobUnit
/*     */   extends AbstractJobUnit
/*     */   implements ISyncJobUnit
/*     */ {
/*     */   @NotNull
/*     */   private final ISynchronizationService synchronizationService;
/*     */   private String syncPair;
/*     */   @NotNull
/*  30 */   private Collection<ISyncPair> configurations = new ArrayList<>();
/*     */   
/*     */   @NotNull
/*  33 */   private static final Map<String, String> lastJobId = new HashMap<>();
/*     */   
/*     */   private boolean hasFinished;
/*     */   
/*     */   public SynchronizationJobUnit(@NotNull String name, @NotNull IJobUnitFactory creator, @NotNull ISynchronizationService synchronizationService) {
/*  38 */     super(name, creator);
/*  39 */     this.synchronizationService = synchronizationService;
/*     */   }
/*     */   
/*     */   @Nullable
/*     */   public String getSyncPair() {
/*  44 */     return this.syncPair;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setSyncPair(@NotNull String syncPair) {
/*  52 */     this.syncPair = syncPair;
/*     */   }
/*     */ 
/*     */   
/*     */   public void activate() throws GenericJobException {
/*  57 */     super.activate();
/*     */     
/*  59 */     if (getScope().getNature() != ContextNature.PROJECT_NATURE) {
/*  60 */       throw new GenericJobException(Localization.getString("synchronizer.jobNotProjectScope"));
/*     */     }
/*  62 */     if (getSyncPair() != null) {
/*  63 */       ISyncPair loadSyncPair = this.synchronizationService.loadSyncPair(getScope().getId(), this.syncPair);
/*  64 */       if (loadSyncPair != null) {
/*  65 */         this.configurations.add(loadSyncPair);
/*     */       }
/*     */     } 
/*  68 */     if (this.configurations.isEmpty()) {
/*  69 */       throw new GenericJobException(Localization.getString("synchronizer.jobMissingSyncPair"));
/*     */     }
/*     */     
/*  72 */     lastJobId.put(String.valueOf(getScope().getId().getContextName()) + "/" + getSyncPair(), getJob().getId());
/*     */   }
/*     */   
/*     */   protected IJobStatus runInternal(IProgressMonitor progress) {
/*     */     IJobStatus status;
/*  77 */     MultiSynchronizationContext multiSynchronizationContext = new MultiSynchronizationContext(this.synchronizationService, (ILogger)new JobLoggerAdapter(getLogger()));
/*  78 */     multiSynchronizationContext.execute(this.configurations, getScope().getId(), progress);
/*     */ 
/*     */ 
/*     */     
/*  82 */     if (multiSynchronizationContext.hasErrors()) {
/*  83 */       status = getStatusFailed(Localization.getString("synchronizer.jobCompletedWithError"), null);
/*  84 */     } else if (multiSynchronizationContext.hasWarnings()) {
/*  85 */       status = getStatusOK(Localization.getString("synchronizer.syncCompletedWithErrors"));
/*     */     } else {
/*  87 */       status = getStatusOK(null);
/*     */     } 
/*  89 */     this.hasFinished = true;
/*  90 */     return status;
/*     */   }
/*     */   
/*     */   public static String getLastExecutionJobId(String projectId, String syncPair) {
/*  94 */     return lastJobId.get(String.valueOf(projectId) + "/" + syncPair);
/*     */   }
/*     */ 
/*     */   
/*     */   public void setConfiguration(@NotNull Collection<ISyncPair> configurations) {
/*  99 */     this.configurations = configurations;
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean hasFinished() {
/* 104 */     return this.hasFinished;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getWorkLength() {
/* 119 */     int AMOUNT_OF_SUBTASKS = 10;
/* 120 */     return 2 * this.configurations.size() * AMOUNT_OF_SUBTASKS;
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/SynchronizationJobUnit.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */