package com.polarion.synchronizer.configuration;

import com.polarion.synchronizer.internal.mapping.HierarchyProcessor;
import com.polarion.synchronizer.model.Side;
import com.polarion.synchronizer.model.TransferItem;
import java.util.Collection;
import java.util.Map;
import org.jetbrains.annotations.NotNull;

public interface IAttributeMapper {
  Collection<String> getRequiredFields(Side paramSide);
  
  BidirectionalResult map(TransferItem paramTransferItem1, Map<String, Object> paramMap1, TransferItem paramTransferItem2, Map<String, Object> paramMap2);
  
  UnidirectionalResult map(TransferItem paramTransferItem, Side paramSide);
  
  HierarchyProcessor loadHierarchyProcessor(Collection<TransferItem> paramCollection1, Collection<TransferItem> paramCollection2, boolean paramBoolean);
  
  public static interface BidirectionalResult {
    @NotNull
    TransferItem getMappedLeft();
    
    @NotNull
    TransferItem getMappedRight();
    
    @NotNull
    TransferItem getLeftSourceBaseline();
    
    @NotNull
    TransferItem getRightSourceBaseline();
    
    @NotNull
    IAttributeMapper.UnidirectionalResult getResultFor(Side param1Side);
  }
  
  public static interface UnidirectionalResult {
    @NotNull
    TransferItem getSource();
    
    @NotNull
    TransferItem getMapped();
    
    @NotNull
    TransferItem getSourceBaseline();
  }
}


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/configuration/IAttributeMapper.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */