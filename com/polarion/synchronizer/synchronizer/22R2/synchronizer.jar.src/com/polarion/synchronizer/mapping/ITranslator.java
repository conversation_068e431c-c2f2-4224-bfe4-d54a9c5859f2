package com.polarion.synchronizer.mapping;

public interface ITranslator<R> {
  TranslationResult<R> translateUnidirectional(Object paramObject1, Object paramObject2);
  
  TranslationResult<R> translateBidirectional(Object paramObject1, Object paramObject2, Object paramObject3, Object paramObject4);
}


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/mapping/ITranslator.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */