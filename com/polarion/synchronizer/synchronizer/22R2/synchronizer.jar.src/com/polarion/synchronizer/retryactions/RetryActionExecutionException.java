/*    */ package com.polarion.synchronizer.retryactions;
/*    */ 
/*    */ import org.jetbrains.annotations.Nullable;
/*    */ 
/*    */ 
/*    */ public class RetryActionExecutionException
/*    */   extends RuntimeException
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/*    */   
/*    */   public RetryActionExecutionException(@Nullable Throwable cause) {
/* 12 */     super(cause);
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/retryactions/RetryActionExecutionException.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */