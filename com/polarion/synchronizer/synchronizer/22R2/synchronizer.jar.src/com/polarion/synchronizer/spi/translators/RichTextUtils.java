/*    */ package com.polarion.synchronizer.spi.translators;
/*    */ 
/*    */ import com.polarion.core.util.xml.HTMLHelper;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ import org.jetbrains.annotations.Nullable;
/*    */ 
/*    */ 
/*    */ public class RichTextUtils
/*    */ {
/*    */   public static final String ATTACHMENT_PREFIX = "attachment:";
/*    */   
/*    */   @Nullable
/*    */   public static String nameFromUrl(@NotNull String url) {
/* 14 */     String name = null;
/* 15 */     if (url.startsWith("attachment:")) {
/* 16 */       name = url.substring("attachment:".length());
/* 17 */       name = HTMLHelper.decodeURLPart(name);
/*    */     } 
/* 19 */     return name;
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/spi/translators/RichTextUtils.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */