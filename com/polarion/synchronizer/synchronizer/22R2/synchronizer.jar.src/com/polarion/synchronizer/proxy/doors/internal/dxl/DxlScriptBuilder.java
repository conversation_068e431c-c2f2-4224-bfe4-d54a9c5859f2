/*     */ package com.polarion.synchronizer.proxy.doors.internal.dxl;
/*     */ 
/*     */ import java.io.IOException;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Collection;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class DxlScriptBuilder
/*     */   implements IDxlScript
/*     */ {
/*     */   private Collection<IDxlScript> scripts;
/*     */   
/*     */   public DxlScriptBuilder() {
/*  58 */     this.scripts = new ArrayList<>();
/*     */   }
/*     */   
/*  61 */   public static DxlScript script(String scriptName) { return new DxlScript(loadScript(scriptName)); }
/*     */   public static class DxlScript implements IDxlScript {
/*     */     @NotNull private String script;
/*     */     private DxlScript(@NotNull String script) { this.script = script; }
/*  65 */     @NotNull public DxlScript replaceParameter(@NotNull String parameterName, @NotNull String value) { this.script = this.script.replace("%" + parameterName + "%", value); return this; } @NotNull public String toString() { return this.script; } } public static DxlScript scriptWithContent(String script) { return new DxlScript(script); }
/*     */ 
/*     */   
/*     */   public static DxlScriptBuilder create() {
/*  69 */     return new DxlScriptBuilder();
/*     */   }
/*     */   
/*     */   public DxlScriptBuilder add(@NotNull IDxlScript script) {
/*  73 */     this.scripts.add(script);
/*  74 */     return this;
/*     */   }
/*     */   
/*     */   public DxlScriptBuilder add(@NotNull String script) {
/*  78 */     this.scripts.add(new DxlScript(script));
/*  79 */     return this;
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public String toString() {
/*  85 */     StringBuilder result = new StringBuilder();
/*  86 */     for (IDxlScript dxlScript : this.scripts) {
/*  87 */       result.append(dxlScript.toString());
/*  88 */       result.append("\n");
/*     */     } 
/*  90 */     return result.toString();
/*     */   }
/*     */   @NotNull
/*     */   private static String loadScript(@NotNull String scriptName) {
/*     */     
/*  95 */     try { Exception exception1 = null, exception2 = null; try {  }
/*     */       finally
/*  97 */       { exception2 = null; if (exception1 == null) { exception1 = exception2; } else if (exception1 != exception2) { exception1.addSuppressed(exception2); }  }  } catch (IOException e)
/*  98 */     { throw new RuntimeException(e); }
/*     */   
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public IDxlScript replaceParameter(@NotNull String parameterName, @NotNull String value) {
/* 105 */     for (IDxlScript dxlScript : this.scripts) {
/* 106 */       dxlScript.replaceParameter(parameterName, value);
/*     */     }
/* 108 */     return this;
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/internal/dxl/DxlScriptBuilder.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */