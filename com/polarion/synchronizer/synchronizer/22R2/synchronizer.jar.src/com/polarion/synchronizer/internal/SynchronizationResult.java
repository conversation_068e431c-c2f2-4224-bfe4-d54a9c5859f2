/*     */ package com.polarion.synchronizer.internal;
/*     */ 
/*     */ import com.polarion.synchronizer.ILogger;
/*     */ import com.polarion.synchronizer.ISynchronizationResult;
/*     */ import com.polarion.synchronizer.IUpdateOperation;
/*     */ import com.polarion.synchronizer.SynchronizationStatistics;
/*     */ import com.polarion.synchronizer.model.CreateResult;
/*     */ import com.polarion.synchronizer.model.TransferItem;
/*     */ import com.polarion.synchronizer.model.UpdateResult;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Collection;
/*     */ import java.util.Collections;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class SynchronizationResult
/*     */   implements ISynchronizationResult, SynchronizationStatistics
/*     */ {
/*     */   @NotNull
/*  41 */   private final Collection<IUpdateOperation> updates = new ArrayList<>();
/*     */   @NotNull
/*  43 */   private Collection<TransferItem> unchanged = Collections.emptyList();
/*     */   
/*     */   @NotNull
/*  46 */   private Collection<TransferItem> sourceItems = new ArrayList<>();
/*     */   
/*     */   private int createdWithError;
/*     */   
/*     */   private int failedToCreate;
/*     */   
/*     */   private int updatedWithError;
/*     */   
/*     */   private int failedToUpdate;
/*     */   
/*     */   private int created;
/*     */   private int updated;
/*     */   private boolean hasWarnings;
/*     */   
/*     */   @NotNull
/*     */   public Collection<IUpdateOperation> getUpdates() {
/*  62 */     return this.updates;
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public Collection<TransferItem> getUnchanged() {
/*  68 */     return this.unchanged;
/*     */   }
/*     */   
/*     */   public void setUnchanged(@NotNull Collection<TransferItem> unchanged) {
/*  72 */     this.unchanged = unchanged;
/*     */   }
/*     */   
/*     */   public void trackCreate(@NotNull CreateResult createResult, @NotNull TransferItem sourceItem, @NotNull TransferItem createContent) {
/*  76 */     this.updates.add(new CreateOperation(sourceItem, createContent, createResult));
/*  77 */     if (createResult.getCreatedId() == null) {
/*  78 */       this.failedToCreate++;
/*  79 */     } else if (createResult.hasError()) {
/*  80 */       this.created++;
/*  81 */       this.createdWithError++;
/*  82 */     } else if (createResult.hasWarning()) {
/*  83 */       this.created++;
/*  84 */       this.hasWarnings = true;
/*     */     } else {
/*  86 */       this.created++;
/*     */     } 
/*     */   }
/*     */   
/*     */   public void trackUpdate(@NotNull UpdateResult updateResult, @NotNull TransferItem sourceItem, @NotNull TransferItem updateContent) {
/*  91 */     this.updates.add(new UpdateOperation(sourceItem, updateContent, updateResult));
/*  92 */     if (updateResult.hasError()) {
/*  93 */       if (updateResult.getFailedFields().isEmpty()) {
/*  94 */         this.failedToUpdate++;
/*     */       } else {
/*  96 */         this.updated++;
/*  97 */         this.updatedWithError++;
/*     */       } 
/*     */     } else {
/* 100 */       this.updated++;
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   public int getCreated() {
/* 106 */     return this.created;
/*     */   }
/*     */ 
/*     */   
/*     */   public int getCreatedWithError() {
/* 111 */     return this.createdWithError;
/*     */   }
/*     */ 
/*     */   
/*     */   public int getFailedToCreate() {
/* 116 */     return this.failedToCreate;
/*     */   }
/*     */ 
/*     */   
/*     */   public int getUpdated() {
/* 121 */     return this.updated;
/*     */   }
/*     */ 
/*     */   
/*     */   public int getUpdatedWithError() {
/* 126 */     return this.updatedWithError;
/*     */   }
/*     */ 
/*     */   
/*     */   public int getFailedToUpdate() {
/* 131 */     return this.failedToUpdate;
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean hasErrors() {
/* 136 */     return !(this.createdWithError <= 0 && this.failedToCreate <= 0 && this.updatedWithError <= 0 && this.failedToUpdate <= 0);
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean hasWarnings() {
/* 141 */     return this.hasWarnings;
/*     */   }
/*     */ 
/*     */   
/*     */   public void setHasWarnings() {
/* 146 */     this.hasWarnings = true;
/*     */   }
/*     */   
/*     */   public void log(@NotNull ILogger logger) {
/* 150 */     logger.info("Total items created:\t" + getCreated());
/* 151 */     if (this.createdWithError > 0) {
/* 152 */       logger.warn("Items created with error:\t" + this.createdWithError);
/*     */     }
/* 154 */     if (this.failedToCreate > 0) {
/* 155 */       logger.warn("Failed to create:\t" + this.failedToCreate);
/*     */     }
/*     */     
/* 158 */     logger.info("Total items updated:\t" + getUpdated());
/* 159 */     if (this.updatedWithError > 0) {
/* 160 */       logger.warn("Items updated with error:\t" + this.updatedWithError);
/*     */     }
/* 162 */     if (this.failedToUpdate > 0) {
/* 163 */       logger.warn("Failed to update:\t" + this.failedToUpdate);
/*     */     }
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public Collection<TransferItem> getSourceItems() {
/* 170 */     return this.sourceItems;
/*     */   }
/*     */   
/*     */   public void setSourceItems(@NotNull Collection<TransferItem> sourceItems) {
/* 174 */     this.sourceItems = sourceItems;
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/internal/SynchronizationResult.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */