package com.polarion.synchronizer;

import com.polarion.synchronizer.model.Side;
import java.util.Map;

public interface IBaselineProvider extends ISynchronizationHook {
  Map<String, Object> loadBaseline(String paramString, Side paramSide);
}


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/IBaselineProvider.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */