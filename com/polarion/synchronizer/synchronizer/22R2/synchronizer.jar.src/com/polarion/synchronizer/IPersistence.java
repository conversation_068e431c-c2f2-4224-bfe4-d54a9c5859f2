package com.polarion.synchronizer;

import org.jetbrains.annotations.NotNull;

public interface IPersistence {
  @NotNull
  IPersistenceOperation createOperation(@NotNull String paramString);
}


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/IPersistence.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */