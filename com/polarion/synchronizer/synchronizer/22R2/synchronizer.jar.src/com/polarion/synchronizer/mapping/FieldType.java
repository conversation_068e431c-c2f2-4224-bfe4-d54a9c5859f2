/*    */ package com.polarion.synchronizer.mapping;
/*    */ 
/*    */ import com.fasterxml.jackson.annotation.JsonValue;
/*    */ 
/*    */ public class FieldType
/*    */ {
/*    */   private final String typeName;
/*    */   private final boolean multi;
/*    */   
/*    */   public FieldType(String typeName, boolean multi) {
/* 11 */     this.typeName = typeName;
/* 12 */     this.multi = multi;
/*    */   }
/*    */   
/*    */   public String getTypeName() {
/* 16 */     return this.typeName;
/*    */   }
/*    */   
/*    */   public boolean isMulti() {
/* 20 */     return this.multi;
/*    */   }
/*    */ 
/*    */   
/*    */   public int hashCode() {
/* 25 */     int prime = 31;
/* 26 */     int result = 1;
/* 27 */     result = 31 * result + (this.multi ? 1231 : 1237);
/* 28 */     result = 31 * result + ((this.typeName == null) ? 0 : this.typeName.hashCode());
/* 29 */     return result;
/*    */   }
/*    */ 
/*    */   
/*    */   public boolean equals(Object obj) {
/* 34 */     if (this == obj)
/* 35 */       return true; 
/* 36 */     if (obj == null)
/* 37 */       return false; 
/* 38 */     if (getClass() != obj.getClass())
/* 39 */       return false; 
/* 40 */     FieldType other = (FieldType)obj;
/* 41 */     if (this.multi != other.multi)
/* 42 */       return false; 
/* 43 */     if (this.typeName == null) {
/* 44 */       if (other.typeName != null)
/* 45 */         return false; 
/* 46 */     } else if (!this.typeName.equals(other.typeName)) {
/* 47 */       return false;
/* 48 */     }  return true;
/*    */   }
/*    */   
/*    */   @JsonValue
/*    */   public String toString() {
/* 53 */     return String.valueOf(isMulti() ? "multi-" : "") + this.typeName;
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/mapping/FieldType.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */