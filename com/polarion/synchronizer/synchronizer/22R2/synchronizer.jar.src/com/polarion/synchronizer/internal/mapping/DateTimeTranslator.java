/*    */ package com.polarion.synchronizer.internal.mapping;
/*    */ 
/*    */ import com.polarion.synchronizer.mapping.TranslationResult;
/*    */ import com.polarion.synchronizer.spi.translators.TypesafeTranslator;
/*    */ import java.util.Date;
/*    */ 
/*    */ public class DateTimeTranslator
/*    */   extends TypesafeTranslator<Date, Date, Date>
/*    */ {
/*    */   public DateTimeTranslator() {
/* 11 */     super(Date.class, Date.class);
/*    */   }
/*    */ 
/*    */   
/*    */   public TranslationResult<Date> translateBidirectionalTypesafe(Date sourceBaseline, Date sourceValue, Date targetBaseline, Date targetValue) {
/* 16 */     return createBidirectionalResult(sourceBaseline, sourceValue, sourceValue, targetBaseline, targetValue);
/*    */   }
/*    */ 
/*    */   
/*    */   public TranslationResult<Date> translateUnidirectionalTypesafe(Date sourceValue, Date targetValue) {
/* 21 */     return createUnidirectionalResult(sourceValue, targetValue);
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/internal/mapping/DateTimeTranslator.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */