/*    */ package com.polarion.synchronizer.mapping;
/*    */ 
/*    */ 
/*    */ 
/*    */ public class TranslationResult<T>
/*    */ {
/*    */   private T resultValue;
/*    */   private boolean isModification;
/*    */   private boolean isConflict;
/*    */   
/*    */   public TranslationResult(T resultValue, boolean isModification, boolean isConflict) {
/* 12 */     this.resultValue = resultValue;
/* 13 */     this.isModification = isModification;
/* 14 */     this.isConflict = isConflict;
/*    */   }
/*    */   
/*    */   public T getResultValue() {
/* 18 */     return this.resultValue;
/*    */   }
/*    */   
/*    */   public boolean isModification() {
/* 22 */     return this.isModification;
/*    */   }
/*    */   
/*    */   public boolean isConflict() {
/* 26 */     return (this.isConflict && this.isModification);
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/mapping/TranslationResult.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */