/*     */ package com.polarion.synchronizer.internal;
/*     */ 
/*     */ import com.polarion.synchronizer.IDependencyManager;
/*     */ import com.polarion.synchronizer.ISynchronizationContext;
/*     */ import com.polarion.synchronizer.ISynchronizationTask;
/*     */ import com.polarion.synchronizer.model.Side;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Collection;
/*     */ import java.util.HashMap;
/*     */ import java.util.HashSet;
/*     */ import java.util.Map;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ import org.jetbrains.annotations.Nullable;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class DependencyManager
/*     */   implements IDependencyManager
/*     */ {
/*     */   @NotNull
/*     */   private final ISynchronizationContext context;
/*     */   private String leftSystemId;
/*     */   private String rightSystemId;
/*     */   private String leftItemId;
/*     */   private String rightItemId;
/*     */   private ISynchronizationTask currentTask;
/*     */   
/*     */   private static class DependentItems
/*     */   {
/*  42 */     private Map<String, Collection<String>> itemsMap = new HashMap<>();
/*     */     
/*     */     private Collection<String> getItems(String dependingOn) {
/*  45 */       Collection<String> items = this.itemsMap.get(dependingOn);
/*  46 */       if (items == null) {
/*  47 */         items = new HashSet<>();
/*  48 */         this.itemsMap.put(dependingOn, items);
/*     */       } 
/*  50 */       return items;
/*     */     }
/*     */   }
/*     */   
/*     */   private static class ResolvedItemsKey {
/*     */     @NotNull
/*     */     private final ISynchronizationTask task;
/*     */     @NotNull
/*     */     private final String systemid;
/*     */     
/*     */     public ResolvedItemsKey(@NotNull ISynchronizationTask task, @NotNull String systemid) {
/*  61 */       this.task = task;
/*  62 */       this.systemid = systemid;
/*     */     }
/*     */ 
/*     */     
/*     */     public int hashCode() {
/*  67 */       int prime = 31;
/*  68 */       int result = 1;
/*  69 */       result = 31 * result + this.systemid.hashCode();
/*  70 */       result = 31 * result + this.task.hashCode();
/*  71 */       return result;
/*     */     }
/*     */ 
/*     */     
/*     */     public boolean equals(Object obj) {
/*  76 */       if (this == obj) {
/*  77 */         return true;
/*     */       }
/*  79 */       if (obj == null) {
/*  80 */         return false;
/*     */       }
/*  82 */       if (getClass() != obj.getClass()) {
/*  83 */         return false;
/*     */       }
/*  85 */       ResolvedItemsKey other = (ResolvedItemsKey)obj;
/*  86 */       if (!this.systemid.equals(other.systemid)) {
/*  87 */         return false;
/*     */       }
/*  89 */       if (!this.task.equals(other.task)) {
/*  90 */         return false;
/*     */       }
/*  92 */       return true;
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @NotNull
/* 111 */   private Map<String, Collection<String>> resolvedItemsMap = new HashMap<>();
/*     */   
/*     */   @NotNull
/* 114 */   private Map<ResolvedItemsKey, DependentItems> dependentItemsMap = new HashMap<>();
/*     */   
/*     */   public DependencyManager(@NotNull ISynchronizationContext context) {
/* 117 */     this.context = context;
/*     */   }
/*     */ 
/*     */   
/*     */   public void setCurrentTask(@NotNull ISynchronizationTask synchronizationTask) {
/* 122 */     System.out.println(synchronizationTask);
/*     */   }
/*     */   
/*     */   public void currentItem(@Nullable String leftItemId, @Nullable String rightItemId) {
/* 126 */     this.leftItemId = leftItemId;
/* 127 */     this.rightItemId = rightItemId;
/*     */   }
/*     */ 
/*     */   
/*     */   public void trackDependency(@NotNull Side side, @NotNull String dependsOnId) {
/* 132 */     String systemId = (side == Side.LEFT) ? this.leftSystemId : this.rightSystemId;
/* 133 */     String waitingItemId = (side == Side.LEFT) ? this.leftItemId : this.rightItemId;
/* 134 */     getDependentItems(this.currentTask, systemId).getItems(dependsOnId).add(waitingItemId);
/* 135 */     this.context.getLogger().debug("Item " + waitingItemId + " is waiting for " + dependsOnId + " in " + systemId + ".");
/*     */   }
/*     */   
/*     */   public void currentSystem(@NotNull String leftSystem, @NotNull String rightSystem) {
/* 139 */     this.leftSystemId = leftSystem;
/* 140 */     this.rightSystemId = rightSystem;
/*     */   }
/*     */   
/*     */   public void currentTask(@NotNull ISynchronizationTask task) {
/* 144 */     this.currentTask = task;
/*     */   }
/*     */ 
/*     */   
/*     */   public void resolve(@NotNull String systemId, @NotNull String unresolvedId) {
/* 149 */     this.context.getLogger().debug("Resolve " + systemId + "/" + unresolvedId);
/* 150 */     getResolvedItems(systemId).add(unresolvedId);
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public Collection<String> getResolvedDependencies(@NotNull ISynchronizationTask task, @NotNull String systemId) {
/* 156 */     Collection<String> resolvedDependencies = new HashSet<>();
/* 157 */     Collection<String> resolvedItems = getResolvedItems(systemId);
/* 158 */     DependentItems dependentItems = getDependentItems(task, systemId);
/* 159 */     for (String resolvedItem : resolvedItems) {
/* 160 */       resolvedDependencies.addAll(dependentItems.getItems(resolvedItem));
/*     */     }
/*     */     
/* 163 */     return resolvedDependencies;
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private Collection<String> getResolvedItems(String systemId) {
/* 168 */     Collection<String> resolvedDependencies = this.resolvedItemsMap.get(systemId);
/* 169 */     if (resolvedDependencies == null) {
/* 170 */       resolvedDependencies = new ArrayList<>();
/* 171 */       this.resolvedItemsMap.put(systemId, resolvedDependencies);
/*     */     } 
/* 173 */     return resolvedDependencies;
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private DependentItems getDependentItems(ISynchronizationTask task, String systemId) {
/* 178 */     ResolvedItemsKey key = new ResolvedItemsKey(task, systemId);
/* 179 */     DependentItems dependentItems = this.dependentItemsMap.get(key);
/* 180 */     if (dependentItems == null) {
/* 181 */       dependentItems = new DependentItems();
/* 182 */       this.dependentItemsMap.put(key, dependentItems);
/*     */     } 
/* 184 */     return dependentItems;
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/internal/DependencyManager.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */