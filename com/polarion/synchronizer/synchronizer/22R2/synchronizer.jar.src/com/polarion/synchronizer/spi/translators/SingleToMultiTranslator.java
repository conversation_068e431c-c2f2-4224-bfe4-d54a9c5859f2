/*    */ package com.polarion.synchronizer.spi.translators;
/*    */ 
/*    */ import com.polarion.core.util.ObjectUtils;
/*    */ import com.polarion.synchronizer.mapping.TranslationResult;
/*    */ import com.polarion.synchronizer.model.CollectionUpdate;
/*    */ import java.util.ArrayList;
/*    */ import java.util.Collection;
/*    */ import java.util.Collections;
/*    */ import javax.inject.Inject;
/*    */ 
/*    */ 
/*    */ public class SingleToMultiTranslator
/*    */   extends TypesafeTranslator<String, Collection<String>, CollectionUpdate<String>>
/*    */ {
/*    */   private final StringValueTranslator stringValueTranslator;
/*    */   
/*    */   @Inject
/*    */   public SingleToMultiTranslator(StringValueTranslator stringValueTranslator) {
/* 19 */     super(Object.class, Collection.class);
/* 20 */     this.stringValueTranslator = stringValueTranslator;
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public TranslationResult<CollectionUpdate<String>> translateBidirectionalTypesafe(String sourceBaseline, String sourceValue, Collection<String> targetBaseline, Collection<String> targetValue) {
/* 26 */     boolean sourceModified = !ObjectUtils.equalsWithNull(sourceBaseline, sourceValue);
/*    */     
/* 28 */     boolean targetModified = MultiToSingleTranslator.contentNotEqual(targetBaseline, targetValue);
/*    */     
/* 30 */     CollectionUpdate<String> mapped = getMapped(sourceModified, sourceValue, targetValue);
/*    */     
/* 32 */     return new TranslationResult(mapped, (
/* 33 */         sourceModified && !mapped.isEmpty()), 
/* 34 */         (!mapped.isEmpty() && targetModified));
/*    */   }
/*    */ 
/*    */   
/*    */   public TranslationResult<CollectionUpdate<String>> translateUnidirectionalTypesafe(String sourceValue, Collection<String> targetValue) {
/* 39 */     CollectionUpdate<String> mapped = getMapped(true, sourceValue, targetValue);
/* 40 */     return new TranslationResult(mapped, !mapped.isEmpty(), false);
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   private CollectionUpdate<String> getMapped(boolean sourceModified, String sourceValue, Collection<String> targetValue) {
/* 46 */     Collection<String> added = Collections.emptyList();
/* 47 */     Collection<String> removed = Collections.emptyList();
/*    */     
/* 49 */     if (sourceModified) {
/* 50 */       String targetString = MultiToSingleTranslator.getFirst(targetValue);
/* 51 */       String mappedString = (String)this.stringValueTranslator.translateUnidirectional(sourceValue, targetString).getResultValue();
/*    */       
/* 53 */       if (mappedString != null && targetValue == null) {
/* 54 */         added = Collections.singleton(mappedString);
/* 55 */       } else if (mappedString == null && targetValue != null) {
/* 56 */         removed = targetValue;
/* 57 */       } else if (mappedString != null && targetValue != null) {
/* 58 */         removed = new ArrayList<>(targetValue);
/* 59 */         if (targetValue.contains(mappedString)) {
/* 60 */           removed.remove(mappedString);
/*    */         } else {
/* 62 */           added = Collections.singleton(mappedString);
/*    */         } 
/*    */       } 
/*    */     } 
/* 66 */     return new CollectionUpdate(added, removed);
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/spi/translators/SingleToMultiTranslator.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */