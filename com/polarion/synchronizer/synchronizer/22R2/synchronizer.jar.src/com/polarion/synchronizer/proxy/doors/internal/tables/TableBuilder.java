/*    */ package com.polarion.synchronizer.proxy.doors.internal.tables;
/*    */ 
/*    */ import com.google.inject.Inject;
/*    */ import com.polarion.synchronizer.ISynchronizationContext;
/*    */ import com.polarion.synchronizer.model.TransferItem;
/*    */ import java.util.Collection;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class TableBuilder
/*    */   implements ITableBuilder
/*    */ {
/*    */   @Inject
/*    */   private ISynchronizationContext context;
/*    */   
/*    */   @NotNull
/*    */   public Collection<ITable> loadTables(@NotNull Collection<TransferItem> allItems) {
/* 40 */     TableProcessor processor = new TableProcessor(this.context.getConnectionMap(), this.context.getLogger());
/* 41 */     return processor.loadTables(allItems);
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/internal/tables/TableBuilder.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */