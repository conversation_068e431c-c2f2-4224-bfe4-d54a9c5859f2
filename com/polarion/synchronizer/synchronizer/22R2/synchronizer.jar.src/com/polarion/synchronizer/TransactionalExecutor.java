/*    */ package com.polarion.synchronizer;
/*    */ 
/*    */ import com.polarion.platform.ITransactionService;
/*    */ import com.polarion.platform.security.ISecurityService;
/*    */ import java.security.PrivilegedAction;
/*    */ import java.security.PrivilegedExceptionAction;
/*    */ import javax.inject.Inject;
/*    */ import javax.security.auth.Subject;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class TransactionalExecutor
/*    */ {
/*    */   private ISecurityService securityService;
/*    */   private ITransactionService transactionService;
/*    */   
/*    */   @Inject
/*    */   public TransactionalExecutor(ISecurityService securityService, ITransactionService transactionService) {
/* 20 */     this.securityService = securityService;
/* 21 */     this.transactionService = transactionService;
/*    */   }
/*    */   
/*    */   public <T> T doInTransaction(Subject subject, final PrivilegedAction<T> action) throws Exception {
/* 25 */     return (T)this.securityService.doAsUser(subject, new PrivilegedExceptionAction<T>()
/*    */         {
/*    */           public T run() throws Exception
/*    */           {
/* 29 */             boolean failed = true;
/* 30 */             boolean isNestedTx = TransactionalExecutor.this.transactionService.txExists();
/* 31 */             if (!isNestedTx) {
/* 32 */               TransactionalExecutor.this.transactionService.beginTx();
/*    */             }
/*    */             try {
/* 35 */               T result = action.run();
/* 36 */               failed = false;
/* 37 */               return result;
/*    */             } finally {
/* 39 */               if (!isNestedTx) {
/* 40 */                 TransactionalExecutor.this.transactionService.endTx(failed);
/*    */               }
/*    */             } 
/*    */           }
/*    */         });
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public <T> T doInTransaction(String accountVaultKey, PrivilegedAction<T> action) throws Exception {
/* 50 */     Subject subject = this.securityService.login().from("com.polarion.synchronizer.TransactionalExecutor").withVaultKey(accountVaultKey).perform();
/* 51 */     return doInTransaction(subject, action);
/*    */   }
/*    */   
/*    */   public <T> T doInTransaction(PrivilegedAction<T> action) throws Exception {
/* 55 */     boolean failed = true;
/* 56 */     boolean isNestedTx = this.transactionService.txExists();
/* 57 */     if (!isNestedTx) {
/* 58 */       this.transactionService.beginTx();
/*    */     }
/*    */     try {
/* 61 */       T result = action.run();
/* 62 */       failed = false;
/* 63 */       return result;
/*    */     } finally {
/* 65 */       if (!isNestedTx)
/* 66 */         this.transactionService.endTx(failed); 
/*    */     } 
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/TransactionalExecutor.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */