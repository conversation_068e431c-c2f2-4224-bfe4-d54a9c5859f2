/*     */ package com.polarion.synchronizer.internal.configuration;
/*     */ 
/*     */ import com.fasterxml.jackson.annotation.JsonIgnore;
/*     */ import com.polarion.platform.i18n.Localization;
/*     */ import com.polarion.synchronizer.IProxyConfiguration;
/*     */ import com.polarion.synchronizer.SynchronizationException;
/*     */ import com.polarion.synchronizer.configuration.IConnection;
/*     */ import com.polarion.synchronizer.configuration.ISyncConfiguration;
/*     */ import com.polarion.synchronizer.configuration.ISyncPair;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Collection;
/*     */ import java.util.HashMap;
/*     */ import java.util.LinkedList;
/*     */ import java.util.Map;
/*     */ import javax.xml.bind.annotation.XmlAccessType;
/*     */ import javax.xml.bind.annotation.XmlAccessorType;
/*     */ import javax.xml.bind.annotation.XmlElement;
/*     */ import javax.xml.bind.annotation.XmlElementWrapper;
/*     */ import javax.xml.bind.annotation.XmlElements;
/*     */ import javax.xml.bind.annotation.XmlRootElement;
/*     */ import javax.xml.bind.annotation.XmlTransient;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @XmlAccessorType(XmlAccessType.FIELD)
/*     */ @XmlRootElement
/*     */ public class SyncConfiguration
/*     */   implements ISyncConfiguration
/*     */ {
/*     */   @XmlTransient
/*     */   private Map<String, ISyncPair> syncSets;
/*     */   @XmlTransient
/*     */   private Map<String, IConnection> connections;
/*     */   @XmlElementWrapper(name = "syncPairs")
/*     */   @XmlElements({@XmlElement(type = SyncPair.class, name = "syncPair")})
/*  37 */   private Collection<ISyncPair> syncPairsList = new LinkedList<>();
/*     */   
/*     */   @XmlElementWrapper(name = "connections")
/*     */   @XmlElements({@XmlElement(type = Object.class, name = "connection")})
/*  41 */   private Collection<IConnection> connectionsList = new LinkedList<>();
/*     */ 
/*     */   
/*     */   @JsonIgnore
/*     */   public synchronized Map<String, ISyncPair> getSyncPairs() {
/*  46 */     if (this.syncSets == null) {
/*  47 */       this.syncSets = new HashMap<>();
/*  48 */       if (this.syncPairsList != null) {
/*  49 */         for (ISyncPair syncSet : this.syncPairsList) {
/*  50 */           this.syncSets.put(syncSet.getId(), syncSet);
/*     */         }
/*     */       }
/*     */     } 
/*  54 */     return this.syncSets;
/*     */   }
/*     */   
/*     */   public void addSyncPair(ISyncPair syncSetConfig) {
/*  58 */     ISyncPair old = getSyncPairs().put(syncSetConfig.getId(), syncSetConfig);
/*  59 */     this.syncPairsList.remove(old);
/*  60 */     this.syncPairsList.add(syncSetConfig);
/*     */   }
/*     */   
/*     */   public void removeSyncPair(String id) {
/*  64 */     ISyncPair remove = getSyncPairs().remove(id);
/*  65 */     this.syncPairsList.remove(remove);
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, IConnection> getConnections() {
/*  70 */     if (this.connections == null) {
/*  71 */       this.connections = new HashMap<>();
/*  72 */       if (this.connectionsList != null) {
/*     */ 
/*     */ 
/*     */         
/*  76 */         Collection<?> connectionObjects = new ArrayList(this.connectionsList);
/*  77 */         for (Object connectionObject : connectionObjects) {
/*  78 */           if (connectionObject instanceof IConnection) {
/*  79 */             IConnection connection = (IConnection)connectionObject;
/*  80 */             this.connections.put(connection.getId(), connection);
/*     */           } 
/*     */         } 
/*     */       } 
/*     */     } 
/*  85 */     return this.connections;
/*     */   }
/*     */   
/*     */   public void addConnection(IConnection connection) {
/*  89 */     IConnection old = getConnections().put(connection.getId(), connection);
/*  90 */     this.connectionsList.remove(old);
/*  91 */     this.connectionsList.add(connection);
/*     */   }
/*     */   
/*     */   public void removeConnection(String id) {
/*  95 */     for (ISyncPair syncPair : this.syncPairsList) {
/*  96 */       IProxyConfiguration right = syncPair.getRight();
/*  97 */       if (right != null) {
/*  98 */         IConnection connection = right.getConnection();
/*  99 */         if (connection != null && connection.getId().equals(id) && syncPair.getProjectId() != null) {
/* 100 */           throw new SynchronizationException(Localization.getString("synchronizer.cantDeleteUsedConnection", new String[] { id, syncPair.getId() }));
/*     */         }
/*     */       } 
/*     */     } 
/*     */     
/* 105 */     IConnection remove = getConnections().remove(id);
/* 106 */     this.connectionsList.remove(remove);
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/internal/configuration/SyncConfiguration.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */