package com.polarion.synchronizer;

import com.polarion.synchronizer.model.TransferItem;
import com.polarion.synchronizer.model.UpdateResult;
import org.jetbrains.annotations.NotNull;

public interface IUpdateOperation {
  @NotNull
  TransferItem getSourceItem();
  
  @NotNull
  TransferItem getContent();
  
  @NotNull
  UpdateResult getResult();
}


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/IUpdateOperation.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */