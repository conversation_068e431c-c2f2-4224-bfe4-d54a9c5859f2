/*    */ package com.polarion.synchronizer.proxy.doors.internal;
/*    */ 
/*    */ import com.google.inject.Inject;
/*    */ import com.polarion.platform.ITransactionService;
/*    */ import com.polarion.platform.context.IContextService;
/*    */ import com.polarion.platform.security.ISecurityService;
/*    */ import com.polarion.platform.service.repository.IRepositoryService;
/*    */ import com.polarion.platform.spi.security.IAuthenticationSource;
/*    */ import com.polarion.synchronizer.proxy.AbstractRepository;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class DoorsRepository
/*    */   extends AbstractRepository
/*    */ {
/*    */   @Inject
/*    */   public DoorsRepository(@NotNull ITransactionService transactionService, @NotNull IRepositoryService repositoryService, @NotNull IAuthenticationSource authenticationSource, @NotNull ISecurityService securityService, @NotNull IContextService contextService) {
/* 37 */     super(transactionService, repositoryService, authenticationSource, securityService, contextService);
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   protected String getLocationPath() {
/* 43 */     return ".polarion/doors";
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/internal/DoorsRepository.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */