/*    */ package com.polarion.synchronizer.internal;
/*    */ 
/*    */ import com.polarion.synchronizer.model.ItemPair;
/*    */ import com.polarion.synchronizer.model.TransferItem;
/*    */ import java.util.Collection;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ class LoadPairsResult
/*    */ {
/*    */   @NotNull
/*    */   public Collection<TransferItem> newLeft;
/*    */   @NotNull
/*    */   public Collection<TransferItem> newRight;
/*    */   @NotNull
/*    */   public Collection<TransferItem> singleLeft;
/*    */   @NotNull
/*    */   public Collection<TransferItem> singleRight;
/*    */   @NotNull
/*    */   public Collection<ItemPair> pairs;
/*    */   
/*    */   public LoadPairsResult(@NotNull Collection<TransferItem> singleLeft, @NotNull Collection<TransferItem> singleRight, @NotNull Collection<ItemPair> pairs, Collection<TransferItem> newLeft, @NotNull Collection<TransferItem> newRight) {
/* 45 */     this.singleLeft = singleLeft;
/* 46 */     this.singleRight = singleRight;
/* 47 */     this.pairs = pairs;
/* 48 */     this.newLeft = newLeft;
/* 49 */     this.newRight = newRight;
/*    */   }
/*    */ 
/*    */   
/*    */   public String toString() {
/* 54 */     return "LoadPairsResult [newLeft=" + this.newLeft + ", newRight=" + this.newRight + ", singleLeft=" + this.singleLeft + ", singleRight=" + this.singleRight + ", pairs=" + this.pairs + "]";
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/internal/LoadPairsResult.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */