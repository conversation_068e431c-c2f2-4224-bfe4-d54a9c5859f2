/*    */ package com.polarion.synchronizer.spi.translators;
/*    */ 
/*    */ import com.google.inject.TypeLiteral;
/*    */ import com.google.inject.util.Types;
/*    */ import com.polarion.synchronizer.mapping.ITranslator;
/*    */ import com.polarion.synchronizer.mapping.ITranslatorFactory;
/*    */ import com.polarion.synchronizer.model.CollectionUpdate;
/*    */ import java.lang.reflect.ParameterizedType;
/*    */ import java.lang.reflect.Type;
/*    */ 
/*    */ 
/*    */ public abstract class CollectionTranslatorModule<T>
/*    */   extends BaseTranslatorModule<CollectionUpdate<T>>
/*    */ {
/*    */   private final TypeLiteral<ITranslator<CollectionUpdate<T>>> translatorType;
/*    */   private final TypeLiteral<ITranslatorFactory<CollectionUpdate<T>>> factoryType;
/*    */   
/*    */   public CollectionTranslatorModule(String sourceItemType, String targetItemType, Class<? extends ITranslator<CollectionUpdate<T>>> translatorClass) {
/* 19 */     super(sourceItemType, targetItemType, true, true, translatorClass);
/*    */     
/* 21 */     ParameterizedType type = (ParameterizedType)getClass().getGenericSuperclass();
/* 22 */     Type[] parameterTypes = type.getActualTypeArguments();
/*    */     
/* 24 */     Type colelctionUpdateType = Types.newParameterizedType(CollectionUpdate.class, new Type[] { parameterTypes[0] });
/*    */     
/* 26 */     this.translatorType = 
/* 27 */       TypeLiteral.get(Types.newParameterizedType(ITranslator.class, new Type[] { colelctionUpdateType }));
/* 28 */     this.factoryType = 
/* 29 */       TypeLiteral.get(Types.newParameterizedType(ITranslatorFactory.class, new Type[] { colelctionUpdateType }));
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   protected TypeLiteral<ITranslator<CollectionUpdate<T>>> getTranslatorType() {
/* 35 */     return this.translatorType;
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   protected TypeLiteral<ITranslatorFactory<CollectionUpdate<T>>> getFactoryType() {
/* 41 */     return this.factoryType;
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/spi/translators/CollectionTranslatorModule.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */