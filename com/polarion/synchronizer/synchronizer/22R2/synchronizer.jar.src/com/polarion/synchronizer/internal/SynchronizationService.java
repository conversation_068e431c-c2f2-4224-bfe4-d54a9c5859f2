/*     */ package com.polarion.synchronizer.internal;
/*     */ 
/*     */ import com.polarion.platform.jobs.IProgressMonitor;
/*     */ import com.polarion.subterra.base.data.identification.IContextId;
/*     */ import com.polarion.synchronizer.IMapping;
/*     */ import com.polarion.synchronizer.IProxyConfiguration;
/*     */ import com.polarion.synchronizer.ISynchronizationContext;
/*     */ import com.polarion.synchronizer.ISynchronizationService;
/*     */ import com.polarion.synchronizer.ISynchronizationTask;
/*     */ import com.polarion.synchronizer.configuration.FieldMappingGroupConfiguration;
/*     */ import com.polarion.synchronizer.configuration.IAttributeMapper;
/*     */ import com.polarion.synchronizer.configuration.IConfigurationHelper;
/*     */ import com.polarion.synchronizer.configuration.IConnection;
/*     */ import com.polarion.synchronizer.configuration.IProjectAware;
/*     */ import com.polarion.synchronizer.configuration.ISyncPair;
/*     */ import com.polarion.synchronizer.internal.configuration.FieldMappingConfiguration;
/*     */ import com.polarion.synchronizer.internal.configuration.SyncConfiguration;
/*     */ import com.polarion.synchronizer.mapping.Mapping;
/*     */ import com.polarion.synchronizer.mapping.MappingFactory;
/*     */ import com.polarion.synchronizer.model.Direction;
/*     */ import com.polarion.synchronizer.model.IProxy;
/*     */ import java.util.Collection;
/*     */ import java.util.function.Predicate;
/*     */ import javax.inject.Inject;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ import org.jetbrains.annotations.Nullable;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class SynchronizationService
/*     */   implements ISynchronizationService
/*     */ {
/*     */   @NotNull
/*     */   private final IConfigurationHelper configurationHelper;
/*     */   @NotNull
/*     */   private final MappingFactory mappingFactory;
/*     */   @NotNull
/*     */   private SingletonContext singletonContext;
/*     */   @NotNull
/*     */   private final ProxyManager proxyManager;
/*     */   
/*     */   @Inject
/*     */   public SynchronizationService(@NotNull IConfigurationHelper configurationHelper, @NotNull SingletonContext singletonContext, @NotNull MappingFactory mappingFactory, @NotNull ProxyManager proxyManager) {
/*  47 */     this.configurationHelper = configurationHelper;
/*  48 */     this.singletonContext = singletonContext;
/*  49 */     this.mappingFactory = mappingFactory;
/*  50 */     this.proxyManager = proxyManager;
/*     */   }
/*     */ 
/*     */   
/*     */   @Nullable
/*     */   public ISyncPair loadSyncPair(@NotNull IContextId contextId, @NotNull String syncSetId) {
/*  56 */     SyncConfiguration syncConfiguration = this.configurationHelper.loadConfiguration(contextId.getContextName(), false);
/*  57 */     return (ISyncPair)syncConfiguration.getSyncPairs().get(syncSetId);
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public ISynchronizationTask createSynchronizationTask(@NotNull ISyncPair configuration, @NotNull IContextId contextId, @NotNull IProgressMonitor progressMonitor) {
/*  63 */     injectProject(configuration.getLeft(), contextId);
/*  64 */     injectProject(configuration.getRight(), contextId);
/*     */     
/*  66 */     IProxyConfiguration<?> leftConfig = configuration.getLeft();
/*  67 */     IProxyConfiguration<?> rightConfig = configuration.getRight();
/*     */     
/*  69 */     String leftSystemId = leftConfig.getSystemIdentifier();
/*  70 */     String rightSystemId = rightConfig.getSystemIdentifier();
/*  71 */     IProxy leftProxy = newProxy((IProxyConfiguration)leftConfig);
/*  72 */     IProxy rightProxy = newProxy((IProxyConfiguration)rightConfig);
/*  73 */     checkForVirtualTypesForPlannedInField(configuration);
/*     */     
/*  75 */     IAttributeMapper attributeMaping = this.mappingFactory.loadMapping(configuration.getMapping(), leftProxy, rightProxy);
/*     */     
/*  77 */     Mapping mapping = new Mapping(attributeMaping, configuration.getNewItemDirection(), configuration.getDeleteDirection());
/*  78 */     SystemContext left = new SystemContext(leftSystemId, leftProxy);
/*  79 */     SystemContext right = new SystemContext(rightSystemId, rightProxy);
/*  80 */     String projectId = contextId.getContextName();
/*  81 */     Collection<String> projectsUsing = this.configurationHelper.getProjectsUsing(rightConfig.getConnection());
/*  82 */     projectsUsing.remove(projectId);
/*  83 */     ISynchronizationTask synchronizationTask = new SynchronizationTask(left, right, projectId, 
/*  84 */         projectsUsing, (IMapping)mapping, this.singletonContext, progressMonitor);
/*  85 */     synchronizationTask.setLeftDeleteAction(configuration.getLeftDeleteAction());
/*  86 */     synchronizationTask.setReAddMissingOnRight(configuration.isReAddMissingOnRight());
/*  87 */     synchronizationTask.setDeleteOutOfscopeItems(configuration.isDeleteOutOfScope());
/*     */     
/*  89 */     if (configuration.getPostProcessingActions() != null) {
/*  90 */       synchronizationTask.addPostProcessingActions(configuration.getPostProcessingActions());
/*     */     }
/*     */     
/*  93 */     return synchronizationTask;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void checkForVirtualTypesForPlannedInField(@NotNull ISyncPair configuration) {
/* 100 */     if (configuration.getMapping().getFieldMappingGroups().stream().anyMatch(mapping -> (mapping.getLeftType().equals("com.polarion.synchronizer:polarion:virtual-type:plan") && mapping.getRightType().equals("version")))) {
/* 101 */       Predicate<? super FieldMappingGroupConfiguration> isPlannedInFieldAdded = mapping -> mapping.getFieldMappings().stream().anyMatch(());
/* 102 */       if (configuration.getMapping().getFieldMappingGroups().stream().filter(isPlannedInFieldAdded).count() == 0L && 
/* 103 */         configuration.getMapping().getDefaultMappingGroup().getFieldMappings().stream().noneMatch(field -> "plannedIn".equals(field.getLeft()))) {
/* 104 */         FieldMappingConfiguration newField = new FieldMappingConfiguration("plannedIn", "fixVersionIds", configuration.getNewItemDirection(), Direction.LEFT_TO_RIGHT);
/* 105 */         configuration.getMapping().getDefaultMappingGroup().getFieldMappings().add(newField);
/*     */       } 
/*     */     } 
/*     */   }
/*     */   
/*     */   private void injectProject(IProxyConfiguration<? extends IConnection> proxyConfig, IContextId contextId) {
/* 111 */     if (proxyConfig instanceof IProjectAware) {
/* 112 */       IProjectAware projectAware = (IProjectAware)proxyConfig;
/* 113 */       projectAware.setProject(contextId.getContextName());
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public IProxy newProxy(@NotNull IProxyConfiguration<? extends IConnection> proxyConfiguration) {
/* 120 */     return this.proxyManager.newProxy(proxyConfiguration);
/*     */   }
/*     */ 
/*     */   
/*     */   @Nullable
/*     */   public String checkConnection(@NotNull IConnection connection) {
/* 126 */     return this.proxyManager.checkConnection(connection);
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public Collection<Class<? extends IProxyConfiguration<? extends IConnection>>> getConfigurationClasses() {
/* 132 */     return this.proxyManager.getConfigurationClasses();
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public Collection<Class<? extends IConnection>> getConnectionClasses() {
/* 138 */     return this.proxyManager.getConnectionClasses();
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public ISynchronizationContext getSynchronizationContext() {
/* 144 */     return this.singletonContext.getContext();
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/internal/SynchronizationService.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */