package com.polarion.synchronizer.retryactions;

import java.util.Optional;
import java.util.function.Consumer;
import java.util.function.Predicate;
import org.jetbrains.annotations.NotNull;

public interface IRetryActionStrategy {
  @NotNull
  <T> Optional<T> execute(@NotNull IRetryAction<T> paramIRetryAction, @NotNull Predicate<T> paramPredicate, @NotNull Consumer<Optional<T>> paramConsumer);
}


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/retryactions/IRetryActionStrategy.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */