package com.polarion.synchronizer.proxy.polarion;

import org.jetbrains.annotations.NotNull;

public interface IActionFactory {
  @NotNull
  IMoveToTrashAction createMoveToTrashAction();
  
  @NotNull
  ISetAttributeAction createSetAttributeAction(@NotNull String paramString1, @NotNull String paramString2);
}


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/polarion/IActionFactory.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */