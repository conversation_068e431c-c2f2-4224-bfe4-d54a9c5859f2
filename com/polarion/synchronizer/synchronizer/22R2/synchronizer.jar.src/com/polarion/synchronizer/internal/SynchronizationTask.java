/*     */ package com.polarion.synchronizer.internal;
/*     */ 
/*     */ import com.polarion.core.util.ObjectUtils;
/*     */ import com.polarion.platform.i18n.Localization;
/*     */ import com.polarion.platform.internal.ExecutionThreadMonitor;
/*     */ import com.polarion.platform.internal.TxLogger;
/*     */ import com.polarion.platform.jobs.IProgressMonitor;
/*     */ import com.polarion.synchronizer.IAction;
/*     */ import com.polarion.synchronizer.IBaselineProvider;
/*     */ import com.polarion.synchronizer.IConnectionMap;
/*     */ import com.polarion.synchronizer.IDependencyManager;
/*     */ import com.polarion.synchronizer.ILogger;
/*     */ import com.polarion.synchronizer.IMapping;
/*     */ import com.polarion.synchronizer.IPersistence;
/*     */ import com.polarion.synchronizer.IPersistenceOperation;
/*     */ import com.polarion.synchronizer.IPostProcessingAction;
/*     */ import com.polarion.synchronizer.ISynchronizationContext;
/*     */ import com.polarion.synchronizer.ISynchronizationHook;
/*     */ import com.polarion.synchronizer.ISynchronizationResult;
/*     */ import com.polarion.synchronizer.ISynchronizationTask;
/*     */ import com.polarion.synchronizer.SynchronizationException;
/*     */ import com.polarion.synchronizer.SynchronizationStatistics;
/*     */ import com.polarion.synchronizer.configuration.IAttributeMapper;
/*     */ import com.polarion.synchronizer.model.CreateResult;
/*     */ import com.polarion.synchronizer.model.Direction;
/*     */ import com.polarion.synchronizer.model.IProxy;
/*     */ import com.polarion.synchronizer.model.Side;
/*     */ import com.polarion.synchronizer.model.TransferItem;
/*     */ import com.polarion.synchronizer.model.UpdateResult;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Arrays;
/*     */ import java.util.Collection;
/*     */ import java.util.Collections;
/*     */ import java.util.HashSet;
/*     */ import java.util.LinkedHashSet;
/*     */ import java.util.List;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ import org.jetbrains.annotations.Nullable;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class SynchronizationTask
/*     */   implements ISynchronizationTask
/*     */ {
/*     */   @Nullable
/*     */   private IProxy left;
/*     */   @Nullable
/*     */   private IProxy right;
/*     */   @Nullable
/*     */   private final IMapping mapping;
/*     */   @Nullable
/*     */   private Collection<String> fieldsLeft;
/*     */   @Nullable
/*     */   private Collection<String> fieldsRight;
/*     */   @Nullable
/*     */   private Collection<ISynchronizationHook> hooks;
/*     */   @NotNull
/*     */   private final ISynchronizationContext context;
/*     */   @NotNull
/*     */   private final ILogger logger;
/*     */   @Nullable
/*     */   private SideContext leftContext;
/*     */   @Nullable
/*     */   private SideContext rightContext;
/*     */   @Nullable
/*     */   private IConnectionMap connectionMap;
/*     */   @Nullable
/*     */   private IAction leftDeleteAction;
/*     */   private boolean reAddMissing;
/*     */   @NotNull
/*  80 */   private List<String> deletedOnLeftInfo = new ArrayList<>();
/*     */   
/*     */   @Nullable
/*  83 */   private Collection<IPostProcessingAction> postProcessingActions = new ArrayList<>(); @Nullable
/*     */   private MultiItemMapping multiItemMapping; @NotNull
/*     */   private final String leftSystemId; @NotNull
/*     */   private final String rightSystemId; @Nullable
/*     */   private IPersistence persistence; @Nullable
/*     */   private IPersistenceOperation persistenceOperation; private final String projectId; @Nullable
/*     */   private PairBuilder pairBuilder; @Nullable
/*     */   private Collection<String> externalProjectIds; @NotNull
/*     */   private IProgressMonitor progressMonitor; private boolean deleteOutOfScope;
/*     */   private class SideContext { public Side side; public IProxy proxy;
/*  93 */     public final SynchronizationResult synchronizationResult = new SynchronizationResult(); public String systemId; public Collection<String> fields;
/*     */     
/*     */     public SideContext(Side side, IProxy proxy, String systemId, Collection<String> fields) {
/*  96 */       this.side = side;
/*  97 */       this.proxy = proxy;
/*  98 */       this.fields = fields;
/*  99 */       this.systemId = systemId;
/*     */     }
/*     */     
/*     */     public void addConnection(String sourceId, String targetId) {
/* 103 */       ((IConnectionMap)ObjectUtils.notNull(SynchronizationTask.this.connectionMap)).addConnection(sourceId, targetId, this.side);
/*     */     } }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @NotNull
/* 132 */   private String txLoggerArea = TxLogger.contributionName(this, getClass());
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public SynchronizationTask(@NotNull SystemContext left, @NotNull SystemContext right, @NotNull String projectId, @NotNull Collection<String> externalProjectIds, @NotNull IMapping mapping, @NotNull SingletonContext singletonContext, @NotNull IProgressMonitor progressMonitor) {
/* 140 */     this.left = left.getProxy();
/* 141 */     this.right = right.getProxy();
/* 142 */     this.leftSystemId = left.getSystemId();
/* 143 */     this.rightSystemId = right.getSystemId();
/* 144 */     this.persistence = singletonContext.getPersistence();
/* 145 */     this.mapping = mapping;
/* 146 */     this.context = singletonContext.getContext();
/* 147 */     this.projectId = projectId;
/* 148 */     this.externalProjectIds = externalProjectIds;
/* 149 */     this.logger = this.context.getLogger();
/* 150 */     this.multiItemMapping = new MultiItemMapping(this.context, mapping.getAttributeMapper());
/* 151 */     this.progressMonitor = progressMonitor;
/*     */   }
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public SynchronizationTask(IProxy left, IProxy right, String leftSystemId, String rightSystemId, IPersistence persistence, String projectId, IMapping mapping, ISynchronizationContext context, @NotNull IProgressMonitor progressMonitor) {
/* 157 */     this(new SystemContext(leftSystemId, left), new SystemContext(rightSystemId, right), projectId, Collections.EMPTY_LIST, mapping, new SingletonContext(persistence, context), progressMonitor);
/*     */   }
/*     */   
/*     */   private void preExecute(boolean isPartialExecution) {
/* 161 */     IPersistenceOperation persistenceOperation = checkClosed(this.persistenceOperation);
/* 162 */     IBaselineProvider baselineProvider = persistenceOperation.getBaselineProvider(this.leftSystemId, this.rightSystemId);
/* 163 */     this.context.setBaselineProvider(baselineProvider);
/* 164 */     this.context.setConnectionMap(persistenceOperation.getConnectionMap(this.leftSystemId, this.rightSystemId, checkClosed(this.externalProjectIds)));
/* 165 */     this.connectionMap = this.context.getConnectionMap();
/* 166 */     this.fieldsLeft = new HashSet<>(getMapping().getAttributeMapper().getRequiredFields(Side.LEFT));
/* 167 */     this.fieldsRight = new HashSet<>(getMapping().getAttributeMapper().getRequiredFields(Side.RIGHT));
/* 168 */     if (!isPartialExecution) {
/* 169 */       for (IPostProcessingAction action : checkClosed(this.postProcessingActions)) {
/* 170 */         getFields(Side.LEFT).addAll(action.getAdditionalKeysLeft(getLeftProxy()));
/* 171 */         getFields(Side.RIGHT).addAll(action.getAdditionalKeysRight(getRightProxy()));
/*     */       } 
/*     */     }
/* 174 */     this.leftContext = new SideContext(Side.LEFT, this.left, this.leftSystemId, this.fieldsLeft);
/* 175 */     this.rightContext = new SideContext(Side.RIGHT, this.right, this.rightSystemId, this.fieldsRight);
/* 176 */     this.hooks = Arrays.asList(new ISynchronizationHook[] { (ISynchronizationHook)baselineProvider });
/* 177 */     this.pairBuilder = new PairBuilder((IConnectionMap)ObjectUtils.notNull(this.connectionMap), this.reAddMissing, this.deleteOutOfScope);
/*     */   }
/*     */   
/*     */   private <T> T executeAndTrackSubtask(@NotNull String subTaskName, @NotNull SyncSubTaskHandler<T> syncSubTaskHandler) {
/* 181 */     this.progressMonitor.subTask(subTaskName);
/* 182 */     TxLogger.began(this.txLoggerArea, subTaskName);
/*     */     try {
/* 184 */       this.logger.info("Starting: " + subTaskName);
/* 185 */       T result = syncSubTaskHandler.handle();
/* 186 */       this.logger.info("Done: " + subTaskName);
/* 187 */       this.progressMonitor.worked(1);
/* 188 */       return result;
/*     */     } finally {
/* 190 */       TxLogger.ended(this.txLoggerArea, subTaskName);
/* 191 */       ExecutionThreadMonitor.checkForInterruption();
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   public void execute() {
/* 197 */     this.persistenceOperation = ((IPersistence)checkClosed(this.persistence)).createOperation(this.projectId);
/*     */     try {
/* 199 */       preExecute(false);
/* 200 */       this.logger.info(getLabel());
/*     */       
/* 202 */       this.context.currentTask(this);
/* 203 */       this.context.currentSystem((getLeftContext()).systemId, (getRightContext()).systemId);
/*     */       
/* 205 */       Collection<TransferItem> leftItems = executeAndTrackSubtask(Localization.getString("polarion.jobs.synchronizer.subtask.retrievingItemsLeftSide"), () -> new LinkedHashSet(getLeftProxy().getScopeItems((getLeftContext()).fields)));
/*     */ 
/*     */       
/* 208 */       Collection<TransferItem> rightItems = executeAndTrackSubtask(Localization.getString("polarion.jobs.synchronizer.subtask.retrievingItemsRightSide", new String[] { (getRightContext()).systemId }), () -> new LinkedHashSet(getRightProxy().getScopeItems((getRightContext()).fields)));
/*     */ 
/*     */       
/* 211 */       if (getLeftProxy().hasNonSynchronizableFields()) {
/* 212 */         (getLeftContext()).synchronizationResult.setHasWarnings();
/*     */       }
/* 214 */       if (getRightProxy().hasNonSynchronizableFields()) {
/* 215 */         (getRightContext()).synchronizationResult.setHasWarnings();
/*     */       }
/*     */       
/* 218 */       synchronize(leftItems, rightItems, false);
/*     */     } finally {
/* 220 */       this.persistenceOperation.close();
/* 221 */       this.context.setBaselineProvider(null);
/* 222 */       this.context.setConnectionMap(null);
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   public void execute(Collection<String> leftIds, Collection<String> rightIds) {
/* 228 */     this.persistenceOperation = ((IPersistence)checkClosed(this.persistence)).createOperation(this.projectId);
/*     */     try {
/* 230 */       preExecute(true);
/* 231 */       this.context.currentTask(this);
/* 232 */       this.context.currentSystem((getLeftContext()).systemId, (getRightContext()).systemId);
/*     */       
/* 234 */       Collection<TransferItem> leftItems = executeAndTrackSubtask(Localization.getString("polarion.jobs.synchronizer.subtask.retrievingItemsLeftSide"), () -> new LinkedHashSet(getLeftProxy().getItems(paramCollection, (getLeftContext()).fields)));
/*     */ 
/*     */       
/* 237 */       Collection<TransferItem> rightItems = executeAndTrackSubtask(Localization.getString("polarion.jobs.synchronizer.subtask.retrievingItemsRightSide", new String[] { (getRightContext()).systemId }), () -> new LinkedHashSet(getRightProxy().getItems(paramCollection, (getRightContext()).fields)));
/*     */ 
/*     */       
/* 240 */       synchronize(leftItems, rightItems, true);
/*     */     } finally {
/* 242 */       this.persistenceOperation.close();
/*     */     } 
/*     */   }
/*     */   
/*     */   private void synchronize(Collection<TransferItem> leftItems, Collection<TransferItem> rightItems, boolean isPartialExection) {
/* 247 */     LoadPairsResult load = executeAndTrackSubtask(Localization.getString("polarion.jobs.synchronizer.subtask.loadingBaseline"), () -> ((PairBuilder)checkClosed(this.pairBuilder)).loadPairs(paramCollection1, paramCollection2));
/*     */ 
/*     */     
/* 250 */     this.logger.info("New items from Polarion:\t" + load.newLeft.size());
/* 251 */     this.logger.info("New items from " + getRightSystemId() + ":\t" + load.newRight.size());
/*     */     
/* 253 */     update(load, false);
/*     */     
/* 255 */     this.logger.info("Results of synchronization in Polarion:");
/* 256 */     (getLeftContext()).synchronizationResult.log(this.logger);
/*     */     
/* 258 */     this.logger.info("Results of synchronization on " + getRightSystemId());
/* 259 */     (getRightContext()).synchronizationResult.log(this.logger);
/*     */     
/* 261 */     if (!isPartialExection) {
/* 262 */       for (IPostProcessingAction action : checkClosed(this.postProcessingActions)) {
/* 263 */         action.execute(this);
/*     */       }
/*     */     }
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private Collection<String> getFields(Side fromSide) {
/* 270 */     return checkClosed((fromSide == Side.LEFT) ? this.fieldsLeft : this.fieldsRight);
/*     */   }
/*     */ 
/*     */   
/*     */   private void update(LoadPairsResult loadPairs, boolean isPartial) {
/* 275 */     List<TransferItem> deletedOnLeft = executeAndTrackSubtask(Localization.getString("polarion.jobs.synchronizer.subtask.retrievingMissingItemsLeftSide"), () -> ((PairBuilder)checkClosed(this.pairBuilder)).loadMissing(paramLoadPairsResult.pairs, paramLoadPairsResult.singleRight, (getRightContext()).side, new LoadContext((getLeftContext()).proxy, (getLeftContext()).fields)));
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 280 */     List<TransferItem> deletedOnRight = executeAndTrackSubtask(Localization.getString("polarion.jobs.synchronizer.subtask.retrievingMissingItemsRightSide", new String[] { (getRightContext()).systemId }), () -> ((PairBuilder)checkClosed(this.pairBuilder)).loadMissing(paramLoadPairsResult.pairs, paramLoadPairsResult.singleLeft, (getLeftContext()).side, new LoadContext((getRightContext()).proxy, (getRightContext()).fields)));
/*     */ 
/*     */ 
/*     */     
/* 284 */     MultiItemMapping.MultiItemMappingResult executeMapping = executeAndTrackSubtask(Localization.getString("polarion.jobs.synchronizer.subtask.mappingCreatesUpdates"), () -> {
/*     */           this.deletedOnLeftInfo.clear();
/*     */           
/*     */           recreate(paramLoadPairsResult, paramList1, paramList2);
/*     */           
/*     */           this.logger.info("Items to be synchronized:\t" + paramLoadPairsResult.pairs.size());
/*     */           return ((MultiItemMapping)checkClosed(this.multiItemMapping)).executeMapping(paramLoadPairsResult, getMapping().getCreateDirection(), paramBoolean);
/*     */         });
/* 292 */     if (!this.deletedOnLeftInfo.isEmpty()) {
/* 293 */       removeDeletedPlanFromConnectionMap(this.deletedOnLeftInfo, Side.LEFT);
/*     */     }
/* 295 */     executeAndTrackSubtask(Localization.getString("polarion.jobs.synchronizer.subtask.updateItemsRightSide", new String[] { (getRightContext()).systemId }), () -> update(paramMultiItemMappingResult, getLeftContext(), getRightContext()));
/*     */     
/* 297 */     executeAndTrackSubtask(Localization.getString("polarion.jobs.synchronizer.subtask.updateItemsLeftSide"), () -> update(paramMultiItemMappingResult, getRightContext(), getLeftContext()));
/*     */     
/* 299 */     executeAndTrackSubtask(Localization.getString("polarion.jobs.synchronizer.subtask.deleteItemsRightSide", new String[] { (getRightContext()).systemId }), () -> {
/*     */           deleteItems(getLeftContext(), loadIds(paramList));
/*     */           
/*     */           return null;
/*     */         });
/* 304 */     executeAndTrackSubtask(Localization.getString("polarion.jobs.synchronizer.subtask.deleteItemsLeftSide"), () -> {
/*     */           deleteItems(getRightContext(), loadIds(paramList));
/*     */           return null;
/*     */         });
/*     */   }
/*     */ 
/*     */   
/*     */   private void recreate(LoadPairsResult loadPairs, List<TransferItem> deletedOnLeft, List<TransferItem> deletedOnRight) {
/* 312 */     if (getMapping().getCreateDirection() != null) {
/* 313 */       if (((Direction)ObjectUtils.notNull(getMapping().getCreateDirection())).isFrom(Side.LEFT)) {
/* 314 */         recreateRight(loadPairs, deletedOnRight);
/*     */       }
/* 316 */       if (((Direction)ObjectUtils.notNull(getMapping().getCreateDirection())).isFrom(Side.RIGHT)) {
/* 317 */         recreateLeft(loadPairs, deletedOnLeft);
/*     */       }
/*     */     } 
/*     */   }
/*     */   
/*     */   private void recreateLeft(@NotNull LoadPairsResult loadPairs, @NotNull List<TransferItem> deletedOnLeft) {
/* 323 */     if ((getMapping().getDeleteDirection() == null || !((Direction)ObjectUtils.notNull(getMapping().getDeleteDirection())).isTo(Side.RIGHT)) && !deletedOnLeft.isEmpty()) {
/*     */       
/* 325 */       this.logger.info("Recreating items deleted in Polarion:\t" + deletedOnLeft.size());
/* 326 */       removeConnections(deletedOnLeft, Side.RIGHT);
/* 327 */       loadPairs.newRight.addAll(deletedOnLeft);
/*     */     } 
/*     */   }
/*     */   
/*     */   private void recreateRight(@NotNull LoadPairsResult loadPairs, @NotNull List<TransferItem> deletedOnRight) {
/* 332 */     if ((getMapping().getDeleteDirection() == null || !((Direction)ObjectUtils.notNull(getMapping().getDeleteDirection())).isTo(Side.LEFT)) && !deletedOnRight.isEmpty()) {
/*     */       
/* 334 */       this.logger.info("Recreating items deleted on " + getRightSystemId() + ":\t" + deletedOnRight.size());
/* 335 */       removeConnections(deletedOnRight, Side.LEFT);
/* 336 */       loadPairs.newLeft.addAll(deletedOnRight);
/*     */     } 
/*     */   }
/*     */   
/*     */   private void removeConnections(List<TransferItem> items, Side side) {
/* 341 */     for (TransferItem transferItem : items) {
/*     */ 
/*     */       
/* 344 */       if (transferItem.getType() != null && transferItem.getType().equals("version")) {
/* 345 */         String deletedPlanOnLeft = ((IConnectionMap)checkClosed(this.connectionMap)).getTargetId(transferItem.getId(), side);
/* 346 */         this.deletedOnLeftInfo.add(deletedPlanOnLeft); continue;
/*     */       } 
/* 348 */       ((IConnectionMap)checkClosed(this.connectionMap)).deleteConnection(transferItem.getId(), side);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   private void removeDeletedPlanFromConnectionMap(@NotNull List<String> ids, Side side) {
/* 355 */     for (String itemId : ids) {
/* 356 */       ((IConnectionMap)checkClosed(this.connectionMap)).deleteConnection(itemId, side);
/*     */     }
/*     */   }
/*     */   
/*     */   private void deleteItems(SideContext sideContext, List<String> deletedIds) {
/* 361 */     if (!deletedIds.isEmpty()) {
/* 362 */       this.logger.info("Items identified for deletion " + ((sideContext.side == Side.LEFT) ? " in Polarion" : (" from " + getRightSystemId())) + ":\t" + deletedIds.size());
/*     */       
/* 364 */       if (sideContext.side == Side.LEFT && this.leftDeleteAction != null) {
/*     */         try {
/* 366 */           this.leftDeleteAction.execute(getLeftProxy(), deletedIds);
/* 367 */           this.logger.info("Executed delete action " + this.leftDeleteAction + " for " + deletedIds);
/* 368 */         } catch (Exception e) {
/* 369 */           this.logger.error("Failed to execute delete action " + this.leftDeleteAction + " for " + deletedIds, e);
/*     */         }
/*     */       
/* 372 */       } else if (getMapping().getDeleteDirection() != null && ((Direction)ObjectUtils.notNull(getMapping().getDeleteDirection())).isTo(sideContext.side)) {
/* 373 */         List<UpdateResult> deleteResult = sideContext.proxy.delete(new ArrayList<>(deletedIds));
/* 374 */         for (int i = 0; i < deleteResult.size(); i++) {
/* 375 */           UpdateResult result = deleteResult.get(i);
/* 376 */           String item = String.valueOf(deletedIds.get(i)) + "' in '" + sideContext.systemId;
/* 377 */           if (result.hasError()) {
/* 378 */             this.logger.error("Failed to delete '" + item + "': " + result.getError());
/*     */           } else {
/* 380 */             this.logger.info(String.valueOf(item) + " successfully deleted.");
/*     */           } 
/*     */         } 
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   private List<String> loadIds(Collection<TransferItem> singleItems) {
/* 389 */     List<String> ids = new ArrayList<>(singleItems.size());
/* 390 */     for (TransferItem singleItem : singleItems) {
/* 391 */       ids.add(singleItem.getId());
/*     */     }
/* 393 */     return ids;
/*     */   }
/*     */   private Collection<String> update(@NotNull MultiItemMapping.MultiItemMappingResult mappingResult, @NotNull SideContext from, @NotNull SideContext to) {
/*     */     Collection<String> createdIds;
/* 397 */     from.synchronizationResult.setUnchanged(mappingResult.getEmptySourceItems(from.side));
/* 398 */     from.synchronizationResult.setSourceItems(mappingResult.getSourceItems(from.side));
/*     */     
/* 400 */     List<TransferItem> mappedItems = mappingResult.getUpdateItemsFor(to.side);
/* 401 */     this.logger.info("Items to be created/updated " + ((to.side == Side.LEFT) ? "in Polarion" : ("on " + getRightSystemId())) + ":\t" + mappedItems.size());
/*     */ 
/*     */     
/* 404 */     if (mappedItems.isEmpty()) {
/* 405 */       createdIds = Collections.emptyList();
/*     */     } else {
/* 407 */       createdIds = processUpdateResults(sendUpdate(to, mappedItems), mappingResult, from, to);
/*     */     } 
/*     */     
/* 410 */     List<TransferItem> allSourceItems = new ArrayList<>(mappingResult.getFilledSourceBaselines(from.side));
/* 411 */     allSourceItems.addAll(mappingResult.getEmptySourceBaselines(from.side));
/*     */     
/* 413 */     for (ISynchronizationHook hook : checkClosed(this.hooks)) {
/* 414 */       hook.afterUpdate(mappedItems, allSourceItems, getFields(from.side), to.side);
/*     */     }
/*     */     
/* 417 */     return createdIds;
/*     */   }
/*     */   
/*     */   private Collection<String> processUpdateResults(@NotNull List<UpdateResult> results, @NotNull MultiItemMapping.MultiItemMappingResult mappingResult, @NotNull SideContext from, @NotNull SideContext to) {
/* 421 */     Collection<String> createdIds = new ArrayList<>();
/*     */ 
/*     */     
/* 424 */     for (int i = results.size() - 1; i >= 0; i--) {
/* 425 */       UpdateResult result = results.get(i);
/*     */       
/* 427 */       mappingResult.getHierarchyProcessor().handleResult(((TransferItem)mappingResult.getFilledSourceBaselines(from.side).get(i)).getId(), result, to.side);
/* 428 */       if (result instanceof CreateResult) {
/* 429 */         processCreateResult(result, mappingResult, from, to, i, createdIds);
/*     */       } else {
/* 431 */         processUpdateResult(result, mappingResult, to, i);
/*     */       } 
/*     */     } 
/*     */     
/* 435 */     return createdIds;
/*     */   }
/*     */   
/*     */   private void processCreateResult(@NotNull UpdateResult result, @NotNull MultiItemMapping.MultiItemMappingResult mappingResult, @NotNull SideContext from, @NotNull SideContext to, int i, @NotNull Collection<String> createdIds) {
/* 439 */     String sourceId = ((TransferItem)mappingResult.getFilledSourceBaselines(from.side).get(i)).getId();
/* 440 */     CreateResult createResult = (CreateResult)result;
/* 441 */     to.synchronizationResult.trackCreate(createResult, ((IAttributeMapper.UnidirectionalResult)mappingResult.getUpdatesFor(to.side).get(i)).getSource(), mappingResult.getUpdateItemsFor(to.side).get(i));
/* 442 */     String createdId = createResult.getCreatedId();
/* 443 */     if (createdId == null) {
/* 444 */       this.logger.error("Could not create item for '" + sourceId + "' on '" + to.systemId + "':" + createResult.getError(), createResult.getException());
/* 445 */       mappingResult.getFilledSourceBaselines(from.side).remove(i);
/* 446 */       mappingResult.getUpdatesFor(to.side).remove(i);
/* 447 */       mappingResult.getUpdateItemsFor(to.side).remove(i);
/* 448 */     } else if (sourceId == null) {
/* 449 */       this.logger.error("Created item but source id is missing. Can't connect created item.");
/*     */     } else {
/* 451 */       ((TransferItem)mappingResult.getUpdateItemsFor(to.side).get(i)).setId(createdId);
/* 452 */       createdIds.add(createdId);
/* 453 */       if (createResult.hasError()) {
/* 454 */         this.logger.warn(String.format("Item '%s' (%s) from '%s' (%s) was created with an error: %s", new Object[] { createdId, to.systemId, sourceId, from.systemId, createResult.getError() }));
/* 455 */         mappingResult.getFilledSourceBaselines(from.side).remove(i);
/* 456 */         mappingResult.getUpdatesFor(to.side).remove(i);
/* 457 */         mappingResult.getUpdateItemsFor(to.side).remove(i);
/*     */       } 
/* 459 */       from.addConnection(sourceId, createResult.getCreatedId());
/* 460 */       IDependencyManager dependencyManager = this.context.getDependencyManager();
/* 461 */       if (dependencyManager != null) {
/* 462 */         dependencyManager.resolve(from.systemId, sourceId);
/*     */       }
/*     */     } 
/*     */   }
/*     */   
/*     */   private void processUpdateResult(@NotNull UpdateResult result, @NotNull MultiItemMapping.MultiItemMappingResult mappingResult, @NotNull SideContext to, int i) {
/* 468 */     String txOperationName = "processUpdateResult";
/* 469 */     TxLogger.began(this.txLoggerArea, txOperationName);
/*     */     try {
/* 471 */       List<TransferItem> mappedItems = mappingResult.getUpdateItemsFor(to.side);
/* 472 */       TransferItem mappedItem = mappedItems.get(i);
/* 473 */       Side from = to.side.getOtherSide();
/* 474 */       to.synchronizationResult.trackUpdate(result, ((IAttributeMapper.UnidirectionalResult)mappingResult.getUpdatesFor(to.side).get(i)).getSource(), mappedItem);
/* 475 */       if (result.hasError()) {
/* 476 */         mappingResult.getFilledSourceBaselines(from).remove(i);
/* 477 */         mappingResult.getUpdatesFor(to.side).remove(i);
/* 478 */         mappedItems.remove(i);
/* 479 */         this.logger.error("Could not update '" + mappedItem.getId() + "' on '" + to.systemId + "':" + result.getError(), result.getException());
/*     */       } 
/*     */     } finally {
/* 482 */       TxLogger.ended(this.txLoggerArea, txOperationName);
/* 483 */       ExecutionThreadMonitor.checkForInterruption();
/*     */     } 
/*     */   }
/*     */   
/*     */   private List<UpdateResult> sendUpdate(SideContext to, List<TransferItem> mappedItems) {
/* 488 */     String txOperationName = "sendUpdate";
/* 489 */     TxLogger.began(this.txLoggerArea, txOperationName);
/*     */     try {
/* 491 */       this.logger.debug("Update content: " + mappedItems);
/* 492 */       List<TransferItem> copiedItems = new ArrayList<>(mappedItems.size());
/* 493 */       for (TransferItem mappedItem : mappedItems) {
/* 494 */         copiedItems.add((TransferItem)mappedItem.clone());
/*     */       }
/* 496 */       List<UpdateResult> results = to.proxy.update(copiedItems);
/* 497 */       if (results.size() != mappedItems.size()) {
/* 498 */         throw new SynchronizationException(
/* 499 */             "Proxy Error: List<UpdateResult> size didn't match number of items to be updated (expected: " + 
/* 500 */             mappedItems.size() + " got: " + results.size() + ".\nUpdate results:" + results.toString());
/*     */       }
/* 502 */       return results;
/*     */     } finally {
/* 504 */       TxLogger.ended(this.txLoggerArea, txOperationName);
/* 505 */       ExecutionThreadMonitor.checkForInterruption();
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   public SynchronizationStatistics getLeftStatistics() {
/* 511 */     return (getLeftContext()).synchronizationResult;
/*     */   }
/*     */ 
/*     */   
/*     */   public SynchronizationStatistics getRightStatistics() {
/* 516 */     return (getRightContext()).synchronizationResult;
/*     */   }
/*     */ 
/*     */   
/*     */   public void setLeftDeleteAction(IAction leftDeleteAction) {
/* 521 */     this.leftDeleteAction = leftDeleteAction;
/*     */   }
/*     */ 
/*     */   
/*     */   public void close() {
/* 526 */     getLeftProxy().close();
/* 527 */     getRightProxy().close();
/*     */ 
/*     */     
/* 530 */     this.left = null;
/* 531 */     this.right = null;
/* 532 */     this.leftContext = null;
/* 533 */     this.rightContext = null;
/* 534 */     this.postProcessingActions = null;
/* 535 */     this.fieldsLeft = null;
/* 536 */     this.fieldsRight = null;
/* 537 */     this.hooks = null;
/* 538 */     this.connectionMap = null;
/* 539 */     this.multiItemMapping = null;
/* 540 */     this.persistenceOperation = null;
/* 541 */     this.persistence = null;
/* 542 */     this.externalProjectIds = null;
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public String getLeftSystemId() {
/* 548 */     return this.leftSystemId;
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public String getRightSystemId() {
/* 554 */     return this.rightSystemId;
/*     */   }
/*     */ 
/*     */   
/*     */   public void setReAddMissingOnRight(boolean reAddMissing) {
/* 559 */     this.reAddMissing = reAddMissing;
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private <T> T checkClosed(@Nullable T object) {
/* 564 */     if (object == null) {
/* 565 */       throw new IllegalStateException("task is closed, can't access this value.");
/*     */     }
/* 567 */     return object;
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public IProxy getLeftProxy() {
/* 573 */     return checkClosed(this.left);
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public IProxy getRightProxy() {
/* 579 */     return checkClosed(this.right);
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public IMapping getMapping() {
/* 585 */     return checkClosed(this.mapping);
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public ISynchronizationResult getLeftResult() {
/* 591 */     return (getLeftContext()).synchronizationResult;
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public ISynchronizationResult getRightResult() {
/* 597 */     return (getRightContext()).synchronizationResult;
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private SideContext getLeftContext() {
/* 602 */     return checkClosed(this.leftContext);
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private SideContext getRightContext() {
/* 607 */     return checkClosed(this.rightContext);
/*     */   }
/*     */ 
/*     */   
/*     */   public void addPostProcessingActions(@NotNull Collection<IPostProcessingAction> actions) {
/* 612 */     ((Collection<IPostProcessingAction>)checkClosed(this.postProcessingActions)).addAll(actions);
/*     */   }
/*     */ 
/*     */   
/*     */   public void setDeleteOutOfscopeItems(boolean delete) {
/* 617 */     this.deleteOutOfScope = delete;
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public ILogger getLogger() {
/* 623 */     return this.logger;
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public String getLabel() {
/* 629 */     String leftTarget = getLeftProxy().getTargetName();
/* 630 */     String rightTarget = getRightProxy().getTargetName();
/* 631 */     String left = (leftTarget == null) ? this.leftSystemId : leftTarget;
/* 632 */     String right = (rightTarget == null) ? this.rightSystemId : rightTarget;
/* 633 */     return "Synchronizing from " + left + " to " + right + ".";
/*     */   }
/*     */   
/*     */   @FunctionalInterface
/*     */   private static interface SyncSubTaskHandler<T> {
/*     */     T handle();
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/internal/SynchronizationTask.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */