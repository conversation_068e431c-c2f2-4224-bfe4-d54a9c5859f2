package com.polarion.synchronizer.proxy.configuration;

import java.util.Collection;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public interface IConfigurationService {
  Collection<SpecificationConfiguration> findConfigurations(@NotNull String paramString1, @NotNull String paramString2, @NotNull String paramString3, boolean paramBoolean);
  
  @Nullable
  SpecificationConfiguration loadConfiguration(@NotNull String paramString1, @NotNull String paramString2);
  
  void saveConfigurationAsTemplate(@Nullable String paramString1, @NotNull String paramString2, @NotNull SpecificationTemplate paramSpecificationTemplate);
  
  void saveConfiguration(@NotNull String paramString1, @NotNull String paramString2, @NotNull SpecificationConfiguration paramSpecificationConfiguration);
  
  @NotNull
  Collection<SpecificationTemplate> loadTemplates(@Nullable String paramString);
}


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/configuration/IConfigurationService.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */