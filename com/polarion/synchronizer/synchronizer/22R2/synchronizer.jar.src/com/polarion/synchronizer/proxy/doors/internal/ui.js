App.DoorsProxyConfiguration = App.ProxyConfiguration.extend({
	resourceProperties : [ 'proxyType', 'connection', 'moduleId', 'typeAttribute'],
	name : 'DOORS Proxy Configuration',
	description : function() {
		var connectionId = this.connection == null ? '_configurationDescriptionNoConnection'.loc() : this.connection.id;
		return 'Connection: ' + connectionId + ', Document: '; // Rest gets added in index.jsp
	}.property('moduleId'),
	closeUrl : function() {
		return "/polarion/doorsconnector/rest/connections/" + this.get('connection.remoteConnectionId') + "/close"
	}.property()
});

App.DoorsProxyConfigurationView = Ember.View.extend({
	template : Ember.Handlebars.compile([
'<div  class="text"><div><div>',
'<a {{action connect target="controller.content.connection"}} href="#" class="proxyAction"><img src="img/connect.png"/> {{t doors.connect}}</a>',
'<a {{action disconnect target="controller.content.connection"}} href="#" class="proxyAction"><img src="img/cancel.gif"/> {{t doors.disconnect}}</a>',
'</div></div></div>',
'<div class="control-group">',
'<label class="control-label" title="'+"_doors.module.tooltip".loc()+'"><span style=\"color:red\">*</span>'+"_doors.module".loc()+'</label>', 
'<div class="controls">',
  '{{view Ember.TextField valueBinding="moduleId"}}', 
'</div>', 
'</div>',
].join('\n')),
	classNames : ['rows']
});

App.DoorsConnectionConfiguration = App.Connection
		.extend({
resourceProperties : [ 'id', 'connectionType', 'remoteConnectionId'],
description : function() {
	return 'Connection to DOORS';
}.property(),
user : function() {
	return "Will be ignored";
}.property('user'),
password : function() {
	return "Will be ignored";
}.property('password'),
remoteConnectionId : function() {
	return this.get('id')
}.property('id'),
connect: function() {
	window.top.copyTextToClipBoard(this.get('script'), 
			"_doors.connectMessage".loc(), "_doors.connectTitle".loc())
},
actionsView: Ember.View.extend({
	template : Ember.Handlebars.compile([
		'<a {{action connect target="controller"}} href="#" class="proxyAction"><img src="img/connect.png"/> {{t doors.connect}}</a>',
		'<a {{action disconnect target="controller"}} href="#" class="proxyAction"><img src="img/cancel.gif"/> {{t doors.disconnect}}</a>'
	].join('\n'))
}),

doorsConnectionUrl : function() {
	return "/polarion/doorsconnector/rest/connections/" + this.get('remoteConnectionId')
}.property(),
sendRequest: function(method, success) {
	var request = jQuery.ajax({
		url: this.get('doorsConnectionUrl'),
		method: method
	}).done(function() {
		success.apply()
	}).fail(function(response) {
		if(response.status != 404) {
			App.showError(response.responseText);
		}
	})
},
connect: function() {
	var script = this.script;
	this.sendRequest('PUT', function() {
		window.top.copyTextToClipBoard(script, 
				"_doors.connectMessage".loc(), "_doors.connectTitle".loc());
	});
},
disconnect: function() {
	this.sendRequest("DELETE", function() {})
}
});

App.DoorsConfigurationController = Ember.ObjectController.extend({
});

App.DoorsConnectionConfigurationController = Ember.ObjectController.extend({});

App.DoorsConnectionConfigurationView = Ember.View.extend({
    template : Ember.Handlebars.compile(''),
});
