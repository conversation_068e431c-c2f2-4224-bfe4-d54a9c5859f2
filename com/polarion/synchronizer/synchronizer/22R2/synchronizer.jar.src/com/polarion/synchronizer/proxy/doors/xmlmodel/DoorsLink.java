/*    */ package com.polarion.synchronizer.proxy.doors.xmlmodel;
/*    */ 
/*    */ import javax.xml.bind.annotation.XmlElement;
/*    */ import javax.xml.bind.annotation.XmlRootElement;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @XmlRootElement(name = "link")
/*    */ public class DoorsLink
/*    */ {
/*    */   @NotNull
/* 33 */   private String source = "";
/*    */   
/*    */   @NotNull
/* 36 */   private String target = "";
/*    */   
/*    */   @XmlElement(name = "source")
/*    */   @NotNull
/*    */   public String getSource() {
/* 41 */     return this.source;
/*    */   }
/*    */   
/*    */   @XmlElement(name = "target")
/*    */   @NotNull
/*    */   public String getTarget() {
/* 47 */     return this.target;
/*    */   }
/*    */   
/*    */   public void setSource(@NotNull String source) {
/* 51 */     this.source = source;
/*    */   }
/*    */   
/*    */   public void setTarget(@NotNull String target) {
/* 55 */     this.target = target;
/*    */   }
/*    */ 
/*    */   
/*    */   public int hashCode() {
/* 60 */     int prime = 31;
/* 61 */     int result = 1;
/* 62 */     result = 31 * result + this.source.hashCode();
/* 63 */     result = 31 * result + this.target.hashCode();
/* 64 */     return result;
/*    */   }
/*    */ 
/*    */   
/*    */   public boolean equals(Object obj) {
/* 69 */     if (this == obj) {
/* 70 */       return true;
/*    */     }
/* 72 */     if (obj == null) {
/* 73 */       return false;
/*    */     }
/* 75 */     if (getClass() != obj.getClass()) {
/* 76 */       return false;
/*    */     }
/* 78 */     DoorsLink other = (DoorsLink)obj;
/* 79 */     if (!this.source.equals(other.source)) {
/* 80 */       return false;
/*    */     }
/* 82 */     if (!this.target.equals(other.target)) {
/* 83 */       return false;
/*    */     }
/* 85 */     return true;
/*    */   }
/*    */ 
/*    */   
/*    */   public String toString() {
/* 90 */     return "DoorsLink [source=" + this.source + ", target=" + this.target + "]";
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/xmlmodel/DoorsLink.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */