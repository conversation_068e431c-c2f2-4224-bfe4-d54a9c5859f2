/*     */ package com.polarion.synchronizer.configuration;
/*     */ 
/*     */ import com.polarion.alm.projects.IProjectService;
/*     */ import com.polarion.alm.projects.model.IProject;
/*     */ import com.polarion.core.util.ObjectUtils;
/*     */ import com.polarion.platform.internal.security.UserAccountVault;
/*     */ import com.polarion.platform.repository.config.IRepositoryConfigService;
/*     */ import com.polarion.subterra.base.data.identification.ContextId;
/*     */ import com.polarion.subterra.base.data.identification.IContextId;
/*     */ import com.polarion.subterra.base.location.ILocation;
/*     */ import com.polarion.synchronizer.ISynchronizationContext;
/*     */ import com.polarion.synchronizer.TransactionalExecutor;
/*     */ import com.polarion.synchronizer.internal.configuration.LoadContext;
/*     */ import com.polarion.synchronizer.internal.configuration.SyncConfiguration;
/*     */ import java.security.PrivilegedAction;
/*     */ import java.util.Collection;
/*     */ import java.util.HashSet;
/*     */ import java.util.Iterator;
/*     */ import java.util.Set;
/*     */ import java.util.regex.Matcher;
/*     */ import java.util.regex.Pattern;
/*     */ import javax.inject.Inject;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ import org.jetbrains.annotations.Nullable;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ConfigurationHelper
/*     */   implements IConfigurationHelper
/*     */ {
/*     */   @Inject
/*     */   private IRepositoryConfigService configService;
/*     */   @Inject
/*     */   private IProjectService projectService;
/*     */   @Inject
/*     */   private TransactionalExecutor executor;
/*     */   @Inject
/*     */   private ISynchronizationContext context;
/*     */   
/*     */   @NotNull
/*     */   public SyncConfiguration loadConfiguration(@Nullable String projectId, boolean write) {
/*     */     SyncConfiguration syncConfiguration;
/*     */     LoadContext loadContext;
/*  47 */     IContextId contextId = loadContextId(projectId);
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  52 */     if (write) {
/*  53 */       loadContext = (LoadContext)this.configService.getConfigurationForUpdate("com.polarion.synchronizer.configuration", contextId);
/*     */     } else {
/*  55 */       loadContext = (LoadContext)this.configService.getReadConfiguration("com.polarion.synchronizer.configuration", contextId).getData();
/*     */     } 
/*     */     
/*  58 */     if (loadContext == null) {
/*  59 */       syncConfiguration = new SyncConfiguration();
/*     */     } else {
/*  61 */       syncConfiguration = loadContext.getConfiguration();
/*     */     } 
/*     */     
/*  64 */     initializeCredentials(projectId, syncConfiguration);
/*     */     
/*  66 */     return syncConfiguration;
/*     */   }
/*     */   
/*     */   private void initializeCredentials(String projectId, SyncConfiguration syncConfiguration) {
/*  70 */     if (projectId != null) {
/*  71 */       for (IConnection connection : syncConfiguration.getConnections().values()) {
/*  72 */         connection.initializeCredentials(projectId);
/*     */       }
/*     */     }
/*     */   }
/*     */ 
/*     */   
/*     */   public void saveConfiguration(@Nullable final String projectId, @NotNull final ISyncConfiguration configuration) {
/*     */     try {
/*  80 */       this.executor.doInTransaction(new PrivilegedAction<Void>()
/*     */           {
/*     */             public Void run()
/*     */             {
/*  84 */               ConfigurationHelper.this.configService.saveConfiguration("com.polarion.synchronizer.configuration", ConfigurationHelper.this.loadContextId(projectId), configuration);
/*  85 */               return null;
/*     */             }
/*     */           });
/*  88 */     } catch (Exception e) {
/*  89 */       throw new RuntimeException(e);
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public IContextId loadContextId(@Nullable String projectId) {
/*  96 */     if (projectId == null) {
/*  97 */       return ContextId.getGlobalContextId("default");
/*     */     }
/*  99 */     IProject project = this.projectService.getProject(projectId);
/* 100 */     if (project.isUnresolvable()) {
/* 101 */       throw new IllegalArgumentException("Can't access " + projectId + ".");
/*     */     }
/* 103 */     return project.getContextId();
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean update(@NotNull ISyncPair syncPair, @Nullable String projectId, @NotNull String id) {
/* 109 */     if (!id.equals(syncPair.getId())) {
/* 110 */       throw new IllegalArgumentException("ID in sync pair and passed ID did not match.");
/*     */     }
/* 112 */     return save(syncPair, projectId, true);
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean create(@NotNull ISyncPair syncPair, @Nullable String projectId) {
/* 117 */     return save(syncPair, projectId, false);
/*     */   }
/*     */   
/*     */   private boolean save(@NotNull ISyncPair syncPair, @Nullable String projectId, boolean update) {
/* 121 */     SyncConfiguration configuration = loadConfiguration(projectId, true);
/*     */     
/* 123 */     ISyncPair existingPair = (ISyncPair)configuration.getSyncPairs().get(syncPair.getId());
/* 124 */     boolean performSave = (update == ((existingPair != null && ObjectUtils.equalsWithNull(existingPair.getProjectId(), projectId))));
/* 125 */     if (performSave) {
/* 126 */       configuration.addSyncPair(syncPair);
/* 127 */       saveConfiguration(projectId, (ISyncConfiguration)configuration);
/*     */     } 
/*     */     
/* 130 */     return performSave;
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean saveOrActivateConnection(@NotNull IConnection connection, @Nullable String projectId, boolean update) {
/* 135 */     IConnection existingConnection = (IConnection)loadConfiguration(projectId, false).getConnections().get(connection.getId());
/*     */     
/* 137 */     boolean performSave = (update == ((existingConnection != null && ObjectUtils.equalsWithNull(existingConnection.getProjectId(), projectId))));
/* 138 */     boolean activate = (update && existingConnection != null && existingConnection.getProjectId() == null && projectId != null);
/*     */     
/* 140 */     if (activate && projectId != null) {
/* 141 */       updateConnection(projectId, connection);
/*     */     }
/* 143 */     else if (performSave) {
/* 144 */       SyncConfiguration configuration = loadConfiguration(projectId, true);
/* 145 */       configuration.addConnection(connection);
/* 146 */       saveConfiguration(projectId, (ISyncConfiguration)configuration);
/*     */     } 
/*     */ 
/*     */     
/* 150 */     return !(!performSave && !activate);
/*     */   }
/*     */ 
/*     */   
/*     */   public void updateConnection(@NotNull String projectId, @NotNull IConnection connection) {
/* 155 */     connection.initializeCredentials(projectId);
/* 156 */     ILocation configLocation = this.configService.getConfigurationLocationForUpload("com.polarion.synchronizer.configuration", loadContextId(projectId));
/* 157 */     this.configService.invalidateLocation(configLocation);
/*     */   }
/*     */ 
/*     */   
/*     */   public Collection<String> getProjectsUsing(@Nullable IConnection connection) {
/* 162 */     Collection<String> projects = new HashSet<>();
/* 163 */     if (connection != null && connection.getProjectId() == null) {
/* 164 */       String connectionId = connection.getId();
/* 165 */       Set<String> accountVaultKeys = UserAccountVault.getInstance().getRecords().keySet();
/* 166 */       Pattern pattern = Pattern.compile("connectors\\.([^.]+)\\." + connectionId + "\\..*");
/* 167 */       for (String accountVaultKey : accountVaultKeys) {
/* 168 */         Matcher matcher = pattern.matcher(accountVaultKey);
/* 169 */         if (matcher.matches()) {
/* 170 */           projects.add(matcher.group(1));
/*     */         }
/*     */       } 
/* 173 */       for (Iterator<String> iterator = projects.iterator(); iterator.hasNext(); ) {
/* 174 */         String project = iterator.next();
/* 175 */         if (this.projectService.getProject(project).isUnresolvable()) {
/* 176 */           iterator.remove(); continue;
/*     */         } 
/*     */         try {
/* 179 */           SyncConfiguration configuration = loadConfiguration(project, false);
/* 180 */           IConnection existingConnection = (IConnection)configuration.getConnections().get(connectionId);
/* 181 */           if (existingConnection != null && existingConnection.getProjectId() != null)
/*     */           {
/* 183 */             iterator.remove();
/*     */           }
/* 185 */         } catch (Exception e) {
/* 186 */           this.context.getLogger().warn(String.format("Failed to check if %s uses global connection %s: %s", new Object[] {
/* 187 */                   project, connectionId, e.getMessage() }));
/* 188 */           iterator.remove();
/*     */         } 
/*     */       } 
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 195 */     return projects;
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/configuration/ConfigurationHelper.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */