/*    */ package com.polarion.synchronizer.proxy.doors;
/*    */ 
/*    */ import com.google.inject.Inject;
/*    */ import com.polarion.platform.guice.internal.GuicePlatform;
/*    */ import com.polarion.synchronizer.proxy.doors.internal.ICommandProcessor;
/*    */ import com.polarion.synchronizer.spi.Connection;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class DoorsConnectionConfiguration
/*    */   extends Connection
/*    */ {
/*    */   @Inject
/*    */   private ICommandProcessor commandProcessor;
/*    */   private String remoteConnectionId;
/*    */   
/*    */   @Deprecated
/*    */   public DoorsConnectionConfiguration() {
/* 42 */     setUser("Not required.");
/* 43 */     setPassword("Not required.");
/* 44 */     GuicePlatform.getGlobalInjector().injectMembers(this);
/*    */   }
/*    */   
/*    */   public DoorsConnectionConfiguration(String connectionId, String remoteConnectionId) {
/* 48 */     super(connectionId, "Not required.", "Not required.");
/* 49 */     GuicePlatform.getGlobalInjector().injectMembers(this);
/* 50 */     this.remoteConnectionId = remoteConnectionId;
/*    */   }
/*    */   
/*    */   public DoorsConnectionConfiguration(String connectionId, String remoteConnectionId, @NotNull ICommandProcessor commandProcessor) {
/* 54 */     super(connectionId, "Not required.", "Not required.");
/* 55 */     this.remoteConnectionId = remoteConnectionId;
/* 56 */     this.commandProcessor = commandProcessor;
/*    */   }
/*    */   
/*    */   @NotNull
/*    */   public String getScript() {
/* 61 */     return this.commandProcessor.getInitScript(getId());
/*    */   }
/*    */   
/*    */   @NotNull
/*    */   public String getRemoteConnectionId() {
/* 66 */     return this.remoteConnectionId;
/*    */   }
/*    */   
/*    */   public void setRemoteConnectionId(@NotNull String remoteConnectionId) {
/* 70 */     this.remoteConnectionId = remoteConnectionId;
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/DoorsConnectionConfiguration.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */