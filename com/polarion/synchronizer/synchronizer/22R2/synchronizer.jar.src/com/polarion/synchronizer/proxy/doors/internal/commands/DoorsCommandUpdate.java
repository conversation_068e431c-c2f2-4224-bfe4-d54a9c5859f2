/*     */ package com.polarion.synchronizer.proxy.doors.internal.commands;
/*     */ 
/*     */ import com.polarion.synchronizer.proxy.doors.internal.dxl.DxlScriptBuilder;
/*     */ import com.polarion.synchronizer.proxy.doors.internal.dxl.IDxlScript;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class DoorsCommandUpdate
/*     */   extends AbstractDoorsCommand<String>
/*     */ {
/*     */   @NotNull
/*     */   private final String attributeValue;
/*     */   @NotNull
/*     */   private final String attributeName;
/*     */   @NotNull
/*     */   private final String absoluteNumber;
/*     */   
/*     */   public DoorsCommandUpdate(@NotNull String attributeValue, @NotNull String attributeName, @NotNull String absoluteNumber) {
/*  39 */     this.attributeValue = attributeValue.replace("\\", "\\\\").replace("\"", "\\\"");
/*  40 */     this.attributeName = attributeName;
/*  41 */     this.absoluteNumber = absoluteNumber;
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   protected IDxlScript getScript() {
/*  47 */     return (IDxlScript)DxlScriptBuilder.script("Update.dxl")
/*  48 */       .replaceParameter("absoluteNumber", this.absoluteNumber)
/*  49 */       .replaceParameter("attributeValue", this.attributeValue)
/*  50 */       .replaceParameter("attributeName", this.attributeName);
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   protected String processResult(@NotNull String result) throws Exception {
/*  56 */     return result;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public int hashCode() {
/*  62 */     int prime = 31;
/*  63 */     int result = 1;
/*  64 */     result = 31 * result + ((this.absoluteNumber == null) ? 0 : this.absoluteNumber.hashCode());
/*  65 */     result = 31 * result + ((this.attributeName == null) ? 0 : this.attributeName.hashCode());
/*  66 */     result = 31 * result + ((this.attributeValue == null) ? 0 : this.attributeValue.hashCode());
/*  67 */     return result;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean equals(Object obj) {
/*  73 */     if (this == obj) {
/*  74 */       return true;
/*     */     }
/*  76 */     if (obj == null) {
/*  77 */       return false;
/*     */     }
/*  79 */     if (getClass() != obj.getClass()) {
/*  80 */       return false;
/*     */     }
/*  82 */     DoorsCommandUpdate other = (DoorsCommandUpdate)obj;
/*  83 */     if (this.absoluteNumber == null) {
/*  84 */       if (other.absoluteNumber != null) {
/*  85 */         return false;
/*     */       }
/*  87 */     } else if (!this.absoluteNumber.equals(other.absoluteNumber)) {
/*  88 */       return false;
/*     */     } 
/*  90 */     if (this.attributeName == null) {
/*  91 */       if (other.attributeName != null) {
/*  92 */         return false;
/*     */       }
/*  94 */     } else if (!this.attributeName.equals(other.attributeName)) {
/*  95 */       return false;
/*     */     } 
/*  97 */     if (this.attributeValue == null) {
/*  98 */       if (other.attributeValue != null) {
/*  99 */         return false;
/*     */       }
/* 101 */     } else if (!this.attributeValue.equals(other.attributeValue)) {
/* 102 */       return false;
/*     */     } 
/* 104 */     return true;
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/internal/commands/DoorsCommandUpdate.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */