/*    */ package com.polarion.synchronizer.proxy.configuration;
/*    */ 
/*    */ import com.polarion.synchronizer.IAction;
/*    */ import com.polarion.synchronizer.proxy.polarion.PolarionProxyConfiguration;
/*    */ import java.util.Collection;
/*    */ import javax.xml.bind.annotation.XmlRootElement;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ import org.jetbrains.annotations.Nullable;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @XmlRootElement
/*    */ public class SpecificationConfiguration
/*    */   extends SpecificationTemplate
/*    */ {
/*    */   private PolarionProxyConfiguration polarionConfiguration;
/*    */   private ReqIfConfiguration reqIfConfiguration;
/*    */   @Nullable
/*    */   private IAction polarionDeleteAction;
/*    */   protected boolean exportNew;
/*    */   protected boolean updateMetadata;
/*    */   @Nullable
/*    */   private String flagModifiedWorkItemsField;
/*    */   @Nullable
/*    */   private Collection<String> flagModifiedWorkItemsIgnoreFields;
/*    */   
/*    */   @NotNull
/*    */   public PolarionProxyConfiguration getPolarionConfiguration() {
/* 35 */     return this.polarionConfiguration;
/*    */   }
/*    */   
/*    */   public void setPolarionConfiguration(@NotNull PolarionProxyConfiguration polarionConfiguration) {
/* 39 */     this.polarionConfiguration = polarionConfiguration;
/*    */   }
/*    */   
/*    */   @NotNull
/*    */   public ReqIfConfiguration getReqIfConfiguration() {
/* 44 */     return this.reqIfConfiguration;
/*    */   }
/*    */   
/*    */   public void setReqIfConfiguration(@NotNull ReqIfConfiguration reqIfConfiguration) {
/* 48 */     this.reqIfConfiguration = reqIfConfiguration;
/*    */   }
/*    */   
/*    */   public boolean isExportNew() {
/* 52 */     return this.exportNew;
/*    */   }
/*    */   
/*    */   public void setExportNew(boolean exportNew) {
/* 56 */     this.exportNew = exportNew;
/*    */   }
/*    */   
/*    */   public boolean isUpdateMetadata() {
/* 60 */     return this.updateMetadata;
/*    */   }
/*    */   
/*    */   public void setUpdateMetadata(boolean updateMetadata) {
/* 64 */     this.updateMetadata = updateMetadata;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   @Nullable
/*    */   public IAction getPolarionDeleteAction() {
/* 73 */     return this.polarionDeleteAction;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void setPolarionDeleteAction(@Nullable IAction polarionDeleteAction) {
/* 81 */     this.polarionDeleteAction = polarionDeleteAction;
/*    */   }
/*    */   
/*    */   public String getFlagModifiedWorkItemsField() {
/* 85 */     return this.flagModifiedWorkItemsField;
/*    */   }
/*    */   
/*    */   public void setFlagModifiedWorkItemsField(String flagModifiedWorkItemsField) {
/* 89 */     this.flagModifiedWorkItemsField = flagModifiedWorkItemsField;
/*    */   }
/*    */   
/*    */   public Collection<String> getFlagModifiedWorkItemsIgnoreFields() {
/* 93 */     return this.flagModifiedWorkItemsIgnoreFields;
/*    */   }
/*    */   
/*    */   public void setFlagModifiedWorkItemsIgnoreFields(Collection<String> flagModifiedWorkItemsIgnoreFields) {
/* 97 */     this.flagModifiedWorkItemsIgnoreFields = flagModifiedWorkItemsIgnoreFields;
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/configuration/SpecificationConfiguration.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */