/*     */ package com.polarion.synchronizer.proxy.doors.xmlmodel;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.Arrays;
/*     */ import java.util.Collection;
/*     */ import javax.xml.bind.annotation.XmlAttribute;
/*     */ import javax.xml.bind.annotation.XmlRootElement;
/*     */ import javax.xml.bind.annotation.XmlTransient;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ import org.jetbrains.annotations.Nullable;
/*     */ import org.w3c.dom.Node;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @XmlRootElement(name = "attribute")
/*     */ public class DoorsAttribute
/*     */ {
/*     */   @NotNull
/*  40 */   private String name = "";
/*     */   
/*     */   @Nullable
/*     */   private RootAttributeValue attributeValue;
/*     */ 
/*     */   
/*     */   @Deprecated
/*     */   public DoorsAttribute() {}
/*     */ 
/*     */   
/*     */   public DoorsAttribute(@NotNull String name, @Nullable String... values) {
/*  51 */     this.name = name;
/*  52 */     if (values != null) {
/*  53 */       this.attributeValue = new RootAttributeValue();
/*  54 */       this.attributeValue.setAttributeValues(Arrays.asList((Object[])values));
/*     */     } 
/*     */   }
/*     */   
/*     */   @XmlAttribute(name = "name")
/*     */   public void setName(@NotNull String name) {
/*  60 */     this.name = name;
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   public String getName() {
/*  65 */     return this.name;
/*     */   }
/*     */   
/*     */   @Nullable
/*     */   public RootAttributeValue getAttributeValue() {
/*  70 */     return this.attributeValue;
/*     */   }
/*     */   
/*     */   @XmlTransient
/*     */   @NotNull
/*     */   public Collection<String> getAttributeValues() {
/*  76 */     Collection<String> attributeValues = new ArrayList<>();
/*     */     
/*  78 */     if (this.attributeValue != null) {
/*  79 */       for (Object value : this.attributeValue.getAttributeValues()) {
/*  80 */         if (value instanceof Node) {
/*  81 */           String stringValue = ((Node)value).getFirstChild().getNodeValue();
/*  82 */           attributeValues.add(stringValue); continue;
/*     */         } 
/*  84 */         attributeValues.add((String)value);
/*     */       } 
/*     */     }
/*     */     
/*  88 */     return attributeValues;
/*     */   }
/*     */   
/*     */   @XmlTransient
/*     */   @Nullable
/*     */   public String getSingleAttributeValue() {
/*  94 */     Collection<String> values = getAttributeValues();
/*  95 */     return values.isEmpty() ? null : values.iterator().next();
/*     */   }
/*     */   
/*     */   public void setAttributeValue(@Nullable RootAttributeValue attributeValue) {
/*  99 */     this.attributeValue = attributeValue;
/*     */   }
/*     */ 
/*     */   
/*     */   public int hashCode() {
/* 104 */     int prime = 31;
/* 105 */     int result = 1;
/* 106 */     RootAttributeValue attributeValue2 = this.attributeValue;
/* 107 */     if (attributeValue2 != null) {
/* 108 */       result = 31 * result + ((this.attributeValue == null) ? 0 : attributeValue2.hashCode());
/*     */     }
/*     */ 
/*     */     
/* 112 */     result = 31 * result + this.name.hashCode();
/* 113 */     return result;
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean equals(Object obj) {
/* 118 */     if (this == obj) {
/* 119 */       return true;
/*     */     }
/* 121 */     if (obj == null) {
/* 122 */       return false;
/*     */     }
/* 124 */     if (getClass() != obj.getClass()) {
/* 125 */       return false;
/*     */     }
/*     */     
/* 128 */     Collection<String> values = getAttributeValues();
/* 129 */     DoorsAttribute other = (DoorsAttribute)obj;
/* 130 */     Collection<String> otherValues = other.getAttributeValues();
/*     */     
/* 132 */     if (!values.equals(otherValues)) {
/* 133 */       return false;
/*     */     }
/*     */     
/* 136 */     if (!this.name.equals(other.name)) {
/* 137 */       return false;
/*     */     }
/* 139 */     return true;
/*     */   }
/*     */ 
/*     */   
/*     */   public String toString() {
/* 144 */     return "DoorsAttribute [name=" + this.name + ", attributeValue=" + this.attributeValue + "]";
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/xmlmodel/DoorsAttribute.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */