/*     */ package com.polarion.synchronizer.internal;
/*     */ 
/*     */ import com.polarion.platform.jobs.ILogger;
/*     */ import com.polarion.synchronizer.ILogger;
/*     */ 
/*     */ public class JobLoggerAdapter
/*     */   implements ILogger {
/*     */   private ILogger jobLogger;
/*     */   
/*     */   public JobLoggerAdapter(ILogger jobLogger) {
/*  11 */     this.jobLogger = jobLogger;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void debug(Object arg0, Throwable arg1) {
/*  20 */     this.jobLogger.debug(arg0, arg1);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void debug(Object arg0) {
/*  28 */     this.jobLogger.debug(arg0);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void error(Object arg0, Throwable arg1) {
/*  36 */     this.jobLogger.error(arg0, arg1);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void error(Object arg0) {
/*  44 */     this.jobLogger.error(arg0);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void fatal(Object arg0, Throwable arg1) {
/*  52 */     this.jobLogger.fatal(arg0, arg1);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void fatal(Object arg0) {
/*  60 */     this.jobLogger.fatal(arg0);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void info(Object arg0, Throwable arg1) {
/*  68 */     this.jobLogger.info(arg0, arg1);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void info(Object arg0) {
/*  76 */     this.jobLogger.info(arg0);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean isDebugEnabled() {
/*  84 */     return this.jobLogger.isDebugEnabled();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean isInfoEnabled() {
/*  95 */     return this.jobLogger.isInfoEnabled();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void warn(Object arg0, Throwable arg1) {
/* 103 */     this.jobLogger.warn(arg0, arg1);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void warn(Object arg0) {
/* 111 */     this.jobLogger.warn(arg0);
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/internal/JobLoggerAdapter.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */