package com.polarion.synchronizer;

import java.util.Collection;
import org.jetbrains.annotations.NotNull;

public interface IPersistenceOperation {
  @NotNull
  IBaselineProvider getBaselineProvider(@NotNull String paramString1, @NotNull String paramString2);
  
  @NotNull
  IConnectionMap getConnectionMap(@NotNull String paramString1, @NotNull String paramString2, @NotNull Collection<String> paramCollection);
  
  void close();
}


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/IPersistenceOperation.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */