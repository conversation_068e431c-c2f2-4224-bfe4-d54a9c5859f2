/*     */ package com.polarion.synchronizer.mapping;
/*     */ 
/*     */ import com.polarion.synchronizer.ISynchronizationContext;
/*     */ import com.polarion.synchronizer.SynchronizationException;
/*     */ import com.polarion.synchronizer.model.FieldDefinition;
/*     */ import com.polarion.synchronizer.model.IProxy;
/*     */ import com.polarion.synchronizer.model.Side;
/*     */ import com.polarion.synchronizer.spi.translators.ChainedTranslatorFactory;
/*     */ import java.util.Collection;
/*     */ import java.util.HashMap;
/*     */ import java.util.HashSet;
/*     */ import java.util.LinkedList;
/*     */ import java.util.Map;
/*     */ import java.util.Set;
/*     */ import javax.inject.Inject;
/*     */ 
/*     */ public class TranslatorRegistry
/*     */ {
/*     */   private final Map<Key, ITranslatorFactory> translators;
/*     */   private final Map<FieldType, Collection<FieldType>> compatibleFieldsFromMap;
/*     */   private final ISynchronizationContext context;
/*     */   
/*     */   public static class Key {
/*     */     private String typeFrom;
/*     */     private String typeTo;
/*     */     private boolean fromMulti;
/*     */     private boolean toMulti;
/*     */     
/*     */     public Key(String typeFrom, String typeTo, boolean fromMulti, boolean toMulti) {
/*  30 */       this.typeFrom = typeFrom;
/*  31 */       this.typeTo = typeTo;
/*  32 */       this.fromMulti = fromMulti;
/*  33 */       this.toMulti = toMulti;
/*     */     }
/*     */ 
/*     */     
/*     */     public int hashCode() {
/*  38 */       int prime = 31;
/*  39 */       int result = 1;
/*  40 */       result = 31 * result + (this.fromMulti ? 1231 : 1237);
/*  41 */       result = 31 * result + (this.toMulti ? 1231 : 1237);
/*  42 */       result = 31 * result + ((this.typeFrom == null) ? 0 : this.typeFrom.hashCode());
/*  43 */       result = 31 * result + ((this.typeTo == null) ? 0 : this.typeTo.hashCode());
/*  44 */       return result;
/*     */     }
/*     */ 
/*     */     
/*     */     public boolean equals(Object obj) {
/*  49 */       if (this == obj) {
/*  50 */         return true;
/*     */       }
/*  52 */       if (obj == null) {
/*  53 */         return false;
/*     */       }
/*  55 */       if (getClass() != obj.getClass()) {
/*  56 */         return false;
/*     */       }
/*  58 */       Key other = (Key)obj;
/*  59 */       if (this.fromMulti != other.fromMulti) {
/*  60 */         return false;
/*     */       }
/*  62 */       if (this.toMulti != other.toMulti) {
/*  63 */         return false;
/*     */       }
/*  65 */       if (this.typeFrom == null) {
/*  66 */         if (other.typeFrom != null) {
/*  67 */           return false;
/*     */         }
/*  69 */       } else if (!this.typeFrom.equals(other.typeFrom)) {
/*  70 */         return false;
/*     */       } 
/*  72 */       if (this.typeTo == null) {
/*  73 */         if (other.typeTo != null) {
/*  74 */           return false;
/*     */         }
/*  76 */       } else if (!this.typeTo.equals(other.typeTo)) {
/*  77 */         return false;
/*     */       } 
/*  79 */       return true;
/*     */     }
/*     */ 
/*     */     
/*     */     public String toString() {
/*  84 */       return "Key [typeFrom=" + this.typeFrom + ", typeTo=" + this.typeTo + ", fromMulti=" + this.fromMulti + ", toMulti=" + this.toMulti + "]";
/*     */     }
/*     */   }
/*     */   
/*     */   public static class CompatibleType
/*     */   {
/*     */     private final FieldType typeA;
/*     */     private final FieldType typeB;
/*     */     
/*     */     public CompatibleType(FieldType typeA, FieldType typeB) {
/*  94 */       this.typeA = typeA;
/*  95 */       this.typeB = typeB;
/*     */     }
/*     */     
/*     */     public FieldType getTypeA() {
/*  99 */       return this.typeA;
/*     */     }
/*     */     
/*     */     public FieldType getTypeB() {
/* 103 */       return this.typeB;
/*     */     }
/*     */ 
/*     */     
/*     */     public int hashCode() {
/* 108 */       int prime = 31;
/* 109 */       int result = 1;
/* 110 */       result = 31 * result + ((this.typeA == null) ? 0 : this.typeA.hashCode());
/* 111 */       result = 31 * result + ((this.typeB == null) ? 0 : this.typeB.hashCode());
/* 112 */       return result;
/*     */     }
/*     */ 
/*     */     
/*     */     public boolean equals(Object obj) {
/* 117 */       if (this == obj) {
/* 118 */         return true;
/*     */       }
/* 120 */       if (obj == null) {
/* 121 */         return false;
/*     */       }
/* 123 */       if (getClass() != obj.getClass()) {
/* 124 */         return false;
/*     */       }
/* 126 */       CompatibleType other = (CompatibleType)obj;
/* 127 */       if (this.typeA == null) {
/* 128 */         if (other.typeA != null) {
/* 129 */           return false;
/*     */         }
/* 131 */       } else if (!this.typeA.equals(other.typeA)) {
/* 132 */         return false;
/*     */       } 
/* 134 */       if (this.typeB == null) {
/* 135 */         if (other.typeB != null) {
/* 136 */           return false;
/*     */         }
/* 138 */       } else if (!this.typeB.equals(other.typeB)) {
/* 139 */         return false;
/*     */       } 
/* 141 */       return true;
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Inject
/*     */   public TranslatorRegistry(Map<Key, ITranslatorFactory> translators, Set<CompatibleType> compatibleTypes, ISynchronizationContext context) {
/* 154 */     this.translators = new HashMap<>(translators);
/* 155 */     this.context = context;
/*     */     
/* 157 */     loadCompatibleTranslators(compatibleTypes);
/*     */     
/* 159 */     loadChainedTranslators();
/*     */     
/* 161 */     this.compatibleFieldsFromMap = new HashMap<>();
/*     */     
/* 163 */     loadCompatibleFields();
/*     */   }
/*     */ 
/*     */   
/*     */   private void loadCompatibleTranslators(Set<CompatibleType> compatibleTypes) {
/* 168 */     Map<FieldType, Collection<FieldType>> compatibleTypesMap = new HashMap<>();
/* 169 */     for (CompatibleType compatibleType : compatibleTypes) {
/* 170 */       addToCollectionMap(compatibleTypesMap, compatibleType.getTypeA(), compatibleType.getTypeB());
/* 171 */       addToCollectionMap(compatibleTypesMap, compatibleType.getTypeB(), compatibleType.getTypeA());
/*     */     } 
/*     */     
/* 174 */     this.context.getLogger().debug("Adding compatible translators (from types).");
/*     */     
/* 176 */     Map<Key, ITranslatorFactory> compatibleTranslators = new HashMap<>();
/* 177 */     for (Map.Entry<Key, ITranslatorFactory> entry : this.translators.entrySet()) {
/* 178 */       FieldType fromType = fromType(entry.getKey());
/* 179 */       FieldType toType = toType(entry.getKey());
/*     */       
/* 181 */       ITranslatorFactory translatorFactory = entry.getValue();
/*     */       
/* 183 */       Collection<FieldType> compatibleTypesFrom = compatibleTypesMap.get(fromType);
/* 184 */       if (compatibleTypesFrom != null) {
/* 185 */         for (FieldType compatibleType : compatibleTypesFrom) {
/* 186 */           Key key = new Key(compatibleType.getTypeName(), toType.getTypeName(), compatibleType.isMulti(), toType.isMulti());
/* 187 */           if (!this.translators.containsKey(key)) {
/* 188 */             compatibleTranslators.put(key, translatorFactory);
/* 189 */             this.context.getLogger().debug("Adding compatible translator " + compatibleType + " > " + toType);
/*     */           } 
/*     */         } 
/*     */       }
/*     */     } 
/*     */ 
/*     */     
/* 196 */     this.translators.putAll(compatibleTranslators);
/* 197 */     compatibleTranslators.clear();
/*     */     
/* 199 */     this.context.getLogger().debug("Adding compatible translators (to types).");
/*     */     
/* 201 */     for (Map.Entry<Key, ITranslatorFactory> entry : this.translators.entrySet()) {
/* 202 */       FieldType fromType = fromType(entry.getKey());
/* 203 */       FieldType toType = toType(entry.getKey());
/*     */       
/* 205 */       ITranslatorFactory translatorFactory = entry.getValue();
/*     */       
/* 207 */       Collection<FieldType> compatibleTypesTo = compatibleTypesMap.get(toType);
/* 208 */       if (compatibleTypesTo != null) {
/* 209 */         for (FieldType compatibleType : compatibleTypesTo) {
/* 210 */           Key key = new Key(fromType.getTypeName(), compatibleType.getTypeName(), fromType.isMulti(), compatibleType.isMulti());
/* 211 */           if (!this.translators.containsKey(key)) {
/* 212 */             compatibleTranslators.put(key, translatorFactory);
/* 213 */             this.context.getLogger().debug("Adding compatible translator " + fromType + " > " + compatibleType);
/*     */           } 
/*     */         } 
/*     */       }
/*     */     } 
/*     */ 
/*     */     
/* 220 */     this.translators.putAll(compatibleTranslators);
/*     */   }
/*     */   
/*     */   private void loadChainedTranslators() {
/* 224 */     Map<FieldType, Collection<Key>> translatorsFrom = new HashMap<>();
/*     */     
/* 226 */     for (Map.Entry<Key, ITranslatorFactory> entry : this.translators.entrySet()) {
/* 227 */       Key registrationKey = entry.getKey();
/* 228 */       FieldType fromType = fromType(registrationKey);
/* 229 */       if (!fromType.isMulti()) {
/* 230 */         addToCollectionMap(translatorsFrom, fromType, registrationKey);
/*     */       }
/*     */     } 
/*     */     
/* 234 */     for (Map<Key, ITranslatorFactory> chainedFactories = loadChainedFactories(translatorsFrom); !chainedFactories.isEmpty(); chainedFactories = loadChainedFactories(translatorsFrom)) {
/* 235 */       this.translators.putAll(chainedFactories);
/*     */     }
/*     */   }
/*     */   
/*     */   private void loadCompatibleFields() {
/* 240 */     for (Key registrationKey : this.translators.keySet()) {
/* 241 */       FieldType fromType = fromType(registrationKey);
/* 242 */       FieldType toType = toType(registrationKey);
/* 243 */       Collection<FieldType> compatibleFields = this.compatibleFieldsFromMap.get(fromType);
/* 244 */       if (compatibleFields == null) {
/* 245 */         compatibleFields = new LinkedList<>();
/* 246 */         this.compatibleFieldsFromMap.put(fromType, compatibleFields);
/*     */       } 
/* 248 */       compatibleFields.add(toType);
/*     */     } 
/*     */   }
/*     */   
/*     */   private Map<Key, ITranslatorFactory> loadChainedFactories(Map<FieldType, Collection<Key>> translatorsFrom) {
/* 253 */     this.context.getLogger().debug("Expanding translator chains.");
/*     */     
/* 255 */     Map<Key, ITranslatorFactory> chainedFactories = new HashMap<>();
/*     */     
/* 257 */     for (Map.Entry<Key, ITranslatorFactory> entry : this.translators.entrySet()) {
/* 258 */       Key registrationKey = entry.getKey();
/* 259 */       ITranslatorFactory factory = entry.getValue();
/*     */       
/* 261 */       FieldType toType = toType(registrationKey);
/*     */       
/* 263 */       if (toType.isMulti()) {
/*     */         continue;
/*     */       }
/*     */       
/* 267 */       Collection<Key> nextTranslators = translatorsFrom.get(toType);
/* 268 */       if (nextTranslators != null) {
/* 269 */         for (Key next : nextTranslators) {
/* 270 */           Key chained = new Key(registrationKey.typeFrom, next.typeTo, registrationKey.fromMulti, next.toMulti);
/* 271 */           if (!this.translators.containsKey(chained)) {
/* 272 */             ITranslatorFactory nextTranslatorFactory = this.translators.get(next);
/*     */             
/* 274 */             Key reverse = new Key(next.typeTo, next.typeFrom, next.toMulti, next.fromMulti);
/* 275 */             ITranslatorFactory reverseTranslatorFactory = this.translators.get(reverse);
/*     */             
/* 277 */             if (reverseTranslatorFactory != null) {
/*     */               
/* 279 */               this.context.getLogger().debug("Adding chained translator factory from " + fromType(registrationKey) + " > " + toType + " > " + toType(next));
/* 280 */               ChainedTranslatorFactory chainedTranslatorFactory = new ChainedTranslatorFactory(factory, nextTranslatorFactory, reverseTranslatorFactory, toType);
/* 281 */               chainedFactories.put(chained, chainedTranslatorFactory);
/*     */             } 
/*     */           } 
/*     */         } 
/*     */       }
/*     */     } 
/*     */     
/* 288 */     return chainedFactories;
/*     */   }
/*     */   
/*     */   private FieldType toType(Key registrationKey) {
/* 292 */     return new FieldType(registrationKey.typeTo, registrationKey.toMulti);
/*     */   }
/*     */   
/*     */   private FieldType fromType(Key registrationKey) {
/* 296 */     return new FieldType(registrationKey.typeFrom, registrationKey.fromMulti);
/*     */   }
/*     */   
/*     */   private <K, V> void addToCollectionMap(Map<K, Collection<V>> translatorsFrom, K key, V value) {
/* 300 */     Collection<V> collection = translatorsFrom.get(key);
/* 301 */     if (collection == null) {
/* 302 */       collection = new HashSet<>();
/* 303 */       translatorsFrom.put(key, collection);
/*     */     } 
/* 305 */     collection.add(value);
/*     */   }
/*     */ 
/*     */   
/*     */   public ITranslator loadTranslator(Side fromSide, FieldDefinition sourceDefinition, FieldDefinition targetDefinition, Collection<ValueMapping> valueMappings, IProxy fromProxy, IProxy toProxy) {
/* 310 */     return loadTranslator(fromSide, sourceDefinition, targetDefinition, valueMappings, fromProxy, toProxy, true);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public ITranslator loadTranslator(Side fromSide, FieldDefinition sourceDefinition, FieldDefinition targetDefinition, Collection<ValueMapping> valueMappings, IProxy fromProxy, IProxy toProxy, boolean required) {
/* 317 */     ITranslatorFactory factory = this.translators.get(new Key(sourceDefinition.getType(), targetDefinition.getType(), sourceDefinition.isMultiValued(), targetDefinition.isMultiValued()));
/* 318 */     if (factory == null) {
/* 319 */       if (required) {
/* 320 */         throw new SynchronizationException("No translator from " + sourceDefinition + " to " + targetDefinition);
/*     */       }
/* 322 */       return null;
/*     */     } 
/*     */ 
/*     */     
/* 326 */     return factory.createTranslator(sourceDefinition, targetDefinition, fromSide, valueMappings, fromProxy, toProxy);
/*     */   }
/*     */   
/*     */   public Collection<FieldType> getCompatibleFieldsFrom(FieldType fieldType) {
/* 330 */     return this.compatibleFieldsFromMap.get(fieldType);
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/mapping/TranslatorRegistry.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */