int absoluteNumber = %absoluteNumber%

Object o = null
print "\n" stringOf(dateAndTime(today)) ": Start loading data." 

int expectedNumberOfObjects = 0
if (null(absoluteNumber)){
	for o in all m do {
		if(level(o) == 1){
			expectedNumberOfObjects++
		}
	}
} else {
	expectedNumberOfObjects++
}


sendResult("<publications expectedNumberOfObjects=\"" expectedNumberOfObjects "\">")

if (null(absoluteNumber)){
	for o in all m do {
		if(level(o) == 1){
			print "\n" stringOf(dateAndTime(today)) ": Sending data data." 
			sendResult(getXML(o))
		}
	}
} else {
	Object singleObject = object(absoluteNumber, m)
	if (!null(singleObject)){
		print "\n" stringOf(dateAndTime(today)) ": Sending data for single object with id " absoluteNumber ""
		sendResult(getXML(singleObject))
	} 
}

print "\n" stringOf(dateAndTime(today)) ": Finished loading data." 
result = "</publications>"