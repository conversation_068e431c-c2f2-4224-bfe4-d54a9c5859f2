/*    */ package com.polarion.synchronizer.proxy.doors.internal;
/*    */ 
/*    */ import com.polarion.platform.i18n.Localization;
/*    */ import com.polarion.synchronizer.model.FieldDefinition;
/*    */ import com.polarion.synchronizer.model.Option;
/*    */ import com.polarion.synchronizer.model.OptionFieldDefinition;
/*    */ import java.util.Collection;
/*    */ import java.util.HashMap;
/*    */ import java.util.HashSet;
/*    */ import java.util.Map;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ import org.jetbrains.annotations.Nullable;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class DoorsProxyMetadataHelper
/*    */ {
/*    */   @NotNull
/* 23 */   private Map<String, FieldDefinition> keyToFieldDefinitions = new HashMap<>();
/*    */   
/*    */   @NotNull
/* 26 */   private Map<String, Object> keyToDefaultValues = new HashMap<>();
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void putFieldDefinition(@NotNull String attributeName, @NotNull FieldDefinition fieldDefinition) {
/* 32 */     this.keyToFieldDefinitions.put(attributeName, fieldDefinition);
/*    */   }
/*    */   
/*    */   @Nullable
/*    */   public FieldDefinition getFieldDefinition(@NotNull String attributeName) {
/* 37 */     return this.keyToFieldDefinitions.get(attributeName);
/*    */   }
/*    */   
/*    */   public void putDefaultValue(@NotNull String attributeName, @Nullable Object value) {
/* 41 */     this.keyToDefaultValues.put(attributeName, value);
/*    */   }
/*    */   
/*    */   @Nullable
/*    */   public Object getDefaultValue(@NotNull String attributeName) {
/* 46 */     return this.keyToDefaultValues.get(attributeName);
/*    */   }
/*    */   
/*    */   public void initFields() {
/* 50 */     Collection<Option> availableRelationOptions = new HashSet<>();
/* 51 */     availableRelationOptions.add(new Option("relates_to", Localization.getString("administration.doorsconnector.relationlabels.relatesto")));
/* 52 */     this.keyToFieldDefinitions.put("relations", new OptionFieldDefinition("relations", Localization.getString("reqif.relationsLabel"), "relation", true, true, availableRelationOptions));
/* 53 */     this.keyToFieldDefinitions.put("type", new FieldDefinition("type", Localization.getString("definition.type"), "option", true, false));
/* 54 */     this.keyToFieldDefinitions.put("attachments", new FieldDefinition("attachments", Localization.getString("definition.attachments"), "attachment", true, true));
/*    */   }
/*    */   
/*    */   @NotNull
/*    */   public Collection<FieldDefinition> getDefinedFields() {
/* 59 */     return this.keyToFieldDefinitions.values();
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/internal/DoorsProxyMetadataHelper.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */