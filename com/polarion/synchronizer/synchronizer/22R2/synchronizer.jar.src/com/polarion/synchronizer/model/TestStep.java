/*    */ package com.polarion.synchronizer.model;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ import java.util.ArrayList;
/*    */ import java.util.LinkedHashMap;
/*    */ import java.util.List;
/*    */ import java.util.Map;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class TestStep
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -1993401859854064099L;
/* 33 */   private final Map<String, String> values = new LinkedHashMap<>();
/*    */   
/*    */   public void insertStepValue(String column, String value) {
/* 36 */     this.values.put(column, value);
/*    */   }
/*    */   
/*    */   public String getStepValue(String column) {
/* 40 */     return this.values.get(column);
/*    */   }
/*    */   
/*    */   public List<String> getKeys() {
/* 44 */     return new ArrayList<>(this.values.keySet());
/*    */   }
/*    */   
/*    */   public List<String> getValues() {
/* 48 */     return new ArrayList<>(this.values.values());
/*    */   }
/*    */   
/*    */   public void replaceKey(String oldKey, String newKey) {
/* 52 */     String oldValue = this.values.get(oldKey);
/*    */     
/* 54 */     this.values.put(newKey, oldValue);
/*    */   }
/*    */ 
/*    */   
/*    */   public int hashCode() {
/* 59 */     int prime = 31;
/* 60 */     int result = 1;
/* 61 */     result = 31 * result + ((this.values == null) ? 0 : this.values.hashCode());
/* 62 */     return result;
/*    */   }
/*    */ 
/*    */   
/*    */   public boolean equals(Object obj) {
/* 67 */     if (this == obj) {
/* 68 */       return true;
/*    */     }
/* 70 */     if (obj == null) {
/* 71 */       return false;
/*    */     }
/* 73 */     if (getClass() != obj.getClass()) {
/* 74 */       return false;
/*    */     }
/* 76 */     TestStep other = (TestStep)obj;
/* 77 */     if (this.values == null) {
/* 78 */       if (other.values != null) {
/* 79 */         return false;
/*    */       }
/* 81 */     } else if (!this.values.equals(other.values)) {
/* 82 */       return false;
/*    */     } 
/* 84 */     return true;
/*    */   }
/*    */ 
/*    */   
/*    */   public String toString() {
/* 89 */     return "TestStep [values=" + this.values + "]";
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/model/TestStep.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */