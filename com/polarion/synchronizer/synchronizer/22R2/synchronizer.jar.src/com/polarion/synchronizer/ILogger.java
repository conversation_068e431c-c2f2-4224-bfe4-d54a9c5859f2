package com.polarion.synchronizer;

public interface ILogger {
  void debug(Object paramObject, Throwable paramThrowable);
  
  void debug(Object paramObject);
  
  void error(Object paramObject, Throwable paramThrowable);
  
  void error(Object paramObject);
  
  void fatal(Object paramObject, Throwable paramThrowable);
  
  void fatal(Object paramObject);
  
  void info(Object paramObject, Throwable paramThrowable);
  
  void info(Object paramObject);
  
  boolean isDebugEnabled();
  
  boolean isInfoEnabled();
  
  void warn(Object paramObject, Throwable paramThrowable);
  
  void warn(Object paramObject);
}


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/ILogger.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */