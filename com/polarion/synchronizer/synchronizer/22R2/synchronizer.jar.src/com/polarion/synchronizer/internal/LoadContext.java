/*    */ package com.polarion.synchronizer.internal;
/*    */ 
/*    */ import com.polarion.synchronizer.model.IProxy;
/*    */ import com.polarion.synchronizer.model.TransferItem;
/*    */ import java.util.Collection;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ class LoadContext
/*    */ {
/*    */   @NotNull
/*    */   private final IProxy proxy;
/*    */   @NotNull
/*    */   private final Collection<String> keys;
/*    */   
/*    */   public LoadContext(@NotNull IProxy proxy, @NotNull Collection<String> keys) {
/* 39 */     this.proxy = proxy;
/* 40 */     this.keys = keys;
/*    */   }
/*    */   
/*    */   @NotNull
/*    */   public Collection<TransferItem> loadItems(@NotNull Collection<String> ids) {
/* 45 */     return this.proxy.getItems(ids, this.keys);
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/internal/LoadContext.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */