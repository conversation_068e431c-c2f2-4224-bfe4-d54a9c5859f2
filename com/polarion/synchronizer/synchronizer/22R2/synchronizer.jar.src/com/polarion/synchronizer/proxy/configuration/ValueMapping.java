/*    */ package com.polarion.synchronizer.proxy.configuration;
/*    */ 
/*    */ import javax.xml.bind.annotation.XmlAttribute;
/*    */ 
/*    */ 
/*    */ public class ValueMapping
/*    */ {
/*    */   private String polarionValue;
/*    */   private String reqIfValue;
/*    */   
/*    */   public ValueMapping() {}
/*    */   
/*    */   public ValueMapping(String polarionValue, String reqIfValue) {
/* 14 */     this.polarionValue = polarionValue;
/* 15 */     this.reqIfValue = reqIfValue;
/*    */   }
/*    */   
/*    */   @XmlAttribute
/*    */   public String getPolarionValue() {
/* 20 */     return this.polarionValue;
/*    */   }
/*    */   
/*    */   public void setPolarionValue(String polarionValue) {
/* 24 */     this.polarionValue = polarionValue;
/*    */   }
/*    */   
/*    */   @XmlAttribute
/*    */   public String getReqIfValue() {
/* 29 */     return this.reqIfValue;
/*    */   }
/*    */   
/*    */   public void setReqIfValue(String reqIfValue) {
/* 33 */     this.reqIfValue = reqIfValue;
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/configuration/ValueMapping.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */