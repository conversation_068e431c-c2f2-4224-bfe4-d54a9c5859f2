package com.polarion.synchronizer;

import com.polarion.synchronizer.model.IProxy;
import java.util.Collection;
import org.jetbrains.annotations.NotNull;

public interface ISynchronizationTask {
  void execute();
  
  void execute(Collection<String> paramCollection1, Collection<String> paramCollection2);
  
  SynchronizationStatistics getLeftStatistics();
  
  SynchronizationStatistics getRightStatistics();
  
  void setLeftDeleteAction(IAction paramIAction);
  
  void close();
  
  @NotNull
  String getLeftSystemId();
  
  @NotNull
  String getRightSystemId();
  
  void setReAddMissingOnRight(boolean paramBoolean);
  
  void setDeleteOutOfscopeItems(boolean paramBoolean);
  
  @NotNull
  IProxy getLeftProxy();
  
  @NotNull
  IProxy getRightProxy();
  
  @NotNull
  IMapping getMapping();
  
  void addPostProcessingActions(@NotNull Collection<IPostProcessingAction> paramCollection);
  
  @NotNull
  ISynchronizationResult getLeftResult();
  
  @NotNull
  ISynchronizationResult getRightResult();
  
  @NotNull
  ILogger getLogger();
  
  @NotNull
  String getLabel();
}


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/ISynchronizationTask.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */