package com.polarion.synchronizer.configuration;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.polarion.synchronizer.internal.configuration.SyncConfiguration;
import java.util.Map;

@JsonDeserialize(as = SyncConfiguration.class)
public interface ISyncConfiguration {
  public static final String CONFIGURATION_ID = "com.polarion.synchronizer.configuration";
  
  Map<String, ISyncPair> getSyncPairs();
  
  Map<String, IConnection> getConnections();
}


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/configuration/ISyncConfiguration.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */