package com.polarion.synchronizer.spi;

import com.polarion.synchronizer.IProxyConfiguration;
import com.polarion.synchronizer.model.IProxy;
import org.jetbrains.annotations.NotNull;

public interface IProxyExtension {
  @NotNull
  IProxy applyToProxy(@NotNull IProxy paramIProxy, @NotNull IProxyConfiguration<?> paramIProxyConfiguration);
  
  @NotNull
  <T extends com.polarion.synchronizer.configuration.IConnection> IProxyConfiguration<T> applyToConfiguration(IProxyConfiguration<T> paramIProxyConfiguration);
}


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/spi/IProxyExtension.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */