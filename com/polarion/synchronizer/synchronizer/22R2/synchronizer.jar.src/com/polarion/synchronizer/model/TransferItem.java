/*     */ package com.polarion.synchronizer.model;
/*     */ 
/*     */ import com.polarion.synchronizer.internal.mapping.MappingHierarchy;
/*     */ import java.util.Collection;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ 
/*     */ 
/*     */ 
/*     */ public class TransferItem
/*     */   implements Cloneable
/*     */ {
/*     */   private ItemKey key;
/*  14 */   private Map<String, Object> values = new HashMap<>();
/*     */   
/*     */   public TransferItem(String id) {
/*  17 */     this(id, false);
/*     */   }
/*     */   
/*     */   public TransferItem(String id, boolean isIdForeign) {
/*  21 */     this.key = new ItemKey(id, isIdForeign);
/*     */   }
/*     */   
/*     */   public String getId() {
/*  25 */     return this.key.isForeign ? null : this.key.id;
/*     */   }
/*     */   
/*     */   public void setId(String id) {
/*  29 */     this.key = new ItemKey(id, false);
/*     */   }
/*     */   
/*     */   public ItemKey getKey() {
/*  33 */     return this.key;
/*     */   }
/*     */   
/*     */   public Map<String, Object> getValues() {
/*  37 */     return this.values;
/*     */   }
/*     */   
/*     */   public Object getValue(String key) {
/*  41 */     return getValues().get(key);
/*     */   }
/*     */   
/*     */   public Object put(String key, Object value) {
/*  45 */     return getValues().put(key, value);
/*     */   }
/*     */   
/*     */   public static void copyTo(Map<String, Object> source, Map<String, Object> target) {
/*  49 */     Collection<Map.Entry<String, Object>> entries = source.entrySet();
/*  50 */     for (Map.Entry<String, Object> newEntry : entries) {
/*  51 */       String valueKey = newEntry.getKey();
/*  52 */       Object value = newEntry.getValue();
/*  53 */       if (value instanceof CollectionUpdate) {
/*  54 */         CollectionUpdate<?> collectionUpdate = (CollectionUpdate)value;
/*  55 */         target.put(valueKey, collectionUpdate.applyTo(target.get(valueKey))); continue;
/*     */       } 
/*  57 */       target.put(valueKey, value);
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   public void copyTo(TransferItem target) {
/*  63 */     copyTo(getValues(), target.getValues());
/*     */   }
/*     */   
/*     */   public String getType() {
/*  67 */     return (String)getValue("type");
/*     */   }
/*     */   
/*     */   public MappingHierarchy getMappingHierarchy() {
/*  71 */     return (MappingHierarchy)getValue("hierarchy");
/*     */   }
/*     */   
/*     */   public Hierarchy getHierarchy() {
/*  75 */     return (Hierarchy)getValue("hierarchy");
/*     */   }
/*     */   
/*     */   public void setHierarchy(Hierarchy hierarchy) {
/*  79 */     put("hierarchy", hierarchy);
/*     */   }
/*     */ 
/*     */   
/*     */   public int hashCode() {
/*  84 */     int prime = 31;
/*  85 */     int result = 1;
/*  86 */     result = 31 * result + ((this.key == null) ? 0 : this.key.hashCode());
/*  87 */     result = 31 * result + ((this.values == null) ? 0 : this.values.hashCode());
/*  88 */     return result;
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean equals(Object obj) {
/*  93 */     if (this == obj) {
/*  94 */       return true;
/*     */     }
/*  96 */     if (obj == null) {
/*  97 */       return false;
/*     */     }
/*  99 */     if (getClass() != obj.getClass()) {
/* 100 */       return false;
/*     */     }
/* 102 */     TransferItem other = (TransferItem)obj;
/* 103 */     if (this.key == null) {
/* 104 */       if (other.key != null) {
/* 105 */         return false;
/*     */       }
/* 107 */     } else if (!this.key.equals(other.key)) {
/* 108 */       return false;
/*     */     } 
/* 110 */     if (this.values == null) {
/* 111 */       if (other.values != null) {
/* 112 */         return false;
/*     */       }
/* 114 */     } else if (!this.values.equals(other.values)) {
/* 115 */       return false;
/*     */     } 
/* 117 */     return true;
/*     */   }
/*     */ 
/*     */   
/*     */   public Object clone() {
/*     */     try {
/* 123 */       TransferItem clone = (TransferItem)super.clone();
/* 124 */       clone.values = (HashMap)((HashMap)clone.values).clone();
/* 125 */       return clone;
/* 126 */     } catch (CloneNotSupportedException e) {
/* 127 */       throw new IllegalStateException(e);
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   public String toString() {
/* 133 */     return "TransferItem [key=" + this.key + ", values=" + this.values + "]";
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/model/TransferItem.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */