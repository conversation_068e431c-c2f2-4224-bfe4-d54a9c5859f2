package com.polarion.synchronizer.proxy.doors;

import org.jetbrains.annotations.NotNull;

public interface IDoorsProject {
  @NotNull
  String getName();
  
  @NotNull
  ProjectNode loadContent();
}


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/IDoorsProject.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */