/*    */ package com.polarion.synchronizer.proxy.doors.internal;
/*    */ 
/*    */ import com.google.inject.Inject;
/*    */ import com.google.inject.assistedinject.Assisted;
/*    */ import com.polarion.core.util.ObjectUtils;
/*    */ import com.polarion.platform.i18n.Localization;
/*    */ import com.polarion.synchronizer.AbstractExchangeService;
/*    */ import com.polarion.synchronizer.ICommonServices;
/*    */ import com.polarion.synchronizer.IProxyConfiguration;
/*    */ import com.polarion.synchronizer.configuration.ISyncPair;
/*    */ import com.polarion.synchronizer.proxy.configuration.IConfigurationService;
/*    */ import com.polarion.synchronizer.proxy.configuration.SpecificationConfiguration;
/*    */ import com.polarion.synchronizer.proxy.doors.DoorsProxyConfiguration;
/*    */ import com.polarion.synchronizer.proxy.doors.IDoorsConnection;
/*    */ import java.util.Collection;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class DoorsExchangeService
/*    */   extends AbstractExchangeService
/*    */   implements IDoorsExchangeService
/*    */ {
/*    */   @NotNull
/*    */   private final IDoorsConnection connection;
/*    */   @NotNull
/*    */   private IDoorsSyncPairService doorsSyncPairService;
/*    */   
/*    */   @Inject
/*    */   public DoorsExchangeService(@NotNull ICommonServices commonServices, @Assisted @NotNull IDoorsConnection connection, @NotNull IConfigurationService configurationService, @NotNull IDoorsSyncPairService doorsSyncPairService) {
/* 51 */     super(commonServices, configurationService);
/* 52 */     this.connection = connection;
/* 53 */     this.doorsSyncPairService = doorsSyncPairService;
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   protected String getImportJobName(@NotNull Collection<SpecificationConfiguration> configurations) {
/* 59 */     String documentList = loadDocumentList(configurations);
/* 60 */     return Localization.getString("administration.doorsimport.importJob", new String[] { documentList });
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   protected IProxyConfiguration getProxyConfiguration(@NotNull SpecificationConfiguration configuration) {
/* 66 */     return (IProxyConfiguration)new DoorsProxyConfiguration(this.connection.getId(), (String)ObjectUtils.notNull(configuration.getReqIfConfiguration().getSpecificationId()));
/*    */   }
/*    */ 
/*    */   
/*    */   protected void onImportFinished() {
/* 71 */     this.connection.close();
/*    */   }
/*    */ 
/*    */   
/*    */   public void createAndUpdateSyncPairs(@NotNull String projectId, @NotNull Collection<SpecificationConfiguration> configurationsCreate, @NotNull Collection<SpecificationConfiguration> configurationsUpdate) {
/* 76 */     Collection<ISyncPair> syncPairsToCreate = loadSyncPairs(configurationsCreate, false);
/* 77 */     Collection<ISyncPair> syncPairsToUpdate = loadSyncPairs(configurationsUpdate, false);
/* 78 */     this.doorsSyncPairService.createAndUpdateSyncPairs(projectId, syncPairsToCreate, syncPairsToUpdate);
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/internal/DoorsExchangeService.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */