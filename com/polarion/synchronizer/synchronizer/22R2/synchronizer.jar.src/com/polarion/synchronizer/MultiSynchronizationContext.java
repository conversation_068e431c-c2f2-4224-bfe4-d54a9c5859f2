/*     */ package com.polarion.synchronizer;
/*     */ 
/*     */ import com.polarion.platform.i18n.Localization;
/*     */ import com.polarion.platform.jobs.IProgressMonitor;
/*     */ import com.polarion.subterra.base.data.identification.IContextId;
/*     */ import com.polarion.synchronizer.configuration.ISyncPair;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Collection;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class MultiSynchronizationContext
/*     */ {
/*     */   private boolean hasErrors;
/*     */   private boolean hasWarnings;
/*     */   
/*     */   private static class ConfigurationTaskPair
/*     */   {
/*     */     @NotNull
/*     */     public final ISyncPair configuration;
/*     */     @NotNull
/*     */     public final ISynchronizationTask task;
/*     */     
/*     */     public ConfigurationTaskPair(@NotNull ISyncPair configuration, @NotNull ISynchronizationTask task) {
/*  46 */       this.configuration = configuration;
/*  47 */       this.task = task;
/*     */     }
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*  53 */   private final Collection<ISynchronizationTask> tasks = new ArrayList<>();
/*     */   
/*     */   @NotNull
/*     */   private final ISynchronizationService synchronizationService;
/*     */   @NotNull
/*     */   private final ILogger logger;
/*     */   
/*     */   public MultiSynchronizationContext(@NotNull ISynchronizationService synchronizationService, @NotNull ILogger logger) {
/*  61 */     this.synchronizationService = synchronizationService;
/*  62 */     this.logger = logger;
/*     */   }
/*     */   
/*     */   public void execute(@NotNull Collection<ISyncPair> syncPairs, @NotNull IContextId contextId, @NotNull IProgressMonitor progressMonitor) {
/*  66 */     ISynchronizationContext context = this.synchronizationService.getSynchronizationContext();
/*  67 */     context.setLogger(this.logger);
/*  68 */     context.initializeDependencyManager();
/*     */     
/*     */     try {
/*  71 */       Collection<ConfigurationTaskPair> configurationTaskPairs = new ArrayList<>(syncPairs.size());
/*  72 */       for (ISyncPair syncPair : syncPairs) {
/*     */         try {
/*  74 */           ISynchronizationTask task = this.synchronizationService.createSynchronizationTask(syncPair, contextId, progressMonitor);
/*  75 */           this.tasks.add(task);
/*  76 */           configurationTaskPairs.add(new ConfigurationTaskPair(syncPair, task));
/*     */           
/*  78 */           progressMonitor.setTaskName(task.getLabel());
/*     */           try {
/*  80 */             task.execute();
/*  81 */             checkTaskResults(task);
/*     */           } finally {
/*  83 */             task.close();
/*     */           } 
/*  85 */         } catch (Exception e) {
/*  86 */           this.logger.error("Unexpected error executing synchronization task.", e);
/*  87 */           this.hasErrors = true;
/*     */         } 
/*     */       } 
/*     */       
/*  91 */       progressMonitor.setTaskName(Localization.getString("polarion.jobs.synchronizer.task.resyncItemsWithResolvedDependencies"));
/*     */       try {
/*  93 */         IDependencyManager dependencyManager = context.getDependencyManager();
/*  94 */         if (dependencyManager == null) {
/*  95 */           throw new RuntimeException("UnexpectedError: Dependency manager was null after initialization.");
/*     */         }
/*     */         
/*  98 */         for (ConfigurationTaskPair configurationTaskPair : configurationTaskPairs) {
/*  99 */           ISynchronizationTask task = configurationTaskPair.task;
/*     */           
/* 101 */           Collection<String> leftIds = dependencyManager.getResolvedDependencies(task, task.getLeftSystemId());
/* 102 */           Collection<String> rightIds = dependencyManager.getResolvedDependencies(task, task.getRightSystemId());
/* 103 */           if (!leftIds.isEmpty() || !rightIds.isEmpty()) {
/* 104 */             this.logger.info("Resynchronizing items with resolved dependencies.");
/* 105 */             this.logger.debug("Polarion: " + leftIds);
/* 106 */             this.logger.debug(String.valueOf(task.getRightSystemId()) + ": " + rightIds);
/*     */             try {
/* 108 */               ISynchronizationTask recreatedTask = this.synchronizationService.createSynchronizationTask(configurationTaskPair.configuration, contextId, progressMonitor);
/* 109 */               this.tasks.add(recreatedTask);
/*     */               try {
/* 111 */                 recreatedTask.execute(leftIds, rightIds);
/* 112 */                 checkTaskResults(recreatedTask);
/*     */               } finally {
/* 114 */                 recreatedTask.close();
/*     */               } 
/* 116 */             } catch (Exception e) {
/* 117 */               this.logger.error("Unexpected error resynchronizing items with resolved dependencies.", e);
/* 118 */               this.hasErrors = true;
/*     */             }  continue;
/*     */           } 
/* 121 */           progressMonitor.worked(10);
/*     */         } 
/*     */       } finally {
/*     */         
/* 125 */         context.setLogger(null);
/* 126 */         context.releaseDependencyManager();
/*     */       } 
/*     */     } finally {
/* 129 */       progressMonitor.done();
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   private void checkTaskResults(ISynchronizationTask task) {
/* 135 */     this.hasWarnings = !(!this.hasWarnings && !task.getLeftStatistics().hasWarnings() && !task.getRightStatistics().hasWarnings());
/* 136 */     this.hasErrors = !(!this.hasErrors && !task.getLeftStatistics().hasErrors() && !task.getRightStatistics().hasErrors());
/*     */   }
/*     */   
/*     */   public boolean hasWarnings() {
/* 140 */     return this.hasWarnings;
/*     */   }
/*     */   
/*     */   public boolean hasErrors() {
/* 144 */     return this.hasErrors;
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/MultiSynchronizationContext.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */