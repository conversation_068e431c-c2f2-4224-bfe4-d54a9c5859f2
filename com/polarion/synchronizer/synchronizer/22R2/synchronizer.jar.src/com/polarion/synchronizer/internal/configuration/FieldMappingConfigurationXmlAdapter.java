/*    */ package com.polarion.synchronizer.internal.configuration;
/*    */ 
/*    */ import com.polarion.synchronizer.model.Direction;
/*    */ import javax.xml.bind.annotation.adapters.XmlAdapter;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class FieldMappingConfigurationXmlAdapter
/*    */   extends XmlAdapter<FieldMappingConfiguration, FieldMappingConfiguration>
/*    */ {
/*    */   public FieldMappingConfiguration unmarshal(FieldMappingConfiguration fieldMappingConfiguration) throws Exception {
/* 16 */     if (fieldMappingConfiguration.getDeleteDirection() == null) {
/* 17 */       fieldMappingConfiguration.setDeleteDirection(Direction.BIDIRECTIONAL);
/*    */     }
/* 19 */     return fieldMappingConfiguration;
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public FieldMappingConfiguration marshal(FieldMappingConfiguration fieldMappingConfiguration) throws Exception {
/* 25 */     if (!"attachments".equals(fieldMappingConfiguration.getLeft()) || fieldMappingConfiguration.getDeleteDirection() == Direction.BIDIRECTIONAL) {
/* 26 */       fieldMappingConfiguration.setDeleteDirection(null);
/*    */     }
/* 28 */     return fieldMappingConfiguration;
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/internal/configuration/FieldMappingConfigurationXmlAdapter.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */