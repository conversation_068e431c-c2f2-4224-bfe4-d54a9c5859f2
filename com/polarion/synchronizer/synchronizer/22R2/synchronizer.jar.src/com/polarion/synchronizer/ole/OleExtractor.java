/*     */ package com.polarion.synchronizer.ole;
/*     */ 
/*     */ import com.polarion.synchronizer.SynchronizationException;
/*     */ import com.polarion.synchronizer.proxy.doors.internal.RtfSourceWithLastPosition;
/*     */ import com.rtfparserkit.parser.IRtfListener;
/*     */ import com.rtfparserkit.parser.IRtfSource;
/*     */ import com.rtfparserkit.parser.RtfListenerAdaptor;
/*     */ import com.rtfparserkit.parser.raw.RawRtfParser;
/*     */ import com.rtfparserkit.rtf.Command;
/*     */ import com.rtfparserkit.rtf.CommandType;
/*     */ import java.io.IOException;
/*     */ import java.io.InputStream;
/*     */ import java.util.ArrayDeque;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Deque;
/*     */ import java.util.List;
/*     */ import java.util.Optional;
/*     */ import java.util.function.Consumer;
/*     */ import org.apache.commons.io.IOUtils;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ import org.jetbrains.annotations.Nullable;
/*     */ 
/*     */ public class OleExtractor
/*     */   extends RtfListenerAdaptor {
/*     */   private static final int TWIPS_TO_PIXEL = 15;
/*     */   
/*     */   public static int twipsToPixels(int twips) {
/*  28 */     return Math.round(twips / 15.0F);
/*     */   }
/*     */   
/*     */   public static int pixelsToTwips(int pixels) {
/*  32 */     return pixels * 15;
/*     */   }
/*     */   
/*     */   private class DataState
/*     */     extends State
/*     */   {
/*     */     @NotNull
/*     */     final EmbeddedObject object;
/*     */     private int dataStart;
/*     */     
/*     */     DataState(EmbeddedObject ole) {
/*  43 */       this.object = ole;
/*  44 */       this.dataStart = OleExtractor.this.rtfSource.getLastPositionRead();
/*     */     }
/*     */ 
/*     */     
/*     */     public void processCommand(@NotNull Command command, int parameter) {
/*  49 */       this.dataStart = OleExtractor.this.rtfSource.getLastPositionRead();
/*     */     }
/*     */ 
/*     */     
/*     */     public void activate() {
/*  54 */       this.dataStart = OleExtractor.this.rtfSource.getLastPositionRead();
/*     */     }
/*     */ 
/*     */     
/*     */     public void processEnd() {
/*  59 */       this.object.setBinaryStart(this.dataStart + 1);
/*  60 */       this.object.setBinaryEnd(OleExtractor.this.rtfSource.getLastPositionRead());
/*     */     }
/*     */   }
/*     */   
/*     */   private final class OleObjectState
/*     */     extends State
/*     */   {
/*     */     @NotNull
/*     */     private OleObject ole;
/*     */     
/*     */     private OleObjectState(int groupStart) {
/*  71 */       this.ole = new OleObject(OleExtractor.this.rtfSource.getData(), groupStart);
/*     */     }
/*     */ 
/*     */     
/*     */     @Nullable
/*     */     public OleExtractor.State processDestinationCommand(@NotNull Command command, int groupStart) {
/*  77 */       if (command == Command.objdata)
/*  78 */         return new OleExtractor.DataState(this.ole); 
/*  79 */       if (command == Command.result) {
/*  80 */         return new OleExtractor.State(OleExtractor.this)
/*     */           {
/*     */             @Nullable
/*     */             public OleExtractor.State processDestinationCommand(@NotNull Command command, int groupStart) {
/*  84 */               if (command == Command.pict) {
/*  85 */                 return new OleExtractor.PictState(groupStart, o -> OleExtractor.OleObjectState.this.ole.setThumbnail(o));
/*     */               }
/*  87 */               return null;
/*     */             }
/*     */           };
/*     */       }
/*  91 */       return null;
/*     */     }
/*     */ 
/*     */     
/*     */     public void processCommand(@NotNull Command command, int parameter) {
/*  96 */       if (command == Command.objw) {
/*  97 */         this.ole.setWidth(OleExtractor.twipsToPixels(parameter));
/*  98 */       } else if (command == Command.objh) {
/*  99 */         this.ole.setHeight(OleExtractor.twipsToPixels(parameter));
/*     */       } 
/*     */     }
/*     */ 
/*     */     
/*     */     public void processEnd() {
/* 105 */       this.ole.setObjectEnd(OleExtractor.this.rtfSource.getLastPositionRead());
/* 106 */       OleExtractor.this.objects.add(this.ole);
/*     */     }
/*     */   }
/*     */   
/*     */   private final class PictState
/*     */     extends DataState
/*     */   {
/*     */     @NotNull
/*     */     private final Consumer<EmbeddedObject> consumer;
/*     */     
/*     */     PictState(@NotNull int groupStart, Consumer<EmbeddedObject> consumer) {
/* 117 */       super(new EmbeddedObject(OleExtractor.this.rtfSource.getData(), groupStart));
/* 118 */       this.consumer = consumer;
/*     */     }
/*     */ 
/*     */     
/*     */     public void processCommand(@NotNull Command command, int parameter) {
/* 123 */       super.processCommand(command, parameter);
/* 124 */       if (command == Command.picwgoal) {
/* 125 */         this.object.setWidth(OleExtractor.twipsToPixels(parameter));
/* 126 */       } else if (command == Command.pichgoal) {
/* 127 */         this.object.setHeight(OleExtractor.twipsToPixels(parameter));
/*     */       } 
/*     */     }
/*     */ 
/*     */     
/*     */     public void processEnd() {
/* 133 */       super.processEnd();
/* 134 */       this.object.setObjectEnd(OleExtractor.this.rtfSource.getLastPositionRead());
/* 135 */       this.consumer.accept(this.object);
/*     */     }
/*     */   }
/*     */ 
/*     */   
/*     */   private final class IgnoreState
/*     */     extends State {}
/*     */ 
/*     */   
/*     */   private final class RootState
/*     */     extends State
/*     */   {
/*     */     @Nullable
/*     */     public OleExtractor.State processDestinationCommand(@NotNull Command command, int groupStart) {
/* 149 */       if (command == Command.rtf) {
/* 150 */         return new OleExtractor.RtfState();
/*     */       }
/* 152 */       return null;
/*     */     }
/*     */   }
/*     */   
/*     */   private final class RtfState
/*     */     extends State
/*     */   {
/*     */     public OleExtractor.State processDestinationCommand(@NotNull Command command, int groupStart) {
/* 160 */       if (command == Command.object)
/* 161 */         return new OleExtractor.OleObjectState(groupStart); 
/* 162 */       if (command == Command.pict)
/* 163 */         return new OleExtractor.PictState(groupStart, o -> {
/*     */             
/* 165 */             });  return null;
/*     */     }
/*     */ 
/*     */ 
/*     */     
/*     */     public void processCharacterBytes(@NotNull byte[] data) {
/* 171 */       if (data.length > 0)
/* 172 */         OleExtractor.this.containsText = true; 
/*     */     }
/*     */   }
/*     */   
/*     */   private abstract class State {
/*     */     private State() {
/* 178 */       this.level = 0;
/*     */     } private int level;
/*     */     public final void processGroupStart() {
/* 181 */       this.level++;
/*     */     }
/*     */ 
/*     */     
/*     */     @Nullable
/*     */     public State processDestinationCommand(@NotNull Command command, int groupStart) {
/* 187 */       return null;
/*     */     }
/*     */ 
/*     */ 
/*     */     
/*     */     public void processCommand(@NotNull Command command, int parameter) {}
/*     */ 
/*     */ 
/*     */     
/*     */     public void processCharacterBytes(@NotNull byte[] data) {}
/*     */ 
/*     */ 
/*     */     
/*     */     public void activate() {}
/*     */ 
/*     */     
/*     */     public final void processGroupEnd() {
/* 204 */       if (this.level > 0) {
/* 205 */         this.level--;
/*     */       } else {
/* 207 */         OleExtractor.this.states.pop();
/* 208 */         ((State)OleExtractor.this.states.peek()).level--;
/* 209 */         ((State)OleExtractor.this.states.peek()).activate();
/* 210 */         processEnd();
/*     */       } 
/*     */     }
/*     */ 
/*     */ 
/*     */     
/*     */     public void processEnd() {}
/*     */   }
/*     */ 
/*     */   
/*     */   public static OleExtractor parse(byte[] data) {
/*     */     try {
/* 222 */       RtfSourceWithLastPosition source = new RtfSourceWithLastPosition(data);
/* 223 */       OleExtractor extractor = new OleExtractor(source);
/* 224 */       (new RawRtfParser()).parse((IRtfSource)source, (IRtfListener)extractor);
/* 225 */       return extractor;
/* 226 */     } catch (IOException e) {
/* 227 */       throw new SynchronizationException("Failed to parse OLE content.", e);
/*     */     } 
/*     */   }
/*     */   
/*     */   public static OleExtractor parse(InputStream data) {
/*     */     try {
/* 233 */       return parse(IOUtils.toByteArray(data));
/* 234 */     } catch (IOException e) {
/* 235 */       throw new SynchronizationException("Failed to load OLE data.", e);
/*     */     } 
/*     */   }
/*     */   
/*     */   @NotNull
/* 240 */   private final Deque<State> states = new ArrayDeque<>();
/*     */   
/*     */   @NotNull
/*     */   private final RtfSourceWithLastPosition rtfSource;
/*     */   
/*     */   @NotNull
/* 246 */   private final List<EmbeddedObject> objects = new ArrayList<>();
/*     */   
/*     */   private boolean containsText = false;
/*     */   
/*     */   private int lastGroupStart;
/*     */   
/*     */   private OleExtractor(@NotNull RtfSourceWithLastPosition rtfSource) {
/* 253 */     this.rtfSource = rtfSource;
/* 254 */     this.states.push(new RootState());
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   public List<EmbeddedObject> getOleObjects() {
/* 259 */     return this.objects;
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   public Optional<EmbeddedObject> getWrappedObject() {
/* 264 */     return (this.objects.size() == 1 && !this.containsText) ? Optional.<EmbeddedObject>of(this.objects.get(0)) : Optional.<EmbeddedObject>empty();
/*     */   }
/*     */ 
/*     */   
/*     */   public void processGroupEnd() {
/* 269 */     ((State)this.states.peek()).processGroupEnd();
/*     */   }
/*     */ 
/*     */   
/*     */   public void processGroupStart() {
/* 274 */     this.lastGroupStart = this.rtfSource.getLastPositionRead();
/* 275 */     ((State)this.states.peek()).processGroupStart();
/*     */   }
/*     */ 
/*     */   
/*     */   public void processCommand(Command command, int parameter, boolean hasParameter, boolean optional) {
/* 280 */     if (command.getCommandType() == CommandType.Destination) {
/* 281 */       State newState = ((State)this.states.peek()).processDestinationCommand(command, this.lastGroupStart);
/* 282 */       this.states.push((newState == null) ? new IgnoreState() : newState);
/*     */     } 
/* 284 */     ((State)this.states.peek()).processCommand(command, parameter);
/*     */   }
/*     */ 
/*     */   
/*     */   public void processCharacterBytes(byte[] data) {
/* 289 */     ((State)this.states.peek()).processCharacterBytes(data);
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/ole/OleExtractor.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */