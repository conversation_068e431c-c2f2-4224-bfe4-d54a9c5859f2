/*    */ package com.polarion.synchronizer.proxy.doors.internal.commands;
/*    */ 
/*    */ import com.polarion.synchronizer.proxy.doors.internal.dxl.DxlScriptBuilder;
/*    */ import com.polarion.synchronizer.proxy.doors.internal.dxl.IDxlScript;
/*    */ import com.polarion.synchronizer.proxy.doors.xmlmodel.DoorsPublications;
/*    */ import java.nio.charset.Charset;
/*    */ import javax.xml.bind.JAXB;
/*    */ import org.apache.commons.io.IOUtils;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class DoorsCommandData
/*    */   extends AbstractDoorsCommand<DoorsPublications>
/*    */ {
/*    */   @NotNull
/*    */   public DoorsPublications processResult(@NotNull String result) {
/* 40 */     return (DoorsPublications)JAXB.unmarshal(IOUtils.toInputStream(result, Charset.forName("UTF-8")), DoorsPublications.class);
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   public IDxlScript getScript() {
/* 46 */     return DxlScriptBuilder.create().add((IDxlScript)DxlScriptBuilder.script("sendResult.dxl"))
/* 47 */       .add((IDxlScript)DxlScriptBuilder.script("getDoorsObjects.dxl"))
/* 48 */       .replaceParameter("absoluteNumber", "null");
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/internal/commands/DoorsCommandData.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */