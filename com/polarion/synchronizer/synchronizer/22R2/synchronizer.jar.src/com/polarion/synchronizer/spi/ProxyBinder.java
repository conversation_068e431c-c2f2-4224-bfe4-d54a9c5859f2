/*    */ package com.polarion.synchronizer.spi;
/*    */ 
/*    */ import com.google.inject.Binder;
/*    */ import com.google.inject.TypeLiteral;
/*    */ import com.google.inject.multibindings.MapBinder;
/*    */ import com.polarion.synchronizer.ProxyContribution;
/*    */ import com.polarion.synchronizer.model.IProxyFactory;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public final class ProxyBinder
/*    */ {
/*    */   public static void bindProxy(Binder binder, ProxyContribution<?, ?> contribution, Class<? extends IProxyFactory> factoryClass) {
/* 17 */     MapBinder<ProxyContribution<?, ?>, IProxyFactory> test = prepareBinding(binder);
/* 18 */     test.addBinding(contribution).to(factoryClass);
/*    */   }
/*    */   
/*    */   public static MapBinder<ProxyContribution<?, ?>, IProxyFactory> prepareBinding(Binder binder) {
/* 22 */     MapBinder<ProxyContribution<?, ?>, IProxyFactory> test = MapBinder.newMapBinder(binder, 
/* 23 */         new TypeLiteral<ProxyContribution<?, ?>>() { 
/* 24 */         },  new TypeLiteral<IProxyFactory>() {  }
/*    */       );
/* 26 */     return test;
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/spi/ProxyBinder.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */