/*    */ package com.polarion.synchronizer.proxy.doors.internal.rest;
/*    */ 
/*    */ import com.google.inject.Inject;
/*    */ import com.polarion.platform.guice.internal.GuicePlatform;
/*    */ import com.polarion.synchronizer.proxy.doors.IDoorsConnection;
/*    */ import com.polarion.synchronizer.proxy.doors.IDoorsService;
/*    */ import com.polarion.synchronizer.proxy.doors.internal.ICommandProcessor;
/*    */ import com.polarion.synchronizer.proxy.doors.internal.commands.IDoorsCommand;
/*    */ import com.polarion.synchronizer.proxy.doors.internal.dxl.DxlScriptBuilder;
/*    */ import javax.ws.rs.DELETE;
/*    */ import javax.ws.rs.GET;
/*    */ import javax.ws.rs.NotFoundException;
/*    */ import javax.ws.rs.POST;
/*    */ import javax.ws.rs.PUT;
/*    */ import javax.ws.rs.Path;
/*    */ import javax.ws.rs.PathParam;
/*    */ import javax.ws.rs.Produces;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @Produces({"text/plain"})
/*    */ @Path("connections/{id}")
/*    */ public class DoorsConnectionResource
/*    */ {
/*    */   @Inject
/*    */   @NotNull
/*    */   private ICommandProcessor commandProcessor;
/*    */   @Inject
/*    */   @NotNull
/*    */   private IDoorsService doorsService;
/*    */   @NotNull
/*    */   private String connectionId;
/*    */   
/*    */   public DoorsConnectionResource(@PathParam("id") String connectionId) {
/* 39 */     GuicePlatform.getGlobalInjector().injectMembers(this);
/* 40 */     this.connectionId = connectionId;
/*    */   }
/*    */ 
/*    */   
/*    */   public DoorsConnectionResource(@NotNull ICommandProcessor commandProcessor, @NotNull IDoorsService doorsService, @PathParam("id") String connectionId) {
/* 45 */     this.commandProcessor = commandProcessor;
/* 46 */     this.doorsService = doorsService;
/* 47 */     this.connectionId = connectionId;
/*    */   }
/*    */   
/*    */   @GET
/*    */   @Path("nextCommand")
/*    */   public String nextCommand() {
/* 53 */     IDoorsCommand<?> command = this.commandProcessor.removeCommand(this.connectionId);
/*    */     
/* 55 */     if (command == null) {
/* 56 */       return "";
/*    */     }
/* 58 */     String commandId = this.commandProcessor.trackExecution(this.connectionId, command);
/* 59 */     return DxlScriptBuilder.create()
/* 60 */       .add(commandId)
/* 61 */       .add("string result = \"\"")
/* 62 */       .add(command.execute())
/* 63 */       .add("return_ result")
/* 64 */       .toString();
/*    */   }
/*    */ 
/*    */   
/*    */   @POST
/*    */   @Path("/commands/{commandId}/result")
/*    */   @Produces({"text/plain"})
/*    */   public void trackResult(@PathParam("commandId") String commandId, @NotNull String result) {
/* 72 */     this.commandProcessor.trackResult(this.connectionId, commandId, result);
/*    */   }
/*    */   
/*    */   @DELETE
/*    */   public void close() {
/* 77 */     IDoorsConnection connection = this.doorsService.getConnection(this.connectionId);
/* 78 */     if (connection == null) {
/* 79 */       throw new NotFoundException(String.format("Connection with ID %s does not exist.", new Object[] { this.connectionId }));
/*    */     }
/* 81 */     connection.close();
/*    */   }
/*    */   
/*    */   @PUT
/*    */   public void create() {
/* 86 */     this.doorsService.getOrCreateConnection(this.connectionId);
/*    */   }
/*    */ 
/*    */   
/*    */   @GET
/*    */   @Path("/close")
/*    */   public String closeByGet() {
/* 93 */     close();
/* 94 */     return "Connection closed";
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/internal/rest/DoorsConnectionResource.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */