/*    */ package com.polarion.synchronizer.mapping;
/*    */ 
/*    */ import com.polarion.synchronizer.IMapping;
/*    */ import com.polarion.synchronizer.configuration.IAttributeMapper;
/*    */ import com.polarion.synchronizer.model.Direction;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ import org.jetbrains.annotations.Nullable;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class Mapping
/*    */   implements IMapping
/*    */ {
/*    */   private final Direction createDirection;
/*    */   private final Direction deleteDirection;
/*    */   @NotNull
/*    */   private final IAttributeMapper attributeMapper;
/*    */   
/*    */   public Mapping(@NotNull IAttributeMapper attributeMapper, @Nullable Direction createDirection, @Nullable Direction deleteDirection) {
/* 41 */     this.attributeMapper = attributeMapper;
/* 42 */     this.createDirection = createDirection;
/* 43 */     this.deleteDirection = deleteDirection;
/*    */   }
/*    */ 
/*    */   
/*    */   @Nullable
/*    */   public Direction getCreateDirection() {
/* 49 */     return this.createDirection;
/*    */   }
/*    */ 
/*    */   
/*    */   @Nullable
/*    */   public Direction getDeleteDirection() {
/* 55 */     return this.deleteDirection;
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   public IAttributeMapper getAttributeMapper() {
/* 61 */     return this.attributeMapper;
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/mapping/Mapping.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */