package com.polarion.synchronizer;

import com.polarion.platform.jobs.IProgressMonitor;
import com.polarion.subterra.base.data.identification.IContextId;
import com.polarion.synchronizer.configuration.IConnection;
import com.polarion.synchronizer.configuration.ISyncPair;
import com.polarion.synchronizer.model.IProxy;
import java.util.Collection;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public interface ISynchronizationService {
  @NotNull
  ISynchronizationTask createSynchronizationTask(@NotNull ISyncPair paramISyncPair, @NotNull IContextId paramIContextId, @NotNull IProgressMonitor paramIProgressMonitor);
  
  @NotNull
  Collection<Class<? extends IProxyConfiguration<? extends IConnection>>> getConfigurationClasses();
  
  @NotNull
  Collection<Class<? extends IConnection>> getConnectionClasses();
  
  @NotNull
  IProxy newProxy(@NotNull IProxyConfiguration<? extends IConnection> paramIProxyConfiguration);
  
  @Nullable
  String checkConnection(@NotNull IConnection paramIConnection);
  
  @Nullable
  ISyncPair loadSyncPair(@NotNull IContextId paramIContextId, @NotNull String paramString);
  
  @NotNull
  ISynchronizationContext getSynchronizationContext();
}


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/ISynchronizationService.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */