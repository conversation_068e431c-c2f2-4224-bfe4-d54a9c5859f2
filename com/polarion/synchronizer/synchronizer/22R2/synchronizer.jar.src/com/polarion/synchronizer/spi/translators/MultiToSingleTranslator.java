/*    */ package com.polarion.synchronizer.spi.translators;
/*    */ 
/*    */ import com.polarion.core.util.ObjectUtils;
/*    */ import com.polarion.synchronizer.mapping.TranslationResult;
/*    */ import java.util.Collection;
/*    */ import java.util.Collections;
/*    */ import java.util.HashSet;
/*    */ import java.util.Iterator;
/*    */ import javax.inject.Inject;
/*    */ 
/*    */ 
/*    */ public class MultiToSingleTranslator
/*    */   extends TypesafeTranslator<Collection<String>, String, String>
/*    */ {
/*    */   private final StringValueTranslator stringValueTranslator;
/*    */   
/*    */   @Inject
/*    */   public MultiToSingleTranslator(StringValueTranslator stringValueTranslator) {
/* 19 */     super(Collection.class, String.class);
/* 20 */     this.stringValueTranslator = stringValueTranslator;
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public TranslationResult<String> translateBidirectionalTypesafe(Collection<String> sourceBaseline, Collection<String> sourceValue, String targetBaseline, String targetValue) {
/* 26 */     boolean sourceModified = contentNotEqual(sourceBaseline, sourceValue);
/*    */     
/* 28 */     TranslationResult<String> mappedValue = getMappedValue(sourceValue, targetValue);
/*    */     
/* 30 */     return new TranslationResult(mappedValue.getResultValue(), (
/* 31 */         mappedValue.isModification() && sourceModified), 
/* 32 */         (sourceModified && mappedValue.isModification() && !ObjectUtils.equalsWithNull(targetBaseline, targetValue)));
/*    */   }
/*    */ 
/*    */   
/*    */   public TranslationResult<String> translateUnidirectionalTypesafe(Collection<String> sourceValue, String targetValue) {
/* 37 */     TranslationResult<String> mapped = getMappedValue(sourceValue, targetValue);
/* 38 */     return new TranslationResult(mapped.getResultValue(), mapped.isModification(), false);
/*    */   }
/*    */   
/*    */   private TranslationResult<String> getMappedValue(Collection<String> value, String targetValue) {
/* 42 */     String firstValue = getFirst(value);
/* 43 */     return this.stringValueTranslator.translateUnidirectional(firstValue, targetValue);
/*    */   }
/*    */   
/*    */   public static String getFirst(Collection<String> value) {
/* 47 */     Collection<String> empty = Collections.emptySet();
/* 48 */     Iterator<String> iter = (value == null) ? empty.iterator() : value.iterator();
/* 49 */     String firstValue = iter.hasNext() ? iter.next() : null;
/* 50 */     return firstValue;
/*    */   }
/*    */   
/*    */   public static boolean contentNotEqual(Collection<String> c1, Collection<String> c2) {
/* 54 */     Collection<String> empty = Collections.emptySet();
/* 55 */     Collection<String> compareableSourceBaseline = (c1 == null) ? empty : new HashSet<>(c1);
/* 56 */     Collection<String> compareableSource = (c2 == null) ? empty : new HashSet<>(c2);
/* 57 */     return !compareableSourceBaseline.equals(compareableSource);
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/spi/translators/MultiToSingleTranslator.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */