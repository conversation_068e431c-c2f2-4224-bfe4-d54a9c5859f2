/*    */ package com.polarion.synchronizer.proxy.doors.html2rtftranslator;
/*    */ 
/*    */ import com.polarion.synchronizer.proxy.htmltranslator.ElementInfo;
/*    */ import com.polarion.synchronizer.proxy.htmltranslator.StyleTag;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class FontWeightStyleTag
/*    */   implements StyleTag
/*    */ {
/*    */   public static final String KEY = "font-weight";
/*    */   
/*    */   public void open(@NotNull ElementInfo elementInfo, @NotNull String text, @NotNull StringBuilder sb) {
/* 35 */     if (!text.isEmpty()) {
/* 36 */       String fontWeight = (String)elementInfo.getStyle().get("font-weight");
/* 37 */       if ("bold".equals(fontWeight)) {
/* 38 */         sb.append("\\b ");
/*    */       }
/*    */     } 
/*    */   }
/*    */ 
/*    */   
/*    */   public void close(@NotNull ElementInfo elementInfo, @NotNull String text, @NotNull StringBuilder sb) {
/* 45 */     if (!text.isEmpty()) {
/* 46 */       String fontWeight = (String)elementInfo.getStyle().get("font-weight");
/* 47 */       if ("bold".equals(fontWeight))
/* 48 */         sb.append("\\b0 "); 
/*    */     } 
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/html2rtftranslator/FontWeightStyleTag.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */