/*    */ package com.polarion.synchronizer.proxy.doors.internal.commands;
/*    */ 
/*    */ import com.polarion.synchronizer.proxy.doors.internal.dxl.DxlScriptBuilder;
/*    */ import com.polarion.synchronizer.proxy.doors.internal.dxl.IDxlScript;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ 
/*    */ public class DoorsCommandFindPosition
/*    */   extends AbstractDoorsCommand<String>
/*    */ {
/*    */   @NotNull
/*    */   private final String itemPosition;
/*    */   @NotNull
/*    */   private final String parentAbsoluteNumber;
/*    */   
/*    */   public DoorsCommandFindPosition(int itemPosition, @NotNull String parentAbsoluteNumber) {
/* 16 */     this.itemPosition = String.valueOf(itemPosition);
/* 17 */     this.parentAbsoluteNumber = parentAbsoluteNumber;
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   protected IDxlScript getScript() {
/* 23 */     return (IDxlScript)DxlScriptBuilder.script("findPosition.dxl")
/* 24 */       .replaceParameter("parentAbsoluteNumber", this.parentAbsoluteNumber)
/* 25 */       .replaceParameter("itemPosition", this.itemPosition);
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   protected String processResult(@NotNull String result) throws Exception {
/* 31 */     return result;
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/internal/commands/DoorsCommandFindPosition.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */