/*     */ package com.polarion.synchronizer.mapping;
/*     */ 
/*     */ import com.polarion.synchronizer.ILogger;
/*     */ import com.polarion.synchronizer.ISynchronizationContext;
/*     */ import com.polarion.synchronizer.configuration.FieldMappingGroupConfiguration;
/*     */ import com.polarion.synchronizer.configuration.IAttributeMapper;
/*     */ import com.polarion.synchronizer.configuration.MappingConfiguration;
/*     */ import com.polarion.synchronizer.internal.configuration.FieldMappingConfiguration;
/*     */ import com.polarion.synchronizer.internal.configuration.ValueMappingConfiguration;
/*     */ import com.polarion.synchronizer.internal.mapping.DefaultFieldMappingGroup;
/*     */ import com.polarion.synchronizer.internal.mapping.FieldMapping;
/*     */ import com.polarion.synchronizer.internal.mapping.FieldMappingGroup;
/*     */ import com.polarion.synchronizer.internal.mapping.MultiTypeMapping;
/*     */ import com.polarion.synchronizer.model.Direction;
/*     */ import com.polarion.synchronizer.model.FieldDefinition;
/*     */ import com.polarion.synchronizer.model.IProxy;
/*     */ import com.polarion.synchronizer.model.Option;
/*     */ import com.polarion.synchronizer.model.OptionFieldDefinition;
/*     */ import com.polarion.synchronizer.model.Side;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Collection;
/*     */ import java.util.Collections;
/*     */ import java.util.HashMap;
/*     */ import java.util.LinkedList;
/*     */ import java.util.Map;
/*     */ import java.util.function.Predicate;
/*     */ import java.util.stream.Collectors;
/*     */ import java.util.stream.Stream;
/*     */ import javax.inject.Inject;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ import org.jetbrains.annotations.Nullable;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class MappingFactory
/*     */ {
/*     */   private TranslatorRegistry translatorRegistry;
/*     */   private ISynchronizationContext context;
/*     */   
/*     */   @Inject
/*     */   public MappingFactory(TranslatorRegistry translatorRegistry, ISynchronizationContext context) {
/*  44 */     this.translatorRegistry = translatorRegistry;
/*  45 */     this.context = context;
/*     */   }
/*     */ 
/*     */   
/*     */   public IAttributeMapper loadMapping(MappingConfiguration configuration, IProxy left, IProxy right) {
/*  50 */     DefaultFieldMappingGroup defaultMapping = loadDefaultMappingGroup(configuration.getDefaultMappingGroup(), 
/*  51 */         configuration.getHierarchyDirection(), configuration.getPrimaryHierarchyDirection(), left, right);
/*  52 */     MultiTypeMapping mapping = new MultiTypeMapping(defaultMapping);
/*     */     
/*  54 */     for (FieldMappingGroupConfiguration fieldMappingGroupConfiguration : configuration.getFieldMappingGroups()) {
/*  55 */       mapping.addTypeMapping(fieldMappingGroupConfiguration.getLeftType(), fieldMappingGroupConfiguration.getRightType(), 
/*  56 */           loadFieldMappingGroup(fieldMappingGroupConfiguration, left, right));
/*     */     }
/*     */     
/*  59 */     return (IAttributeMapper)mapping;
/*     */   }
/*     */ 
/*     */   
/*     */   public DefaultFieldMappingGroup loadDefaultMappingGroup(FieldMappingGroupConfiguration configuration, Direction hierarchyDirection, Direction primaryHierarchyDirection, IProxy left, IProxy right) {
/*  64 */     DefaultFieldMappingGroup defaultMapping = new DefaultFieldMappingGroup(this.context, hierarchyDirection, primaryHierarchyDirection);
/*     */     
/*  66 */     addFieldMappings((FieldMappingGroup)defaultMapping, configuration.getFieldMappings(), 
/*  67 */         loadCommonFields(left), loadCommonFields(right), left, right);
/*     */     
/*  69 */     return defaultMapping;
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public FieldMappingGroup loadFieldMappingGroup(@NotNull FieldMappingGroupConfiguration configuration, @NotNull IProxy left, @NotNull IProxy right) {
/*  75 */     FieldMappingGroup fieldMappingGroup = new FieldMappingGroup(this.context);
/*  76 */     Collection<FieldDefinition> rightDefinedFields = right.getDefinedFields(configuration.getRightType());
/*  77 */     Collection<FieldDefinition> leftDefinedFields = left.getDefinedFields(configuration.getLeftType());
/*  78 */     addFieldMappings(fieldMappingGroup, configuration.getFieldMappings(), 
/*  79 */         (leftDefinedFields == null) ? Collections.<FieldDefinition>emptyList() : leftDefinedFields, 
/*  80 */         (rightDefinedFields == null) ? Collections.<FieldDefinition>emptyList() : rightDefinedFields, left, right);
/*     */     
/*  82 */     return fieldMappingGroup;
/*     */   }
/*     */ 
/*     */   
/*     */   private void addFieldMappings(@NotNull FieldMappingGroup fieldMappingGroup, @NotNull Collection<FieldMappingConfiguration> fieldMappings, @NotNull Collection<FieldDefinition> leftFieldDefinitions, @NotNull Collection<FieldDefinition> rightFieldDefinitions, IProxy left, IProxy right) {
/*  87 */     Map<String, FieldDefinition> definedFieldsLeftMap = new HashMap<>();
/*  88 */     Map<String, FieldDefinition> definedFieldsRightMap = new HashMap<>();
/*     */     
/*  90 */     loadDefinedFields(leftFieldDefinitions, definedFieldsLeftMap);
/*  91 */     loadDefinedFields(rightFieldDefinitions, definedFieldsRightMap);
/*     */     
/*  93 */     for (FieldMappingConfiguration fieldMappingConfiguration : fieldMappings) {
/*  94 */       FieldMapping fieldMapping = loadFieldMapping(this.context, left, right, definedFieldsLeftMap, definedFieldsRightMap, fieldMappingConfiguration);
/*  95 */       if (fieldMapping != null) {
/*  96 */         fieldMappingGroup.addFieldMapping(fieldMapping);
/*     */       }
/*     */     } 
/*     */   }
/*     */   
/*     */   public Collection<FieldDefinition> loadCommonFields(@NotNull IProxy proxy) {
/* 102 */     return loadCommonFields(proxy, null);
/*     */   }
/*     */   
/*     */   public Collection<FieldDefinition> loadCommonFields(@NotNull IProxy proxy, @Nullable Collection<Option> configuredTypes) {
/* 106 */     Collection<FieldDefinition> commonFields = null;
/*     */     
/* 108 */     if (configuredTypes == null) {
/* 109 */       types = proxy.getDefinedTypes();
/*     */     } else {
/* 111 */       types = configuredTypes;
/*     */     } 
/* 113 */     Collection<Option> types = (Collection<Option>)types.stream().filter(type -> !type.isVirtualType()).collect(Collectors.toList());
/*     */     
/* 115 */     for (Option type : types) {
/* 116 */       Collection<FieldDefinition> definedFields = proxy.getDefinedFields(type.getId());
/* 117 */       if (definedFields != null) {
/* 118 */         if (commonFields == null) {
/* 119 */           commonFields = new ArrayList<>(definedFields); continue;
/*     */         } 
/* 121 */         commonFields = filterCommonFields(commonFields, definedFields);
/*     */       } 
/*     */     } 
/*     */     
/* 125 */     if (commonFields == null) {
/* 126 */       commonFields = Collections.emptyList();
/*     */     }
/* 128 */     return commonFields;
/*     */   }
/*     */   
/*     */   @Nullable
/*     */   private Collection<FieldDefinition> filterCommonFields(@NotNull Collection<FieldDefinition> commonFields, @NotNull Collection<FieldDefinition> definedFields) {
/* 133 */     Predicate<FieldDefinition> optionFieldPredicate = field -> field instanceof OptionFieldDefinition;
/* 134 */     Predicate<FieldDefinition> fieldPredicate = field -> !(field instanceof OptionFieldDefinition);
/*     */     
/* 136 */     Collection<FieldDefinition> optionCommonFieldDefinition = (Collection<FieldDefinition>)commonFields.stream().filter(optionFieldPredicate).collect(Collectors.toList());
/* 137 */     Collection<FieldDefinition> optionDefineFieldDefinition = (Collection<FieldDefinition>)definedFields.stream().filter(optionFieldPredicate).collect(Collectors.toList());
/*     */     
/* 139 */     Stream<FieldDefinition> optionCommonFields = optionCommonFieldDefinition.stream().filter(optionField -> paramCollection.stream().anyMatch(()));
/*     */ 
/*     */     
/* 142 */     Collection<FieldDefinition> fieldCommonFieldDefinition = (Collection<FieldDefinition>)commonFields.stream().filter(fieldPredicate).collect(Collectors.toList());
/* 143 */     Collection<FieldDefinition> fieldDefineFieldDefinition = (Collection<FieldDefinition>)definedFields.stream().filter(fieldPredicate).collect(Collectors.toList());
/*     */     
/* 145 */     Stream<FieldDefinition> commonDefinedFields = fieldCommonFieldDefinition.stream().filter(fieldDefineFieldDefinition::contains);
/*     */     
/* 147 */     return (Collection<FieldDefinition>)Stream.<FieldDefinition>concat(commonDefinedFields, optionCommonFields).collect(Collectors.toList());
/*     */   }
/*     */ 
/*     */   
/*     */   @Nullable
/*     */   private FieldMapping loadFieldMapping(@NotNull ISynchronizationContext context, @NotNull IProxy leftProxy, @NotNull IProxy rightProxy, @NotNull Map<String, FieldDefinition> definedFieldsLeftMap, @NotNull Map<String, FieldDefinition> definedFieldsRightMap, @NotNull FieldMappingConfiguration fieldMappingConfiguration) {
/* 153 */     String leftKey = fieldMappingConfiguration.getLeft();
/* 154 */     String rightKey = fieldMappingConfiguration.getRight();
/*     */     
/* 156 */     FieldDefinition leftFieldDefinition = definedFieldsLeftMap.get(leftKey);
/* 157 */     FieldDefinition rightFieldDefinition = definedFieldsRightMap.get(rightKey);
/*     */     
/* 159 */     ILogger logger = context.getLogger();
/*     */     
/* 161 */     if (leftFieldDefinition == null || rightFieldDefinition == null) {
/* 162 */       if (leftFieldDefinition == null) {
/* 163 */         logger.error("No definition found for field '" + leftKey + "' on Polarion of a field mapping.");
/*     */       }
/* 165 */       if (rightFieldDefinition == null) {
/* 166 */         logger.error("No definition found for field '" + rightKey + "' on " + rightProxy.getTargetName() + " of a field mapping.");
/*     */       }
/*     */       
/* 169 */       return null;
/*     */     } 
/*     */     
/* 172 */     Collection<ValueMapping> valueMappings = new LinkedList<>();
/* 173 */     for (ValueMappingConfiguration valueMappingConfiguration : fieldMappingConfiguration.getValueMappings()) {
/* 174 */       valueMappings.add(new ValueMapping(valueMappingConfiguration.getLeft(), valueMappingConfiguration.getRight()));
/*     */     }
/*     */     
/* 177 */     Direction direction = fieldMappingConfiguration.getDirection();
/*     */ 
/*     */     
/* 180 */     ITranslator leftToRight = this.translatorRegistry.loadTranslator(Side.LEFT, leftFieldDefinition, rightFieldDefinition, valueMappings, 
/* 181 */         leftProxy, rightProxy, direction.isFrom(Side.LEFT));
/*     */     
/* 183 */     ITranslator rightToLeft = this.translatorRegistry.loadTranslator(Side.RIGHT, rightFieldDefinition, leftFieldDefinition, valueMappings, 
/* 184 */         rightProxy, leftProxy, direction.isFrom(Side.RIGHT));
/*     */     
/* 186 */     FieldMapping fieldMapping = new FieldMapping(leftFieldDefinition, rightFieldDefinition, direction, 
/* 187 */         fieldMappingConfiguration.getPrimaryDirection(), fieldMappingConfiguration.getDeleteDirection(), context, leftToRight, rightToLeft);
/* 188 */     return fieldMapping;
/*     */   }
/*     */   
/*     */   private void loadDefinedFields(Collection<FieldDefinition> definedFieldsLeft, Map<String, FieldDefinition> target) {
/* 192 */     for (FieldDefinition fieldDefinition : definedFieldsLeft)
/* 193 */       target.put(fieldDefinition.getKey(), fieldDefinition); 
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/mapping/MappingFactory.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */