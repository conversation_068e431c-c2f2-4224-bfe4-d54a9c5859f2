/*    */ package com.polarion.synchronizer.proxy.doors.internal.commands;
/*    */ 
/*    */ import com.polarion.synchronizer.proxy.doors.internal.dxl.DxlScriptBuilder;
/*    */ import com.polarion.synchronizer.proxy.doors.internal.dxl.IDxlScript;
/*    */ import com.polarion.synchronizer.proxy.doors.xmlmodel.DoorsPublications;
/*    */ import java.nio.charset.Charset;
/*    */ import javax.xml.bind.JAXB;
/*    */ import org.apache.commons.io.IOUtils;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class DoorsCommandDataForObject
/*    */   extends AbstractDoorsCommand<DoorsPublications>
/*    */ {
/*    */   @NotNull
/*    */   private final String absoluteNumber;
/*    */   
/*    */   public DoorsCommandDataForObject(@NotNull String absoluteNumber) {
/* 41 */     this.absoluteNumber = absoluteNumber;
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   public DoorsPublications processResult(@NotNull String result) {
/* 47 */     return (DoorsPublications)JAXB.unmarshal(IOUtils.toInputStream(result, Charset.forName("UTF-8")), DoorsPublications.class);
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   public IDxlScript getScript() {
/* 53 */     return DxlScriptBuilder.create().add((IDxlScript)DxlScriptBuilder.script("sendResult.dxl"))
/* 54 */       .add((IDxlScript)DxlScriptBuilder.script("getDoorsObjects.dxl"))
/* 55 */       .replaceParameter("absoluteNumber", this.absoluteNumber);
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/internal/commands/DoorsCommandDataForObject.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */