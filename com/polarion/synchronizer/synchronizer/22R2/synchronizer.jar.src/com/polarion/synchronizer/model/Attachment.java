/*     */ package com.polarion.synchronizer.model;
/*     */ 
/*     */ import com.polarion.core.config.Configuration;
/*     */ import com.polarion.core.util.logging.Logger;
/*     */ import com.polarion.synchronizer.SynchronizationException;
/*     */ import com.polarion.synchronizer.internal.ole.Ole2FsInputStream;
/*     */ import com.polarion.synchronizer.ole.EmbeddedObject;
/*     */ import com.polarion.synchronizer.ole.OleExtractor;
/*     */ import java.io.ByteArrayInputStream;
/*     */ import java.io.IOException;
/*     */ import java.io.InputStream;
/*     */ import java.io.SequenceInputStream;
/*     */ import java.io.Serializable;
/*     */ import java.security.MessageDigest;
/*     */ import java.security.NoSuchAlgorithmException;
/*     */ import java.util.Arrays;
/*     */ import java.util.Optional;
/*     */ import java.util.Random;
/*     */ import org.apache.commons.io.IOUtils;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ import org.jetbrains.annotations.Nullable;
/*     */ 
/*     */ 
/*     */ 
/*     */ public class Attachment
/*     */   implements Serializable
/*     */ {
/*  28 */   private static final Logger log = Logger.getLogger(Attachment.class);
/*     */   
/*     */   private static final long serialVersionUID = 8913524057510863715L;
/*     */   @NotNull
/*     */   private final byte[] contentHash;
/*     */   @Nullable
/*     */   private transient byte[] thumbnail;
/*     */   @NotNull
/*     */   private final String fileName;
/*     */   @Nullable
/*     */   private transient EmbeddedObject embeddedObject;
/*     */   @NotNull
/*     */   private transient IAttachmentLoader attachmentLoader;
/*  41 */   private boolean isLazyLoadEnabled = Configuration.getInstance().connectors().getAttachmentsLazyLoadingEnabled();
/*     */   
/*     */   public Attachment(@NotNull String fileName, @NotNull EmbeddedObject embeddedObject) {
/*  44 */     this.fileName = fileName;
/*  45 */     this.embeddedObject = embeddedObject;
/*     */     try {
/*  47 */       byte[] contentArray = IOUtils.toByteArray(embeddedObject.getDataAsRtf());
/*  48 */       this.attachmentLoader = (() -> paramArrayOfbyte);
/*     */ 
/*     */       
/*  51 */       this.contentHash = createHash(contentArray, Optional.of(embeddedObject));
/*  52 */     } catch (IOException e) {
/*  53 */       throw new RuntimeException("Failed to process contents of attachment " + fileName, e);
/*     */     } 
/*     */   }
/*     */   
/*     */   public Attachment(@NotNull String fileName, @NotNull IAttachmentLoader attachmentLoader) {
/*  58 */     this.fileName = fileName;
/*  59 */     this.attachmentLoader = attachmentLoader;
/*     */     try {
/*  61 */       byte[] contentArray = attachmentLoader.load();
/*  62 */       this.contentHash = createHash(contentArray);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/*  68 */       if (!this.isLazyLoadEnabled) {
/*  69 */         this.attachmentLoader = (() -> paramArrayOfbyte);
/*     */       
/*     */       }
/*     */     }
/*  73 */     catch (Exception e) {
/*  74 */       throw new RuntimeException("Failed to process contents of attachment " + fileName, e);
/*     */     } 
/*     */   }
/*     */   
/*     */   public Attachment(@NotNull String fileName, @NotNull InputStream content) {
/*  79 */     this.fileName = fileName;
/*     */     try {
/*  81 */       byte[] contentArray = IOUtils.toByteArray(content);
/*  82 */       this.attachmentLoader = (() -> paramArrayOfbyte);
/*     */ 
/*     */       
/*  85 */       this.contentHash = createHash(contentArray);
/*  86 */     } catch (IOException e) {
/*  87 */       throw new RuntimeException("Failed to process contents of attachment " + fileName, e);
/*     */     } 
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private byte[] createHash(@NotNull byte[] contentArray) {
/*  93 */     return createHash(contentArray, loadEmbeddedObject(contentArray));
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private byte[] createHash(@NotNull byte[] contentArray, @NotNull Optional<EmbeddedObject> object) {
/*     */     try {
/*  99 */       InputStream hashIn = object
/* 100 */         .<InputStream>map(o -> {
/*     */             InputStream data = o.getBinaryData();
/*     */             
/*     */             InputStream oleStream = Ole2FsInputStream.create(data).orElse(data);
/*     */             return withDimensions(oleStream, o);
/* 105 */           }).orElseGet(() -> new ByteArrayInputStream(paramArrayOfbyte));
/* 106 */       return hashStream(hashIn);
/* 107 */     } catch (Exception e) {
/* 108 */       log.error("Failed to calculate hash of attachment " + this.fileName + ". Using random hash.", e);
/* 109 */       byte[] hash = new byte[10];
/* 110 */       (new Random()).nextBytes(hash);
/* 111 */       return hash;
/*     */     } 
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private Optional<EmbeddedObject> loadEmbeddedObject(@NotNull byte[] contentArray) {
/*     */     Optional<EmbeddedObject> object;
/*     */     try {
/* 119 */       object = this.fileName.endsWith(".rtf") ? OleExtractor.parse(contentArray).getWrappedObject() : Optional.<EmbeddedObject>empty();
/* 120 */     } catch (Exception e) {
/* 121 */       object = Optional.empty();
/* 122 */       log.error("Failed to process " + this.fileName + "as OLE object, creating content based hash.", e);
/*     */     } 
/* 124 */     this.embeddedObject = object.orElse(null);
/* 125 */     return object;
/*     */   }
/*     */   
/*     */   private InputStream withDimensions(InputStream in, EmbeddedObject o) {
/* 129 */     return new SequenceInputStream(in, new ByteArrayInputStream(String.format("%s:%s", new Object[] { Integer.valueOf(o.getWidth()), Integer.valueOf(o.getHeight()) }).getBytes()));
/*     */   }
/*     */   @NotNull
/*     */   private byte[] hashStream(@NotNull InputStream content) throws IOException {
/*     */     try {
/* 134 */       Exception exception1 = null, exception2 = null;
/*     */     }
/*     */     finally {
/*     */       
/* 138 */       content.close();
/*     */     } 
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private MessageDigest getDigest() {
/*     */     try {
/* 145 */       return MessageDigest.getInstance("MD5");
/* 146 */     } catch (NoSuchAlgorithmException e) {
/* 147 */       throw new RuntimeException("Internal Error: Failed to load MD5 digest");
/*     */     } 
/*     */   }
/*     */   
/*     */   public void setThumbnail(@NotNull InputStream thumbnailContent) {
/*     */     try {
/* 153 */       this.thumbnail = IOUtils.toByteArray(thumbnailContent);
/* 154 */     } catch (IOException e) {
/* 155 */       throw new SynchronizationException("Failed to load attachment thumbnail.", e);
/*     */     } 
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   public InputStream getContent() {
/*     */     try {
/* 162 */       return new ByteArrayInputStream(this.attachmentLoader.load());
/* 163 */     } catch (Exception e) {
/* 164 */       throw new RuntimeException("Error while loading the content of the attachment " + this.fileName, e);
/*     */     } 
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   public Optional<EmbeddedObject> getEmbeddedObject() {
/* 170 */     return Optional.ofNullable(this.embeddedObject);
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   public String getFileName() {
/* 175 */     return this.fileName;
/*     */   }
/*     */   
/*     */   public byte[] getThumbnail() {
/* 179 */     return this.thumbnail;
/*     */   }
/*     */ 
/*     */   
/*     */   public String toString() {
/* 184 */     return "Attachment [fileName=" + this.fileName + ", contentHash=" + Arrays.toString(this.contentHash) + "]";
/*     */   }
/*     */ 
/*     */   
/*     */   public int hashCode() {
/* 189 */     int prime = 31;
/* 190 */     int result = 1;
/* 191 */     result = 31 * result + Arrays.hashCode(this.contentHash);
/* 192 */     result = 31 * result + this.fileName.hashCode();
/* 193 */     return result;
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean equals(Object obj) {
/* 198 */     if (this == obj) {
/* 199 */       return true;
/*     */     }
/* 201 */     if (obj == null) {
/* 202 */       return false;
/*     */     }
/* 204 */     if (getClass() != obj.getClass()) {
/* 205 */       return false;
/*     */     }
/* 207 */     Attachment other = (Attachment)obj;
/* 208 */     if (!Arrays.equals(this.contentHash, other.contentHash)) {
/* 209 */       return false;
/*     */     }
/* 211 */     if (!this.fileName.equals(other.fileName)) {
/* 212 */       return false;
/*     */     }
/* 214 */     return true;
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/model/Attachment.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */