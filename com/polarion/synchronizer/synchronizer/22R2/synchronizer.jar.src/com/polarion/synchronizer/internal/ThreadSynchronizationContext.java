/*     */ package com.polarion.synchronizer.internal;
/*     */ 
/*     */ import com.polarion.synchronizer.IBaselineProvider;
/*     */ import com.polarion.synchronizer.IConflictLog;
/*     */ import com.polarion.synchronizer.IConnectionMap;
/*     */ import com.polarion.synchronizer.IDependencyManager;
/*     */ import com.polarion.synchronizer.ILogger;
/*     */ import com.polarion.synchronizer.ISynchronizationContext;
/*     */ import com.polarion.synchronizer.ISynchronizationTask;
/*     */ import com.polarion.synchronizer.spi.Log4jLoggerAdapter;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ import org.jetbrains.annotations.Nullable;
/*     */ 
/*     */ public class ThreadSynchronizationContext
/*     */   implements ISynchronizationContext
/*     */ {
/*  17 */   private ThreadLocal<SynchronizationContext> localContext = new ThreadLocal<>();
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public IConnectionMap getConnectionMap() {
/*  22 */     return ((SynchronizationContext)this.localContext.get()).getConnectionMap();
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public ILogger getLogger() {
/*  28 */     SynchronizationContext context = this.localContext.get();
/*  29 */     if (context == null) {
/*  30 */       return (ILogger)new Log4jLoggerAdapter();
/*     */     }
/*  32 */     return context.getLogger();
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public IConflictLog getConflictLog() {
/*  39 */     return getContext().getConflictLog();
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private SynchronizationContext getContext() {
/*  44 */     SynchronizationContext context = this.localContext.get();
/*  45 */     if (context == null) {
/*  46 */       context = new SynchronizationContext();
/*  47 */       this.localContext.set(context);
/*     */     } 
/*  49 */     return context;
/*     */   }
/*     */ 
/*     */   
/*     */   public void setLogger(@Nullable ILogger logger) {
/*  54 */     getContext().setLogger(logger);
/*     */   }
/*     */ 
/*     */   
/*     */   public void setConnectionMap(@Nullable IConnectionMap connectionMap) {
/*  59 */     getContext().setConnectionMap(connectionMap);
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public IBaselineProvider getBaselineProvider() {
/*  65 */     return getContext().getBaselineProvider();
/*     */   }
/*     */ 
/*     */   
/*     */   public void setBaselineProvider(@Nullable IBaselineProvider baselineProvider) {
/*  70 */     getContext().setBaselineProvider(baselineProvider);
/*     */   }
/*     */ 
/*     */   
/*     */   @Nullable
/*     */   public IDependencyManager getDependencyManager() {
/*  76 */     return getContext().getDependencyManager();
/*     */   }
/*     */ 
/*     */   
/*     */   public void currentItem(@Nullable String leftItemId, @Nullable String rightItemId) {
/*  81 */     getContext().currentItem(leftItemId, rightItemId);
/*     */   }
/*     */ 
/*     */   
/*     */   public void currentSystem(@NotNull String leftSystem, @NotNull String rightSystem) {
/*  86 */     getContext().currentSystem(leftSystem, rightSystem);
/*     */   }
/*     */ 
/*     */   
/*     */   public void currentTask(@NotNull ISynchronizationTask task) {
/*  91 */     getContext().currentTask(task);
/*     */   }
/*     */ 
/*     */   
/*     */   public void initializeDependencyManager() {
/*  96 */     getContext().initializeDependencyManager();
/*     */   }
/*     */ 
/*     */   
/*     */   public void releaseDependencyManager() {
/* 101 */     getContext().releaseDependencyManager();
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/internal/ThreadSynchronizationContext.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */