/*    */ package com.polarion.synchronizer.internal.ole.preview;
/*    */ 
/*    */ import com.polarion.core.util.StringUtils;
/*    */ import com.polarion.ooxml.oleconverter.IOleConverter;
/*    */ import com.polarion.ooxml.oleconverter.OleConverter;
/*    */ import com.siemens.polarion.previewer.IPreviewResult;
/*    */ import com.siemens.polarion.previewer.IPreviewer;
/*    */ import com.siemens.polarion.previewer.PreviewerParameters;
/*    */ import java.io.InputStream;
/*    */ import java.util.Set;
/*    */ import java.util.function.Supplier;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ import org.jetbrains.annotations.Nullable;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class OLEPreviewer
/*    */   implements IPreviewer
/*    */ {
/*    */   @NotNull
/*    */   private final IPreviewer previewer;
/*    */   
/*    */   public OLEPreviewer() {
/* 25 */     String application = System.getProperty("com.polarion.oleconverter.app");
/* 26 */     if (StringUtils.getNullIfEmpty(application) == null) {
/* 27 */       this.previewer = (IPreviewer)new IPreviewer.EmptyPreviewer();
/*    */     } else {
/* 29 */       this.previewer = (IPreviewer)new OLEPreviewerImpl((IOleConverter)OleConverter.getInstance());
/*    */     } 
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public boolean supportsPriority() {
/* 36 */     return this.previewer.supportsPriority();
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   public Set<String> getSupportedTypeExtensions() {
/* 42 */     return this.previewer.getSupportedTypeExtensions();
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   public IPreviewResult generatePreview(@NotNull InputStream data, @NotNull PreviewerParameters previewParameters, @Nullable Supplier<Boolean> executeGeneration) {
/* 48 */     return this.previewer.generatePreview(data, previewParameters, executeGeneration);
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/internal/ole/preview/OLEPreviewer.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */