/*    */ package com.polarion.synchronizer.model;
/*    */ 
/*    */ public enum Direction
/*    */ {
/*  5 */   LEFT_TO_RIGHT(Side.LEFT), RIGHT_TO_LEFT(Side.RIGHT), BIDIRECTIONAL, DISABLED(null);
/*    */   
/*    */   private Side fromSide;
/*    */   
/*    */   private boolean disabled;
/*    */   private boolean bidirectional;
/*    */   
/*    */   Direction(Side from) {
/* 13 */     this.disabled = (from == null);
/* 14 */     this.bidirectional = false;
/* 15 */     this.fromSide = from;
/*    */   }
/*    */   
/*    */   Direction() {
/* 19 */     this.bidirectional = true;
/*    */   }
/*    */   
/*    */   public boolean isFrom(Side from) {
/* 23 */     return (!this.disabled && (this.bidirectional || this.fromSide == from));
/*    */   }
/*    */   
/*    */   public boolean isTo(Side to) {
/* 27 */     return (!this.disabled && (this.bidirectional || this.fromSide != to));
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/model/Direction.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */