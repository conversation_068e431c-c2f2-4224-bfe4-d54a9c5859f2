/*     */ package com.polarion.synchronizer.internal.ole;
/*     */ 
/*     */ import com.polarion.core.config.Configuration;
/*     */ import java.io.BufferedInputStream;
/*     */ import java.io.IOException;
/*     */ import java.io.InputStream;
/*     */ import java.io.SequenceInputStream;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Collections;
/*     */ import java.util.Comparator;
/*     */ import java.util.HashSet;
/*     */ import java.util.List;
/*     */ import java.util.Optional;
/*     */ import java.util.Set;
/*     */ import org.apache.poi.poifs.filesystem.DirectoryNode;
/*     */ import org.apache.poi.poifs.filesystem.Entry;
/*     */ import org.apache.poi.poifs.filesystem.POIFSFileSystem;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class Ole2FsInputStream
/*     */   extends InputStream
/*     */ {
/*     */   private static final long OLE2_MAGIC_NUMBER = -3400479537158350111L;
/*     */   @NotNull
/*     */   private final InputStream ole2Content;
/*     */   @NotNull
/*  36 */   private final Set<String> filenameBlacklist = new HashSet<>(Configuration.getInstance().connectors().getOLE2FilenameBlacklist());
/*     */   
/*     */   @NotNull
/*     */   private final POIFSFileSystem fs;
/*     */   
/*     */   @NotNull
/*     */   public static Optional<InputStream> create(@NotNull InputStream content) {
/*  43 */     if (!content.markSupported()) {
/*  44 */       content = new BufferedInputStream(content, 8);
/*     */     }
/*     */     try {
/*  47 */       if (isOle2FileSystem(content)) {
/*  48 */         return Optional.of(new Ole2FsInputStream(content));
/*     */       }
/*  50 */       return Optional.empty();
/*     */     }
/*  52 */     catch (IOException e) {
/*  53 */       throw new RuntimeException("Failed to load OLE2 data.", e);
/*     */     } 
/*     */   }
/*     */   
/*     */   private static boolean isOle2FileSystem(@NotNull InputStream is) {
/*  58 */     return (peekMagicNumber(is) == -3400479537158350111L);
/*     */   }
/*     */   
/*     */   private Ole2FsInputStream(@NotNull InputStream ole2Content) throws IOException {
/*  62 */     this.fs = new POIFSFileSystem(ole2Content);
/*  63 */     this.ole2Content = dirStream(this.fs.getRoot());
/*     */   }
/*     */   
/*     */   private static long peekMagicNumber(@NotNull InputStream content) {
/*  67 */     content.mark(8); 
/*  68 */     try { Exception exception1 = null, exception2 = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/*     */       try {  }
/*     */       finally
/*  76 */       { exception2 = null; if (exception1 == null) { exception1 = exception2; } else if (exception1 != exception2) { exception1.addSuppressed(exception2); }  }  } catch (IOException e)
/*  77 */     { throw new AssertionError("Unable to copy bytes", e); }
/*     */   
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private InputStream dirStream(@NotNull DirectoryNode root) throws IOException {
/*  83 */     List<Entry> sortedEntries = new ArrayList<>();
/*  84 */     root.getEntries().forEachRemaining(sortedEntries::add);
/*  85 */     sortedEntries.sort(Comparator.comparing(e -> e.getName()));
/*  86 */     List<InputStream> substreams = new ArrayList<>();
/*  87 */     for (Entry entry : sortedEntries) {
/*  88 */       if (entry.isDirectoryEntry() && entry instanceof DirectoryNode) {
/*  89 */         substreams.add(dirStream((DirectoryNode)entry)); continue;
/*  90 */       }  if (entry.isDocumentEntry() && !this.filenameBlacklist.contains(entry.getName())) {
/*  91 */         substreams.add(root.createDocumentInputStream(entry.getName()));
/*     */       }
/*     */     } 
/*  94 */     return new SequenceInputStream(Collections.enumeration(substreams));
/*     */   }
/*     */ 
/*     */   
/*     */   public int read() throws IOException {
/*  99 */     return this.ole2Content.read();
/*     */   }
/*     */ 
/*     */   
/*     */   public int read(byte[] b) throws IOException {
/* 104 */     return this.ole2Content.read(b);
/*     */   }
/*     */ 
/*     */   
/*     */   public int read(byte[] b, int off, int len) throws IOException {
/* 109 */     return this.ole2Content.read(b, off, len);
/*     */   }
/*     */ 
/*     */   
/*     */   public void close() throws IOException {
/* 114 */     this.fs.close();
/* 115 */     this.ole2Content.close();
/* 116 */     this.fs.close();
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/internal/ole/Ole2FsInputStream.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */