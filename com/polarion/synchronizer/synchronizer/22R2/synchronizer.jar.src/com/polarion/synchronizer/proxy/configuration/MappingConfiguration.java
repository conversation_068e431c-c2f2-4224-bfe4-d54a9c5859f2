/*    */ package com.polarion.synchronizer.proxy.configuration;
/*    */ 
/*    */ import java.util.Collection;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class MappingConfiguration
/*    */ {
/*    */   private Collection<MappingGroup> mappingGroups;
/*    */   
/*    */   @Deprecated
/*    */   public MappingConfiguration() {}
/*    */   
/*    */   public MappingConfiguration(@NotNull Collection<MappingGroup> mappingGroups) {
/* 17 */     this.mappingGroups = mappingGroups;
/*    */   }
/*    */   
/*    */   public Collection<MappingGroup> getMappingGroups() {
/* 21 */     return this.mappingGroups;
/*    */   }
/*    */   
/*    */   public void setMappingGroups(Collection<MappingGroup> mappingGroups) {
/* 25 */     this.mappingGroups = mappingGroups;
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/configuration/MappingConfiguration.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */