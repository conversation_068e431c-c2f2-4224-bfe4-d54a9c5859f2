/*     */ package com.polarion.synchronizer.proxy.doors.internal;
/*     */ 
/*     */ import com.polarion.core.util.ObjectUtils;
/*     */ import com.polarion.platform.i18n.Localization;
/*     */ import com.polarion.synchronizer.IProxyConfiguration;
/*     */ import com.polarion.synchronizer.SynchronizationException;
/*     */ import com.polarion.synchronizer.model.FieldDefinition;
/*     */ import com.polarion.synchronizer.model.IProxy;
/*     */ import com.polarion.synchronizer.model.Option;
/*     */ import com.polarion.synchronizer.model.OptionFieldDefinition;
/*     */ import com.polarion.synchronizer.model.TransferItem;
/*     */ import com.polarion.synchronizer.model.UpdateResult;
/*     */ import com.polarion.synchronizer.proxy.doors.DoorsProxyConfiguration;
/*     */ import com.polarion.synchronizer.spi.IProxyExtension;
/*     */ import com.polarion.synchronizer.spi.ProxyFilter;
/*     */ import java.util.Collection;
/*     */ import java.util.List;
/*     */ import java.util.Optional;
/*     */ import java.util.function.Function;
/*     */ import java.util.stream.Collectors;
/*     */ import java.util.stream.Stream;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ import org.jetbrains.annotations.Nullable;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class TypeAttributeExtension
/*     */   implements IProxyExtension
/*     */ {
/*     */   private final class ExtendedTypeProxy
/*     */     extends ProxyFilter
/*     */   {
/*     */     @NotNull
/*     */     private static final String EMPTY_ID = "--empty--";
/*     */     @NotNull
/*     */     private final OptionFieldDefinition typeAttribute;
/*     */     @NotNull
/*     */     private final String typeAttributeKey;
/*     */     @NotNull
/*     */     private final String typeId;
/*     */     
/*     */     private ExtendedTypeProxy(@NotNull IProxy delegate, @NotNull OptionFieldDefinition typeAttribute, String typeId) {
/*  48 */       super(delegate);
/*  49 */       this.typeAttribute = typeAttribute;
/*  50 */       this.typeAttributeKey = typeAttribute.getKey();
/*  51 */       this.typeId = typeId;
/*     */     }
/*     */ 
/*     */     
/*     */     @NotNull
/*     */     public Collection<Option> getDefinedTypes() {
/*  57 */       return (Collection<Option>)Stream.concat(this.typeAttribute.getAvailableOptions().stream(), Stream.of(new Option("--empty--", Localization.getString("synchronizer.typeAttribute.empty"))))
/*  58 */         .map(value -> new Option(value.getId(), Localization.getString("synchronizer.typeAttribute.value", new String[] { this.typeAttribute.getLabel(), value.getName()
/*  59 */               }))).collect(Collectors.toList());
/*     */     }
/*     */ 
/*     */     
/*     */     @Nullable
/*     */     public Collection<FieldDefinition> getDefinedFields(@Nullable String typeId) {
/*  65 */       return super.getDefinedFields(this.typeId);
/*     */     }
/*     */ 
/*     */     
/*     */     @NotNull
/*     */     public Collection<TransferItem> getScopeItems(@NotNull Collection<String> keys) {
/*  71 */       return adjustItems(k -> super.getScopeItems(k), keys);
/*     */     }
/*     */ 
/*     */     
/*     */     @NotNull
/*     */     public Collection<TransferItem> getItems(@NotNull Collection<String> ids, @NotNull Collection<String> keys) {
/*  77 */       return adjustItems(k -> super.getItems(param1Collection1, k), keys);
/*     */     }
/*     */     
/*     */     @NotNull
/*     */     private Collection<TransferItem> adjustItems(@NotNull Function<Collection<String>, Collection<TransferItem>> getItems, Collection<String> keys) {
/*  82 */       return (Collection<TransferItem>)((Collection)getItems.apply((Collection<String>)keys.stream().map(key -> key.equals("type") ? this.typeAttributeKey : key).collect(Collectors.toList())))
/*  83 */         .stream().map(item -> {
/*     */             TransferItem mapped = new TransferItem(item.getId());
/*     */             
/*     */             mapped.getValues().putAll(item.getValues());
/*     */             if (!param1Collection.contains(this.typeAttributeKey)) {
/*     */               mapped.getValues().remove(this.typeAttributeKey);
/*     */             }
/*     */             mapped.getValues().put("type", Optional.<Object>ofNullable(item.getValue(this.typeAttributeKey)).orElse("--empty--"));
/*     */             return mapped;
/*  92 */           }).collect(Collectors.toList());
/*     */     }
/*     */ 
/*     */     
/*     */     @NotNull
/*     */     public List<UpdateResult> update(@NotNull List<TransferItem> items) {
/*  98 */       return super.update(adjustForUpdate(items));
/*     */     }
/*     */     
/*     */     @NotNull
/*     */     private List<TransferItem> adjustForUpdate(@NotNull List<TransferItem> items) {
/* 103 */       List<TransferItem> collect = (List<TransferItem>)items.stream().map(item -> {
/*     */             TransferItem mapped = new TransferItem((item.getKey()).id, (item.getKey()).isForeign);
/*     */             
/*     */             mapped.getValues().putAll(item.getValues());
/*     */             
/*     */             Optional.<String>ofNullable((String)mapped.getValues().remove("type")).ifPresent(());
/*     */             return mapped;
/* 110 */           }).collect(Collectors.toList());
/* 111 */       return collect;
/*     */     }
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public IProxy applyToProxy(@NotNull IProxy proxy, @NotNull IProxyConfiguration<?> configuration) {
/*     */     IProxy extended;
/* 119 */     String typeAttribute = getTypeAttribute(configuration);
/*     */     
/* 121 */     if (typeAttribute != null) {
/* 122 */       Collection<Option> definedTypes = proxy.getDefinedTypes();
/* 123 */       Option type = definedTypes.iterator().next();
/*     */       
/* 125 */       OptionFieldDefinition typeAttributeDefinition = (OptionFieldDefinition)((Collection)ObjectUtils.notNull(proxy.getDefinedFields(type.getId()))).stream()
/* 126 */         .filter(field -> field.getType().equals("option"))
/* 127 */         .filter(field -> !field.isMultiValued())
/* 128 */         .filter(field -> field instanceof OptionFieldDefinition)
/* 129 */         .filter(field -> field.getKey().equals(paramString))
/* 130 */         .findFirst()
/* 131 */         .orElseThrow(() -> new SynchronizationException(String.format("Invalid type attribute '%s'.", new Object[] { paramString })));
/*     */       
/* 133 */       ExtendedTypeProxy extendedTypeProxy = new ExtendedTypeProxy(proxy, typeAttributeDefinition, type.getId());
/*     */     } else {
/* 135 */       extended = proxy;
/*     */     } 
/* 137 */     return extended;
/*     */   }
/*     */   
/*     */   @Nullable
/*     */   private String getTypeAttribute(@NotNull IProxyConfiguration<?> configuration) {
/* 142 */     if (configuration instanceof DoorsProxyConfiguration) {
/* 143 */       DoorsProxyConfiguration doorsConfiguration = (DoorsProxyConfiguration)configuration;
/* 144 */       return doorsConfiguration.getTypeAttribute();
/*     */     } 
/* 146 */     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public <T extends com.polarion.synchronizer.configuration.IConnection> IProxyConfiguration<T> applyToConfiguration(IProxyConfiguration<T> configuration) {
/* 152 */     return configuration;
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/internal/TypeAttributeExtension.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */