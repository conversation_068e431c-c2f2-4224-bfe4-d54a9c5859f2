/*     */ package com.polarion.synchronizer.internal;
/*     */ 
/*     */ import com.polarion.synchronizer.IBaselineProvider;
/*     */ import com.polarion.synchronizer.IConflictLog;
/*     */ import com.polarion.synchronizer.IConnectionMap;
/*     */ import com.polarion.synchronizer.IDependencyManager;
/*     */ import com.polarion.synchronizer.ILogger;
/*     */ import com.polarion.synchronizer.ISynchronizationContext;
/*     */ import com.polarion.synchronizer.ISynchronizationTask;
/*     */ import com.polarion.synchronizer.spi.Log4jLoggerAdapter;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ import org.jetbrains.annotations.Nullable;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class SynchronizationContext
/*     */   implements ISynchronizationContext
/*     */ {
/*     */   private IConnectionMap connectionMap;
/*  29 */   private ILogger logger = (ILogger)new Log4jLoggerAdapter();
/*     */   
/*     */   protected IConflictLog conflictLog;
/*     */   
/*     */   @NotNull
/*     */   public IConnectionMap getConnectionMap() {
/*  35 */     if (this.connectionMap == null) {
/*  36 */       throw new IllegalStateException("ConnectionMap has not been initialized.");
/*     */     }
/*  38 */     return this.connectionMap;
/*     */   }
/*     */   private IBaselineProvider baselineProvider; private DependencyManager dependencyManager;
/*     */   
/*     */   @NotNull
/*     */   public ILogger getLogger() {
/*  44 */     return this.logger;
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public IConflictLog getConflictLog() {
/*  50 */     if (this.conflictLog == null) {
/*  51 */       throw new IllegalStateException("ConflictLog has not been initialized.");
/*     */     }
/*  53 */     return this.conflictLog;
/*     */   }
/*     */ 
/*     */   
/*     */   public void setConnectionMap(@Nullable IConnectionMap connectionMap) {
/*  58 */     this.connectionMap = connectionMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public void setLogger(@Nullable ILogger logger) {
/*  63 */     this.logger = logger;
/*  64 */     if (logger != null) {
/*  65 */       this.conflictLog = new ConflictLog(logger);
/*     */     } else {
/*  67 */       this.conflictLog = null;
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public IBaselineProvider getBaselineProvider() {
/*  74 */     if (this.baselineProvider == null) {
/*  75 */       throw new IllegalStateException("baselineProvider has not been initialized.");
/*     */     }
/*  77 */     return this.baselineProvider;
/*     */   }
/*     */ 
/*     */   
/*     */   public void setBaselineProvider(@Nullable IBaselineProvider baselineProvider) {
/*  82 */     this.baselineProvider = baselineProvider;
/*     */   }
/*     */ 
/*     */   
/*     */   @Nullable
/*     */   public IDependencyManager getDependencyManager() {
/*  88 */     return this.dependencyManager;
/*     */   }
/*     */   
/*     */   public void setDependencyManager(@Nullable DependencyManager dependencyManager) {
/*  92 */     this.dependencyManager = dependencyManager;
/*     */   }
/*     */ 
/*     */   
/*     */   public void currentItem(@Nullable String leftItemId, @Nullable String rightItemId) {
/*  97 */     getConflictLog().current(leftItemId, rightItemId);
/*  98 */     if (this.dependencyManager != null) {
/*  99 */       this.dependencyManager.currentItem(leftItemId, rightItemId);
/*     */     }
/*     */   }
/*     */ 
/*     */   
/*     */   public void currentSystem(@NotNull String leftSystem, @NotNull String rightSystem) {
/* 105 */     if (this.dependencyManager != null) {
/* 106 */       this.dependencyManager.currentSystem(leftSystem, rightSystem);
/*     */     }
/*     */   }
/*     */ 
/*     */   
/*     */   public void currentTask(@NotNull ISynchronizationTask task) {
/* 112 */     if (this.dependencyManager != null) {
/* 113 */       this.dependencyManager.currentTask(task);
/*     */     }
/*     */   }
/*     */ 
/*     */   
/*     */   public void initializeDependencyManager() {
/* 119 */     this.dependencyManager = new DependencyManager(this);
/*     */   }
/*     */ 
/*     */   
/*     */   public void releaseDependencyManager() {
/* 124 */     this.dependencyManager = null;
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/internal/SynchronizationContext.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */