/*    */ package com.polarion.synchronizer.internal.mapping;
/*    */ 
/*    */ import com.polarion.synchronizer.ISynchronizationContext;
/*    */ import com.polarion.synchronizer.model.Direction;
/*    */ import com.polarion.synchronizer.model.Side;
/*    */ import com.polarion.synchronizer.model.TransferItem;
/*    */ import java.util.Collection;
/*    */ 
/*    */ 
/*    */ public class DefaultFieldMappingGroup
/*    */   extends FieldMappingGroup
/*    */ {
/*    */   private final Direction hierarchyDirection;
/*    */   private final Direction primaryHierarchyDirection;
/*    */   
/*    */   public DefaultFieldMappingGroup(ISynchronizationContext context, Direction hierarchyDirection, Direction primaryHierarchyDirection) {
/* 17 */     super(context);
/* 18 */     this.hierarchyDirection = hierarchyDirection;
/* 19 */     this.primaryHierarchyDirection = primaryHierarchyDirection;
/*    */   }
/*    */   
/*    */   public HierarchyProcessor loadHierarchyProcessor(Collection<TransferItem> leftSourceItems, Collection<TransferItem> rightSourceItems, boolean isPartial) {
/* 23 */     return new HierarchyProcessor(leftSourceItems, rightSourceItems, isPartial ? null : this.hierarchyDirection, this.primaryHierarchyDirection, 
/* 24 */         this.context);
/*    */   }
/*    */ 
/*    */   
/*    */   public Collection<String> getRequiredFields(Side side) {
/* 29 */     Collection<String> requiredFields = super.getRequiredFields(side);
/* 30 */     if (this.hierarchyDirection != null) {
/* 31 */       requiredFields.add("hierarchy");
/*    */     }
/* 33 */     return requiredFields;
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/internal/mapping/DefaultFieldMappingGroup.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */