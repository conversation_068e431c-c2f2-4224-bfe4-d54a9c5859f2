package com.polarion.synchronizer;

import com.polarion.platform.jobs.IJobUnit;
import com.polarion.synchronizer.configuration.ISyncPair;
import java.util.Collection;
import org.jetbrains.annotations.NotNull;

public interface ISyncJobUnit extends IJobUnit {
  public static final String NAME = "synchronizer";
  
  void setSyncPair(@NotNull String paramString);
  
  void setConfiguration(@NotNull Collection<ISyncPair> paramCollection);
  
  boolean hasFinished();
}


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/ISyncJobUnit.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */