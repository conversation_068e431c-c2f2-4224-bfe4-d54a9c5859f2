package com.polarion.synchronizer.proxy.doors.internal.tables;

import java.util.Collection;
import org.jetbrains.annotations.NotNull;

public interface ITable {
  @NotNull
  String toHtml();
  
  @NotNull
  String getId();
  
  @NotNull
  Collection<String> getContainedItemIds();
}


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/internal/tables/ITable.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */