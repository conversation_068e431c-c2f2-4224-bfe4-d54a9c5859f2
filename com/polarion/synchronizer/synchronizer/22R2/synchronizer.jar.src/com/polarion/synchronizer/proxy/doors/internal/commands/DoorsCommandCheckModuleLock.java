/*    */ package com.polarion.synchronizer.proxy.doors.internal.commands;
/*    */ 
/*    */ import com.polarion.synchronizer.proxy.doors.internal.dxl.DxlScriptBuilder;
/*    */ import com.polarion.synchronizer.proxy.doors.internal.dxl.IDxlScript;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ 
/*    */ public class DoorsCommandCheckModuleLock
/*    */   extends AbstractDoorsCommand<Boolean>
/*    */ {
/*    */   private final String moduleId;
/*    */   
/*    */   public DoorsCommandCheckModuleLock(String moduleId) {
/* 13 */     this.moduleId = moduleId;
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   protected IDxlScript getScript() {
/* 19 */     return (IDxlScript)DxlScriptBuilder.script("checkModuleLockByID.dxl").replaceParameter("module.id", this.moduleId);
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   protected Boolean processResult(@NotNull String lockStatus) throws Exception {
/* 25 */     return Boolean.valueOf(lockStatus.equals("LOCKED"));
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/internal/commands/DoorsCommandCheckModuleLock.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */