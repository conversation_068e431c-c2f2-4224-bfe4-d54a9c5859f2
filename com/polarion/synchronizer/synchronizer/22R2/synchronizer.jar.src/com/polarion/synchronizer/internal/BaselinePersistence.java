/*     */ package com.polarion.synchronizer.internal;
/*     */ 
/*     */ import com.polarion.platform.internal.ExecutionThreadMonitor;
/*     */ import com.polarion.platform.internal.TxLogger;
/*     */ import com.polarion.synchronizer.IBaselineProvider;
/*     */ import com.polarion.synchronizer.ISynchronizationHook;
/*     */ import com.polarion.synchronizer.SynchronizationException;
/*     */ import com.polarion.synchronizer.model.CollectionUpdate;
/*     */ import com.polarion.synchronizer.model.Side;
/*     */ import com.polarion.synchronizer.model.TransferItem;
/*     */ import java.util.Collection;
/*     */ import java.util.HashMap;
/*     */ import java.util.HashSet;
/*     */ import java.util.Map;
/*     */ import javax.inject.Inject;
/*     */ import javax.inject.Singleton;
/*     */ 
/*     */ 
/*     */ 
/*     */ public class BaselinePersistence
/*     */   implements IBaselineProvider, ISynchronizationHook
/*     */ {
/*  23 */   private ThreadLocal<Collection<TransferItem>> mappedRight = new ThreadLocal<>();
/*     */   
/*     */   private final Map<String, Map<String, Object>> leftBaseline;
/*     */   private final Map<String, Map<String, Object>> rightBaseline;
/*     */   
/*     */   @Inject
/*     */   @Singleton
/*     */   public BaselinePersistence(Map<String, Map<String, Object>> leftBaseline, Map<String, Map<String, Object>> rightBaseline) {
/*  31 */     this.leftBaseline = leftBaseline;
/*  32 */     this.rightBaseline = rightBaseline;
/*     */   }
/*     */ 
/*     */   
/*     */   public void afterCreate(Collection<TransferItem> transferItems, Collection<TransferItem> sourceItems, Side targetSide, Collection<String> sourceFields) {
/*  37 */     updateBaselines(transferItems, sourceItems, targetSide, sourceFields);
/*     */   }
/*     */   
/*     */   private void updateBaselines(Collection<TransferItem> mappedItems, Collection<TransferItem> sourceItems, Side targetSide, Collection<String> sourceFields) {
/*  41 */     updateSourceBaseline(sourceItems, targetSide.getOtherSide(), sourceFields);
/*  42 */     updateMappedBaseline(mappedItems, targetSide);
/*     */   }
/*     */   
/*     */   private void updateSourceBaseline(Collection<TransferItem> sourceItems, Side targetSide, Collection<String> sourceFields) {
/*  46 */     Map<String, Map<String, Object>> sourceData = getData(targetSide);
/*  47 */     for (TransferItem transferItem : sourceItems) {
/*  48 */       Map<String, Object> baselienData = transferItem.getValues();
/*  49 */       baselienData.keySet().retainAll(sourceFields);
/*  50 */       updateBaselineItem(sourceData, transferItem.getId(), baselienData);
/*     */     } 
/*     */   }
/*     */   
/*     */   private void updateMappedBaseline(Collection<TransferItem> transferItems, Side targetSide) {
/*  55 */     Map<String, Map<String, Object>> targetData = getData(targetSide);
/*  56 */     for (TransferItem transferItem : transferItems) {
/*  57 */       updateBaseline(targetData, transferItem, true);
/*     */     }
/*     */   }
/*     */   
/*     */   private void updateBaseline(Map<String, Map<String, Object>> data, TransferItem transferItem, boolean isTargetData) {
/*  62 */     String key = isTargetData ? transferItem.getId() : transferItem.getId();
/*  63 */     Map<String, Object> newBaseline = transferItem.getValues();
/*     */     
/*  65 */     updateBaselineItem(data, key, newBaseline);
/*     */   }
/*     */   
/*     */   private void updateBaselineItem(Map<String, Map<String, Object>> data, String key, Map<String, Object> newBaseline) {
/*  69 */     Map<String, Object> baseline = data.get(key);
/*  70 */     if (baseline == null) {
/*  71 */       baseline = new HashMap<>();
/*     */     }
/*  73 */     copyToBaseline(newBaseline, baseline);
/*  74 */     data.put(key, baseline);
/*     */   }
/*     */   
/*     */   public void copyToBaseline(Map<String, Object> source, Map<String, Object> target) {
/*  78 */     Collection<Map.Entry<String, Object>> entries = source.entrySet();
/*  79 */     for (Map.Entry<String, Object> newEntry : entries) {
/*  80 */       String valueKey = newEntry.getKey();
/*  81 */       Object value = newEntry.getValue();
/*  82 */       if (value instanceof CollectionUpdate) {
/*  83 */         CollectionUpdate<?> collectionUpdate = (CollectionUpdate)value;
/*  84 */         target.put(valueKey, collectionUpdate.applyTo(target.get(valueKey))); continue;
/*  85 */       }  if (value instanceof Collection) {
/*  86 */         target.put(valueKey, new HashSet((Collection)value)); continue;
/*     */       } 
/*  88 */       target.put(valueKey, value);
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   private synchronized Map<String, Map<String, Object>> getData(Side side) {
/*  94 */     return (side == Side.LEFT) ? this.leftBaseline : this.rightBaseline;
/*     */   }
/*     */ 
/*     */   
/*     */   public void afterUpdate(Collection<TransferItem> mappedItems, Collection<TransferItem> sourceItems, Collection<String> sourceFields, Side targetSide) {
/*  99 */     String txOperationName = "afterUpdate";
/* 100 */     String area = TxLogger.contributionName(this, getClass());
/* 101 */     TxLogger.began(area, txOperationName);
/*     */     try {
/* 103 */       updateBaselines(mappedItems, sourceItems, targetSide, sourceFields);
/*     */       
/* 105 */       if (targetSide == Side.RIGHT) {
/* 106 */         this.mappedRight.set(mappedItems);
/*     */       } else {
/* 108 */         Collection<TransferItem> mappedRight = this.mappedRight.get();
/* 109 */         if (mappedRight == null) {
/* 110 */           throw new SynchronizationException("Internal Error: afterUpdate was not called for right side in this run.");
/*     */         }
/*     */         
/* 113 */         updateMappedBaseline(mappedRight, targetSide.getOtherSide());
/*     */       } 
/*     */     } finally {
/* 116 */       TxLogger.ended(area, txOperationName);
/* 117 */       ExecutionThreadMonitor.checkForInterruption();
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Object> loadBaseline(String id, Side side) {
/* 123 */     Map<String, Map<String, Object>> data = getData(side);
/*     */ 
/*     */     
/* 126 */     return data.get(id);
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/internal/BaselinePersistence.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */