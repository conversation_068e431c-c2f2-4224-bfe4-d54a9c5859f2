/*    */ package com.polarion.synchronizer.spi.translators;
/*    */ 
/*    */ import com.google.inject.assistedinject.Assisted;
/*    */ import com.polarion.synchronizer.mapping.ValueMapping;
/*    */ import com.polarion.synchronizer.model.Side;
/*    */ import java.util.Collection;
/*    */ import java.util.LinkedHashSet;
/*    */ import javax.inject.Inject;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ import org.jetbrains.annotations.Nullable;
/*    */ 
/*    */ 
/*    */ 
/*    */ public final class StringCollectionTranslator
/*    */   extends AbstractCollectionTranslator<String, String>
/*    */ {
/*    */   @Inject
/*    */   public StringCollectionTranslator(@Assisted Collection<ValueMapping> valueMappings, @Assisted Side fromSide) {
/* 19 */     super(valueMappings, fromSide);
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   protected Collection<String> mapCollection(@Nullable Collection<String> sourceValue, @Nullable Collection<String> otherValue, boolean explode) {
/* 25 */     Collection<String> resultCollection = new LinkedHashSet<>();
/* 26 */     if (sourceValue != null) {
/* 27 */       for (String string : sourceValue) {
/*    */         
/* 29 */         if (explode) {
/* 30 */           Collection<String> matches = loadPotentialMatches(string);
/* 31 */           if (matches.isEmpty()) {
/* 32 */             resultCollection.add(string); continue;
/*    */           } 
/* 34 */           resultCollection.addAll(matches);
/*    */           continue;
/*    */         } 
/* 37 */         resultCollection.add(loadBestMatch(otherValue, string));
/*    */       } 
/*    */     }
/*    */ 
/*    */     
/* 42 */     return resultCollection;
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/spi/translators/StringCollectionTranslator.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */