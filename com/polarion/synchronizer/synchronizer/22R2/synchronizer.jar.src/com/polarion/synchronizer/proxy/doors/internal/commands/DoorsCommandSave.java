/*    */ package com.polarion.synchronizer.proxy.doors.internal.commands;
/*    */ 
/*    */ import com.polarion.synchronizer.proxy.doors.internal.dxl.DxlScriptBuilder;
/*    */ import com.polarion.synchronizer.proxy.doors.internal.dxl.IDxlScript;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ 
/*    */ 
/*    */ public class DoorsCommandSave
/*    */   extends AbstractDoorsCommand<Boolean>
/*    */ {
/*    */   @NotNull
/*    */   protected IDxlScript getScript() {
/* 13 */     return (IDxlScript)DxlScriptBuilder.script("Save.dxl");
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   protected Boolean processResult(@NotNull String result) throws Exception {
/* 19 */     return Boolean.valueOf(true);
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/internal/commands/DoorsCommandSave.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */