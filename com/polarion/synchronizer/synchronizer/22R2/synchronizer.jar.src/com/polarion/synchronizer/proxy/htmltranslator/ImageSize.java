/*    */ package com.polarion.synchronizer.proxy.htmltranslator;
/*    */ 
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import java.util.regex.Matcher;
/*    */ import java.util.regex.Pattern;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class ImageSize
/*    */ {
/*    */   private int width;
/*    */   private int height;
/*    */   
/*    */   public ImageSize(int width, int height) {
/* 18 */     this.width = width;
/* 19 */     this.height = height;
/*    */   }
/*    */   
/*    */   public int getWidth() {
/* 23 */     return this.width;
/*    */   }
/*    */   
/*    */   public int getHeight() {
/* 27 */     return this.height;
/*    */   }
/*    */   
/*    */   @NotNull
/*    */   public static ImageSize getDimensions(@NotNull String styleAttributes) {
/* 32 */     String[] styles = styleAttributes.split(";");
/* 33 */     Map<String, Integer> styleMap = new HashMap<>(); byte b; int i; String[] arrayOfString1;
/* 34 */     for (i = (arrayOfString1 = styles).length, b = 0; b < i; ) { String style = arrayOfString1[b];
/* 35 */       String[] vals = style.split(":");
/* 36 */       if (vals.length == 2) {
/* 37 */         String key = vals[0];
/* 38 */         int value = getNumValue(vals[1]);
/* 39 */         styleMap.put(key, Integer.valueOf(value));
/*    */       }  b++; }
/*    */     
/* 42 */     int width = styleMap.containsKey("width") ? ((Integer)styleMap.get("width")).intValue() : 0;
/* 43 */     int height = styleMap.containsKey("height") ? ((Integer)styleMap.get("height")).intValue() : 0;
/* 44 */     return new ImageSize(width, height);
/*    */   }
/*    */   
/*    */   public static int getNumValue(@NotNull String str) {
/* 48 */     Matcher matcher = Pattern.compile("\\d+").matcher(str);
/* 49 */     if (!matcher.find()) {
/* 50 */       return 0;
/*    */     }
/* 52 */     return Integer.parseInt(matcher.group());
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/htmltranslator/ImageSize.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */