/*     */ package com.polarion.synchronizer.internal.mapping;
/*     */ 
/*     */ import com.google.common.base.Preconditions;
/*     */ import com.polarion.synchronizer.IDependencyManager;
/*     */ import com.polarion.synchronizer.ISynchronizationContext;
/*     */ import com.polarion.synchronizer.SynchronizationException;
/*     */ import com.polarion.synchronizer.mapping.ITranslator;
/*     */ import com.polarion.synchronizer.mapping.TranslationResult;
/*     */ import com.polarion.synchronizer.model.Attachment;
/*     */ import com.polarion.synchronizer.model.CollectionUpdate;
/*     */ import com.polarion.synchronizer.model.Direction;
/*     */ import com.polarion.synchronizer.model.FieldDefinition;
/*     */ import com.polarion.synchronizer.model.Side;
/*     */ import com.polarion.synchronizer.model.TransferItem;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Collection;
/*     */ import java.util.Collections;
/*     */ import java.util.HashMap;
/*     */ import java.util.LinkedHashSet;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ import org.jetbrains.annotations.Nullable;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class FieldMapping
/*     */ {
/*     */   private final Direction direction;
/*     */   private final Direction primaryDirection;
/*     */   private final Direction deleteDirection;
/*     */   private final FieldDefinition left;
/*     */   private final FieldDefinition right;
/*     */   private final ITranslator<?> leftTranslator;
/*     */   private final ITranslator<?> rightTranslator;
/*     */   private final ISynchronizationContext context;
/*     */   
/*     */   private abstract class FieldMappingContext
/*     */   {
/*     */     @NotNull
/*     */     protected final Side fromSide;
/*     */     @NotNull
/*     */     protected final String fromKey;
/*     */     @NotNull
/*     */     protected final String targetKey;
/*     */     @NotNull
/*     */     protected final TransferItem result;
/*     */     
/*     */     protected FieldMappingContext(@NotNull Side fromSide, @Nullable TransferItem source, @Nullable Map<String, Object> sourceBaseline, @Nullable TransferItem targetItem, @NotNull Map<String, Object> targetBaseline, TransferItem result) {
/*  53 */       this.fromSide = fromSide;
/*  54 */       this.result = result;
/*     */       
/*  56 */       this.fromKey = (fromSide == Side.LEFT) ? FieldMapping.this.left.getKey() : FieldMapping.this.right.getKey();
/*  57 */       FieldDefinition targetDefinition = (fromSide == Side.LEFT) ? FieldMapping.this.right : FieldMapping.this.left;
/*  58 */       this.targetKey = targetDefinition.getKey();
/*     */       
/*  60 */       this.sourceValue = source.getValues().get(this.fromKey);
/*  61 */       this.targetValue = (targetItem == null) ? null : targetItem.getValues().get(this.targetKey);
/*     */       
/*  63 */       this.sourceBaselineValue = (sourceBaseline == null) ? null : sourceBaseline.get(this.fromKey);
/*  64 */       this.targetBaselineValue = (targetBaseline == null) ? null : targetBaseline.get(this.targetKey); } @Nullable
/*     */     protected final Object sourceValue; @Nullable
/*     */     protected final Object sourceBaselineValue; @Nullable
/*     */     protected final Object targetValue; @Nullable
/*     */     protected final Object targetBaselineValue; @NotNull
/*  69 */     public ITranslator<?> getTranslator() { ITranslator<? extends Object> translator = (this.fromSide == Side.LEFT) ? (ITranslator)FieldMapping.this.leftTranslator : (ITranslator)FieldMapping.this.rightTranslator;
/*  70 */       if (translator == null) {
/*  71 */         throw new SynchronizationException("No translator from " + this.fromSide);
/*     */       }
/*  73 */       return translator; }
/*     */ 
/*     */     
/*     */     @NotNull
/*     */     public TranslationResult<?> translate() {
/*  78 */       if ("plannedIn".equals(this.targetKey) || "fixVersionIds".equals(this.targetKey)) {
/*  79 */         return translatePlannedInAndFixVersionIdsFields();
/*     */       }
/*  81 */       if (FieldMapping.this.direction == Direction.BIDIRECTIONAL) {
/*  82 */         return getTranslator().translateBidirectional(this.sourceBaselineValue, this.sourceValue, this.targetBaselineValue, this.targetValue);
/*     */       }
/*  84 */       if (this.targetKey.equals("attachments") && FieldMapping.this.deleteDirection == Direction.DISABLED) {
/*  85 */         Collection<Attachment> targetAttachmentsbaseline = (this.targetBaselineValue == null) ? Collections.EMPTY_LIST : (Collection<Attachment>)this.targetBaselineValue;
/*  86 */         Collection<Attachment> targetAttachments = (this.targetValue == null) ? null : (Collection<Attachment>)this.targetValue;
/*  87 */         if (targetAttachments != null) {
/*  88 */           targetAttachments.retainAll(targetAttachmentsbaseline);
/*     */         }
/*  90 */         return getTranslator().translateUnidirectional(this.sourceValue, targetAttachments);
/*     */       } 
/*  92 */       return getTranslator().translateUnidirectional(this.sourceValue, this.targetValue);
/*     */     }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*     */     @NotNull
/*     */     private TranslationResult<?> translatePlannedInAndFixVersionIdsFields() {
/*     */       TranslationResult<?> translateResult;
/*     */       Map<String, String> targetSourcePlanIdMap;
/* 103 */       IDependencyManager dependencyManager = FieldMapping.this.context.getDependencyManager();
/* 104 */       Collection<String> newSourceValue = new ArrayList<>();
/* 105 */       Collection<String> newsourceBaselineValue = new ArrayList<>();
/* 106 */       if (FieldMapping.this.direction == Direction.BIDIRECTIONAL) {
/* 107 */         targetSourcePlanIdMap = convertSourceBaselineValuesForCompare(this.fromSide, this.sourceValue, this.sourceBaselineValue, newSourceValue, newsourceBaselineValue);
/* 108 */         translateResult = getTranslator().translateBidirectional(newsourceBaselineValue, newSourceValue, this.targetBaselineValue, this.targetValue);
/*     */       } else {
/* 110 */         targetSourcePlanIdMap = convertSourceBaselineValuesForCompare(this.fromSide, this.sourceValue, null, newSourceValue, newsourceBaselineValue);
/* 111 */         translateResult = getTranslator().translateUnidirectional(newSourceValue, this.targetValue);
/*     */       } 
/* 113 */       CollectionUpdate<String> resultValue = (CollectionUpdate<String>)translateResult.getResultValue();
/* 114 */       Collection<String> addedPlans = resultValue.getAdded();
/* 115 */       List<String> modifiedPlans = new ArrayList<>();
/* 116 */       modifiedPlans.addAll(addedPlans);
/* 117 */       boolean isTrackDependencySet = false;
/* 118 */       for (String planID : addedPlans) {
/* 119 */         if (targetSourcePlanIdMap.containsKey(planID)) {
/* 120 */           planID = targetSourcePlanIdMap.get(planID);
/*     */         }
/* 122 */         String targetId = FieldMapping.this.context.getConnectionMap().getTargetId(planID, this.fromSide, true);
/* 123 */         if (targetId == null && dependencyManager != null) {
/* 124 */           dependencyManager.trackDependency(this.fromSide, planID);
/*     */           
/* 126 */           isTrackDependencySet = true;
/* 127 */           modifiedPlans.remove(planID);
/*     */         } 
/*     */       } 
/* 130 */       if (isTrackDependencySet) {
/* 131 */         return new TranslationResult(new CollectionUpdate(modifiedPlans, resultValue.getRemoved()), false, false);
/*     */       }
/* 133 */       return translateResult;
/*     */     }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*     */     @NotNull
/*     */     private Map<String, String> convertSourceBaselineValuesForCompare(@NotNull Side fromSide, @Nullable Object sourceValue, @Nullable Object sourceBaselineValue, @NotNull Collection<String> newSourceValue, @NotNull Collection<String> newSourceBaselineValue) {
/* 146 */       Map<String, String> targetSourcePlanIdMap = new HashMap<>();
/* 147 */       if (sourceValue != null) {
/* 148 */         translateToTargetIdsAndUpdateMap(fromSide, (Collection<String>)sourceValue, newSourceValue, targetSourcePlanIdMap);
/*     */       }
/* 150 */       if (sourceBaselineValue != null) {
/* 151 */         translateToTargetIdsAndUpdateMap(fromSide, (Collection<String>)sourceBaselineValue, newSourceBaselineValue, targetSourcePlanIdMap);
/*     */       }
/* 153 */       return targetSourcePlanIdMap;
/*     */     }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*     */     private void translateToTargetIdsAndUpdateMap(@NotNull Side fromSide, @NotNull Collection<String> existingIds, @NotNull Collection<String> newSourceValues, Map<String, String> targetSourcePlanIdMap) {
/* 163 */       for (String planID : existingIds) {
/* 164 */         String targetId = FieldMapping.this.context.getConnectionMap().getTargetId(planID, fromSide, true);
/* 165 */         if (targetId != null) {
/* 166 */           newSourceValues.add(targetId);
/* 167 */           targetSourcePlanIdMap.put(targetId, planID); continue;
/*     */         } 
/* 169 */         newSourceValues.add(planID);
/*     */       } 
/*     */     }
/*     */ 
/*     */     
/*     */     @Nullable
/*     */     public Object getResultValue() {
/* 176 */       return this.result.getValue(this.targetKey);
/*     */     }
/*     */     
/*     */     public void setResultValue(@Nullable Object value) {
/* 180 */       this.result.put(this.targetKey, value);
/*     */     }
/*     */ 
/*     */     
/*     */     public TranslationResult<?> createSingleValueResult() {
/* 185 */       TranslationResult<?> translationResult = translate();
/* 186 */       Object resultValue = translationResult.getResultValue();
/* 187 */       if (translationResult.isModification()) {
/* 188 */         if (!translationResult.isConflict() || FieldMapping.this.primaryDirection.isFrom(this.fromSide)) {
/* 189 */           setResultValue(resultValue);
/*     */         } else {
/*     */           
/* 192 */           FieldMapping.this.context.getConflictLog().reportConflict(this.targetKey, this.fromKey, this.fromSide.getOtherSide());
/*     */         } 
/*     */       }
/* 195 */       return translationResult;
/*     */     }
/*     */     
/*     */     @NotNull
/*     */     public abstract TranslationResult<?> executeMapping();
/*     */   }
/*     */   
/*     */   private class SingleValueMappingContext
/*     */     extends FieldMappingContext
/*     */   {
/*     */     protected SingleValueMappingContext(Side fromSide, TransferItem source, Map<String, Object> sourceBaseline, TransferItem targetItem, Map<String, Object> targetBaseline, TransferItem result) {
/* 206 */       super(fromSide, source, sourceBaseline, targetItem, targetBaseline, result);
/*     */     }
/*     */ 
/*     */     
/*     */     @NotNull
/*     */     public TranslationResult<?> executeMapping() {
/* 212 */       return createSingleValueResult();
/*     */     }
/*     */   }
/*     */ 
/*     */   
/*     */   private class MultiValueMappingContext
/*     */     extends FieldMappingContext
/*     */   {
/*     */     private CollectionUpdate<?> previousUpdate;
/*     */     
/*     */     private Collection<Object> targetCollection;
/*     */ 
/*     */     
/*     */     protected MultiValueMappingContext(@NotNull Side fromSide, @Nullable TransferItem source, @Nullable Map<String, Object> sourceBaseline, @Nullable TransferItem targetItem, @NotNull Map<String, Object> targetBaseline, TransferItem result) {
/* 226 */       super(fromSide, source, sourceBaseline, targetItem, targetBaseline, result);
/* 227 */       Object previousValue = getResultValue();
/* 228 */       if (previousValue != null) {
/* 229 */         if (previousValue instanceof CollectionUpdate) {
/* 230 */           this.previousUpdate = (CollectionUpdate)previousValue;
/*     */         } else {
/* 232 */           FieldMapping.this.context.getLogger().error(String.format("Invalid previousValue for multi value field mapping, merging is disabled. previousValue = %s", new Object[] { previousValue }));
/*     */         } 
/*     */       }
/* 235 */       if (this.targetValue == null) {
/* 236 */         this.targetCollection = new LinkedHashSet();
/* 237 */       } else if (this.targetValue instanceof Collection) {
/* 238 */         this.targetCollection = (Collection<Object>)this.targetValue;
/*     */       } else {
/* 240 */         FieldMapping.this.context.getLogger().error(String.format("Invalid targetValue for multi value field mapping, merging is disabled. targetValue = %s", new Object[] { this.targetValue }));
/*     */       } 
/*     */     }
/*     */ 
/*     */     
/*     */     @NotNull
/*     */     public TranslationResult<?> executeMapping() {
/* 247 */       return mapMultiValued();
/*     */     }
/*     */     @NotNull
/*     */     private TranslationResult<?> mapMultiValued() {
/*     */       CollectionUpdate<Object> merged;
/* 252 */       Object previousValue = getResultValue();
/* 253 */       if (previousValue == null || this.targetCollection == null) {
/* 254 */         return addCollectionValue();
/*     */       }
/* 256 */       TranslationResult<?> translationResult = translate();
/*     */       
/* 258 */       Object resultValue = translationResult.getResultValue();
/* 259 */       if (!(resultValue instanceof CollectionUpdate)) {
/* 260 */         FieldMapping.this.context.getLogger().error(String.format("Invalid translation result %s for multi value attribute, value will be ignored.", new Object[] { resultValue }));
/* 261 */         return translationResult;
/*     */       } 
/*     */ 
/*     */       
/* 265 */       CollectionUpdate<Object> collectionUpdate = (CollectionUpdate<Object>)resultValue;
/* 266 */       CollectionUpdate<Object> previousCollectionUpdate = (CollectionUpdate)this.previousUpdate;
/*     */       
/* 268 */       if (FieldMapping.this.direction == Direction.BIDIRECTIONAL) {
/* 269 */         merged = mergeBidirectional(previousCollectionUpdate, collectionUpdate, loadRelativeUpdate());
/*     */       } else {
/* 271 */         merged = mergeUnidirectional(previousCollectionUpdate, collectionUpdate, 
/* 272 */             this.targetCollection);
/*     */       } 
/* 274 */       setResultValue(merged);
/* 275 */       return translationResult;
/*     */     }
/*     */ 
/*     */ 
/*     */     
/*     */     @NotNull
/*     */     private CollectionUpdate<Object> loadRelativeUpdate() {
/* 282 */       Collection<?> updatedCollection = this.previousUpdate.applyTo(this.targetCollection);
/* 283 */       TranslationResult<CollectionUpdate<Object>> fullTranslationResult = getTranslator().translateBidirectional(this.sourceBaselineValue, this.sourceValue, this.targetBaselineValue, updatedCollection);
/* 284 */       return (CollectionUpdate<Object>)fullTranslationResult.getResultValue();
/*     */     }
/*     */     
/*     */     @NotNull
/*     */     private TranslationResult<?> addCollectionValue() {
/* 289 */       TranslationResult<?> translationResult = translate();
/* 290 */       Object resultValue = translationResult.getResultValue();
/* 291 */       if (resultValue instanceof CollectionUpdate) {
/*     */         
/* 293 */         setResultValue(resultValue);
/*     */       } else {
/* 295 */         FieldMapping.this.context.getLogger().error(String.format("Invalid colection update %s will be ignored.", new Object[] { resultValue }));
/*     */       } 
/* 297 */       return translationResult;
/*     */     }
/*     */     
/*     */     @NotNull
/*     */     private CollectionUpdate<Object> mergeBidirectional(@NotNull CollectionUpdate<Object> previousCollectionUpdate, @NotNull CollectionUpdate<Object> collectionUpdate, @NotNull CollectionUpdate<Object> fullCollectionUpdate) {
/* 302 */       previousCollectionUpdate.getAdded().addAll(collectionUpdate.getAdded());
/* 303 */       previousCollectionUpdate.getRemoved().addAll(previousCollectionUpdate.getRemoved());
/*     */       
/* 305 */       previousCollectionUpdate.getRemoved().removeAll(fullCollectionUpdate.getAdded());
/*     */       
/* 307 */       previousCollectionUpdate.getAdded().removeAll(previousCollectionUpdate.getRemoved());
/* 308 */       previousCollectionUpdate.getRemoved().removeAll(previousCollectionUpdate.getAdded());
/* 309 */       return previousCollectionUpdate;
/*     */     }
/*     */ 
/*     */     
/*     */     @NotNull
/*     */     private CollectionUpdate<Object> mergeUnidirectional(@NotNull CollectionUpdate<Object> previousCollectionUpdate, @NotNull CollectionUpdate<Object> collectionUpdate, @NotNull Collection<Object> targetCollection) {
/* 315 */       Collection<Object> previousCollection = new LinkedHashSet(targetCollection);
/* 316 */       previousCollection.addAll(previousCollectionUpdate.getAdded());
/* 317 */       previousCollection.removeAll(previousCollectionUpdate.getRemoved());
/*     */       
/* 319 */       Collection<Object> currentCollection = new LinkedHashSet(targetCollection);
/* 320 */       currentCollection.addAll(collectionUpdate.getAdded());
/* 321 */       currentCollection.removeAll(collectionUpdate.getRemoved());
/*     */       
/* 323 */       Collection<Object> merged = previousCollection;
/* 324 */       merged.addAll(currentCollection);
/* 325 */       Collection<Object> added = new LinkedHashSet(merged);
/* 326 */       added.removeAll(targetCollection);
/* 327 */       Collection<Object> removed = new LinkedHashSet(targetCollection);
/* 328 */       removed.removeAll(merged);
/* 329 */       return new CollectionUpdate(added, removed);
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public FieldMapping(@NotNull FieldDefinition leftField, @NotNull FieldDefinition rightField, @NotNull Direction direction, @Nullable Direction primaryDirection, @Nullable Direction deleteDirection, @NotNull ISynchronizationContext context, @Nullable ITranslator<?> leftTranslator, @Nullable ITranslator<?> rightTranslator) {
/* 347 */     this.left = (FieldDefinition)Preconditions.checkNotNull(leftField);
/* 348 */     this.right = (FieldDefinition)Preconditions.checkNotNull(rightField);
/* 349 */     this.direction = (Direction)Preconditions.checkNotNull(direction);
/*     */     
/* 351 */     Preconditions.checkArgument((primaryDirection != Direction.BIDIRECTIONAL), "primaryDirection BIDIRECTIONAL is not allowed.");
/* 352 */     if (direction == Direction.BIDIRECTIONAL) {
/* 353 */       Preconditions.checkArgument(!(deleteDirection != null && deleteDirection != Direction.BIDIRECTIONAL), "delete direction - [" + deleteDirection + "] not allowed for BIDIRECTIONAL sync .");
/*     */     }
/*     */     
/* 356 */     this.primaryDirection = (primaryDirection == null) ? Direction.LEFT_TO_RIGHT : primaryDirection;
/* 357 */     this.deleteDirection = (deleteDirection == null) ? Direction.BIDIRECTIONAL : deleteDirection;
/*     */     
/* 359 */     this.leftTranslator = leftTranslator;
/* 360 */     this.rightTranslator = rightTranslator;
/*     */     
/* 362 */     this.context = context;
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public String getLeft() {
/* 368 */     return this.left.getKey();
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   public String getRight() {
/* 373 */     return this.right.getKey();
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   public Direction getDirection() {
/* 378 */     return this.direction;
/*     */   }
/*     */   
/*     */   @Nullable
/*     */   public Direction getPrimaryDirection() {
/* 383 */     return this.primaryDirection;
/*     */   }
/*     */   
/*     */   @Nullable
/*     */   public Direction getDeleteDirection() {
/* 388 */     return this.deleteDirection;
/*     */   }
/*     */   
/*     */   public void mapField(@NotNull TransferItem source, @Nullable Map<String, Object> sourceBaseline, @Nullable TransferItem targetItem, @Nullable Map<String, Object> targetBaseline, @NotNull TransferItem result, @NotNull Side fromSide, @NotNull Map<String, Object> newSourceBaseline) {
/* 392 */     FieldDefinition targetDefinition = (fromSide == Side.LEFT) ? this.right : this.left;
/*     */     
/* 394 */     FieldMappingContext fieldMappingContext = targetDefinition.isMultiValued() ? new MultiValueMappingContext(fromSide, source, sourceBaseline, targetItem, targetBaseline, result) : 
/* 395 */       new SingleValueMappingContext(fromSide, source, sourceBaseline, targetItem, targetBaseline, result);
/*     */     
/* 397 */     TranslationResult<?> translationResult = fieldMappingContext.executeMapping();
/* 398 */     newSourceBaseline.put(fieldMappingContext.fromKey, 
/* 399 */         loadMappedSourceBaseline(fieldMappingContext.sourceValue, fieldMappingContext.sourceBaselineValue, fieldMappingContext.targetValue, fromSide, translationResult.getResultValue()));
/*     */   }
/*     */   private Object loadMappedSourceBaseline(@Nullable Object sourceValue, @Nullable Object sourceBaselineValue, @Nullable Object targetValue, @NotNull Side fromSide, @Nullable Object resultValue) {
/*     */     Object mappedSourceBaselineValue;
/* 403 */     ITranslator<?> reverseTranslator = (fromSide == Side.LEFT) ? this.rightTranslator : this.leftTranslator;
/*     */ 
/*     */     
/* 406 */     if (resultValue == null) {
/* 407 */       mappedSourceBaselineValue = null;
/*     */     
/*     */     }
/* 410 */     else if (resultValue instanceof CollectionUpdate && reverseTranslator != null) {
/* 411 */       if ("plannedIn".equals(this.left.getKey())) {
/* 412 */         CollectionUpdate<?> newReverseUpdate = updateToSourceValueForBaseline(fromSide, (CollectionUpdate)resultValue);
/* 413 */         mappedSourceBaselineValue = newReverseUpdate;
/*     */       } else {
/*     */         
/* 416 */         CollectionUpdate<?> update = (CollectionUpdate)resultValue;
/* 417 */         resultValue = update.applyTo(targetValue);
/* 418 */         TranslationResult<?> reverse = null;
/* 419 */         reverse = reverseTranslator.translateUnidirectional(resultValue, sourceBaselineValue);
/*     */         
/* 421 */         if (reverse.getResultValue() instanceof CollectionUpdate) {
/* 422 */           CollectionUpdate<Object> reverseUpdate = (CollectionUpdate<Object>)reverse.getResultValue();
/*     */           
/* 424 */           if (sourceValue == null) {
/* 425 */             reverseUpdate.getAdded().clear();
/*     */           } else {
/* 427 */             reverseUpdate.getAdded().retainAll((Collection)sourceValue);
/* 428 */             reverseUpdate.getRemoved().removeAll((Collection)sourceValue);
/*     */           } 
/*     */           
/* 431 */           mappedSourceBaselineValue = reverseUpdate;
/*     */         } else {
/* 433 */           mappedSourceBaselineValue = sourceValue;
/*     */         } 
/*     */       } 
/*     */     } else {
/*     */       
/* 438 */       mappedSourceBaselineValue = sourceValue;
/*     */     } 
/*     */     
/* 441 */     return mappedSourceBaselineValue;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   private CollectionUpdate<?> updateToSourceValueForBaseline(@NotNull Side fromSide, @NotNull CollectionUpdate<?> reverseUpdate) {
/* 451 */     Side otherSide = (fromSide == Side.LEFT) ? Side.RIGHT : Side.LEFT;
/* 452 */     List<String> targetAddedIds = translateToTargetIds(otherSide, reverseUpdate.getAdded());
/* 453 */     List<String> targetRemovedIds = translateToTargetIds(otherSide, reverseUpdate.getRemoved());
/* 454 */     return new CollectionUpdate(targetAddedIds, targetRemovedIds);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   private List<String> translateToTargetIds(@NotNull Side fromSide, @NotNull Collection<String> existingIds) {
/* 463 */     List<String> targetIds = new ArrayList<>();
/* 464 */     for (String planID : existingIds) {
/* 465 */       String targetId = this.context.getConnectionMap().getTargetId(planID, fromSide, true);
/* 466 */       if (targetId != null) {
/* 467 */         targetIds.add(targetId); continue;
/*     */       } 
/* 469 */       targetIds.add(planID);
/*     */     } 
/*     */     
/* 472 */     return targetIds;
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/internal/mapping/FieldMapping.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */