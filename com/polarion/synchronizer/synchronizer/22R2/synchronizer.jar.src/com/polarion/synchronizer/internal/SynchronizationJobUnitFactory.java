/*    */ package com.polarion.synchronizer.internal;
/*    */ 
/*    */ import com.polarion.platform.guice.internal.GuicePlatform;
/*    */ import com.polarion.platform.i18n.Localization;
/*    */ import com.polarion.platform.jobs.GenericJobException;
/*    */ import com.polarion.platform.jobs.IJobDescriptor;
/*    */ import com.polarion.platform.jobs.IJobUnit;
/*    */ import com.polarion.platform.jobs.IJobUnitFactory;
/*    */ import com.polarion.platform.jobs.spi.BasicJobDescriptor;
/*    */ import com.polarion.platform.jobs.spi.JobParameterPrimitiveType;
/*    */ import com.polarion.platform.jobs.spi.SimpleJobParameter;
/*    */ import com.polarion.synchronizer.ISynchronizationService;
/*    */ import com.polarion.synchronizer.SynchronizationJobUnit;
/*    */ import javax.inject.Inject;
/*    */ 
/*    */ 
/*    */ public class SynchronizationJobUnitFactory
/*    */   implements IJobUnitFactory
/*    */ {
/* 20 */   private static final String JOB_LABEL = Localization.getString("polarion.jobs.synchronizer");
/*    */   
/*    */   @Inject
/*    */   private ISynchronizationService synchronizationService;
/*    */   
/*    */   public SynchronizationJobUnitFactory() {
/* 26 */     GuicePlatform.getGlobalInjector().injectMembers(this);
/*    */   }
/*    */ 
/*    */   
/*    */   public IJobUnit createJobUnit(String name) throws GenericJobException {
/* 31 */     return (IJobUnit)new SynchronizationJobUnit(name, this, this.synchronizationService);
/*    */   }
/*    */ 
/*    */   
/*    */   public IJobDescriptor getJobDescriptor(IJobUnit jobUnit) {
/* 36 */     JobParameterPrimitiveType stringType = new JobParameterPrimitiveType("String", String.class);
/* 37 */     BasicJobDescriptor desc = new BasicJobDescriptor(JOB_LABEL, jobUnit);
/* 38 */     desc.addParameter((IJobDescriptor.IJobParameter)new SimpleJobParameter(desc.getRootParameterGroup(), "syncPair", 
/* 39 */           "ID of the synchronization pair to execute.", (IJobDescriptor.IJobParameterType)stringType));
/* 40 */     return (IJobDescriptor)desc;
/*    */   }
/*    */ 
/*    */   
/*    */   public String getName() {
/* 45 */     return "synchronizer";
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/internal/SynchronizationJobUnitFactory.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */