/*    */ package com.polarion.synchronizer.proxy.doors.internal.commands;
/*    */ 
/*    */ import com.polarion.synchronizer.proxy.doors.internal.dxl.DxlScriptBuilder;
/*    */ import com.polarion.synchronizer.proxy.doors.internal.dxl.IDxlScript;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ 
/*    */ public class DoorsCommandMoveObject
/*    */   extends AbstractDoorsCommand<String>
/*    */ {
/*    */   @NotNull
/*    */   private final String absoluteNumber;
/*    */   @NotNull
/*    */   private final String referenceAbsoluteNumber;
/*    */   @NotNull
/*    */   private final String itemPosition;
/*    */   
/*    */   public DoorsCommandMoveObject(@NotNull String absoluteNumber, @NotNull String referenceAbsoluteNumber, int itemPosition) {
/* 18 */     this.absoluteNumber = absoluteNumber;
/* 19 */     this.referenceAbsoluteNumber = referenceAbsoluteNumber;
/* 20 */     this.itemPosition = String.valueOf(itemPosition);
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   protected IDxlScript getScript() {
/* 26 */     return (IDxlScript)DxlScriptBuilder.script("moveObject.dxl")
/* 27 */       .replaceParameter("absoluteNumber", this.absoluteNumber)
/* 28 */       .replaceParameter("referenceAbsoluteNumber", this.referenceAbsoluteNumber)
/* 29 */       .replaceParameter("itemPosition", this.itemPosition);
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   protected String processResult(@NotNull String result) throws Exception {
/* 35 */     return result;
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/internal/commands/DoorsCommandMoveObject.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */