package com.polarion.synchronizer.retryactions;

import org.jetbrains.annotations.Nullable;

@FunctionalInterface
public interface IRetryAction<T> {
  @Nullable
  T handle() throws Exception;
}


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/retryactions/IRetryAction.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */