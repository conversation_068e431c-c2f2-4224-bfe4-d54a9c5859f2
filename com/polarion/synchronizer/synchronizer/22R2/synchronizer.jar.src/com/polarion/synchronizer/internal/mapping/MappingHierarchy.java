/*     */ package com.polarion.synchronizer.internal.mapping;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class MappingHierarchy implements Serializable {
/*     */   private static final long serialVersionUID = 32024276296967673L;
/*     */   private final String parent;
/*     */   private final Position position;
/*     */   private final transient Position sourceBaselinePosition;
/*     */   private boolean forceUpdate;
/*     */   private boolean disableUpdate;
/*     */   
/*     */   public static class Position implements Serializable {
/*     */     private static final long serialVersionUID = 7197148124358185375L;
/*     */     
/*     */     public Position(String knownPredecessorId, int predecessorOffset) {
/*  17 */       this.predecessorOffset = predecessorOffset;
/*  18 */       this.knownPredecessorId = knownPredecessorId;
/*     */     }
/*     */     private final int predecessorOffset; private final String knownPredecessorId;
/*     */     public int getPredecessorOffset() {
/*  22 */       return this.predecessorOffset;
/*     */     }
/*     */     
/*     */     public String getKnownPredecessorId() {
/*  26 */       return this.knownPredecessorId;
/*     */     }
/*     */ 
/*     */     
/*     */     public int hashCode() {
/*  31 */       int prime = 31;
/*  32 */       int result = 1;
/*  33 */       result = 31 * result + ((this.knownPredecessorId == null) ? 0 : this.knownPredecessorId.hashCode());
/*  34 */       result = 31 * result + this.predecessorOffset;
/*  35 */       return result;
/*     */     }
/*     */ 
/*     */     
/*     */     public boolean equals(Object obj) {
/*  40 */       if (this == obj)
/*  41 */         return true; 
/*  42 */       if (obj == null)
/*  43 */         return false; 
/*  44 */       if (getClass() != obj.getClass())
/*  45 */         return false; 
/*  46 */       Position other = (Position)obj;
/*  47 */       if (this.knownPredecessorId == null) {
/*  48 */         if (other.knownPredecessorId != null)
/*  49 */           return false; 
/*  50 */       } else if (!this.knownPredecessorId.equals(other.knownPredecessorId)) {
/*  51 */         return false;
/*  52 */       }  if (this.predecessorOffset != other.predecessorOffset)
/*  53 */         return false; 
/*  54 */       return true;
/*     */     }
/*     */ 
/*     */     
/*     */     public String toString() {
/*  59 */       return "Position [predecessorOffset=" + this.predecessorOffset + ", knownPredecessorId=" + this.knownPredecessorId + "]";
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public MappingHierarchy(String parent, Position position, Position sourceBaselinePosition) {
/*  77 */     this.parent = parent;
/*  78 */     this.position = position;
/*  79 */     this.sourceBaselinePosition = sourceBaselinePosition;
/*  80 */     this.disableUpdate = false;
/*  81 */     this.forceUpdate = false;
/*     */   }
/*     */   
/*     */   public String getParent() {
/*  85 */     return this.parent;
/*     */   }
/*     */   
/*     */   public Position getPosition() {
/*  89 */     return this.position;
/*     */   }
/*     */   
/*     */   public Position getSourceBaselinePosition() {
/*  93 */     return this.sourceBaselinePosition;
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean isForceUpdate() {
/*  98 */     return this.forceUpdate;
/*     */   }
/*     */   
/*     */   public void setForceUpdate(boolean forceUpdate) {
/* 102 */     this.forceUpdate = forceUpdate;
/*     */   }
/*     */   
/*     */   public boolean isDisableUpdate() {
/* 106 */     return this.disableUpdate;
/*     */   }
/*     */   
/*     */   public void setDisableUpdate(boolean disableUpdate) {
/* 110 */     this.disableUpdate = disableUpdate;
/*     */   }
/*     */ 
/*     */   
/*     */   public int hashCode() {
/* 115 */     int prime = 31;
/* 116 */     int result = 1;
/* 117 */     result = 31 * result + ((this.parent == null) ? 0 : this.parent.hashCode());
/* 118 */     result = 31 * result + ((this.position == null) ? 0 : this.position.hashCode());
/* 119 */     return result;
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean equals(Object obj) {
/* 124 */     if (this == obj)
/* 125 */       return true; 
/* 126 */     if (obj == null)
/* 127 */       return false; 
/* 128 */     if (getClass() != obj.getClass())
/* 129 */       return false; 
/* 130 */     MappingHierarchy other = (MappingHierarchy)obj;
/* 131 */     if (this.parent == null) {
/* 132 */       if (other.parent != null)
/* 133 */         return false; 
/* 134 */     } else if (!this.parent.equals(other.parent)) {
/* 135 */       return false;
/* 136 */     }  if (this.position == null) {
/* 137 */       if (other.position != null)
/* 138 */         return false; 
/* 139 */     } else if (!this.position.equals(other.position)) {
/* 140 */       return false;
/* 141 */     }  return true;
/*     */   }
/*     */ 
/*     */   
/*     */   public String toString() {
/* 146 */     return "MappingHierarchy [parent=" + this.parent + ", position=" + this.position + "]";
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/internal/mapping/MappingHierarchy.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */