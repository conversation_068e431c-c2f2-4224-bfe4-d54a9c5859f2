/*    */ package com.polarion.synchronizer.model;
/*    */ 
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ import org.jetbrains.annotations.Nullable;
/*    */ 
/*    */ public class Option
/*    */ {
/*    */   @NotNull
/*    */   private final String id;
/*    */   @Nullable
/*    */   private final String name;
/*    */   private final boolean isVirtualType;
/*    */   
/*    */   public Option(@NotNull String id, @Nullable String name) {
/* 15 */     this(id, name, false);
/*    */   }
/*    */   
/*    */   public Option(@NotNull String id, @Nullable String name, boolean isVirtualType) {
/* 19 */     this.id = id;
/* 20 */     this.name = name;
/* 21 */     this.isVirtualType = isVirtualType;
/*    */   }
/*    */   
/*    */   @NotNull
/*    */   public String getId() {
/* 26 */     return this.id;
/*    */   }
/*    */   
/*    */   @Nullable
/*    */   public String getName() {
/* 31 */     return this.name;
/*    */   }
/*    */   
/*    */   public boolean isVirtualType() {
/* 35 */     return this.isVirtualType;
/*    */   }
/*    */ 
/*    */   
/*    */   public int hashCode() {
/* 40 */     int prime = 31;
/* 41 */     int result = 1;
/* 42 */     result = 31 * result + this.id.hashCode();
/* 43 */     result = 31 * result + ((this.name != null) ? this.name.hashCode() : 0);
/* 44 */     return result;
/*    */   }
/*    */ 
/*    */   
/*    */   public boolean equals(Object obj) {
/* 49 */     if (this == obj) {
/* 50 */       return true;
/*    */     }
/* 52 */     if (obj == null) {
/* 53 */       return false;
/*    */     }
/* 55 */     if (getClass() != obj.getClass()) {
/* 56 */       return false;
/*    */     }
/* 58 */     Option other = (Option)obj;
/* 59 */     if (!this.id.equals(other.id)) {
/* 60 */       return false;
/*    */     }
/* 62 */     if (this.name == null && other.name != null)
/* 63 */       return false; 
/* 64 */     if (other.name == null && this.name != null) {
/* 65 */       return false;
/*    */     }
/* 67 */     if (other.name != null && this.name != null && !this.name.equals(other.name)) {
/* 68 */       return false;
/*    */     }
/*    */     
/* 71 */     return (this.isVirtualType == other.isVirtualType);
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public String toString() {
/* 77 */     return "Option [id=" + this.id + ", name=" + this.name + "]";
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/model/Option.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */