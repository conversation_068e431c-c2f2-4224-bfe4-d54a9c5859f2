/*    */ package com.polarion.synchronizer.proxy.configuration;
/*    */ 
/*    */ import javax.xml.bind.annotation.XmlRootElement;
/*    */ import javax.xml.bind.annotation.XmlTransient;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @XmlRootElement
/*    */ public class SpecificationTemplate
/*    */ {
/*    */   protected MappingConfiguration mappingConfiguration;
/*    */   @XmlTransient
/*    */   private String templateName;
/*    */   private boolean isForImport;
/*    */   private boolean isForRIF;
/*    */   
/*    */   @NotNull
/*    */   public MappingConfiguration getMappingConfiguration() {
/* 43 */     return this.mappingConfiguration;
/*    */   }
/*    */   
/*    */   public void setMappingConfiguration(@NotNull MappingConfiguration mappingConfiguration) {
/* 47 */     this.mappingConfiguration = mappingConfiguration;
/*    */   }
/*    */   
/*    */   @XmlTransient
/*    */   @NotNull
/*    */   public String getTemplateName() {
/* 53 */     return this.templateName;
/*    */   }
/*    */   
/*    */   public void setTemplateName(@NotNull String templateName) {
/* 57 */     this.templateName = templateName;
/*    */   }
/*    */   
/*    */   public boolean isForImport() {
/* 61 */     return this.isForImport;
/*    */   }
/*    */   
/*    */   public void setForImport(boolean isForImport) {
/* 65 */     this.isForImport = isForImport;
/*    */   }
/*    */   
/*    */   public boolean isForRIF() {
/* 69 */     return this.isForRIF;
/*    */   }
/*    */   
/*    */   public void setForRIF(boolean isForRIF) {
/* 73 */     this.isForRIF = isForRIF;
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/configuration/SpecificationTemplate.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */