/*    */ package com.polarion.synchronizer.internal;
/*    */ 
/*    */ import com.polarion.synchronizer.ICreateOperation;
/*    */ import com.polarion.synchronizer.model.CreateResult;
/*    */ import com.polarion.synchronizer.model.TransferItem;
/*    */ import com.polarion.synchronizer.model.UpdateResult;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class CreateOperation
/*    */   implements ICreateOperation
/*    */ {
/*    */   @NotNull
/*    */   private final TransferItem sourceItem;
/*    */   @NotNull
/*    */   private final TransferItem content;
/*    */   @NotNull
/*    */   private final CreateResult result;
/*    */   
/*    */   public CreateOperation(@NotNull TransferItem sourceItem, @NotNull TransferItem content, @NotNull CreateResult result) {
/* 40 */     this.sourceItem = sourceItem;
/* 41 */     this.content = content;
/* 42 */     this.result = result;
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   public TransferItem getSourceItem() {
/* 48 */     return this.sourceItem;
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   public TransferItem getContent() {
/* 54 */     return this.content;
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   public CreateResult getResult() {
/* 60 */     return this.result;
/*    */   }
/*    */ 
/*    */   
/*    */   public int hashCode() {
/* 65 */     int prime = 31;
/* 66 */     int result = 1;
/* 67 */     result = 31 * result + this.content.hashCode();
/* 68 */     result = 31 * result + this.result.hashCode();
/* 69 */     result = 31 * result + this.sourceItem.hashCode();
/* 70 */     return result;
/*    */   }
/*    */ 
/*    */   
/*    */   public boolean equals(Object obj) {
/* 75 */     if (this == obj) {
/* 76 */       return true;
/*    */     }
/* 78 */     if (obj == null) {
/* 79 */       return false;
/*    */     }
/* 81 */     if (getClass() != obj.getClass()) {
/* 82 */       return false;
/*    */     }
/* 84 */     CreateOperation other = (CreateOperation)obj;
/* 85 */     if (!this.content.equals(other.content)) {
/* 86 */       return false;
/*    */     }
/* 88 */     if (!this.result.equals(other.result)) {
/* 89 */       return false;
/*    */     }
/* 91 */     if (!this.sourceItem.equals(other.sourceItem)) {
/* 92 */       return false;
/*    */     }
/* 94 */     return true;
/*    */   }
/*    */ 
/*    */   
/*    */   public String toString() {
/* 99 */     return "Create [sourceItem=" + this.sourceItem + ", content=" + this.content + ", result=" + this.result + "]";
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/internal/CreateOperation.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */