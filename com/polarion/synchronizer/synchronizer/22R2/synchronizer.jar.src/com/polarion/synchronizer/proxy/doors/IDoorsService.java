package com.polarion.synchronizer.proxy.doors;

import com.google.inject.ImplementedBy;
import com.polarion.synchronizer.proxy.doors.internal.DoorsService;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@ImplementedBy(DoorsService.class)
public interface IDoorsService {
  @NotNull
  IDoorsConnection createConnection();
  
  @NotNull
  IDoorsConnection getOrCreateConnection(@NotNull String paramString);
  
  @Nullable
  IDoorsConnection getConnection(@NotNull String paramString);
}


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/IDoorsService.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */