/*    */ package com.polarion.synchronizer.spi;
/*    */ 
/*    */ import javax.xml.bind.annotation.adapters.XmlAdapter;
/*    */ 
/*    */ public class AnyTypeAdapter
/*    */   extends XmlAdapter<Object, Object>
/*    */ {
/*    */   public Object unmarshal(Object v) throws Exception {
/*  9 */     return v;
/*    */   }
/*    */ 
/*    */   
/*    */   public Object marshal(Object v) throws Exception {
/* 14 */     return v;
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/spi/AnyTypeAdapter.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */