/*    */ package com.polarion.synchronizer.model;
/*    */ 
/*    */ public class CreateResult
/*    */   extends UpdateResult {
/*    */   private String createdId;
/*    */   
/*    */   public CreateResult(String createdId, String error) {
/*  8 */     super(error);
/*  9 */     this.createdId = createdId;
/*    */   }
/*    */   
/*    */   public CreateResult(String createdId, String errorMessage, Exception exception) {
/* 13 */     super(errorMessage, exception);
/* 14 */     this.createdId = createdId;
/*    */   }
/*    */   
/*    */   public String getCreatedId() {
/* 18 */     return this.createdId;
/*    */   }
/*    */   
/*    */   public void setCreatedId(String id) {
/* 22 */     this.createdId = id;
/*    */   }
/*    */ 
/*    */   
/*    */   public int hashCode() {
/* 27 */     int prime = 31;
/* 28 */     int result = 1;
/* 29 */     result = 31 * result + ((this.createdId == null) ? 0 : this.createdId.hashCode());
/* 30 */     return result;
/*    */   }
/*    */ 
/*    */   
/*    */   public boolean equals(Object obj) {
/* 35 */     if (this == obj) {
/* 36 */       return true;
/*    */     }
/* 38 */     if (obj == null) {
/* 39 */       return false;
/*    */     }
/* 41 */     if (getClass() != obj.getClass()) {
/* 42 */       return false;
/*    */     }
/* 44 */     CreateResult other = (CreateResult)obj;
/* 45 */     if (this.createdId == null) {
/* 46 */       if (other.createdId != null) {
/* 47 */         return false;
/*    */       }
/* 49 */     } else if (!this.createdId.equals(other.createdId)) {
/* 50 */       return false;
/*    */     } 
/* 52 */     return true;
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/model/CreateResult.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */