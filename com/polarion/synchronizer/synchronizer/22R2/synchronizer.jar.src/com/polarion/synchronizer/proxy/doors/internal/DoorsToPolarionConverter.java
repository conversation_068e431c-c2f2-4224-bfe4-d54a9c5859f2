/*     */ package com.polarion.synchronizer.proxy.doors.internal;
/*     */ 
/*     */ import com.polarion.synchronizer.ILogger;
/*     */ import com.polarion.synchronizer.SynchronizationException;
/*     */ import com.polarion.synchronizer.helper.RichTextHelper;
/*     */ import com.polarion.synchronizer.model.Attachment;
/*     */ import com.polarion.synchronizer.model.FieldDefinition;
/*     */ import com.polarion.synchronizer.model.Option;
/*     */ import com.polarion.synchronizer.model.OptionFieldDefinition;
/*     */ import com.polarion.synchronizer.ole.EmbeddedObject;
/*     */ import com.polarion.synchronizer.ole.OleExtractor;
/*     */ import com.polarion.synchronizer.ole.OleObject;
/*     */ import com.polarion.synchronizer.proxy.doors.html2rtftranslator.Ole1OutputStream;
/*     */ import com.rtfparserkit.parser.IRtfListener;
/*     */ import com.rtfparserkit.parser.IRtfSource;
/*     */ import com.rtfparserkit.parser.RtfStringSource;
/*     */ import com.rtfparserkit.parser.standard.StandardRtfParser;
/*     */ import java.io.ByteArrayInputStream;
/*     */ import java.io.IOException;
/*     */ import java.io.InputStream;
/*     */ import java.text.ParseException;
/*     */ import java.text.SimpleDateFormat;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Arrays;
/*     */ import java.util.Collection;
/*     */ import java.util.Collections;
/*     */ import java.util.Date;
/*     */ import java.util.List;
/*     */ import java.util.Locale;
/*     */ import java.util.function.Supplier;
/*     */ import java.util.stream.Collectors;
/*     */ import org.apache.commons.io.IOUtils;
/*     */ import org.apache.poi.poifs.filesystem.Ole10Native;
/*     */ import org.apache.poi.poifs.filesystem.Ole10NativeException;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ import org.jetbrains.annotations.Nullable;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class DoorsToPolarionConverter
/*     */ {
/*     */   @NotNull
/*  44 */   private List<SimpleDateFormat> simpleDateFormats = Arrays.asList(new SimpleDateFormat[] { new SimpleDateFormat("d MMMM yyyy", Locale.ENGLISH), 
/*  45 */         new SimpleDateFormat("MM/dd/yy HH:mm:ss", Locale.ENGLISH) });
/*     */   
/*     */   @NotNull
/*  48 */   private RichTextHelper richTextHelper = new RichTextHelper();
/*     */   
/*     */   @NotNull
/*     */   private ILogger logger;
/*     */   
/*     */   public DoorsToPolarionConverter(@NotNull ILogger logger) {
/*  54 */     this.logger = logger;
/*     */   }
/*     */   @Nullable
/*     */   public Object processSingleValuedAttributes(@Nullable String value, @NotNull FieldDefinition fieldDefinition, boolean isDxl, @Nullable Collection<Attachment> embeddedObjects) {
/*     */     Object convertedValue;
/*  59 */     if (value == null) {
/*  60 */       return null;
/*     */     }
/*     */ 
/*     */     
/*  64 */     if (fieldDefinition.getType().equals("date")) {
/*  65 */       convertedValue = handleDateAttribute(value);
/*  66 */     } else if (fieldDefinition.getType().equals("float")) {
/*  67 */       convertedValue = Float.valueOf(Float.parseFloat(value));
/*  68 */     } else if (fieldDefinition.getType().equals("integer")) {
/*  69 */       convertedValue = Integer.valueOf(Integer.parseInt(value));
/*  70 */     } else if (fieldDefinition.getType().equals("option") && !isDxl) {
/*  71 */       convertedValue = loadEnumValue(value, ((OptionFieldDefinition)fieldDefinition).getAvailableOptions());
/*  72 */     } else if (fieldDefinition.getType().equals("rich-text")) {
/*  73 */       convertedValue = value.startsWith("{\\rtf") ? parseRTF(fieldDefinition.getKey(), value, embeddedObjects) : value;
/*  74 */     } else if (fieldDefinition.getType().equals("string")) {
/*  75 */       convertedValue = value;
/*  76 */     } else if (fieldDefinition.getType().equals("boolean")) {
/*  77 */       convertedValue = Boolean.valueOf(!(!"1".equals(value) && !"True".equalsIgnoreCase(value)));
/*     */     } else {
/*  79 */       convertedValue = value;
/*     */     } 
/*  81 */     return convertedValue;
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   public List<String> loadMultiEnum(@NotNull Collection<String> attributeValues, @NotNull Collection<Option> availableOptions) {
/*  86 */     List<String> selectedOptions = new ArrayList<>();
/*  87 */     for (String attributeValue : attributeValues) {
/*  88 */       selectedOptions.add(loadEnumValue(attributeValue, availableOptions));
/*     */     }
/*  90 */     return selectedOptions;
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private String loadEnumValue(@NotNull String attributeValue, @NotNull Collection<Option> availableOptions) {
/*  95 */     int index = Integer.parseInt(attributeValue);
/*  96 */     if (index >= availableOptions.size()) {
/*  97 */       throw new SynchronizationException(String.format("Invalid enum selection %s with available values %s.", new Object[] { Integer.valueOf(index), availableOptions }));
/*     */     }
/*  99 */     return ((Option)(new ArrayList<>(availableOptions)).get(index)).getId();
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private Date handleDateAttribute(@NotNull String attributeValue) {
/* 104 */     Date returnValue = null;
/* 105 */     ParseException parseException = null;
/*     */     
/* 107 */     for (SimpleDateFormat simpleDateFormat : this.simpleDateFormats) {
/*     */       try {
/* 109 */         returnValue = simpleDateFormat.parse(attributeValue);
/* 110 */         parseException = null;
/* 111 */         return returnValue;
/* 112 */       } catch (ParseException e) {
/* 113 */         parseException = e;
/*     */       } 
/*     */     } 
/* 116 */     if (parseException != null) {
/* 117 */       throw new SynchronizationException("Could not parse Date value from Doors Attribute", parseException);
/*     */     }
/*     */ 
/*     */     
/* 121 */     throw new RuntimeException("Unexpected exception parsing date value " + attributeValue + " from Doors");
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   public String parseRTF(@NotNull String key, @NotNull String rtfString, @Nullable Collection<Attachment> embeddedObjects) {
/* 126 */     StandardRtfParser standardRtfParser = new StandardRtfParser();
/* 127 */     if (embeddedObjects != null) {
/* 128 */       List<EmbeddedObject> oleObjects = OleExtractor.parse(rtfString.getBytes()).getOleObjects();
/* 129 */       for (int index = 0; index < oleObjects.size(); index++) {
/* 130 */         EmbeddedObject oleObject = oleObjects.get(index);
/* 131 */         if (Ole1OutputStream.isOle1Package(oleObject.getData())) {
/* 132 */           Attachment extractedAttachment = extractOle1PackedAttachment(((OleObject)oleObject).getOle1BinaryData());
/* 133 */           if (extractedAttachment != null) {
/* 134 */             embeddedObjects.add(extractedAttachment);
/*     */           } else {
/* 136 */             this.logger.warn("Error parsing Package from Doors. Including attachment as it is in rtf");
/* 137 */             embeddedObjects.add(new Attachment(String.valueOf(key) + " " + (index + 1) + ".rtf", oleObject));
/*     */           } 
/*     */         } else {
/*     */           
/* 141 */           embeddedObjects.add(new Attachment(String.valueOf(key) + " " + (index + 1) + ".rtf", oleObject));
/*     */         } 
/*     */       } 
/*     */     } 
/* 145 */     List<String> attachmentFilenames = (embeddedObjects != null) ? (List<String>)embeddedObjects.stream().map(Attachment::getFileName).collect(Collectors.toCollection(ArrayList::new)) : Collections.EMPTY_LIST;
/* 146 */     RtfToHtmlConverter rtfListenerAdaptor = new RtfToHtmlConverter(this.richTextHelper, attachmentFilenames);
/*     */     try {
/* 148 */       standardRtfParser.parse((IRtfSource)new RtfStringSource(rtfString), (IRtfListener)rtfListenerAdaptor);
/* 149 */     } catch (IOException e) {
/* 150 */       throw new SynchronizationException("Could not parse RTF string", e);
/*     */     } 
/* 152 */     return rtfListenerAdaptor.getHtmlStringWithFormatting();
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   public String convertTypeToPolarionType(@NotNull String type, @NotNull String name) {
/* 157 */     if (type.equals("String")) {
/* 158 */       type = (name.equals("Created By") || name.equals("Last Modified By") || name.equals("PictureName")) ? "string" : "rich-text";
/* 159 */     } else if (type.equals("Integer")) {
/* 160 */       type = "integer";
/* 161 */     } else if (type.equals("Boolean")) {
/* 162 */       type = "boolean";
/* 163 */     } else if (type.equals("Text")) {
/* 164 */       type = "rich-text";
/* 165 */     } else if (type.equals("Date")) {
/* 166 */       type = "date";
/* 167 */     } else if (type.equals("Enumeration")) {
/* 168 */       type = "option";
/* 169 */     } else if (type.equals("Real")) {
/* 170 */       type = "float";
/* 171 */     } else if (type.equals("Username")) {
/* 172 */       type = "string";
/*     */     } else {
/* 174 */       type = "unknown";
/*     */     } 
/* 176 */     return type;
/*     */   }
/*     */ 
/*     */   
/*     */   @Nullable
/*     */   private Attachment extractOle1PackedAttachment(@NotNull InputStream input) {
/*     */     try {
/* 183 */       Ole10Native ole10Native = new Ole10Native(IOUtils.toByteArray(input), 0);
/* 184 */       InputStream ole10Stream = new ByteArrayInputStream(ole10Native.getDataBuffer());
/* 185 */       return new Attachment(ole10Native.getLabel(), ole10Stream);
/* 186 */     } catch (Ole10NativeException e) {
/* 187 */       return null;
/* 188 */     } catch (IOException e) {
/* 189 */       throw new RuntimeException("Failed to read oleObject" + e);
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/internal/DoorsToPolarionConverter.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */