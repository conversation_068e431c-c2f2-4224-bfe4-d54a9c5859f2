string replace (string sSource, string sSearch, string sReplace) 
{
    int iLen = length sSource
    if (iLen == 0) return ""
    
    int iLenSearch = length(sSearch)
    
    if (iLenSearch == 0) 
    { 
        print "search string must not be empty"
        return "" 
    }
    
    // read the first char for latter comparison -> speed optimization
    char firstChar = sSearch[0]
    
    Buffer s = create() 
    int pos = 0, d1,d2;    
    int i
    
    while (pos < iLen) { 
        char ch = sSource[pos]; 
        bool found = true
        
        if (ch != firstChar) {pos ++; s+= ch; continue}
        for (i = 1; i < iLenSearch; i++) 
           if (sSource[pos+i] != sSearch[i]) { found = false; break }
        if (!found) {pos++; s+= ch; continue}
        s += sReplace
        pos += iLenSearch
    }
    
    string replaceResult = stringOf s
    delete s
    return replaceResult
}

string cleanName(string name){

	string cleanedName	

	cleanedName =  replace(name, "&" ,"&amp;") 

	cleanedName =  replace(cleanedName , "\"" ,"&quot;")

	cleanedName =  replace(cleanedName , "'" ,"&apos;")

	cleanedName =  replace(cleanedName ,"<" ,"&lt;") 

	cleanedName =  replace(cleanedName ,">" ,"&gt;") 

	return cleanedName

}
