string attrName
AttrType t

Skip dxlAttrNames = create
int n = 0

for attrName in m do {
    AttrDef ad = find(m, attrName)

    if (ad.dxl){
        t = ad.type

        AttrType at = find(m Module, t.name)

        string systemTypeName = stringOf at.type

        if(t.name == "Boolean"){
            systemTypeName = "Boolean"
        }
        put(dxlAttrNames , n, attrName)
        n++
    }
}

Object o = null

string objectXML
string currAttrName
string value
string cleanAttrName
string cleanValue
int i

sendResult "<additional-information-publications>"
for o in entire m do {
    if (isDeleted(o)){
        objectXML = "<deleted-object-publication objectId=\"" o."Absolute Number" "\" />"
        sendResult objectXML
    }
    else
    {
        objectXML = "<dxl-value-publication objectId=\"" o."Absolute Number" "\">"
        objectXML = objectXML "<attributes>"
        if (n > 0)
        {
            for i in 0:n-1 do
            {
                find(dxlAttrNames, i, currAttrName)
                value = o.currAttrName
                cleanAttrName = cleanName(currAttrName)
                cleanValue = cleanName(value)
                objectXML = objectXML "<attribute name=\"" cleanAttrName "\">"
                objectXML = objectXML "<attributeValue>" cleanValue "</attributeValue>"
                objectXML = objectXML "</attribute>"
            }
        }
        objectXML = objectXML "</attributes>"
        objectXML = objectXML "</dxl-value-publication>"
        sendResult objectXML
    }
}
delete dxlAttrNames

result = "</additional-information-publications>"