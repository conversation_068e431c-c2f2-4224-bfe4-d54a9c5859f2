/*     */ package com.polarion.synchronizer.mapping;
/*     */ 
/*     */ import com.pietjonas.wmfwriter2d.WMF;
/*     */ import com.pietjonas.wmfwriter2d.WMFGraphics;
/*     */ import java.awt.image.BufferedImage;
/*     */ import java.io.ByteArrayInputStream;
/*     */ import java.io.ByteArrayOutputStream;
/*     */ import java.io.IOException;
/*     */ import java.io.InputStream;
/*     */ import java.io.OutputStream;
/*     */ import java.io.OutputStreamWriter;
/*     */ import java.util.Arrays;
/*     */ import java.util.Collection;
/*     */ import java.util.Collections;
/*     */ import java.util.HashSet;
/*     */ import javax.imageio.ImageIO;
/*     */ import org.apache.commons.io.FilenameUtils;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ImagesToRtf
/*     */ {
/*  33 */   private static final Collection<String> IMAGE_EXTENSIONS = Collections.unmodifiableCollection(
/*  34 */       new HashSet<>(Arrays.asList(new String[] { "bmp", "gif", "jpg", "jpeg", "png" })));
/*     */   
/*     */   public static boolean isImage(@NotNull String fileName) {
/*  37 */     return IMAGE_EXTENSIONS.contains(FilenameUtils.getExtension(fileName).toLowerCase());
/*     */   }
/*     */ 
/*     */   
/*     */   public void convertToRtf(@NotNull InputStream input, @NotNull OutputStream result) throws IOException {
/*  42 */     BufferedImage image = ImageIO.read(input);
/*     */     
/*  44 */     int width = image.getWidth();
/*  45 */     int height = image.getHeight();
/*     */     
/*  47 */     writeRTF(result, width, height, loadWMF(image, width, height));
/*     */   }
/*     */ 
/*     */   
/*     */   private void writeRTF(@NotNull OutputStream result, int width, int height, @NotNull InputStream wmfContent) throws IOException {
/*  52 */     OutputStreamWriter resultWriter = new OutputStreamWriter(result);
/*  53 */     resultWriter.write("{\\rtf1 {\\pict");
/*  54 */     resultWriter.append("\\wmetafile8")
/*  55 */       .append("\\picw" + (width * 20))
/*  56 */       .append("\\pich" + (height * 20))
/*  57 */       .append("\n");
/*     */     
/*  59 */     writeImageContent(wmfContent, resultWriter);
/*     */     
/*  61 */     resultWriter.append("}\n}");
/*  62 */     resultWriter.flush();
/*  63 */     resultWriter.close();
/*     */   }
/*     */   
/*     */   private void writeImageContent(@NotNull InputStream wmfContent, @NotNull OutputStreamWriter resultWriter) {
/*  67 */     int count = 0;
/*     */ 
/*     */     
/*     */     while (true) {
/*     */       try {
/*  72 */         int i = wmfContent.read();
/*     */         
/*  74 */         if (i == -1) {
/*     */           break;
/*     */         }
/*     */         
/*  78 */         String hexStr = Integer.toHexString(i);
/*     */         
/*  80 */         if (hexStr.length() == 1) {
/*  81 */           hexStr = "0" + hexStr;
/*     */         }
/*     */         
/*  84 */         count += 2;
/*     */         
/*  86 */         resultWriter.append(hexStr);
/*     */         
/*  88 */         if (count == 64) {
/*  89 */           count = 0;
/*  90 */           resultWriter.append("\n");
/*     */         }
/*     */       
/*  93 */       } catch (IOException e) {
/*  94 */         e.printStackTrace();
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   private InputStream loadWMF(@NotNull BufferedImage image, int width, int height) throws IOException {
/* 101 */     WMF wmf = new WMF();
/* 102 */     WMFGraphics wMFGraphics = new WMFGraphics(wmf, width, height);
/*     */     
/* 104 */     wMFGraphics.drawImage(image, 0, 0, null);
/*     */     
/* 106 */     ByteArrayOutputStream buffer = new ByteArrayOutputStream();
/* 107 */     wmf.writeWMF(buffer);
/*     */     
/* 109 */     InputStream imageContent = new ByteArrayInputStream(buffer.toByteArray());
/* 110 */     return imageContent;
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/mapping/ImagesToRtf.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */