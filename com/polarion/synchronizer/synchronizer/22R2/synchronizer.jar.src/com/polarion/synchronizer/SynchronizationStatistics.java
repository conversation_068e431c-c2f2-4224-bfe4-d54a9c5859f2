package com.polarion.synchronizer;

public interface SynchronizationStatistics {
  boolean hasErrors();
  
  boolean hasWarnings();
  
  void setHasWarnings();
  
  int getFailedToUpdate();
  
  int getUpdatedWithError();
  
  int getUpdated();
  
  int getFailedToCreate();
  
  int getCreatedWithError();
  
  int getCreated();
}


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/SynchronizationStatistics.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */