/*     */ package com.polarion.synchronizer.proxy.doors.xmlmodel;
/*     */ 
/*     */ import java.util.List;
/*     */ import javax.xml.bind.annotation.XmlElement;
/*     */ import javax.xml.bind.annotation.XmlElementWrapper;
/*     */ import javax.xml.bind.annotation.XmlRootElement;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ import org.jetbrains.annotations.Nullable;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @XmlRootElement(name = "Object")
/*     */ public class DoorsObject
/*     */ {
/*     */   private List<DoorsAttribute> attributes;
/*     */   @Nullable
/*     */   List<DoorsObject> children;
/*     */   
/*     */   @NotNull
/*     */   public List<DoorsAttribute> getAttributes() {
/*  44 */     return this.attributes;
/*     */   }
/*     */   
/*     */   @XmlElementWrapper(name = "attributes")
/*     */   @XmlElement(name = "attribute")
/*     */   public void setAttributes(@NotNull List<DoorsAttribute> attributes) {
/*  50 */     this.attributes = attributes;
/*     */   }
/*     */   
/*     */   @Nullable
/*     */   public List<DoorsObject> getChildren() {
/*  55 */     return this.children;
/*     */   }
/*     */   
/*     */   @XmlElementWrapper(name = "children")
/*     */   @XmlElement(name = "Object")
/*     */   public void setChildren(@Nullable List<DoorsObject> children) {
/*  61 */     this.children = children;
/*     */   }
/*     */ 
/*     */   
/*     */   public int hashCode() {
/*  66 */     int prime = 31;
/*  67 */     int result = 1;
/*  68 */     result = 31 * result + ((this.attributes == null) ? 0 : this.attributes.hashCode());
/*  69 */     List<DoorsObject> children2 = this.children;
/*  70 */     if (children2 != null) {
/*  71 */       result = 31 * result + ((this.children == null) ? 0 : children2.hashCode());
/*     */     }
/*     */ 
/*     */     
/*  75 */     return result;
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean equals(Object obj) {
/*  80 */     if (this == obj) {
/*  81 */       return true;
/*     */     }
/*  83 */     if (obj == null) {
/*  84 */       return false;
/*     */     }
/*  86 */     if (getClass() != obj.getClass()) {
/*  87 */       return false;
/*     */     }
/*  89 */     DoorsObject other = (DoorsObject)obj;
/*  90 */     if (this.attributes == null) {
/*  91 */       if (other.attributes != null) {
/*  92 */         return false;
/*     */       }
/*  94 */     } else if (!this.attributes.equals(other.attributes)) {
/*  95 */       return false;
/*     */     } 
/*  97 */     if (this.children == null) {
/*  98 */       if (other.children != null) {
/*  99 */         return false;
/*     */       }
/*     */     } else {
/* 102 */       List<DoorsObject> children2 = this.children;
/* 103 */       if (children2 != null && 
/* 104 */         !children2.equals(other.children)) {
/* 105 */         return false;
/*     */       }
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 111 */     return true;
/*     */   }
/*     */ 
/*     */   
/*     */   public String toString() {
/* 116 */     return "DoorsObject [attributes=" + this.attributes + ", children=" + this.children + "]";
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/xmlmodel/DoorsObject.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */