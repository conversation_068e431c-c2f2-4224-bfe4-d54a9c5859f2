/*    */ package com.polarion.synchronizer.spi.translators;
/*    */ 
/*    */ import com.polarion.core.util.ObjectUtils;
/*    */ import com.polarion.synchronizer.ILogger;
/*    */ import com.polarion.synchronizer.ISynchronizationContext;
/*    */ import com.polarion.synchronizer.helper.RichTextHelper;
/*    */ import com.polarion.synchronizer.mapping.TranslationResult;
/*    */ import javax.inject.Inject;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ import org.jetbrains.annotations.Nullable;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class RichTextToRichTextTranslator
/*    */   extends TypesafeTranslator<String, String, String>
/*    */ {
/*    */   @NotNull
/*    */   private final ILogger log;
/*    */   @NotNull
/* 41 */   private RichTextHelper richTextHelper = new RichTextHelper();
/*    */   
/*    */   @Inject
/*    */   public RichTextToRichTextTranslator(@NotNull ISynchronizationContext context) {
/* 45 */     super(String.class, String.class);
/* 46 */     this.log = context.getLogger();
/*    */   }
/*    */ 
/*    */   
/*    */   public TranslationResult<String> translateBidirectionalTypesafe(@Nullable String sourceBaseline, @Nullable String sourceValue, @Nullable String targetBaseline, @Nullable String targetValue) {
/* 51 */     return createBidirectionalResult(sourceBaseline, sourceValue, translateValue(sourceValue), targetBaseline, targetValue);
/*    */   }
/*    */ 
/*    */   
/*    */   public TranslationResult<String> translateUnidirectionalTypesafe(@Nullable String sourceValue, @Nullable String targetValue) {
/* 56 */     String mappedValue = translateValue(sourceValue);
/* 57 */     if (targetValue != null && !ObjectUtils.equalsWithNull(mappedValue, targetValue)) {
/* 58 */       this.log.debug("Change in RichTextToRichTextTranslator.translateUnidirectionalTypesafe(String, String) detected:");
/* 59 */       this.log.debug("mappedValue: " + mappedValue);
/* 60 */       this.log.debug("targetValue: " + targetValue);
/*    */     } 
/* 62 */     return createUnidirectionalResult(mappedValue, targetValue);
/*    */   }
/*    */   
/*    */   @Nullable
/*    */   private String translateValue(@Nullable String sourceValue) {
/* 67 */     return (sourceValue == null) ? null : this.richTextHelper.sortAttributes(sourceValue);
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/spi/translators/RichTextToRichTextTranslator.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */