package com.polarion.synchronizer;

import com.polarion.alm.tracker.ITrackerService;
import com.polarion.platform.context.IContextService;
import com.polarion.platform.jobs.IJobService;
import org.jetbrains.annotations.NotNull;

public interface ICommonServices {
  @NotNull
  IContextService getContextService();
  
  @NotNull
  IJobService getJobService();
  
  @NotNull
  ISynchronizationService getSynchronizationService();
  
  @NotNull
  ITrackerService getTrackerService();
}


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/ICommonServices.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */