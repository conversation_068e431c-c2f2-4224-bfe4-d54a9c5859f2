/*     */ package com.polarion.synchronizer.spi.translators;
/*     */ 
/*     */ import com.polarion.synchronizer.mapping.TranslationResult;
/*     */ import com.polarion.synchronizer.mapping.ValueMapping;
/*     */ import com.polarion.synchronizer.model.CollectionUpdate;
/*     */ import com.polarion.synchronizer.model.Side;
/*     */ import com.polarion.synchronizer.spi.ICollectionTranslator;
/*     */ import java.util.Collection;
/*     */ import java.util.Collections;
/*     */ import java.util.HashSet;
/*     */ import java.util.LinkedHashSet;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ import org.jetbrains.annotations.Nullable;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public abstract class AbstractCollectionTranslator<S, T>
/*     */   extends TypesafeTranslator<Collection<S>, Collection<T>, CollectionUpdate<T>>
/*     */   implements ICollectionTranslator<T>
/*     */ {
/*     */   @NotNull
/*  41 */   private Collection<ValueMapping> valueMappings = new HashSet<>();
/*     */   @NotNull
/*     */   protected Side fromSide;
/*     */   
/*     */   public AbstractCollectionTranslator(@NotNull Collection<ValueMapping> valueMappings, @NotNull Side fromSide) {
/*  46 */     super(Collection.class, Collection.class);
/*  47 */     this.valueMappings = valueMappings;
/*  48 */     this.fromSide = fromSide;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public final TranslationResult<CollectionUpdate<T>> translateUnidirectionalTypesafe(Collection<S> sourceValue, Collection<T> targetValue) {
/*  56 */     CollectionUpdate<T> value = loadCollectionUpdate(mapCollection(sourceValue, targetValue, false), targetValue);
/*  57 */     return createResult(value);
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private TranslationResult<CollectionUpdate<T>> createResult(@NotNull CollectionUpdate<T> value) {
/*  62 */     TranslationResult<CollectionUpdate<T>> result = new TranslationResult(value, !value.isEmpty(), false);
/*  63 */     return result;
/*     */   }
/*     */   @NotNull
/*     */   private <X> CollectionUpdate<X> loadCollectionUpdate(@Nullable Collection<X> expectedContent, @Nullable Collection<X> content) {
/*     */     Collection<X> removed;
/*     */     Collection<X> added;
/*  69 */     if (content == null) {
/*  70 */       removed = Collections.emptySet();
/*     */     } else {
/*  72 */       removed = new LinkedHashSet<>(content);
/*  73 */       if (expectedContent != null) {
/*  74 */         removed.removeAll(expectedContent);
/*     */       }
/*     */     } 
/*     */ 
/*     */     
/*  79 */     if (expectedContent == null) {
/*  80 */       added = Collections.emptySet();
/*     */     } else {
/*  82 */       added = new LinkedHashSet<>(expectedContent);
/*  83 */       if (content != null) {
/*  84 */         added.removeAll(content);
/*     */       }
/*     */     } 
/*     */     
/*  88 */     return new CollectionUpdate(added, removed);
/*     */   }
/*     */ 
/*     */   
/*     */   public final TranslationResult<CollectionUpdate<T>> translateBidirectionalTypesafe(Collection<S> sourceBaseline, Collection<S> sourceValue, Collection<T> targetBaseline, Collection<T> targetValue) {
/*  93 */     CollectionUpdate<S> update = loadCollectionUpdate(sourceValue, sourceBaseline);
/*     */     
/*  95 */     CollectionUpdate<T> mappedUpdate = new CollectionUpdate(
/*  96 */         mapCollection(update.getAdded(), targetValue, false), 
/*  97 */         mapCollection(update.getRemoved(), targetValue, true));
/*     */     
/*  99 */     if (targetValue != null) {
/*     */       
/* 101 */       Collection<T> targetCollection = targetValue;
/* 102 */       mappedUpdate.getAdded().removeAll(targetCollection);
/* 103 */       mappedUpdate.getRemoved().retainAll(targetCollection);
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 108 */     return createResult(mappedUpdate);
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   protected final Collection<String> loadPotentialMatches(@NotNull String stringValue, @NotNull Side from) {
/* 113 */     Collection<String> potentialMatch = new LinkedHashSet<>();
/* 114 */     for (ValueMapping valueMapping : this.valueMappings) {
/* 115 */       String match = (from == Side.LEFT) ? valueMapping.getLeft() : valueMapping.getRight();
/* 116 */       String replace = (from == Side.RIGHT) ? valueMapping.getLeft() : valueMapping.getRight();
/* 117 */       if (match.equals(stringValue)) {
/* 118 */         potentialMatch.add(replace);
/*     */       }
/*     */     } 
/* 121 */     return potentialMatch;
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   protected final Collection<String> loadPotentialMatches(@NotNull String stringValue) {
/* 126 */     return loadPotentialMatches(stringValue, this.fromSide);
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   protected final String loadBestMatch(@Nullable Collection<?> otherCollection, @NotNull String value) {
/* 131 */     String result = value;
/* 132 */     Collection<String> potentialMatches = loadPotentialMatches(value);
/* 133 */     if (!potentialMatches.isEmpty()) {
/* 134 */       result = potentialMatches.iterator().next();
/* 135 */       if (otherCollection != null) {
/* 136 */         potentialMatches.retainAll(otherCollection);
/* 137 */         if (!potentialMatches.isEmpty()) {
/* 138 */           result = potentialMatches.iterator().next();
/*     */         }
/*     */       } 
/*     */     } 
/* 142 */     return result;
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   protected abstract Collection<T> mapCollection(@Nullable Collection<S> paramCollection, @Nullable Collection<T> paramCollection1, boolean paramBoolean);
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/spi/translators/AbstractCollectionTranslator.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */