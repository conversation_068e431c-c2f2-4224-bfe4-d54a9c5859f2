package com.polarion.synchronizer.proxy.doors.internal.tables;

import com.polarion.synchronizer.model.TransferItem;
import java.util.Collection;
import org.jetbrains.annotations.NotNull;

public interface ITableBuilder {
  @NotNull
  Collection<ITable> loadTables(@NotNull Collection<TransferItem> paramCollection);
}


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/internal/tables/ITableBuilder.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */