package com.polarion.synchronizer;

import com.polarion.synchronizer.model.IProxy;
import java.util.Collection;
import org.jetbrains.annotations.NotNull;

public interface IPostProcessingAction {
  void execute(@NotNull ISynchronizationTask paramISynchronizationTask);
  
  @NotNull
  Collection<String> getAdditionalKeysLeft(@NotNull IProxy paramIProxy);
  
  @NotNull
  Collection<String> getAdditionalKeysRight(@NotNull IProxy paramIProxy);
}


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/IPostProcessingAction.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */