/*     */ package com.polarion.synchronizer.internal.hierarchy;
/*     */ 
/*     */ import com.polarion.synchronizer.ILogger;
/*     */ import com.polarion.synchronizer.model.Hierarchy;
/*     */ import com.polarion.synchronizer.model.ItemKey;
/*     */ import com.polarion.synchronizer.model.TransferItem;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Collection;
/*     */ import java.util.HashMap;
/*     */ import java.util.Iterator;
/*     */ import java.util.LinkedList;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ 
/*     */ public class ItemTree
/*     */   implements Cloneable
/*     */ {
/*     */   private final Map<ItemKey, Node> nodeMap;
/*     */   private final Node root;
/*     */   private final ILogger logger;
/*     */   
/*     */   public class Node
/*     */     implements Cloneable {
/*     */     private final ItemKey key;
/*     */     private final List<Node> children;
/*     */     private Node parent;
/*  27 */     private int position = -1;
/*     */     
/*     */     private boolean blocked;
/*     */     
/*     */     private boolean modified;
/*     */     
/*     */     private Node(ItemKey key) {
/*  34 */       this.key = key;
/*  35 */       this.children = new ArrayList<>();
/*     */     }
/*     */     
/*     */     private Node(Node source) {
/*  39 */       this.key = source.key;
/*  40 */       ItemTree.this.nodeMap.put(this.key, this);
/*  41 */       this.children = new ArrayList<>();
/*  42 */       for (Node sourceChild : source.children) {
/*  43 */         if (sourceChild != null) {
/*  44 */           Node clonedChild = new Node(sourceChild);
/*  45 */           clonedChild.parent = this;
/*  46 */           this.children.add(clonedChild); continue;
/*     */         } 
/*  48 */         this.children.add(null);
/*     */       } 
/*     */ 
/*     */       
/*  52 */       this.position = source.position;
/*  53 */       this.blocked = source.blocked;
/*  54 */       this.modified = source.modified;
/*     */     }
/*     */     
/*     */     public String getId() {
/*  58 */       return (this.key == null) ? null : this.key.id;
/*     */     }
/*     */     
/*     */     public ItemKey getKey() {
/*  62 */       return this.key;
/*     */     }
/*     */     
/*     */     public boolean isIdForeign() {
/*  66 */       return (this.key == null) ? false : this.key.isForeign;
/*     */     }
/*     */     
/*     */     public List<Node> getChildren() {
/*  70 */       return this.children;
/*     */     }
/*     */     
/*     */     public int countUnknownChildren() {
/*  74 */       return processUnknownChildren(false);
/*     */     }
/*     */     
/*     */     private int processUnknownChildren(boolean remove) {
/*  78 */       int count = 0;
/*  79 */       for (Iterator<Node> iter = this.children.iterator(); iter.hasNext(); ) {
/*  80 */         Node child = iter.next();
/*  81 */         if (child == null) {
/*  82 */           count++;
/*  83 */           if (remove) {
/*  84 */             ItemTree.this.logger.debug("Remove unknown from " + this);
/*  85 */             iter.remove();
/*     */           }  continue;
/*  87 */         }  if (remove) {
/*  88 */           child.position -= count;
/*     */         }
/*     */       } 
/*  91 */       return count;
/*     */     }
/*     */     
/*     */     public void removeUnknownChildren() {
/*  95 */       processUnknownChildren(true);
/*     */     }
/*     */     
/*     */     public int getPosition() {
/*  99 */       return this.position;
/*     */     }
/*     */     
/*     */     public Node getParent() {
/* 103 */       return this.parent;
/*     */     }
/*     */     
/*     */     public void markModified() {
/* 107 */       this.modified = true;
/* 108 */       if (this.parent != null) {
/* 109 */         this.parent.blocked = true;
/*     */       }
/*     */     }
/*     */     
/*     */     public boolean areChildrenModified() {
/* 114 */       return this.blocked;
/*     */     }
/*     */     
/*     */     public boolean isModified() {
/* 118 */       return this.modified;
/*     */     }
/*     */     
/*     */     public boolean addNode(Node node, int position) {
/* 122 */       boolean success = false;
/*     */       
/* 124 */       Node oldParent = (node == null) ? null : node.getParent();
/*     */       
/* 126 */       boolean parentChanged = (oldParent != null && oldParent != this);
/*     */       
/* 128 */       boolean isNew = (oldParent == null && node != null && node.key.isForeign);
/*     */       
/* 130 */       if (parentChanged) {
/* 131 */         if (oldParent.areChildrenModified()) {
/* 132 */           ItemTree.this.logger.info("Can't move item " + node.key + " because children of old parent " + oldParent.key + " were modified.");
/* 133 */         } else if (areChildrenModified()) {
/* 134 */           ItemTree.this.logger.info("Can't move item " + node.key + " because children of new parent " + this.key + " were modified.");
/*     */         } else {
/* 136 */           oldParent.children.remove(node);
/* 137 */           reinsert(node, position);
/* 138 */           success = true;
/*     */         } 
/* 140 */       } else if (isNew) {
/* 141 */         if (areChildrenModified()) {
/* 142 */           ItemTree.this.logger.info("Can't add item " + node.key + " at poisition " + position + " of " + this.key + " because children were modified. Will be inserted as last child.");
/* 143 */           this.children.add(node);
/* 144 */           node.insert(this, this.children.size() - 1);
/*     */         } else {
/* 146 */           reinsert(node, position);
/* 147 */           success = true;
/*     */         } 
/*     */       } else {
/*     */         
/* 151 */         Node previous = (position < getChildren().size()) ? getChildren().get(position) : null;
/* 152 */         if (previous != null && node != null && previous.getKey().equals(node.getKey())) {
/*     */           
/* 154 */           success = true;
/*     */         }
/* 156 */         else if (areChildrenModified()) {
/* 157 */           ItemTree.this.logger.info("Can't move item " + node.key + " because children of new parent " + this.key + " were modified.");
/*     */         } else {
/* 159 */           getChildren().remove(node);
/* 160 */           reinsert(node, position);
/* 161 */           success = true;
/*     */         } 
/*     */       } 
/*     */ 
/*     */       
/* 166 */       return success;
/*     */     }
/*     */     
/*     */     private void reinsert(Node node, int position) {
/* 170 */       this.children.add(position, node);
/* 171 */       if (node != null) {
/* 172 */         node.parent = this;
/*     */       }
/* 174 */       for (int pos = 0; pos < this.children.size(); pos++) {
/* 175 */         Node child = this.children.get(pos);
/* 176 */         if (child != null) {
/* 177 */           child.position = pos;
/*     */         }
/*     */       } 
/*     */     }
/*     */     
/*     */     private void insert(Node parent, int position) {
/* 183 */       if (this.parent == null && this.position == -1) {
/* 184 */         this.parent = parent;
/* 185 */         this.position = position;
/*     */       } else {
/* 187 */         ItemTree.this.logger.error(String.format("Tried to reinsert item %s.", new Object[] { this.key }));
/*     */       } 
/*     */     }
/*     */ 
/*     */     
/*     */     public String toString() {
/* 193 */       return "Node [key=" + this.key + ", children=" + this.children + "]" + (isModified() ? "*" : "");
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public ItemTree(Collection<TransferItem> items, ILogger logger) {
/* 206 */     this.nodeMap = new HashMap<>();
/*     */     
/* 208 */     this.root = new Node(null);
/* 209 */     this.logger = logger;
/*     */     
/* 211 */     this.nodeMap.put(null, this.root);
/*     */     
/* 213 */     for (TransferItem item : items) {
/*     */       
/* 215 */       Hierarchy hierarchy = (Hierarchy)item.getValue("hierarchy");
/*     */       
/* 217 */       if (hierarchy == null) {
/*     */         continue;
/*     */       }
/*     */       
/* 221 */       ItemKey key = item.getKey();
/*     */       
/* 223 */       Node node = this.nodeMap.get(key);
/* 224 */       if (node == null) {
/* 225 */         node = new Node(key);
/* 226 */         this.nodeMap.put(key, node);
/*     */       } 
/*     */       
/* 229 */       ItemKey parentItemId = hierarchy.getParentKey();
/*     */       
/* 231 */       Node parent = this.nodeMap.get(parentItemId);
/* 232 */       if (parent == null) {
/* 233 */         parent = new Node(parentItemId);
/* 234 */         this.nodeMap.put(parentItemId, parent);
/*     */       } 
/*     */       
/* 237 */       List<Node> children = parent.getChildren();
/*     */       
/* 239 */       int index = hierarchy.getPosition();
/*     */       
/* 241 */       for (int resize = index - children.size(); resize >= 0; resize--) {
/* 242 */         children.add(null);
/*     */       }
/*     */       
/* 245 */       Node old = children.set(index, node);
/* 246 */       if (old != null) {
/* 247 */         logger.error(String.format("Proxy reported item %s and %s as child %s of %s.", new Object[] {
/* 248 */                 node.getId(), old.getId(), Integer.valueOf(index), (parentItemId == null) ? "<root>" : parentItemId }));
/* 249 */         children.add(old);
/*     */       } 
/*     */       
/* 252 */       node.insert(parent, index);
/*     */     } 
/*     */ 
/*     */     
/* 256 */     Collection<Node> unrootedNodes = new LinkedList<>();
/* 257 */     for (Node node : getAllNodes()) {
/* 258 */       if (node.getKey() != null && node.getParent() == null) {
/* 259 */         unrootedNodes.add(node);
/*     */       }
/*     */     } 
/*     */     
/* 263 */     if (!unrootedNodes.isEmpty()) {
/* 264 */       logger.warn("Tree seems to be incomplete (filtered parent items), found nodes without parent: " + unrootedNodes);
/*     */       
/* 266 */       for (Node unrootedNode : unrootedNodes) {
/* 267 */         removeWithChildren(unrootedNode);
/*     */       }
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   private void removeWithChildren(Node unrootedNode) {
/* 274 */     this.logger.debug("Removing unrooted node " + unrootedNode);
/* 275 */     this.nodeMap.remove(unrootedNode.getKey());
/* 276 */     for (Node child : unrootedNode.getChildren()) {
/* 277 */       if (child != null) {
/* 278 */         removeWithChildren(child);
/*     */       }
/*     */     } 
/*     */   }
/*     */   
/*     */   public ItemTree(ItemTree source) {
/* 284 */     this.logger = source.logger;
/* 285 */     this.nodeMap = new HashMap<>();
/*     */     
/* 287 */     this.root = new Node(source.root);
/*     */   }
/*     */ 
/*     */   
/*     */   public Node getRoot() {
/* 292 */     return this.root;
/*     */   }
/*     */   
/*     */   public Node getNode(ItemKey key) {
/* 296 */     return this.nodeMap.get(key);
/*     */   }
/*     */   
/*     */   public Node getNode(String id) {
/* 300 */     return this.nodeMap.get((id == null) ? null : new ItemKey(id, false));
/*     */   }
/*     */   
/*     */   public Collection<Node> getAllNodes() {
/* 304 */     return this.nodeMap.values();
/*     */   }
/*     */   
/*     */   public Node createForeignNode(String id) {
/* 308 */     ItemKey key = new ItemKey(id, true);
/* 309 */     Node node = new Node(key);
/* 310 */     this.nodeMap.put(key, node);
/* 311 */     return node;
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/internal/hierarchy/ItemTree.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */