/*    */ package com.polarion.synchronizer.proxy.doors.internal.tables;
/*    */ 
/*    */ import com.google.inject.Inject;
/*    */ import com.polarion.synchronizer.IPostProcessingAction;
/*    */ import com.polarion.synchronizer.IPostProcessingActionFactory;
/*    */ import com.polarion.synchronizer.ISynchronizationContext;
/*    */ import com.polarion.synchronizer.configuration.ISyncPair;
/*    */ import java.util.Arrays;
/*    */ import java.util.Collection;
/*    */ import java.util.Collections;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class DoorsPostProcessingActionFactory
/*    */   implements IPostProcessingActionFactory
/*    */ {
/*    */   @Inject
/*    */   private ISynchronizationContext context;
/*    */   @Inject
/*    */   private ITableBuilder tableBuidler;
/*    */   
/*    */   @NotNull
/*    */   public Collection<IPostProcessingAction> createActions(@NotNull ISyncPair syncPair) {
/* 48 */     return (syncPair.getRight() instanceof com.polarion.synchronizer.proxy.doors.DoorsProxyConfiguration) ? Arrays.<IPostProcessingAction>asList(new IPostProcessingAction[] { new TablePostProcessingAction(this.tableBuidler, this.context) }) : Collections.EMPTY_LIST;
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/internal/tables/DoorsPostProcessingActionFactory.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */