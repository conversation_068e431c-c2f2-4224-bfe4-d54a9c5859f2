/*     */ package com.polarion.synchronizer.internal;
/*     */ 
/*     */ import com.polarion.synchronizer.IMetadataService;
/*     */ import com.polarion.synchronizer.IProxyConfiguration;
/*     */ import com.polarion.synchronizer.ISynchronizationService;
/*     */ import com.polarion.synchronizer.configuration.IConfigurationHelper;
/*     */ import com.polarion.synchronizer.configuration.IConnection;
/*     */ import com.polarion.synchronizer.configuration.IProjectAware;
/*     */ import com.polarion.synchronizer.internal.configuration.SyncConfiguration;
/*     */ import com.polarion.synchronizer.mapping.FieldType;
/*     */ import com.polarion.synchronizer.mapping.MappingFactory;
/*     */ import com.polarion.synchronizer.mapping.TranslatorRegistry;
/*     */ import com.polarion.synchronizer.model.FieldDefinition;
/*     */ import com.polarion.synchronizer.model.IProxy;
/*     */ import com.polarion.synchronizer.model.IProxyMetadata;
/*     */ import com.polarion.synchronizer.model.Option;
/*     */ import com.polarion.synchronizer.model.ProxyMetaData;
/*     */ import java.util.Collection;
/*     */ import java.util.Collections;
/*     */ import java.util.HashMap;
/*     */ import java.util.HashSet;
/*     */ import java.util.Map;
/*     */ import javax.inject.Inject;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ import org.jetbrains.annotations.Nullable;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class MetadataService
/*     */   implements IMetadataService
/*     */ {
/*     */   @NotNull
/*     */   private final ISynchronizationService synchronizationService;
/*     */   @NotNull
/*     */   private final IConfigurationHelper configurationHelper;
/*     */   @NotNull
/*     */   private final TranslatorRegistry translatorRegistry;
/*     */   @NotNull
/*     */   private final MappingFactory mappingFactory;
/*     */   
/*     */   @Inject
/*     */   public MetadataService(ISynchronizationService synchronizationService, IConfigurationHelper configurationHelper, TranslatorRegistry translatorRegistry, MappingFactory mappingFactory) {
/*  67 */     this.synchronizationService = synchronizationService;
/*  68 */     this.configurationHelper = configurationHelper;
/*  69 */     this.translatorRegistry = translatorRegistry;
/*  70 */     this.mappingFactory = mappingFactory;
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public IProxy createTemporaryProxy(@Nullable String projectId, @NotNull IProxyConfiguration configuration) {
/*  76 */     if (configuration instanceof IProjectAware && projectId != null) {
/*  77 */       ((IProjectAware)configuration).setProject(projectId);
/*     */     }
/*     */     
/*  80 */     IConnection connection = configuration.getConnection();
/*  81 */     if (connection != null) {
/*  82 */       SyncConfiguration syncConfiguration = this.configurationHelper.loadConfiguration(projectId, false);
/*  83 */       IConnection savedConnection = (IConnection)syncConfiguration.getConnections().get(connection.getId());
/*  84 */       configuration.setConnection(savedConnection);
/*     */     } 
/*     */     
/*  87 */     return this.synchronizationService.newProxy(configuration);
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public IProxyMetadata loadMetadata(@Nullable String projectId, @NotNull IProxyConfiguration configuration) {
/*  93 */     return loadMetadata(projectId, configuration, Collections.EMPTY_LIST);
/*     */   }
/*     */   
/*     */   private void loadTypes(Collection<FieldDefinition> fieldDefinitions, Collection<FieldType> allTypes) {
/*  97 */     for (FieldDefinition fieldDefinition : fieldDefinitions) {
/*  98 */       allTypes.add(new FieldType(fieldDefinition.getType(), fieldDefinition.isMultiValued()));
/*     */     }
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public IProxyMetadata loadMetadata(@Nullable String projectId, @NotNull IProxyConfiguration configuration, @Nullable Collection<String> typeIds) {
/* 105 */     IProxy proxy = createTemporaryProxy(projectId, configuration);
/*     */     
/* 107 */     Collection<Option> types = (typeIds == null) ? proxy.getDefinedTypes() : proxy.getDefinedTypes(typeIds);
/*     */     
/* 109 */     Collection<FieldDefinition> commonFields = this.mappingFactory.loadCommonFields(proxy, types);
/*     */     
/* 111 */     Map<String, Collection<FieldDefinition>> definedFields = new HashMap<>();
/*     */     
/* 113 */     Collection<FieldType> allTypes = new HashSet<>();
/*     */     
/* 115 */     loadTypes(commonFields, allTypes);
/*     */     
/* 117 */     for (Option type : types) {
/* 118 */       String typeId = type.getId();
/* 119 */       Collection<FieldDefinition> definedTypeFields = proxy.getDefinedFields(typeId);
/* 120 */       definedFields.put(typeId, definedTypeFields);
/* 121 */       loadTypes(definedTypeFields, allTypes);
/*     */     } 
/*     */     
/* 124 */     Map<FieldType, Collection<FieldType>> compatibleFieldsFrom = new HashMap<>();
/* 125 */     for (FieldType fieldType : allTypes) {
/* 126 */       Collection<FieldType> fields = this.translatorRegistry.getCompatibleFieldsFrom(fieldType);
/* 127 */       if (fields == null) {
/* 128 */         fields = Collections.emptyList();
/*     */       }
/* 130 */       compatibleFieldsFrom.put(fieldType, fields);
/*     */     } 
/*     */     
/* 133 */     ProxyMetaData proxyMetaData = new ProxyMetaData(types, proxy.isHierarchySupported(), commonFields, definedFields, 
/* 134 */         compatibleFieldsFrom);
/*     */     
/* 136 */     proxy.close();
/* 137 */     return (IProxyMetadata)proxyMetaData;
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/internal/MetadataService.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */