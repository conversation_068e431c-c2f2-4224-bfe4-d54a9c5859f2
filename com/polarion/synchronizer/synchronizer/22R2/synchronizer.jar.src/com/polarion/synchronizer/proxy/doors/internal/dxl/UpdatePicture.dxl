Object o = object(%absoluteNumber%, m)
string pictAsOle = "%pictureAsOle%"
string pictName = "%pictureName%"
Regexp re = regexp "<img [^>]+\">"
string tempObjText = ""
string regMatch = ""
string possibleFinalText = ""
Buffer finalText = create

if(oleIsObject(o))
{
	tempObjText = richTextWithOle o."Object Text"
	possibleFinalText = tempObjText
}
else
{
	tempObjText = richText o."Object Text"
	possibleFinalText = tempObjText
}
	
while(re tempObjText)
{
	int matchStart = (start 0)
	int matchEnd = (end 0)
	regMatch = tempObjText[(start 0):(end 0)]
	
	if(matches(pictName, regMatch))
	{
		if(matchStart > 0)
			finalText += tempObjText[0:matchStart-1]
			
		finalText += pictAsOle
		tempObjText = tempObjText[matchEnd+1:]
	}
	else
	{
		finalText += tempObjText[0:matchEnd]
		tempObjText = tempObjText[matchEnd+1:]
	}
}

if(length(tempObjText) > 0)
	finalText += tempObjText

result = "OK"

if(length(finalText) == 0)
{
	finalText = possibleFinalText
	result = "FAIL"
}
	
o."Object Text" = richText(stringOf(finalText))

delete(finalText)