/*    */ package com.polarion.synchronizer.proxy.doors.xmlmodel;
/*    */ 
/*    */ import com.icl.saxon.om.NodeInfo;
/*    */ import java.util.ArrayList;
/*    */ import java.util.List;
/*    */ import javax.xml.bind.annotation.XmlAnyElement;
/*    */ import javax.xml.bind.annotation.XmlMixed;
/*    */ import javax.xml.bind.annotation.XmlRootElement;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @XmlRootElement(name = "attributeValue")
/*    */ public class RootAttributeValue
/*    */ {
/*    */   @NotNull
/* 39 */   private List<Object> attributeValues = new ArrayList();
/*    */   
/*    */   @XmlMixed
/*    */   @XmlAnyElement
/*    */   @NotNull
/*    */   public List<Object> getAttributeValues() {
/* 45 */     return this.attributeValues;
/*    */   }
/*    */   
/*    */   public void setAttributeValues(@NotNull List<Object> attributeValues) {
/* 49 */     this.attributeValues = processAttributeValues(attributeValues);
/*    */   }
/*    */   
/*    */   @NotNull
/*    */   private List<Object> processAttributeValues(@NotNull List<Object> attributeValues) {
/* 54 */     List<Object> processedAttributeValues = new ArrayList();
/*    */     
/* 56 */     if (!attributeValues.isEmpty() && attributeValues.get(0) instanceof NodeInfo) {
/*    */       
/* 58 */       for (int i = 0; i < attributeValues.size(); i++) {
/* 59 */         NodeInfo nodeInfo = (NodeInfo)attributeValues.get(i);
/* 60 */         String value = nodeInfo.getStringValue();
/* 61 */         processedAttributeValues.add(value);
/*    */       } 
/*    */     } else {
/*    */       
/* 65 */       processedAttributeValues = attributeValues;
/*    */     } 
/* 67 */     return processedAttributeValues;
/*    */   }
/*    */ 
/*    */   
/*    */   public int hashCode() {
/* 72 */     int prime = 31;
/* 73 */     int result = 1;
/* 74 */     result = 31 * result + this.attributeValues.hashCode();
/* 75 */     return result;
/*    */   }
/*    */ 
/*    */   
/*    */   public boolean equals(Object obj) {
/* 80 */     if (this == obj) {
/* 81 */       return true;
/*    */     }
/* 83 */     if (obj == null) {
/* 84 */       return false;
/*    */     }
/* 86 */     if (getClass() != obj.getClass()) {
/* 87 */       return false;
/*    */     }
/* 89 */     RootAttributeValue other = (RootAttributeValue)obj;
/* 90 */     if (!this.attributeValues.equals(other.attributeValues)) {
/* 91 */       return false;
/*    */     }
/* 93 */     return true;
/*    */   }
/*    */ 
/*    */   
/*    */   public String toString() {
/* 98 */     return "RootAttributeValue [attributeValues=" + this.attributeValues + "]";
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/xmlmodel/RootAttributeValue.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */