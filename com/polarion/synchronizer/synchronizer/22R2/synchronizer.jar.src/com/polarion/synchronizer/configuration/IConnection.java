/*    */ package com.polarion.synchronizer.configuration;
/*    */ 
/*    */ import com.fasterxml.jackson.annotation.JsonTypeInfo;
/*    */ import com.polarion.synchronizer.internal.configuration.LoadContext;
/*    */ import javax.xml.bind.annotation.XmlAccessType;
/*    */ import javax.xml.bind.annotation.XmlAccessorType;
/*    */ import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ import org.jetbrains.annotations.Nullable;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @JsonTypeInfo(use = JsonTypeInfo.Id.NAME, property = "connectionType")
/*    */ @XmlJavaTypeAdapter(LoadContext.ProjectAwareAdapter.class)
/*    */ @XmlAccessorType(XmlAccessType.PUBLIC_MEMBER)
/*    */ public interface IConnection
/*    */   extends IProjectAware
/*    */ {
/*    */   default String getConnectionType() {
/* 23 */     return getClass().getSimpleName();
/*    */   }
/*    */   
/*    */   String getId();
/*    */   
/*    */   String getUser();
/*    */   
/*    */   String getPassword();
/*    */   
/*    */   void setPassword(String paramString);
/*    */   
/*    */   @Nullable
/*    */   String getProjectId();
/*    */   
/*    */   boolean isEnabled();
/*    */   
/*    */   void initializeCredentials(@NotNull String paramString);
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/configuration/IConnection.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */