package com.polarion.synchronizer.spi;

import com.polarion.synchronizer.mapping.ITranslator;
import com.polarion.synchronizer.model.CollectionUpdate;

public interface ICollectionTranslator<T> extends ITranslator<CollectionUpdate<T>> {}


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/spi/ICollectionTranslator.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */