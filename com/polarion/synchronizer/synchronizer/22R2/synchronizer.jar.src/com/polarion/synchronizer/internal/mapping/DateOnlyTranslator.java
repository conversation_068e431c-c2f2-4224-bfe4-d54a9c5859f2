/*    */ package com.polarion.synchronizer.internal.mapping;
/*    */ 
/*    */ import com.polarion.synchronizer.mapping.TranslationResult;
/*    */ import com.polarion.synchronizer.spi.translators.TypesafeTranslator;
/*    */ import java.util.Calendar;
/*    */ import java.util.Date;
/*    */ 
/*    */ public class DateOnlyTranslator
/*    */   extends TypesafeTranslator<Date, Date, Date>
/*    */ {
/*    */   public DateOnlyTranslator() {
/* 12 */     super(Date.class, Date.class);
/*    */   }
/*    */ 
/*    */   
/*    */   public TranslationResult<Date> translateBidirectionalTypesafe(Date sourceBaseline, Date sourceValue, Date targetBaseline, Date targetValue) {
/* 17 */     return createBidirectionalResult(sourceValue, sourceBaseline, getDateOnly(sourceValue), targetBaseline, targetValue);
/*    */   }
/*    */ 
/*    */   
/*    */   public TranslationResult<Date> translateUnidirectionalTypesafe(Date sourceValue, Date targetValue) {
/* 22 */     return createUnidirectionalResult(getDateOnly(sourceValue), targetValue);
/*    */   }
/*    */   
/*    */   private Date getDateOnly(Date date) {
/* 26 */     if (date == null) {
/* 27 */       return null;
/*    */     }
/* 29 */     Calendar calendar = Calendar.getInstance();
/* 30 */     calendar.setTime(date);
/* 31 */     Calendar dateOnly = Calendar.getInstance();
/* 32 */     dateOnly.clear();
/* 33 */     dateOnly.set(calendar.get(1), calendar.get(2), calendar.get(5));
/*    */     
/* 35 */     Date dateOnlyDate = dateOnly.getTime();
/* 36 */     return dateOnlyDate;
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/internal/mapping/DateOnlyTranslator.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */