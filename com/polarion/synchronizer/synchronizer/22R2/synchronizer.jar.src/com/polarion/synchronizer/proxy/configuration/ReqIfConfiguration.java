/*     */ package com.polarion.synchronizer.proxy.configuration;
/*     */ 
/*     */ import com.fasterxml.jackson.annotation.JsonIgnore;
/*     */ import com.polarion.synchronizer.IProxyConfiguration;
/*     */ import com.polarion.synchronizer.SynchronizationException;
/*     */ import com.polarion.synchronizer.configuration.IConnection;
/*     */ import com.polarion.synchronizer.configuration.IProjectAware;
/*     */ import javax.xml.bind.annotation.XmlAttribute;
/*     */ import javax.xml.bind.annotation.XmlTransient;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ import org.jetbrains.annotations.Nullable;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ReqIfConfiguration
/*     */   implements IProxyConfiguration<IConnection>, IProjectAware
/*     */ {
/*     */   public static final String SYSTEM_ID = "ReqIF";
/*     */   private String fileName;
/*     */   private String documentName;
/*     */   @Nullable
/*     */   private String specificationId;
/*     */   private String projectId;
/*     */   private boolean doorsCompatibilityMode;
/*     */   private boolean disableVirtualHeadings;
/*     */   private boolean isRif;
/*     */   
/*     */   public ReqIfConfiguration() {}
/*     */   
/*     */   public ReqIfConfiguration(@NotNull String fileName, @NotNull String documentName, @Nullable String specificationId) {
/*  36 */     this.fileName = fileName;
/*  37 */     this.documentName = documentName;
/*  38 */     this.specificationId = specificationId;
/*     */   }
/*     */ 
/*     */   
/*     */   @XmlTransient
/*     */   @Nullable
/*     */   public IConnection getConnection() {
/*  45 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void setConnection(@Nullable IConnection connection) {}
/*     */ 
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public String getSystemIdentifier() {
/*  56 */     return "ReqIF";
/*     */   }
/*     */   
/*     */   @XmlAttribute
/*     */   @NotNull
/*     */   public String getDocumentName() {
/*  62 */     return this.documentName;
/*     */   }
/*     */   
/*     */   public void setDocumentName(@NotNull String documentName) {
/*  66 */     this.documentName = documentName;
/*     */   }
/*     */   
/*     */   @XmlTransient
/*     */   @NotNull
/*     */   public String getProject() {
/*  72 */     return this.projectId;
/*     */   }
/*     */ 
/*     */   
/*     */   public void setProject(@Nullable String projectId) {
/*  77 */     this.projectId = projectId;
/*     */   }
/*     */   
/*     */   @XmlAttribute
/*     */   @NotNull
/*     */   public String getFileName() {
/*  83 */     return this.fileName;
/*     */   }
/*     */   
/*     */   public void setFileName(@NotNull String fileName) {
/*  87 */     this.fileName = fileName;
/*     */   }
/*     */   
/*     */   @XmlTransient
/*     */   @Nullable
/*     */   public String getSpecificationId() {
/*  93 */     return this.specificationId;
/*     */   }
/*     */   
/*     */   @XmlTransient
/*     */   @JsonIgnore
/*     */   public String getProjectId() {
/*  99 */     return this.projectId;
/*     */   }
/*     */   
/*     */   public void setProjectId(@Nullable String projectId) {
/* 103 */     this.projectId = projectId;
/*     */   }
/*     */   
/*     */   public void setSpecificationId(@Nullable String specificationId) {
/* 107 */     this.specificationId = specificationId;
/*     */   }
/*     */   
/*     */   @XmlAttribute
/*     */   public boolean isDoorsCompatibilityMode() {
/* 112 */     return this.doorsCompatibilityMode;
/*     */   }
/*     */   
/*     */   public void setDoorsCompatibilityMode(boolean doorsCompatibilityMode) {
/* 116 */     this.doorsCompatibilityMode = doorsCompatibilityMode;
/*     */   }
/*     */   
/*     */   @XmlTransient
/*     */   public boolean isDisableVirtualHeadings() {
/* 121 */     return this.disableVirtualHeadings;
/*     */   }
/*     */   
/*     */   public void setDisableVirtualHeadings(boolean useVirualHeadings) {
/* 125 */     this.disableVirtualHeadings = useVirualHeadings;
/*     */   }
/*     */   
/*     */   @Deprecated
/*     */   @XmlAttribute
/*     */   public boolean isRif() {
/* 131 */     return this.isRif;
/*     */   }
/*     */   
/*     */   public void setRif(boolean isRif) {
/* 135 */     this.isRif = isRif;
/*     */   }
/*     */   
/*     */   public void validate() throws SynchronizationException {
/* 139 */     String fileName = getFileName();
/* 140 */     if (fileName.isEmpty()) {
/* 141 */       throw new SynchronizationException("Missing parameter file name.");
/*     */     }
/*     */     
/* 144 */     String documentName = getDocumentName();
/* 145 */     if (documentName.isEmpty()) {
/* 146 */       throw new SynchronizationException("Missing parameter document name.");
/*     */     }
/*     */     
/* 149 */     String specificationId = getSpecificationId();
/* 150 */     if (specificationId == null || specificationId.isEmpty()) {
/* 151 */       throw new SynchronizationException("Missing parameter specification id.");
/*     */     }
/*     */   }
/*     */ 
/*     */   
/*     */   public String toString() {
/* 157 */     return "ReqIfConfiguration [fileName=" + this.fileName + ", documentName=" + this.documentName + ", specificationId=" + this.specificationId + ", projectId=" + this.projectId + ", doorsCompatibilityMode=" + this.doorsCompatibilityMode + 
/* 158 */       ", disableVirtualHeadings=" + this.disableVirtualHeadings + ", isRif=" + this.isRif + "]";
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/configuration/ReqIfConfiguration.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */