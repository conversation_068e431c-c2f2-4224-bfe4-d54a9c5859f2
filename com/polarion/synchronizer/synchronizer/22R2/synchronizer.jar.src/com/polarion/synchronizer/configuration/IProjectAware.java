package com.polarion.synchronizer.configuration;

import com.polarion.synchronizer.internal.configuration.LoadContext;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

@XmlJavaTypeAdapter(LoadContext.ProjectAwareAdapter.class)
public interface IProjectAware {
  void setProject(String paramString);
}


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/configuration/IProjectAware.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */