/*    */ package com.polarion.synchronizer.proxy.htmltranslator;
/*    */ 
/*    */ import java.util.Collection;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ import org.jsoup.nodes.Element;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class OpenCloseTag
/*    */   extends RecursiveTag
/*    */ {
/*    */   @NotNull
/*    */   private final String openStr;
/*    */   @NotNull
/*    */   private final String closeStr;
/*    */   
/*    */   public OpenCloseTag(@NotNull String openStr, @NotNull String closeStr) {
/* 36 */     this.openStr = openStr;
/* 37 */     this.closeStr = closeStr;
/*    */   }
/*    */ 
/*    */   
/*    */   public void open(@NotNull Element element, @NotNull ElementInfo elementInfo, @NotNull StringBuilder sb, @NotNull Collection<Tag> openTags) {
/* 42 */     sb.append(this.openStr);
/*    */   }
/*    */ 
/*    */   
/*    */   public void close(@NotNull Element element, @NotNull ElementInfo elementInfo, @NotNull StringBuilder sb, @NotNull Collection<Tag> openTags) {
/* 47 */     sb.append(this.closeStr);
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/htmltranslator/OpenCloseTag.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */