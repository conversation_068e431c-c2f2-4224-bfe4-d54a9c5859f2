/*    */ package com.polarion.synchronizer.proxy.doors.internal.rest;
/*    */ 
/*    */ import java.util.Arrays;
/*    */ import java.util.HashSet;
/*    */ import java.util.Set;
/*    */ import javax.ws.rs.core.Application;
/*    */ 
/*    */ 
/*    */ public class DoorsApplication
/*    */   extends Application
/*    */ {
/*    */   public Set<Class<?>> getClasses() {
/* 13 */     return new HashSet<>(Arrays.asList(new Class[] { DoorsConnectionResource.class }));
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/internal/rest/DoorsApplication.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */