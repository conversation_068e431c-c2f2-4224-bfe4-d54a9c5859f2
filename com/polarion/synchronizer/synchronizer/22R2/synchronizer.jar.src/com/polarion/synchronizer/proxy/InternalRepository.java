package com.polarion.synchronizer.proxy;

import com.polarion.subterra.base.location.ILocation;
import java.io.InputStream;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public interface InternalRepository {
  @NotNull
  ILocation getDocumentLocation(@NotNull String paramString1, @NotNull String paramString2);
  
  @Nullable
  InputStream loadContentForLocation(@NotNull ILocation paramILocation);
  
  @NotNull
  ILocation getBaseLocation(@NotNull String paramString);
  
  @NotNull
  ILocation getGlobalBaseLocation();
}


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/InternalRepository.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */