package com.polarion.synchronizer;

import com.polarion.synchronizer.model.Side;
import com.polarion.synchronizer.model.TransferItem;
import java.util.Collection;

public interface ISynchronizationHook {
  void afterCreate(Collection<TransferItem> paramCollection1, Collection<TransferItem> paramCollection2, Side paramSide, Collection<String> paramCollection);
  
  void afterUpdate(Collection<TransferItem> paramCollection1, Collection<TransferItem> paramCollection2, Collection<String> paramCollection, Side paramSide);
}


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/ISynchronizationHook.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */