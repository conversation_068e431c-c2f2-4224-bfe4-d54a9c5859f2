/*    */ package com.polarion.synchronizer.internal.ole.preview;
/*    */ 
/*    */ import com.polarion.core.config.Configuration;
/*    */ import com.polarion.ooxml.oleconverter.IOleConverter;
/*    */ import com.siemens.polarion.previewer.PreviewerParameters;
/*    */ import com.siemens.polarion.previewer.internal.IFilePreviewGenerator;
/*    */ import com.siemens.polarion.previewer.internal.IPreviewTask;
/*    */ import java.io.InputStream;
/*    */ import java.util.Arrays;
/*    */ import java.util.Collections;
/*    */ import java.util.List;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public final class OLEPreviewGenerator
/*    */   implements IFilePreviewGenerator
/*    */ {
/*    */   @NotNull
/* 22 */   private static final List<String> SUPPORTED_EXTENSIONS = Collections.unmodifiableList(Arrays.asList(new String[] { "rtf" }));
/*    */   
/*    */   @NotNull
/*    */   private IOleConverter converter;
/*    */   
/*    */   public OLEPreviewGenerator(@NotNull IOleConverter converter) {
/* 28 */     this.converter = converter;
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   public List<String> getSupportedExtensions() {
/* 34 */     List<String> extensions = Configuration.getInstance().previews().externalGeneratorProperties().extensions();
/* 35 */     return extensions.contains("rtf") ? Collections.EMPTY_LIST : SUPPORTED_EXTENSIONS;
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   public IPreviewTask createPreviewTask(@NotNull InputStream inputStream, @NotNull PreviewerParameters previewParameters) {
/* 41 */     return new OLEConverterPreviewTask(inputStream, this.converter);
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/internal/ole/preview/OLEPreviewGenerator.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */