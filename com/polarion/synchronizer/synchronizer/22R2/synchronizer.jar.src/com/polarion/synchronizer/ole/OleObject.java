/*    */ package com.polarion.synchronizer.ole;
/*    */ 
/*    */ import java.io.IOException;
/*    */ import java.io.InputStream;
/*    */ import java.nio.ByteBuffer;
/*    */ import java.nio.ByteOrder;
/*    */ import java.util.Optional;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ import org.jetbrains.annotations.Nullable;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class OleObject
/*    */   extends EmbeddedObject
/*    */ {
/*    */   @Nullable
/*    */   private EmbeddedObject thumbnail;
/*    */   
/*    */   public OleObject(@NotNull byte[] data, int objectStart) {
/* 21 */     super(data, objectStart);
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   public InputStream getBinaryData() {
/* 27 */     InputStream binaryData = super.getBinaryData();
/* 28 */     skipHeader(binaryData);
/* 29 */     return binaryData;
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   public Optional<EmbeddedObject> getThumbnail() {
/* 35 */     return Optional.ofNullable(this.thumbnail);
/*    */   }
/*    */   
/*    */   private void skipHeader(InputStream in) {
/*    */     try {
/* 40 */       in.skip(8L);
/* 41 */       int nameLength = getNameLength(in);
/* 42 */       in.skip((nameLength + 14));
/* 43 */     } catch (IOException e) {
/* 44 */       throw new RuntimeException("Unexpected error.", e);
/*    */     } 
/*    */   }
/*    */   
/*    */   @NotNull
/*    */   public InputStream getOle1BinaryData() {
/* 50 */     InputStream binaryData = super.getBinaryData();
/* 51 */     skipOle1headers(binaryData);
/* 52 */     return binaryData;
/*    */   }
/*    */   
/*    */   private void skipOle1headers(InputStream in) {
/*    */     try {
/* 57 */       in.skip(28L);
/* 58 */     } catch (IOException e) {
/* 59 */       throw new RuntimeException("Unexpected error.", e);
/*    */     } 
/*    */   }
/*    */   
/*    */   private int getNameLength(InputStream in) throws IOException {
/* 64 */     byte[] nameLenght = new byte[2];
/* 65 */     in.read(nameLenght);
/* 66 */     ByteBuffer buffer = ByteBuffer.wrap(nameLenght);
/* 67 */     buffer.order(ByteOrder.LITTLE_ENDIAN);
/* 68 */     return buffer.getShort();
/*    */   }
/*    */   
/*    */   void setThumbnail(@NotNull EmbeddedObject thumbnail) {
/* 72 */     this.thumbnail = thumbnail;
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/ole/OleObject.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */