package com.polarion.synchronizer.model;

import java.io.Serializable;

public interface IBaselineable extends Serializable {
  Serializable getBaselineValue(boolean paramBoolean);
}


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/model/IBaselineable.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */