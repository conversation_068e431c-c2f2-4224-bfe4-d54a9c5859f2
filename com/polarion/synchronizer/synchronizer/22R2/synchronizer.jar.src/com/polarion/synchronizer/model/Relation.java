/*    */ package com.polarion.synchronizer.model;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ import org.jetbrains.annotations.Nullable;
/*    */ 
/*    */ 
/*    */ 
/*    */ public class Relation
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -332687513615130377L;
/*    */   private String role;
/*    */   private String targetId;
/*    */   @Nullable
/*    */   private String url;
/*    */   
/*    */   public Relation(String role, String targetId) {
/* 19 */     this.role = role;
/* 20 */     this.targetId = targetId;
/*    */   }
/*    */   
/*    */   public Relation(@NotNull String role, @NotNull String targetId, @NotNull String url) {
/* 24 */     this.role = role;
/* 25 */     this.targetId = targetId;
/* 26 */     this.url = url;
/*    */   }
/*    */   
/*    */   public String getRole() {
/* 30 */     return this.role;
/*    */   }
/*    */   
/*    */   public String getTargetId() {
/* 34 */     return this.targetId;
/*    */   }
/*    */   
/*    */   @Nullable
/*    */   public String getUrl() {
/* 39 */     return this.url;
/*    */   }
/*    */ 
/*    */   
/*    */   public int hashCode() {
/* 44 */     int prime = 31;
/* 45 */     int result = 1;
/* 46 */     result = 31 * result + ((this.role == null) ? 0 : this.role.hashCode());
/* 47 */     result = 31 * result + ((this.targetId == null) ? 0 : this.targetId.hashCode());
/* 48 */     return result;
/*    */   }
/*    */ 
/*    */   
/*    */   public boolean equals(Object obj) {
/* 53 */     if (this == obj) {
/* 54 */       return true;
/*    */     }
/* 56 */     if (obj == null) {
/* 57 */       return false;
/*    */     }
/* 59 */     if (getClass() != obj.getClass()) {
/* 60 */       return false;
/*    */     }
/* 62 */     Relation other = (Relation)obj;
/* 63 */     if (this.role == null) {
/* 64 */       if (other.role != null) {
/* 65 */         return false;
/*    */       }
/* 67 */     } else if (!this.role.equals(other.role)) {
/* 68 */       return false;
/*    */     } 
/* 70 */     if (this.targetId == null) {
/* 71 */       if (other.targetId != null) {
/* 72 */         return false;
/*    */       }
/* 74 */     } else if (!this.targetId.equals(other.targetId)) {
/* 75 */       return false;
/*    */     } 
/* 77 */     return true;
/*    */   }
/*    */ 
/*    */   
/*    */   public String toString() {
/* 82 */     return "Relation [role=" + this.role + ", targetId=" + this.targetId + "]";
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/model/Relation.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */