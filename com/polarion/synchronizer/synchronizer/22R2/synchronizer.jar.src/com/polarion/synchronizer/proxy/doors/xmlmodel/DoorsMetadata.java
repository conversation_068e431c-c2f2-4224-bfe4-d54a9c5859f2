/*    */ package com.polarion.synchronizer.proxy.doors.xmlmodel;
/*    */ 
/*    */ import java.util.ArrayList;
/*    */ import java.util.List;
/*    */ import javax.xml.bind.annotation.XmlElement;
/*    */ import javax.xml.bind.annotation.XmlElementWrapper;
/*    */ import javax.xml.bind.annotation.XmlRootElement;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @XmlRootElement(name = "metadata")
/*    */ public class DoorsMetadata
/*    */ {
/*    */   @NotNull
/* 37 */   private List<DoorsAttributeDefinition> attributes = new ArrayList<>();
/*    */   
/*    */   @XmlElementWrapper(name = "attributes")
/*    */   @XmlElement(name = "attribute")
/*    */   public void setAttributeDefinitions(@NotNull List<DoorsAttributeDefinition> attributes) {
/* 42 */     this.attributes = attributes;
/*    */   }
/*    */   
/*    */   @NotNull
/*    */   public List<DoorsAttributeDefinition> getAttributeDefinitions() {
/* 47 */     return this.attributes;
/*    */   }
/*    */ 
/*    */   
/*    */   public int hashCode() {
/* 52 */     int prime = 31;
/* 53 */     int result = 1;
/* 54 */     result = 31 * result + this.attributes.hashCode();
/* 55 */     return result;
/*    */   }
/*    */ 
/*    */   
/*    */   public boolean equals(Object obj) {
/* 60 */     if (this == obj) {
/* 61 */       return true;
/*    */     }
/* 63 */     if (obj == null) {
/* 64 */       return false;
/*    */     }
/* 66 */     if (getClass() != obj.getClass()) {
/* 67 */       return false;
/*    */     }
/* 69 */     DoorsMetadata other = (DoorsMetadata)obj;
/* 70 */     if (!this.attributes.equals(other.attributes)) {
/* 71 */       return false;
/*    */     }
/* 73 */     return true;
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/xmlmodel/DoorsMetadata.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */