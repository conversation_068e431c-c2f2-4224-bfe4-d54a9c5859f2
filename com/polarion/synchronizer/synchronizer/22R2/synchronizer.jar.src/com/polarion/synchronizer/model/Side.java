/*   */ package com.polarion.synchronizer.model;
/*   */ 
/*   */ public enum Side {
/* 4 */   LEFT, RIGHT;
/*   */   
/*   */   public Side getOtherSide() {
/* 7 */     return (this == LEFT) ? RIGHT : LEFT;
/*   */   }
/*   */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/model/Side.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */