/*    */ package com.polarion.synchronizer.internal.mapping;
/*    */ 
/*    */ import com.polarion.synchronizer.configuration.IAttributeMapper;
/*    */ import com.polarion.synchronizer.model.TransferItem;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ 
/*    */ public class UnidirectionalResultImpl
/*    */   implements IAttributeMapper.UnidirectionalResult
/*    */ {
/*    */   @NotNull
/*    */   private final TransferItem source;
/*    */   @NotNull
/*    */   private final TransferItem mapped;
/*    */   @NotNull
/*    */   private final TransferItem sourceBaseline;
/*    */   
/*    */   public UnidirectionalResultImpl(@NotNull TransferItem source, @NotNull TransferItem mapped, @NotNull TransferItem sourceBaseline) {
/* 18 */     this.source = source;
/* 19 */     this.mapped = mapped;
/* 20 */     this.sourceBaseline = sourceBaseline;
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   public TransferItem getSource() {
/* 26 */     return this.source;
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   public TransferItem getMapped() {
/* 32 */     return this.mapped;
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   public TransferItem getSourceBaseline() {
/* 38 */     return this.sourceBaseline;
/*    */   }
/*    */ 
/*    */   
/*    */   public int hashCode() {
/* 43 */     int prime = 31;
/* 44 */     int result = 1;
/* 45 */     result = 31 * result + this.mapped.hashCode();
/* 46 */     result = 31 * result + this.sourceBaseline.hashCode();
/* 47 */     return result;
/*    */   }
/*    */ 
/*    */   
/*    */   public boolean equals(Object obj) {
/* 52 */     if (this == obj) {
/* 53 */       return true;
/*    */     }
/* 55 */     if (obj == null) {
/* 56 */       return false;
/*    */     }
/* 58 */     if (getClass() != obj.getClass()) {
/* 59 */       return false;
/*    */     }
/* 61 */     UnidirectionalResultImpl other = (UnidirectionalResultImpl)obj;
/* 62 */     if (!this.mapped.equals(other.mapped)) {
/* 63 */       return false;
/*    */     }
/* 65 */     if (!this.sourceBaseline.equals(other.sourceBaseline)) {
/* 66 */       return false;
/*    */     }
/* 68 */     return true;
/*    */   }
/*    */ 
/*    */   
/*    */   public String toString() {
/* 73 */     return "UnidirectionalResultImpl [mapped=" + this.mapped + ", sourceBaseline=" + this.sourceBaseline + "]";
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/internal/mapping/UnidirectionalResultImpl.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */