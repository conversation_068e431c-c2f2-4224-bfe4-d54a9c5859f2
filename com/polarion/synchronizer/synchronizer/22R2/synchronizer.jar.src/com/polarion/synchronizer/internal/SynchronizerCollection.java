/*    */ package com.polarion.synchronizer.internal;
/*    */ 
/*    */ import com.polarion.synchronizer.model.CollectionUpdate;
/*    */ import java.util.Collection;
/*    */ import java.util.Collections;
/*    */ import java.util.HashSet;
/*    */ import java.util.LinkedHashSet;
/*    */ 
/*    */ 
/*    */ 
/*    */ public class SynchronizerCollection<T>
/*    */ {
/*    */   private Collection<T> collection;
/*    */   
/*    */   public SynchronizerCollection(Collection<T> data, boolean isSourceData) {
/* 16 */     this.collection = data;
/* 17 */     if (data == null) {
/* 18 */       this.collection = new LinkedHashSet<>();
/*    */     }
/*    */   }
/*    */ 
/*    */   
/*    */   public CollectionUpdate<T> loadCollectionUpdate(Collection newContent, Object targetContent) {
/* 24 */     CollectionUpdate<T> update = loadCollectionUpdate(newContent);
/* 25 */     if (targetContent != null && targetContent instanceof Collection) {
/*    */       
/* 27 */       Collection<?> targetCollection = (Collection)targetContent;
/* 28 */       update.getAdded().removeAll(targetCollection);
/* 29 */       update.getRemoved().retainAll(targetCollection);
/*    */     } 
/*    */ 
/*    */ 
/*    */ 
/*    */     
/* 35 */     return update;
/*    */   }
/*    */   
/*    */   private CollectionUpdate<T> loadCollectionUpdate(Object newContent) {
/* 39 */     Collection<T> newCollection = (newContent == null) ? Collections.<T>emptySet() : (Collection<T>)newContent;
/*    */     
/* 41 */     Collection<T> removed = new HashSet<>(this.collection);
/* 42 */     removed.removeAll(newCollection);
/*    */     
/* 44 */     Collection<T> added = new HashSet<>(newCollection);
/* 45 */     added.removeAll(this.collection);
/*    */     
/* 47 */     return new CollectionUpdate(added, removed);
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/internal/SynchronizerCollection.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */