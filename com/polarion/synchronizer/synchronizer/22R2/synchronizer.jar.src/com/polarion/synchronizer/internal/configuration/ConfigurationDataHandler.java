/*     */ package com.polarion.synchronizer.internal.configuration;
/*     */ 
/*     */ import com.polarion.platform.context.ContextNature;
/*     */ import com.polarion.platform.context.IContext;
/*     */ import com.polarion.platform.context.IContextService;
/*     */ import com.polarion.platform.guice.internal.GuicePlatform;
/*     */ import com.polarion.platform.repository.config.IDataHandlerContext;
/*     */ import com.polarion.platform.repository.config.RepositoryConfigurationException;
/*     */ import com.polarion.platform.repository.spi.config.AbstractDataHandler;
/*     */ import com.polarion.subterra.base.location.ILocation;
/*     */ import com.polarion.synchronizer.internal.SynchronizationService;
/*     */ import java.io.ByteArrayInputStream;
/*     */ import java.io.ByteArrayOutputStream;
/*     */ import java.io.InputStream;
/*     */ import java.util.LinkedList;
/*     */ import java.util.List;
/*     */ import javax.inject.Inject;
/*     */ import javax.xml.bind.JAXBContext;
/*     */ import javax.xml.bind.JAXBException;
/*     */ import javax.xml.bind.Marshaller;
/*     */ import javax.xml.bind.Unmarshaller;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ConfigurationDataHandler
/*     */   extends AbstractDataHandler
/*     */ {
/*     */   private SynchronizationService synchronizationService;
/*     */   private IContextService contextService;
/*     */   
/*     */   @Inject
/*     */   public void setSynchronizationService(SynchronizationService synchronizationService) {
/*  37 */     this.synchronizationService = synchronizationService;
/*     */   }
/*     */   
/*     */   @Inject
/*     */   public void setContextService(IContextService contextService) {
/*  42 */     this.contextService = contextService;
/*     */   }
/*     */   
/*     */   public ConfigurationDataHandler() {
/*  46 */     GuicePlatform.getGlobalInjector().injectMembers(this);
/*     */   }
/*     */ 
/*     */   
/*     */   protected Object mergeData(@NotNull Object previousResult, @NotNull Object singleConf) throws Exception {
/*  51 */     LoadContext singleLoadContext = (LoadContext)singleConf;
/*  52 */     LoadContext previousLoadContext = (LoadContext)previousResult;
/*  53 */     previousLoadContext.merge(singleLoadContext);
/*  54 */     return previousLoadContext;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void writeLocation(ILocation location, Object data, IDataHandlerContext context) {
/*     */     ByteArrayOutputStream out;
/*     */     try {
/*  62 */       Marshaller marshaller = loadJaxbContext().createMarshaller();
/*  63 */       marshaller.setProperty("jaxb.formatted.output", Boolean.valueOf(true));
/*  64 */       LoadContext loadContext = new LoadContext(loadProjectId(location));
/*  65 */       marshaller.setAdapter(LoadContext.ProjectAwareAdapter.class, loadContext.getProjectAwareAdapter());
/*  66 */       marshaller.setAdapter(LoadContext.ProxyConfigurationTracker.class, loadContext.getProxyConfigurationTracker());
/*     */       
/*  68 */       out = new ByteArrayOutputStream();
/*  69 */       marshaller.marshal(data, out);
/*  70 */     } catch (Exception e) {
/*  71 */       throw new RepositoryConfigurationException(location, "Failed to marshall configuration", e);
/*     */     } 
/*     */     
/*  74 */     super.writeLocation(location, new ByteArrayInputStream(out.toByteArray()), context);
/*     */   }
/*     */   
/*     */   private Class<?>[] loadContextClasses() {
/*  78 */     LinkedList<Class<?>> contextClasses = new LinkedList<>();
/*  79 */     contextClasses.add(SyncConfiguration.class);
/*  80 */     contextClasses.addAll(this.synchronizationService.getConfigurationClasses());
/*  81 */     contextClasses.addAll(this.synchronizationService.getConnectionClasses());
/*  82 */     return (Class[])contextClasses.<Class<?>[]>toArray((Class<?>[][])new Class[contextClasses.size()]);
/*     */   }
/*     */ 
/*     */   
/*     */   protected InputStream processConfig(@NotNull Object config) throws Exception {
/*  87 */     return (InputStream)config;
/*     */   }
/*     */ 
/*     */   
/*     */   protected Object processData(@NotNull InputStream in, @NotNull ILocation location) throws Exception {
/*  92 */     LoadContext loadContext = new LoadContext(loadProjectId(location));
/*     */     
/*  94 */     Unmarshaller unmarshaller = loadJaxbContext().createUnmarshaller();
/*  95 */     unmarshaller.setAdapter(LoadContext.ProjectAwareAdapter.class, loadContext.getProjectAwareAdapter());
/*  96 */     unmarshaller.setAdapter(LoadContext.ProxyConfigurationTracker.class, loadContext.getProxyConfigurationTracker());
/*  97 */     loadContext.setConfiguration((SyncConfiguration)unmarshaller.unmarshal(in));
/*  98 */     loadContext.injectConnections(false);
/*  99 */     return loadContext;
/*     */   }
/*     */   @NotNull
/*     */   private JAXBContext loadJaxbContext() throws JAXBException {
/*     */     JAXBContext jaxbContext;
/* 104 */     ClassLoader oldClassLoader = Thread.currentThread().getContextClassLoader();
/*     */     
/* 106 */     Thread.currentThread().setContextClassLoader(getClass().getClassLoader());
/*     */     try {
/* 108 */       jaxbContext = JAXBContext.newInstance(loadContextClasses());
/*     */     } finally {
/* 110 */       Thread.currentThread().setContextClassLoader(oldClassLoader);
/*     */     } 
/* 112 */     return jaxbContext;
/*     */   }
/*     */   
/*     */   private String loadProjectId(ILocation location) {
/* 116 */     IContext context = this.contextService.getLocationContext(location, true);
/* 117 */     if (context.getNature() != ContextNature.PROJECT_NATURE) {
/* 118 */       return null;
/*     */     }
/* 120 */     return context.getId().getContextName();
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public Object readLocations(List<ILocation> locations, IDataHandlerContext context) {
/* 126 */     LoadContext loadContext = (LoadContext)super.readLocations(locations, context);
/* 127 */     if (loadContext != null) {
/* 128 */       loadContext.injectConnections(true);
/*     */     }
/* 130 */     return loadContext;
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/internal/configuration/ConfigurationDataHandler.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */