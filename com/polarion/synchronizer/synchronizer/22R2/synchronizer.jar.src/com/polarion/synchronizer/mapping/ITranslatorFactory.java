package com.polarion.synchronizer.mapping;

import com.google.inject.assistedinject.Assisted;
import com.polarion.synchronizer.model.FieldDefinition;
import com.polarion.synchronizer.model.IProxy;
import com.polarion.synchronizer.model.Side;
import java.util.Collection;

public interface ITranslatorFactory<R> {
  ITranslator<R> createTranslator(@Assisted("fromField") FieldDefinition paramFieldDefinition1, @Assisted("toField") FieldDefinition paramFieldDefinition2, Side paramSide, Collection<ValueMapping> paramCollection, @Assisted("fromProxy") IProxy paramIProxy1, @Assisted("toProxy") IProxy paramIProxy2);
}


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/mapping/ITranslatorFactory.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */