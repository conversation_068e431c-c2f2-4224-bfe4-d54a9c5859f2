/*    */ package com.polarion.synchronizer.proxy.configuration;
/*    */ public class MappingGroup {
/*    */   private String polarionType;
/*    */   private String reqIfType;
/*    */   private Collection<FieldMapping> fieldMappings;
/*    */   
/*    */   @Deprecated
/*    */   public MappingGroup() {}
/*    */   
/* 10 */   private Direction direction = Direction.IMPORT;
/*    */ 
/*    */   
/*    */   public enum Direction
/*    */   {
/* 15 */     IMPORT(false), EXPORT(true);
/*    */     
/*    */     private final boolean export;
/*    */     
/*    */     Direction(boolean export) {
/* 20 */       this.export = export;
/*    */     }
/*    */     
/*    */     public boolean isExport() {
/* 24 */       return this.export;
/*    */     }
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public MappingGroup(String polarionType, String reqIfType, Direction direction, Collection<FieldMapping> fieldMappings) {
/* 36 */     this.polarionType = polarionType;
/* 37 */     this.reqIfType = reqIfType;
/* 38 */     this.direction = direction;
/* 39 */     this.fieldMappings = fieldMappings;
/*    */   }
/*    */   
/*    */   @XmlAttribute
/*    */   public String getPolarionType() {
/* 44 */     return this.polarionType;
/*    */   }
/*    */   
/*    */   public void setPolarionType(String polarionType) {
/* 48 */     this.polarionType = polarionType;
/*    */   }
/*    */   
/*    */   @XmlAttribute
/*    */   public String getReqIfType() {
/* 53 */     return this.reqIfType;
/*    */   }
/*    */   
/*    */   public void setReqIfType(String reqifType) {
/* 57 */     this.reqIfType = reqifType;
/*    */   }
/*    */   
/*    */   public Collection<FieldMapping> getFieldMappings() {
/* 61 */     return this.fieldMappings;
/*    */   }
/*    */   
/*    */   public void setFieldMappings(Collection<FieldMapping> fieldMappings) {
/* 65 */     this.fieldMappings = fieldMappings;
/*    */   }
/*    */   
/*    */   @XmlAttribute
/*    */   public Direction getDirection() {
/* 70 */     return this.direction;
/*    */   }
/*    */   
/*    */   public void setDirection(Direction direction) {
/* 74 */     this.direction = direction;
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/configuration/MappingGroup.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */