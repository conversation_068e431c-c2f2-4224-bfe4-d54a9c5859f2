/*     */ package com.polarion.synchronizer.proxy.doors.xmlmodel;
/*     */ 
/*     */ import java.util.List;
/*     */ import javax.xml.bind.annotation.XmlAttribute;
/*     */ import javax.xml.bind.annotation.XmlElement;
/*     */ import javax.xml.bind.annotation.XmlElementWrapper;
/*     */ import javax.xml.bind.annotation.XmlRootElement;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @XmlRootElement(name = "dxl-value-publication")
/*     */ public class DoorsDxlValuePublication
/*     */ {
/*     */   private String objectId;
/*     */   private List<DoorsAttribute> attributes;
/*     */   
/*     */   @NotNull
/*     */   public List<DoorsAttribute> getAttributes() {
/*  44 */     return this.attributes;
/*     */   }
/*     */   
/*     */   @XmlElementWrapper(name = "attributes")
/*     */   @XmlElement(name = "attribute")
/*     */   public void setAttributes(@NotNull List<DoorsAttribute> attributes) {
/*  50 */     this.attributes = attributes;
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   public String getObjectId() {
/*  55 */     return this.objectId;
/*     */   }
/*     */   
/*     */   @XmlAttribute
/*     */   public void setObjectId(String objectId) {
/*  60 */     this.objectId = objectId;
/*     */   }
/*     */ 
/*     */   
/*     */   public int hashCode() {
/*  65 */     int prime = 31;
/*  66 */     int result = 1;
/*  67 */     result = 31 * result + ((this.attributes == null) ? 0 : this.attributes.hashCode());
/*  68 */     result = 31 * result + ((this.objectId == null) ? 0 : this.objectId.hashCode());
/*  69 */     return result;
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean equals(Object obj) {
/*  74 */     if (this == obj) {
/*  75 */       return true;
/*     */     }
/*  77 */     if (obj == null) {
/*  78 */       return false;
/*     */     }
/*  80 */     if (getClass() != obj.getClass()) {
/*  81 */       return false;
/*     */     }
/*  83 */     DoorsDxlValuePublication other = (DoorsDxlValuePublication)obj;
/*  84 */     if (this.attributes == null) {
/*  85 */       if (other.attributes != null) {
/*  86 */         return false;
/*     */       }
/*  88 */     } else if (!this.attributes.equals(other.attributes)) {
/*  89 */       return false;
/*     */     } 
/*  91 */     if (this.objectId == null) {
/*  92 */       if (other.objectId != null) {
/*  93 */         return false;
/*     */       }
/*  95 */     } else if (!this.objectId.equals(other.objectId)) {
/*  96 */       return false;
/*     */     } 
/*  98 */     return true;
/*     */   }
/*     */ 
/*     */   
/*     */   public String toString() {
/* 103 */     return "DoorsDxlValuePublication [objectId=" + this.objectId + ", attributes=" + this.attributes + 
/* 104 */       "]";
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/xmlmodel/DoorsDxlValuePublication.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */