/*    */ package com.polarion.synchronizer.mapping;
/*    */ 
/*    */ import java.io.ByteArrayInputStream;
/*    */ import java.io.IOException;
/*    */ import java.io.InputStream;
/*    */ import java.io.SequenceInputStream;
/*    */ import java.nio.charset.Charset;
/*    */ import java.util.Arrays;
/*    */ import java.util.Collections;
/*    */ import java.util.List;
/*    */ import java.util.regex.Pattern;
/*    */ import org.apache.commons.io.IOUtils;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class RtfWrapper
/*    */ {
/*    */   private static final String PREFIX = "{\\rtf1 ";
/*    */   private static final String SUFFIX = "}";
/*    */   
/*    */   @NotNull
/*    */   public static String wrapRTF(@NotNull String oleObject) {
/* 28 */     return "{\\rtf1 " + oleObject + "}";
/*    */   }
/*    */   
/*    */   @NotNull
/*    */   public static String unWrapRTF(@NotNull String rtfObject) {
/* 33 */     String trimmedRtfObject = rtfObject.trim();
/* 34 */     if (isWrapped(trimmedRtfObject)) {
/* 35 */       StringBuilder strb = new StringBuilder(trimmedRtfObject.replaceFirst(Pattern.quote("{\\rtf1 ".trim()), "").trim());
/* 36 */       int index = strb.lastIndexOf("}");
/* 37 */       strb.replace(index, "}".length() + index, "");
/* 38 */       return strb.toString();
/*    */     } 
/* 40 */     return rtfObject;
/*    */   }
/*    */   
/*    */   public static InputStream unWrapRTF(@NotNull InputStream rtfObject) throws IOException {
/* 44 */     String unWrapped = unWrapRTF(IOUtils.toString(rtfObject, Charset.defaultCharset()));
/* 45 */     return new ByteArrayInputStream(unWrapped.getBytes());
/*    */   }
/*    */   
/*    */   public static InputStream wrapRTF(@NotNull InputStream oleObject) {
/* 49 */     List<InputStream> streams = Arrays.asList(new InputStream[] { new ByteArrayInputStream("{\\rtf1 ".getBytes()), oleObject, new ByteArrayInputStream("}".getBytes()) });
/* 50 */     return new SequenceInputStream(Collections.enumeration(streams));
/*    */   }
/*    */   
/*    */   public static boolean isWrapped(@NotNull String rtfObject) {
/* 54 */     return (rtfObject.startsWith("{\\rtf1 ".trim()) && rtfObject.endsWith("}"));
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/mapping/RtfWrapper.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */