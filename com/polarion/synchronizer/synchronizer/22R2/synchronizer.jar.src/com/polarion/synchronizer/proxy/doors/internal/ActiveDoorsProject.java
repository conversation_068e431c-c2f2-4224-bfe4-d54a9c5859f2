/*    */ package com.polarion.synchronizer.proxy.doors.internal;
/*    */ 
/*    */ import com.polarion.synchronizer.proxy.doors.IDoorsProject;
/*    */ import com.polarion.synchronizer.proxy.doors.ProjectNode;
/*    */ import com.polarion.synchronizer.proxy.doors.internal.commands.IDoorsCommand;
/*    */ import com.polarion.synchronizer.proxy.doors.internal.commands.LoadProjectContentCommand;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class ActiveDoorsProject
/*    */   implements IDoorsProject
/*    */ {
/*    */   @NotNull
/*    */   private final String name;
/*    */   @NotNull
/*    */   private final InternalDoorsConnection connection;
/*    */   
/*    */   public ActiveDoorsProject(@NotNull String name, @NotNull InternalDoorsConnection connection) {
/* 38 */     this.name = name;
/* 39 */     this.connection = connection;
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   public String getName() {
/* 45 */     return this.name;
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   public ProjectNode loadContent() {
/* 51 */     return this.connection.<ProjectNode>execute((IDoorsCommand<ProjectNode>)new LoadProjectContentCommand(this.name));
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/internal/ActiveDoorsProject.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */