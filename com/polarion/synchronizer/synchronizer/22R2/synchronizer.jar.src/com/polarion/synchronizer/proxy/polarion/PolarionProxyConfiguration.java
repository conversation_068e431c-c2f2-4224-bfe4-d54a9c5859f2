/*     */ package com.polarion.synchronizer.proxy.polarion;
/*     */ 
/*     */ import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
/*     */ import com.polarion.synchronizer.IProxyConfiguration;
/*     */ import com.polarion.synchronizer.configuration.IConnection;
/*     */ import com.polarion.synchronizer.configuration.IProjectAware;
/*     */ import javax.xml.bind.annotation.XmlAttribute;
/*     */ import javax.xml.bind.annotation.XmlTransient;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ import org.jetbrains.annotations.Nullable;
/*     */ 
/*     */ @JsonIgnoreProperties(ignoreUnknown = true)
/*     */ public class PolarionProxyConfiguration
/*     */   implements IProxyConfiguration<IConnection>, IProjectAware {
/*     */   public static final String SYSTEM_ID = "POLARION";
/*     */   private String accountVaultKey;
/*     */   private String project;
/*     */   private String type;
/*     */   private String query;
/*     */   private String space;
/*     */   private String document;
/*     */   private boolean useCurrentUser = false;
/*     */   private boolean useTitleAsRoot = false;
/*     */   private boolean canDefineLayouts = false;
/*     */   private boolean ignoreStructureLinks = false;
/*     */   private boolean deleteOnlyRichTextReferencedAttachments = false;
/*     */   @Nullable
/*     */   private String revision;
/*     */   
/*     */   @XmlAttribute
/*     */   @Nullable
/*     */   public String getAccountVaultKey() {
/*  33 */     return this.accountVaultKey;
/*     */   }
/*     */   
/*     */   public void setAccountVaultKey(@NotNull String accountVaultKey) {
/*  37 */     this.accountVaultKey = accountVaultKey;
/*     */   }
/*     */   
/*     */   @XmlTransient
/*     */   @NotNull
/*     */   public String getProject() {
/*  43 */     return this.project;
/*     */   }
/*     */   
/*     */   @XmlAttribute
/*     */   @Nullable
/*     */   public String getType() {
/*  49 */     return this.type;
/*     */   }
/*     */   
/*     */   @XmlAttribute
/*     */   @Nullable
/*     */   public String getQuery() {
/*  55 */     return this.query;
/*     */   }
/*     */ 
/*     */   
/*     */   public void setProject(String project) {
/*  60 */     this.project = project;
/*     */   }
/*     */   
/*     */   public void setType(@NotNull String type) {
/*  64 */     this.type = type;
/*     */   }
/*     */   
/*     */   public void setQuery(@NotNull String query) {
/*  68 */     this.query = query;
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public String getSystemIdentifier() {
/*  74 */     return "POLARION";
/*     */   }
/*     */ 
/*     */   
/*     */   @XmlTransient
/*     */   @Nullable
/*     */   public IConnection getConnection() {
/*  81 */     return null;
/*     */   }
/*     */   
/*     */   @XmlAttribute
/*     */   @Nullable
/*     */   public String getSpace() {
/*  87 */     return this.space;
/*     */   }
/*     */   
/*     */   public void setSpace(@Nullable String space) {
/*  91 */     this.space = space;
/*     */   }
/*     */   
/*     */   @XmlAttribute
/*     */   @Nullable
/*     */   public String getDocument() {
/*  97 */     return this.document;
/*     */   }
/*     */   
/*     */   public void setDocument(@Nullable String document) {
/* 101 */     this.document = document;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void setConnection(@Nullable IConnection connection) {}
/*     */ 
/*     */   
/*     */   @XmlAttribute
/*     */   public boolean isUseCurrentUser() {
/* 111 */     return this.useCurrentUser;
/*     */   }
/*     */   
/*     */   public void setUseCurrentUser(boolean useCurrentUser) {
/* 115 */     this.useCurrentUser = useCurrentUser;
/*     */   }
/*     */   
/*     */   @XmlAttribute
/*     */   public boolean isUseTitleAsRoot() {
/* 120 */     return this.useTitleAsRoot;
/*     */   }
/*     */   
/*     */   public void setUseTitleAsRoot(boolean useTitleAsRoot) {
/* 124 */     this.useTitleAsRoot = useTitleAsRoot;
/*     */   }
/*     */   
/*     */   @XmlTransient
/*     */   public boolean isCanDefineLayouts() {
/* 129 */     return this.canDefineLayouts;
/*     */   }
/*     */   
/*     */   public void setCanDefineLayouts(boolean canDefineLayouts) {
/* 133 */     this.canDefineLayouts = canDefineLayouts;
/*     */   }
/*     */   
/*     */   public boolean isIgnoreStructureLinks() {
/* 137 */     return this.ignoreStructureLinks;
/*     */   }
/*     */   
/*     */   public void setIgnoreStructureLinks(boolean ignoreStructureLinks) {
/* 141 */     this.ignoreStructureLinks = ignoreStructureLinks;
/*     */   }
/*     */   
/*     */   @XmlTransient
/*     */   public boolean isDeleteOnlyRichTextReferencedAttachments() {
/* 146 */     return this.deleteOnlyRichTextReferencedAttachments;
/*     */   }
/*     */   
/*     */   public void setDeleteOnlyRichTextReferencedAttachments(boolean deleteOnlyRichTextReferencedAttachments) {
/* 150 */     this.deleteOnlyRichTextReferencedAttachments = deleteOnlyRichTextReferencedAttachments;
/*     */   }
/*     */   
/*     */   @Nullable
/*     */   public String getRevision() {
/* 155 */     return this.revision;
/*     */   }
/*     */   
/*     */   public void setRevision(@Nullable String revision) {
/* 159 */     this.revision = revision;
/*     */   }
/*     */ 
/*     */   
/*     */   public int hashCode() {
/* 164 */     int prime = 31;
/* 165 */     int result = 1;
/* 166 */     result = 31 * result + ((this.accountVaultKey == null) ? 0 : this.accountVaultKey.hashCode());
/* 167 */     result = 31 * result + (this.canDefineLayouts ? 1231 : 1237);
/* 168 */     result = 31 * result + (this.deleteOnlyRichTextReferencedAttachments ? 1231 : 1237);
/* 169 */     result = 31 * result + ((this.document == null) ? 0 : this.document.hashCode());
/* 170 */     result = 31 * result + (this.ignoreStructureLinks ? 1231 : 1237);
/* 171 */     result = 31 * result + ((this.project == null) ? 0 : this.project.hashCode());
/* 172 */     result = 31 * result + ((this.query == null) ? 0 : this.query.hashCode());
/* 173 */     result = 31 * result + ((this.space == null) ? 0 : this.space.hashCode());
/* 174 */     result = 31 * result + ((this.type == null) ? 0 : this.type.hashCode());
/* 175 */     result = 31 * result + (this.useCurrentUser ? 1231 : 1237);
/* 176 */     result = 31 * result + (this.useTitleAsRoot ? 1231 : 1237);
/* 177 */     return result;
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean equals(Object obj) {
/* 182 */     if (this == obj) {
/* 183 */       return true;
/*     */     }
/* 185 */     if (obj == null) {
/* 186 */       return false;
/*     */     }
/* 188 */     if (getClass() != obj.getClass()) {
/* 189 */       return false;
/*     */     }
/* 191 */     PolarionProxyConfiguration other = (PolarionProxyConfiguration)obj;
/* 192 */     if (this.accountVaultKey == null) {
/* 193 */       if (other.accountVaultKey != null) {
/* 194 */         return false;
/*     */       }
/* 196 */     } else if (!this.accountVaultKey.equals(other.accountVaultKey)) {
/* 197 */       return false;
/*     */     } 
/* 199 */     if (this.canDefineLayouts != other.canDefineLayouts) {
/* 200 */       return false;
/*     */     }
/* 202 */     if (this.deleteOnlyRichTextReferencedAttachments != other.deleteOnlyRichTextReferencedAttachments) {
/* 203 */       return false;
/*     */     }
/* 205 */     if (this.document == null) {
/* 206 */       if (other.document != null) {
/* 207 */         return false;
/*     */       }
/* 209 */     } else if (!this.document.equals(other.document)) {
/* 210 */       return false;
/*     */     } 
/* 212 */     if (this.ignoreStructureLinks != other.ignoreStructureLinks) {
/* 213 */       return false;
/*     */     }
/* 215 */     if (this.project == null) {
/* 216 */       if (other.project != null) {
/* 217 */         return false;
/*     */       }
/* 219 */     } else if (!this.project.equals(other.project)) {
/* 220 */       return false;
/*     */     } 
/* 222 */     if (this.query == null) {
/* 223 */       if (other.query != null) {
/* 224 */         return false;
/*     */       }
/* 226 */     } else if (!this.query.equals(other.query)) {
/* 227 */       return false;
/*     */     } 
/* 229 */     if (this.space == null) {
/* 230 */       if (other.space != null) {
/* 231 */         return false;
/*     */       }
/* 233 */     } else if (!this.space.equals(other.space)) {
/* 234 */       return false;
/*     */     } 
/* 236 */     if (this.type == null) {
/* 237 */       if (other.type != null) {
/* 238 */         return false;
/*     */       }
/* 240 */     } else if (!this.type.equals(other.type)) {
/* 241 */       return false;
/*     */     } 
/* 243 */     if (this.useCurrentUser != other.useCurrentUser) {
/* 244 */       return false;
/*     */     }
/* 246 */     if (this.useTitleAsRoot != other.useTitleAsRoot) {
/* 247 */       return false;
/*     */     }
/* 249 */     return true;
/*     */   }
/*     */ 
/*     */   
/*     */   public String toString() {
/* 254 */     return "PolarionProxyConfiguration [accountVaultKey=" + this.accountVaultKey + ", project=" + this.project + ", type=" + this.type + ", query=" + this.query + ", space=" + this.space + ", document=" + this.document + ", useCurrentUser=" + this.useCurrentUser + 
/* 255 */       ", useTitleAsRoot=" + this.useTitleAsRoot + ", canDefineLayouts=" + this.canDefineLayouts + ", ignoreStructureLinks=" + this.ignoreStructureLinks + ", deleteOnlyRichTextReferencedAttachments=" + this.deleteOnlyRichTextReferencedAttachments + 
/* 256 */       "]";
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/polarion/PolarionProxyConfiguration.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */