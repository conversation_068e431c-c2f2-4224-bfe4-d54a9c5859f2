/*    */ package com.polarion.synchronizer.internal.configuration;
/*    */ 
/*    */ import com.polarion.synchronizer.spi.Connection;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class ConnectionPlaceholder
/*    */   extends Connection
/*    */ {
/*    */   ConnectionPlaceholder(String id) {
/* 29 */     super(id, null, null);
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/internal/configuration/ConnectionPlaceholder.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */