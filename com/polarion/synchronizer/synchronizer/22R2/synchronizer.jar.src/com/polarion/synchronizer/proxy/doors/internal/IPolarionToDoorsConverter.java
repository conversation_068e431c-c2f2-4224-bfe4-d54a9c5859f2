package com.polarion.synchronizer.proxy.doors.internal;

import com.polarion.synchronizer.model.Attachment;
import com.polarion.synchronizer.model.FieldDefinition;
import com.polarion.synchronizer.model.Hierarchy;
import com.polarion.synchronizer.model.TransferItem;
import java.util.Collection;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public interface IPolarionToDoorsConverter {
  @Nullable
  String getDoorsAttributeValue(@Nullable Object paramObject, @NotNull FieldDefinition paramFieldDefinition, @NotNull Collection<Attachment> paramCollection);
  
  void putPolarionDoorsParentID(@NotNull String paramString1, @NotNull String paramString2);
  
  @NotNull
  String getObjectLocation(@NotNull Hierarchy paramHierarchy);
  
  @NotNull
  String handleHierarchy(@NotNull String paramString, @NotNull Hierarchy paramHierarchy);
  
  @NotNull
  String handleCreateItem(@NotNull TransferItem paramTransferItem, @Nullable Hierarchy paramHierarchy);
  
  @NotNull
  String handleStandardAttribute(@NotNull String paramString1, @NotNull String paramString2, @Nullable Object paramObject, @Nullable FieldDefinition paramFieldDefinition, @NotNull Collection<Attachment> paramCollection);
}


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/internal/IPolarionToDoorsConverter.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */