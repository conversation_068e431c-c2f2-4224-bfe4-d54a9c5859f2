/*    */ package com.polarion.synchronizer.model;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class TimeObject
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -8091172488146352361L;
/*    */   private final String originalEstimate;
/*    */   private final String remainingEstimate;
/*    */   
/*    */   public TimeObject(String originalEstimate, String remainingEstimate) {
/* 13 */     this.originalEstimate = originalEstimate;
/* 14 */     this.remainingEstimate = remainingEstimate;
/*    */   }
/*    */   
/*    */   public String getOriginalEstimate() {
/* 18 */     return this.originalEstimate;
/*    */   }
/*    */   
/*    */   public String getRemainingEstimate() {
/* 22 */     return this.remainingEstimate;
/*    */   }
/*    */ 
/*    */   
/*    */   public boolean equals(Object obj) {
/* 27 */     if (this == obj)
/* 28 */       return true; 
/* 29 */     if (this != null && obj == null)
/* 30 */       return false; 
/* 31 */     if (getClass() != obj.getClass())
/* 32 */       return false; 
/* 33 */     TimeObject other = (TimeObject)obj;
/* 34 */     if (other.getOriginalEstimate() == null || other.getRemainingEstimate() == null)
/* 35 */       return false; 
/* 36 */     if (!other.getOriginalEstimate().equals(this.originalEstimate) || 
/* 37 */       !other.getRemainingEstimate().equals(this.remainingEstimate))
/* 38 */       return false; 
/* 39 */     return true;
/*    */   }
/*    */ 
/*    */   
/*    */   public String toString() {
/* 44 */     return (this == null || (this.originalEstimate == null && this.remainingEstimate == null)) ? null : (
/* 45 */       "TimeObject [originalEstimate=" + this.originalEstimate + " remainingEstimate=" + this.remainingEstimate + 
/* 46 */       "]");
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/model/TimeObject.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */