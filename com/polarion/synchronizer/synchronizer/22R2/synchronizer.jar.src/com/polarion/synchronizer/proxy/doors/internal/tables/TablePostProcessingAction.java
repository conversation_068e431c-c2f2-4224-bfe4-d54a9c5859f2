/*     */ package com.polarion.synchronizer.proxy.doors.internal.tables;
/*     */ 
/*     */ import com.polarion.alm.tracker.model.IModule;
/*     */ import com.polarion.core.util.ObjectUtils;
/*     */ import com.polarion.synchronizer.ILogger;
/*     */ import com.polarion.synchronizer.IPostProcessingAction;
/*     */ import com.polarion.synchronizer.ISynchronizationContext;
/*     */ import com.polarion.synchronizer.ISynchronizationTask;
/*     */ import com.polarion.synchronizer.model.IProxy;
/*     */ import com.polarion.synchronizer.model.TransferItem;
/*     */ import com.polarion.synchronizer.model.UpdateResult;
/*     */ import com.polarion.synchronizer.proxy.polarion.IPolarionProxy;
/*     */ import java.security.PrivilegedAction;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Arrays;
/*     */ import java.util.Collection;
/*     */ import java.util.Collections;
/*     */ import java.util.List;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class TablePostProcessingAction
/*     */   implements IPostProcessingAction
/*     */ {
/*     */   @NotNull
/*     */   private final ITableBuilder tableBuilder;
/*     */   @NotNull
/*     */   private final ISynchronizationContext context;
/*     */   
/*     */   public TablePostProcessingAction(@NotNull ITableBuilder tableBuilder, @NotNull ISynchronizationContext context) {
/*  54 */     this.tableBuilder = tableBuilder;
/*  55 */     this.context = context;
/*     */   }
/*     */ 
/*     */   
/*     */   public void execute(@NotNull ISynchronizationTask executedTask) {
/*  60 */     IPolarionProxy polarionProxy = (IPolarionProxy)executedTask.getLeftProxy();
/*     */     
/*  62 */     ILogger logger = this.context.getLogger();
/*  63 */     logger.info("Post processing tables...");
/*     */     
/*  65 */     Collection<TransferItem> sourceItems = executedTask.getRightResult().getSourceItems();
/*  66 */     Collection<ITable> tables = this.tableBuilder.loadTables(sourceItems);
/*  67 */     fixTableContent(polarionProxy, tables);
/*  68 */     unreferenceTableItems(polarionProxy, tables);
/*     */   }
/*     */   
/*     */   private void unreferenceTableItems(@NotNull final IPolarionProxy polarionProxy, @NotNull final Collection<ITable> tables) {
/*  72 */     polarionProxy.doInTransaction(new PrivilegedAction<Void>()
/*     */         {
/*     */           public Void run()
/*     */           {
/*  76 */             IModule document = (IModule)ObjectUtils.notNull(polarionProxy.getDocument());
/*  77 */             for (ITable table : tables) {
/*  78 */               Collection<String> containedItemIds = table.getContainedItemIds();
/*  79 */               for (String id : containedItemIds) {
/*  80 */                 document.unreference(document.getWorkItem(id.split("/")[1]));
/*     */               }
/*     */             } 
/*  83 */             document.save();
/*     */             
/*  85 */             return null;
/*     */           }
/*     */         });
/*     */   }
/*     */   
/*     */   private void fixTableContent(@NotNull IPolarionProxy polarionProxy, @NotNull Collection<ITable> tables) {
/*  91 */     List<TransferItem> updates = new ArrayList<>();
/*  92 */     for (ITable table : tables) {
/*  93 */       TransferItem update = new TransferItem(table.getId());
/*  94 */       update.put("description", table.toHtml());
/*  95 */       updates.add(update);
/*     */     } 
/*     */     
/*  98 */     List<UpdateResult> updateResults = polarionProxy.update(updates);
/*  99 */     for (UpdateResult updateResult : updateResults) {
/* 100 */       if (updateResult.hasError()) {
/* 101 */         this.context.getLogger().error(updateResult.toString());
/*     */       }
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public Collection<String> getAdditionalKeysLeft(@NotNull IProxy proxy) {
/* 109 */     return Collections.EMPTY_LIST;
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public Collection<String> getAdditionalKeysRight(@NotNull IProxy proxy) {
/* 115 */     return Arrays.asList(new String[] { "TableType" });
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/internal/tables/TablePostProcessingAction.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */