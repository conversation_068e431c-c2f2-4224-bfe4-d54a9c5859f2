/*   */ package com.polarion.synchronizer.spi.translators;
/*   */ 
/*   */ 
/*   */ public class TextToStringTranslator
/*   */   extends AbstractStringTranslator
/*   */ {
/*   */   protected String mapValue(String sourceValue, String targetValue) {
/* 8 */     String cleanedValue = sourceValue.replaceAll("[\n\r]", " ").trim();
/* 9 */     return cleanedValue.isEmpty() ? null : cleanedValue;
/*   */   }
/*   */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/spi/translators/TextToStringTranslator.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */