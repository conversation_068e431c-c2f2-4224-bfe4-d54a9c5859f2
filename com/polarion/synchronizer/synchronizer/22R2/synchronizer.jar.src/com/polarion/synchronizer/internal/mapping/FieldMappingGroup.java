/*     */ package com.polarion.synchronizer.internal.mapping;
/*     */ 
/*     */ import com.polarion.synchronizer.ISynchronizationContext;
/*     */ import com.polarion.synchronizer.configuration.IAttributeMapper;
/*     */ import com.polarion.synchronizer.model.CollectionUpdate;
/*     */ import com.polarion.synchronizer.model.Side;
/*     */ import com.polarion.synchronizer.model.TransferItem;
/*     */ import java.util.Collection;
/*     */ import java.util.HashSet;
/*     */ import java.util.Iterator;
/*     */ import java.util.LinkedList;
/*     */ import java.util.Map;
/*     */ 
/*     */ 
/*     */ 
/*     */ public class FieldMappingGroup
/*     */ {
/*  18 */   private final Collection<FieldMapping> fieldMappings = new LinkedList<>();
/*     */   
/*  20 */   private final Collection<String> sourceLeft = new HashSet<>();
/*  21 */   private final Collection<String> sourceRight = new HashSet<>();
/*     */   
/*     */   protected final ISynchronizationContext context;
/*     */   
/*     */   public FieldMappingGroup(ISynchronizationContext context) {
/*  26 */     this.context = context;
/*     */   }
/*     */   
/*     */   public void addFieldMapping(FieldMapping fieldMapping) {
/*  30 */     this.fieldMappings.add(fieldMapping);
/*     */     
/*  32 */     if (fieldMapping.getDirection().isFrom(Side.LEFT)) {
/*  33 */       this.sourceLeft.add(fieldMapping.getLeft());
/*     */     }
/*     */     
/*  36 */     if (fieldMapping.getDirection().isFrom(Side.RIGHT)) {
/*  37 */       this.sourceRight.add(fieldMapping.getRight());
/*     */     }
/*     */   }
/*     */   
/*     */   public Collection<String> getRequiredFields(Side side) {
/*  42 */     Collection<String> result = new HashSet<>();
/*  43 */     for (FieldMapping fieldMapping : this.fieldMappings) {
/*  44 */       if (fieldMapping.getDirection().isFrom(side) || fieldMapping.getDirection().isTo(side)) {
/*  45 */         result.add((side == Side.LEFT) ? fieldMapping.getLeft() : fieldMapping.getRight());
/*     */       }
/*     */     } 
/*  48 */     return result;
/*     */   }
/*     */   
/*     */   public Collection<String> getSourceFields(Side side) {
/*  52 */     return (side == Side.LEFT) ? this.sourceLeft : this.sourceRight;
/*     */   }
/*     */   
/*     */   public IAttributeMapper.UnidirectionalResult map(TransferItem sourceItem, Side fromSide) {
/*  56 */     return map(sourceItem, null, null, null, fromSide);
/*     */   }
/*     */   
/*     */   public IAttributeMapper.BidirectionalResult map(TransferItem leftItem, Map<String, Object> leftBaseline, TransferItem rightItem, Map<String, Object> rightBaseline) {
/*  60 */     IAttributeMapper.UnidirectionalResult leftResult = map(leftItem, leftBaseline, rightItem, rightBaseline, Side.LEFT);
/*  61 */     IAttributeMapper.UnidirectionalResult rightResult = map(rightItem, rightBaseline, leftItem, leftBaseline, Side.RIGHT);
/*  62 */     TransferItem leftMapped = leftResult.getMapped();
/*  63 */     String rightId = (leftMapped.getKey()).id;
/*  64 */     TransferItem rightMapped = rightResult.getMapped();
/*  65 */     String leftId = (rightMapped.getKey()).id;
/*  66 */     leftMapped.setId(leftId);
/*  67 */     rightMapped.setId(rightId);
/*     */     
/*  69 */     return new BidirectionalResultImpl(leftResult, rightResult);
/*     */   }
/*     */   
/*     */   Collection<FieldMapping> getFieldMappings() {
/*  73 */     return this.fieldMappings;
/*     */   }
/*     */ 
/*     */   
/*     */   public IAttributeMapper.UnidirectionalResult map(TransferItem sourceItem, Map<String, Object> sourceBaseline, TransferItem targetItem, Map<String, Object> targetBaseline, Side fromSide) {
/*  78 */     TransferItem sourceBaselineUpdate = new TransferItem(sourceItem.getId());
/*  79 */     TransferItem mapped = map(sourceItem, sourceBaseline, targetItem, targetBaseline, sourceBaselineUpdate, fromSide);
/*     */     
/*  81 */     return new UnidirectionalResultImpl(sourceItem, mapped, sourceBaselineUpdate);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public TransferItem map(TransferItem sourceItem, Map<String, Object> sourceBaseline, TransferItem targetItem, Map<String, Object> targetBaseline, TransferItem sourceBaselineUpdate, Side fromSide) {
/*  87 */     TransferItem result = new TransferItem(sourceItem.getId(), true);
/*     */     
/*  89 */     for (FieldMapping fieldMapping : this.fieldMappings) {
/*  90 */       if (fieldMapping.getDirection().isFrom(fromSide)) {
/*  91 */         fieldMapping.mapField(sourceItem, sourceBaseline, targetItem, targetBaseline, result, fromSide, sourceBaselineUpdate.getValues());
/*     */       }
/*     */     } 
/*     */     
/*  95 */     for (Iterator<Object> updateValueIterator = result.getValues().values().iterator(); updateValueIterator.hasNext(); ) {
/*  96 */       Object updateValue = updateValueIterator.next();
/*  97 */       if (updateValue instanceof CollectionUpdate) {
/*  98 */         CollectionUpdate<Object> collectionUpdate = (CollectionUpdate<Object>)updateValue;
/*  99 */         if (collectionUpdate.isEmpty()) {
/* 100 */           updateValueIterator.remove();
/*     */         }
/*     */       } 
/*     */     } 
/*     */     
/* 105 */     return result;
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/internal/mapping/FieldMappingGroup.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */