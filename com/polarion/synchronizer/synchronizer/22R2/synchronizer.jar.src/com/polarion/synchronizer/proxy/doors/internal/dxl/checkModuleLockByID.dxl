string modulename = name(itemFromID("%module.id%"))
string moduleFullName = fullName(itemFromID("%module.id%"))
Module m = read(moduleFullName , false)
if(null(m))
{
	print(modulename" could not be opened\n")
}
if (isLocked(module m)){
	result = "UNLOCKED"
	LockList lcklist 
  	Lock lockItem
  	lcklist = getLocksInFolder(getParentFolder(module m),true,true)
  	for lockItem in lcklist do 
  	{   
		int mode =lockItem.lockMode
		Item litem = lockItem.item
		string itemname = name(litem)
		if(modulename==itemname )
		{
			result = "LOCKED"
		}
  	}
	
}
else{ 
	result = "UNLOCKED"
}
