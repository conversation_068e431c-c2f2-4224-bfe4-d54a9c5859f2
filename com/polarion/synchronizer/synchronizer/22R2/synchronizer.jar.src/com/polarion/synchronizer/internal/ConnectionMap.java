/*     */ package com.polarion.synchronizer.internal;
/*     */ 
/*     */ import com.polarion.synchronizer.IConnectionMap;
/*     */ import com.polarion.synchronizer.model.ItemKey;
/*     */ import com.polarion.synchronizer.model.Side;
/*     */ import java.util.Collection;
/*     */ import java.util.Collections;
/*     */ import java.util.Map;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ 
/*     */ 
/*     */ public class ConnectionMap
/*     */   implements IConnectionMap
/*     */ {
/*     */   @NotNull
/*     */   private final Map<String, String> leftToRight;
/*     */   @NotNull
/*     */   private final Map<String, String> rightToLeft;
/*     */   @NotNull
/*     */   private final Collection<IConnectionMap> externalMaps;
/*     */   
/*     */   public ConnectionMap(@NotNull Map<String, String> leftToRight, @NotNull Map<String, String> rightToLeft) {
/*  23 */     this(leftToRight, rightToLeft, Collections.EMPTY_LIST);
/*     */   }
/*     */ 
/*     */   
/*     */   public ConnectionMap(@NotNull Map<String, String> leftToRight, @NotNull Map<String, String> rightToLeft, @NotNull Collection<IConnectionMap> externalMaps) {
/*  28 */     this.leftToRight = leftToRight;
/*  29 */     this.rightToLeft = rightToLeft;
/*  30 */     this.externalMaps = externalMaps;
/*     */   }
/*     */ 
/*     */   
/*     */   public String getTargetId(String sourceId, Side fromSide, boolean includeExternal) {
/*  35 */     String targetId = (fromSide == Side.LEFT) ? this.leftToRight.get(sourceId) : this.rightToLeft.get(sourceId);
/*  36 */     if (targetId == null && includeExternal) {
/*  37 */       for (IConnectionMap externalMap : this.externalMaps) {
/*  38 */         targetId = externalMap.getTargetId(sourceId, fromSide);
/*  39 */         if (targetId != null) {
/*     */           break;
/*     */         }
/*     */       } 
/*     */     }
/*  44 */     return targetId;
/*     */   }
/*     */ 
/*     */   
/*     */   public String getTargetId(String sourceId, Side fromSide) {
/*  49 */     return getTargetId(sourceId, fromSide, false);
/*     */   }
/*     */ 
/*     */   
/*     */   public void addConnection(String sourceId, String targetId, Side side) {
/*  54 */     if (side == Side.LEFT) {
/*  55 */       this.leftToRight.put(sourceId, targetId);
/*  56 */       this.rightToLeft.put(targetId, sourceId);
/*     */     } else {
/*  58 */       this.rightToLeft.put(sourceId, targetId);
/*  59 */       this.leftToRight.put(targetId, sourceId);
/*     */     } 
/*     */   }
/*     */   
/*     */   public void clear() {
/*  64 */     this.leftToRight.clear();
/*  65 */     this.rightToLeft.clear();
/*     */   }
/*     */ 
/*     */   
/*     */   public ItemKey getTargetKey(String sourceId, Side side) {
/*  70 */     ItemKey result = null;
/*  71 */     if (sourceId != null) {
/*  72 */       String targetId = getTargetId(sourceId, side);
/*  73 */       if (targetId == null) {
/*  74 */         result = new ItemKey(sourceId, true);
/*     */       } else {
/*  76 */         result = new ItemKey(targetId, false);
/*     */       } 
/*     */     } 
/*  79 */     return result;
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean existsInScope(String id, Side side, String scope) {
/*  84 */     throw new UnsupportedOperationException();
/*     */   }
/*     */ 
/*     */   
/*     */   public void addToScope(String id, Side side, String scope) {
/*  89 */     throw new UnsupportedOperationException();
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void deleteConnection(String id, Side side) {
/*  95 */     if (side == Side.LEFT) {
/*  96 */       String rightId = this.leftToRight.remove(id);
/*  97 */       if (rightId != null) {
/*  98 */         this.rightToLeft.remove(rightId);
/*     */       }
/*     */     } else {
/* 101 */       String leftId = this.rightToLeft.remove(id);
/* 102 */       if (leftId != null)
/* 103 */         this.leftToRight.remove(leftId); 
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/internal/ConnectionMap.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */