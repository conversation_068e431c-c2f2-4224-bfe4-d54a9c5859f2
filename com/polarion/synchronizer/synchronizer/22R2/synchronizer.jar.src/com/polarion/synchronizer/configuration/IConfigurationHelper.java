package com.polarion.synchronizer.configuration;

import com.polarion.subterra.base.data.identification.IContextId;
import com.polarion.synchronizer.internal.configuration.SyncConfiguration;
import java.util.Collection;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public interface IConfigurationHelper {
  @NotNull
  SyncConfiguration loadConfiguration(@Nullable String paramString, boolean paramBoolean);
  
  void saveConfiguration(@Nullable String paramString, @NotNull ISyncConfiguration paramISyncConfiguration);
  
  @NotNull
  IContextId loadContextId(@Nullable String paramString);
  
  boolean update(@NotNull ISyncPair paramISyncPair, @Nullable String paramString1, @NotNull String paramString2);
  
  boolean create(@NotNull ISyncPair paramISyncPair, @Nullable String paramString);
  
  boolean saveOrActivateConnection(@NotNull IConnection paramIConnection, @Nullable String paramString, boolean paramBoolean);
  
  void updateConnection(@NotNull String paramString, @NotNull IConnection paramIConnection);
  
  Collection<String> getProjectsUsing(@Nullable IConnection paramIConnection);
}


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/configuration/IConfigurationHelper.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */