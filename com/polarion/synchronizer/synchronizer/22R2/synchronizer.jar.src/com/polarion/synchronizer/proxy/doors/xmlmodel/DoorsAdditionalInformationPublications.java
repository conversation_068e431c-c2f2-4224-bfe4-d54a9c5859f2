/*    */ package com.polarion.synchronizer.proxy.doors.xmlmodel;
/*    */ 
/*    */ import java.util.ArrayList;
/*    */ import java.util.List;
/*    */ import javax.xml.bind.annotation.XmlElement;
/*    */ import javax.xml.bind.annotation.XmlRootElement;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @XmlRootElement(name = "additional-information-publications")
/*    */ public class DoorsAdditionalInformationPublications
/*    */ {
/*    */   @NotNull
/* 36 */   private final List<DoorsDxlValuePublication> dxlValuePublications = new ArrayList<>();
/*    */   
/*    */   @NotNull
/* 39 */   private final List<DoorsDeletedObjectPublication> deletedObjectPublications = new ArrayList<>();
/*    */   
/*    */   private String id;
/*    */ 
/*    */   
/*    */   @XmlElement(name = "dxl-value-publication")
/*    */   @NotNull
/*    */   public List<DoorsDxlValuePublication> getDxlValuePublications() {
/* 47 */     return this.dxlValuePublications;
/*    */   }
/*    */   
/*    */   @XmlElement(name = "deleted-object-publication")
/*    */   @NotNull
/*    */   public List<DoorsDeletedObjectPublication> getDeletedObjectPublications() {
/* 53 */     return this.deletedObjectPublications;
/*    */   }
/*    */   
/*    */   public String getId() {
/* 57 */     return this.id;
/*    */   }
/*    */   
/*    */   public void setId(String id) {
/* 61 */     this.id = id;
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/proxy/doors/xmlmodel/DoorsAdditionalInformationPublications.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */