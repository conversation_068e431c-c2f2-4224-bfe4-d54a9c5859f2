/*    */ package com.polarion.synchronizer.spi.translators;
/*    */ 
/*    */ import com.polarion.synchronizer.mapping.FieldType;
/*    */ import com.polarion.synchronizer.mapping.ITranslator;
/*    */ import com.polarion.synchronizer.mapping.ITranslatorFactory;
/*    */ import com.polarion.synchronizer.mapping.ValueMapping;
/*    */ import com.polarion.synchronizer.model.FieldDefinition;
/*    */ import com.polarion.synchronizer.model.IProxy;
/*    */ import com.polarion.synchronizer.model.Side;
/*    */ import java.util.Collection;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class ChainedTranslatorFactory<T>
/*    */   implements ITranslatorFactory<T>
/*    */ {
/*    */   private final ITranslatorFactory<?> translatorFactory;
/*    */   private final ITranslatorFactory<T> nextTranslatorFactory;
/*    */   private final ITranslatorFactory<?> reverseTranslatorFactory;
/*    */   private final FieldType pivotType;
/*    */   
/*    */   public ChainedTranslatorFactory(ITranslatorFactory<?> translatorFactory, ITranslatorFactory<T> nextTranslatorFactory, ITranslatorFactory<?> reverseTranslatorFactory, FieldType pivotType) {
/* 24 */     this.translatorFactory = translatorFactory;
/* 25 */     this.nextTranslatorFactory = nextTranslatorFactory;
/* 26 */     this.reverseTranslatorFactory = reverseTranslatorFactory;
/* 27 */     this.pivotType = pivotType;
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public ITranslator<T> createTranslator(FieldDefinition fromField, FieldDefinition toField, Side from, Collection<ValueMapping> valueMappings, IProxy fromProxy, IProxy toProxy) {
/* 33 */     FieldDefinition pivotTo = new FieldDefinition(fromField.getKey(), fromField.getLabel(), this.pivotType.getTypeName(), fromField.isReadOnly(), this.pivotType.isMulti());
/* 34 */     ITranslator<?> translator = this.translatorFactory.createTranslator(fromField, pivotTo, from, valueMappings, fromProxy, toProxy);
/*    */     
/* 36 */     FieldDefinition nextFrom = new FieldDefinition(fromField.getKey(), fromField.getLabel(), this.pivotType.getTypeName(), fromField.isReadOnly(), this.pivotType.isMulti());
/* 37 */     ITranslator<T> nextTranslator = this.nextTranslatorFactory.createTranslator(nextFrom, toField, from, valueMappings, fromProxy, toProxy);
/*    */     
/* 39 */     FieldDefinition reverseFrom = new FieldDefinition(toField.getKey(), toField.getLabel(), this.pivotType.getTypeName(), fromField.isReadOnly(), this.pivotType.isMulti());
/* 40 */     ITranslator<?> reverseTranslator = this.reverseTranslatorFactory.createTranslator(reverseFrom, pivotTo, from.getOtherSide(), valueMappings, toProxy, fromProxy);
/*    */     
/* 42 */     return new ChainedTranslator<>(translator, nextTranslator, reverseTranslator);
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/spi/translators/ChainedTranslatorFactory.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */