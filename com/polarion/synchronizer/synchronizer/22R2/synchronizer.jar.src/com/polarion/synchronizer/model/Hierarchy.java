/*    */ package com.polarion.synchronizer.model;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ 
/*    */ public class Hierarchy
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -6430253337774378309L;
/*    */   private final ItemKey parentKey;
/*    */   private final int position;
/*    */   
/*    */   public Hierarchy(String parentId, boolean isForeignParent, int position) {
/* 14 */     this((parentId == null) ? null : new ItemKey(parentId, isForeignParent), position);
/*    */   }
/*    */   
/*    */   public Hierarchy(String parentId, int position) {
/* 18 */     this(parentId, false, position);
/*    */   }
/*    */   
/*    */   public static Hierarchy withParentKey(ItemKey parentKey, int position) {
/* 22 */     return new Hierarchy(parentKey, position);
/*    */   }
/*    */   
/*    */   private Hierarchy(ItemKey parentKey, int position) {
/* 26 */     this.parentKey = parentKey;
/* 27 */     this.position = position;
/*    */   }
/*    */   
/*    */   public ItemKey getParentKey() {
/* 31 */     return this.parentKey;
/*    */   }
/*    */   
/*    */   public String getParentItemId() {
/* 35 */     return (this.parentKey == null) ? null : this.parentKey.id;
/*    */   }
/*    */   
/*    */   public int getPosition() {
/* 39 */     return this.position;
/*    */   }
/*    */ 
/*    */   
/*    */   public int hashCode() {
/* 44 */     int prime = 31;
/* 45 */     int result = 1;
/* 46 */     result = 31 * result + ((this.parentKey == null) ? 0 : this.parentKey.hashCode());
/* 47 */     result = 31 * result + this.position;
/* 48 */     return result;
/*    */   }
/*    */ 
/*    */   
/*    */   public boolean equals(Object obj) {
/* 53 */     if (this == obj) {
/* 54 */       return true;
/*    */     }
/* 56 */     if (obj == null) {
/* 57 */       return false;
/*    */     }
/* 59 */     if (getClass() != obj.getClass()) {
/* 60 */       return false;
/*    */     }
/* 62 */     Hierarchy other = (Hierarchy)obj;
/* 63 */     if (this.parentKey == null) {
/* 64 */       if (other.parentKey != null) {
/* 65 */         return false;
/*    */       }
/* 67 */     } else if (!this.parentKey.equals(other.parentKey)) {
/* 68 */       return false;
/*    */     } 
/* 70 */     if (this.position != other.position) {
/* 71 */       return false;
/*    */     }
/* 73 */     return true;
/*    */   }
/*    */ 
/*    */   
/*    */   public String toString() {
/* 78 */     return "Hierarchy [parentKey=" + this.parentKey + ", position=" + this.position + "]";
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/model/Hierarchy.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */