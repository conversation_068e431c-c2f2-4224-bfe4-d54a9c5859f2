/*    */ package com.polarion.synchronizer;
/*    */ 
/*    */ import java.io.OutputStream;
/*    */ import java.util.logging.Formatter;
/*    */ import java.util.logging.Handler;
/*    */ import java.util.logging.Level;
/*    */ import java.util.logging.LogRecord;
/*    */ import java.util.logging.Logger;
/*    */ import java.util.logging.SimpleFormatter;
/*    */ import java.util.logging.StreamHandler;
/*    */ import java.util.stream.Stream;
/*    */ import org.glassfish.jersey.logging.LoggingFeature;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ 
/*    */ public class JerseyRequestLoggingHandler
/*    */   extends LoggingFeature
/*    */ {
/*    */   public JerseyRequestLoggingHandler(@NotNull String name, @NotNull ILogger log, @NotNull Level level) {
/* 19 */     super(getLogger(name, log, level), level, LoggingFeature.Verbosity.PAYLOAD_ANY, Integer.valueOf(10240));
/*    */   }
/*    */   
/*    */   @NotNull
/*    */   private static Logger getLogger(@NotNull String name, @NotNull ILogger logger, @NotNull Level level) {
/* 24 */     Logger jlogger = Logger.getLogger(name);
/* 25 */     jlogger.setLevel(level);
/* 26 */     jlogger.setUseParentHandlers(false);
/* 27 */     Stream.<Handler>of(jlogger.getHandlers()).forEach(jlogger::removeHandler);
/* 28 */     jlogger.addHandler(buildStreamHandler(logger, level));
/* 29 */     return jlogger;
/*    */   }
/*    */   
/*    */   @NotNull
/*    */   public static StreamHandler buildStreamHandler(@NotNull final ILogger log, @NotNull Level level) {
/* 34 */     StreamHandler streamHandler = new StreamHandler(System.err, new SimpleFormatter())
/*    */       {
/*    */         public synchronized void publish(LogRecord record) {
/*    */           try {
/* 38 */             log.debug(getFormatter().format(record));
/* 39 */           } catch (Exception ex) {
/* 40 */             log.error(ex);
/*    */           } 
/* 42 */           flush();
/*    */         }
/*    */       };
/* 45 */     streamHandler.setLevel(level);
/* 46 */     return streamHandler;
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer_3.22.1/synchronizer.jar!/com/polarion/synchronizer/JerseyRequestLoggingHandler.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */