int itemPosition = %itemPosition%
string strPrntAbsNum = "%parentAbsoluteNumber%"
int parentAbsNum = 0
int childNum = 0
Object o, pObj
bool positionFound = false
string qualID
string qualAbsNo

if(!null(strPrntAbsNum))
	parentAbsNum = intOf(strPrntAbsNum)
	
if(parentAbsNum != 0)
{
	pObj = object(parentAbsNum, m)
	
	if(itemPosition == 0)	//first child, send parent ID back
	{
		qualID = qualifiedUniqueID(m)
		qualAbsNo = qualID "/" pObj."Absolute Number"""

		result = qualAbsNo
		positionFound = true
	}
	else	//otherwise find sibling
	{
		for o in pObj do
		{
			childNum++

			if(itemPosition == childNum)
			{
				qualID = qualifiedUniqueID(m)
				qualAbsNo = qualID "/" o."Absolute Number"""

				result = qualAbsNo
				positionFound = true
				break;
			}
		}
	}
}

//couldn't find parent, return ID of top level object in module at itemPosition
if(!positionFound)
{
	qualID = qualifiedUniqueID(m)
	if(itemPosition > 0)
	{
		o1 = (current Module)[itemPosition]
		if(!null(o1))
		{
			qualAbsNo = qualID "/" o1."Absolute Number"""
		}
		else
		{
			qualAbsNo = qualID "/0"
		}
	}
	else
	{
		qualAbsNo = qualID "/0"
	}
		result = qualAbsNo
}
print(result"\n")