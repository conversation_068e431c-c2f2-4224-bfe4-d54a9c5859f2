/*     */ package com.polarion.synchronizer.proxy.jira;
/*     */ 
/*     */ import com.fasterxml.jackson.core.JsonProcessingException;
/*     */ import com.fasterxml.jackson.databind.MapperFeature;
/*     */ import com.fasterxml.jackson.databind.ObjectMapper;
/*     */ import com.polarion.synchronizer.ILogger;
/*     */ import com.polarion.synchronizer.model.Attachment;
/*     */ import com.polarion.synchronizer.model.CollectionUpdate;
/*     */ import com.polarion.synchronizer.model.Duration;
/*     */ import com.polarion.synchronizer.model.FieldDefinition;
/*     */ import com.polarion.synchronizer.model.Relation;
/*     */ import com.polarion.synchronizer.model.TransferItem;
/*     */ import com.polarion.synchronizer.model.UpdateResult;
/*     */ import com.polarion.synchronizer.proxy.jira.json.Field;
/*     */ import com.polarion.synchronizer.proxy.jira.json.Issue;
/*     */ import com.polarion.synchronizer.proxy.jira.json.JiraRelation;
/*     */ import com.polarion.synchronizer.proxy.jira.json.Transition;
/*     */ import com.polarion.synchronizer.proxy.jira.json.Transitions;
/*     */ import com.polarion.synchronizer.proxy.jira.json.UpdateIssue;
/*     */ import com.polarion.synchronizer.proxy.jira.json.Version;
/*     */ import com.polarion.synchronizer.proxy.jira.model.JiraComment;
/*     */ import java.io.UnsupportedEncodingException;
/*     */ import java.nio.charset.StandardCharsets;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Collection;
/*     */ import java.util.Collections;
/*     */ import java.util.HashMap;
/*     */ import java.util.HashSet;
/*     */ import java.util.Iterator;
/*     */ import java.util.Map;
/*     */ import javax.mail.internet.MimeUtility;
/*     */ import javax.ws.rs.core.MediaType;
/*     */ import javax.ws.rs.core.Response;
/*     */ import org.glassfish.jersey.media.multipart.MultiPart;
/*     */ import org.glassfish.jersey.media.multipart.file.StreamDataBodyPart;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ import org.jetbrains.annotations.Nullable;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public abstract class AbstractTransferHelper
/*     */ {
/*     */   @NotNull
/*     */   private final ILogger log;
/*     */   @NotNull
/*     */   protected final JiraProxyTranslator translator;
/*     */   @NotNull
/*     */   protected final JiraProxy jiraProxy;
/*     */   @NotNull
/*     */   protected final JiraConnector jiraConnector;
/*     */   
/*     */   public AbstractTransferHelper(@NotNull JiraProxy jiraProxy, @NotNull JiraConnector jiraConnector, @NotNull JiraProxyTranslator translator, @NotNull ILogger log) {
/*  77 */     this.jiraProxy = jiraProxy;
/*  78 */     this.jiraConnector = jiraConnector;
/*  79 */     this.translator = translator;
/*  80 */     this.log = log;
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   public UpdateResult transfer(@NotNull TransferItem transferItem) {
/*  85 */     Collection<String> unprocessedFields = new HashSet<>(transferItem.getValues().keySet());
/*  86 */     removeSpecialFields(unprocessedFields);
/*     */     
/*  88 */     Collection<String> editableFields = new ArrayList<>();
/*  89 */     String error = loadEditableFields(transferItem, unprocessedFields, editableFields);
/*  90 */     if (error != null) {
/*  91 */       return createErrorResult(error);
/*     */     }
/*     */     
/*  94 */     editableFields.add("type");
/*  95 */     unprocessedFields.removeAll(editableFields);
/*     */     
/*  97 */     Map<String, String> conversionErrors = new HashMap<>();
/*  98 */     TransferItem oldItem = loadOldItemIfRequired(transferItem);
/*  99 */     UpdateIssue issue = loadContent(transferItem, oldItem, conversionErrors, editableFields);
/*     */     
/* 101 */     UpdateResult result = doInitialTransfer(transferItem, editableFields, issue);
/* 102 */     if (result.isFailed()) {
/* 103 */       return result;
/*     */     }
/* 105 */     addConversionErrors(conversionErrors, result);
/* 106 */     updateAdditionalAttributes(transferItem, oldItem, result);
/* 107 */     oldItem = loadOldItemIfRequired(transferItem);
/* 108 */     updateStatus(transferItem, oldItem, unprocessedFields, result);
/* 109 */     addUnknownFieldErrors(unprocessedFields, result);
/* 110 */     return result;
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   public UpdateResult transferVirtualItems(@NotNull TransferItem transferItem, @NotNull String projectId) {
/* 115 */     Map<String, String> conversionErrors = new HashMap<>();
/* 116 */     Collection<String> updatingFields = transferItem.getValues().keySet();
/* 117 */     TransferItem oldItem = loadOldVersionIfRequired(transferItem);
/* 118 */     if (oldItem != null) {
/* 119 */       transferItem = updateTransferItemUnchangedValue(transferItem, oldItem);
/*     */     }
/* 121 */     Version version = loadVersionContent(transferItem, conversionErrors, projectId);
/* 122 */     UpdateResult result = doInitialTransferForVersion(transferItem, version, updatingFields);
/* 123 */     if (result.isFailed()) {
/* 124 */       return result;
/*     */     }
/* 126 */     addConversionErrors(conversionErrors, result);
/* 127 */     return result;
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private TransferItem updateTransferItemUnchangedValue(@NotNull TransferItem transferItem, @NotNull TransferItem oldItem) {
/* 132 */     transferItem.copyTo(oldItem);
/* 133 */     return oldItem;
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   protected UpdateResult createErrorResult(@NotNull String error) {
/* 138 */     return new UpdateResult(error);
/*     */   }
/*     */   
/*     */   private void addUnknownFieldErrors(@NotNull Collection<String> unprocessedFields, @NotNull UpdateResult result) {
/* 142 */     for (String unprocessedField : unprocessedFields) {
/* 143 */       FieldDefinition fieldDefinition = this.translator.getDefinedFieldDefinition(unprocessedField);
/* 144 */       result.addError(unprocessedField, String.format("Field [%s] not updated - Not on screen or unknown.", new Object[] { (fieldDefinition != null) ? fieldDefinition.getLabel() : unprocessedField }));
/*     */     } 
/*     */   }
/*     */   
/*     */   private UpdateResult doInitialTransfer(@NotNull TransferItem transferItem, @NotNull Collection<String> editableFields, @NotNull UpdateIssue issue) {
/*     */     UpdateResult result;
/* 150 */     if (!issue.getUpdate().isEmpty() || (issue.getFields() != null && !issue.getFields().isEmpty())) {
/* 151 */       result = performUpdate(issue, transferItem, editableFields);
/*     */     } else {
/* 153 */       result = UpdateResult.success();
/*     */     } 
/* 155 */     return result;
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private UpdateResult doInitialTransferForVersion(@NotNull TransferItem transferItem, @NotNull Version version, @NotNull Collection<String> updatingFields) {
/*     */     UpdateResult result;
/* 161 */     if (!updatingFields.isEmpty() && (transferItem.getKey()).id != null) {
/* 162 */       result = performUpdateVersion(version, transferItem, updatingFields);
/*     */     } else {
/* 164 */       result = UpdateResult.success();
/*     */     } 
/* 166 */     return result;
/*     */   }
/*     */ 
/*     */   
/*     */   private void addConversionErrors(@NotNull Map<String, String> conversionErrors, @NotNull UpdateResult result) {
/* 171 */     for (Map.Entry<String, String> conversionError : conversionErrors.entrySet()) {
/* 172 */       result.addError(conversionError.getKey(), conversionError.getValue());
/*     */     }
/*     */   }
/*     */   
/*     */   private void updateStatus(@NotNull TransferItem transferItem, @Nullable TransferItem oldItem, @NotNull Collection<String> unprocessedFields, @NotNull UpdateResult result) {
/* 177 */     String status = (String)transferItem.getValue("status");
/* 178 */     if (status != null) {
/* 179 */       unprocessedFields.removeAll(updateStatus(transferItem, oldItem, status, result, unprocessedFields));
/*     */     }
/*     */   }
/*     */   
/*     */   private void removeSpecialFields(@NotNull Collection<String> unprocessedFields) {
/* 184 */     unprocessedFields.remove("timetracking.timeSpent");
/* 185 */     unprocessedFields.remove("relations");
/* 186 */     unprocessedFields.remove("attachments");
/* 187 */     unprocessedFields.remove("comments");
/* 188 */     unprocessedFields.remove("fixVersionIds");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   private UpdateIssue loadContent(@NotNull TransferItem transferItem, @Nullable TransferItem oldItem, @NotNull Map<String, String> conversionErrors, @NotNull Collection<String> editableFields) {
/* 199 */     return this.translator.transferItemToIssue(transferItem, oldItem, editableFields, conversionErrors);
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private Version loadVersionContent(@NotNull TransferItem transferItem, @NotNull Map<String, String> conversionErrors, @NotNull String projecId) {
/* 204 */     return this.translator.transferItemToVersion(transferItem, conversionErrors, projecId);
/*     */   }
/*     */   
/*     */   private UpdateResult performUpdate(UpdateIssue issue, TransferItem transferItem, Collection<String> fields) {
/* 208 */     if (this.log.isDebugEnabled()) {
/* 209 */       ObjectMapper mapper = (new ObjectMapper()).enable(new MapperFeature[] { MapperFeature.BLOCK_UNSAFE_POLYMORPHIC_BASE_TYPES });
/*     */       try {
/* 211 */         this.log.debug("Performing update issue :" + mapper.writeValueAsString(issue));
/* 212 */       } catch (JsonProcessingException e) {
/* 213 */         this.log.error("Error while serializing issue to string", (Throwable)e);
/*     */       } 
/*     */     } 
/* 216 */     return performTransfer(issue, transferItem, fields);
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private UpdateResult performUpdateVersion(@NotNull Version version, @NotNull TransferItem transferItem, @NotNull Collection<String> fields) {
/* 221 */     if (this.log.isDebugEnabled()) {
/* 222 */       ObjectMapper mapper = (new ObjectMapper()).enable(new MapperFeature[] { MapperFeature.BLOCK_UNSAFE_POLYMORPHIC_BASE_TYPES });
/*     */       try {
/* 224 */         this.log.debug("Performing update version :" + mapper.writeValueAsString(version));
/* 225 */       } catch (JsonProcessingException e) {
/* 226 */         this.log.error("Error while serializing version to string", (Throwable)e);
/*     */       } 
/*     */     } 
/* 229 */     return performTransferVersion(version, transferItem, fields);
/*     */   }
/*     */   
/*     */   protected final void addFieldErrors(UpdateResult result, Collection<String> fields, String error) {
/* 233 */     if (error != null) {
/* 234 */       for (String editableField : fields) {
/* 235 */         result.addError(editableField, error);
/*     */       }
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void updateAdditionalAttributes(@NotNull TransferItem transferItem, TransferItem oldItem, UpdateResult result) {
/* 250 */     String id = transferItem.getId();
/*     */     
/* 252 */     CollectionUpdate<JiraComment> comments = (CollectionUpdate<JiraComment>)transferItem.getValue("comments");
/* 253 */     if (comments != null) {
/* 254 */       String addCommentResult = processComments(id, comments.getAdded());
/* 255 */       if (addCommentResult != null) {
/* 256 */         result.addError("comments", addCommentResult);
/*     */       }
/*     */     } 
/*     */     
/* 260 */     CollectionUpdate<Relation> relations = (CollectionUpdate<Relation>)transferItem.getValue("relations");
/* 261 */     if (relations != null) {
/* 262 */       processRelations(id, relations, result);
/*     */     }
/*     */     
/* 265 */     CollectionUpdate<Attachment> attachments = (CollectionUpdate<Attachment>)transferItem.getValue("attachments");
/* 266 */     if (attachments != null) {
/*     */       try {
/* 268 */         UpdateResult addAttachmentResult = processAttachments(id, attachments);
/* 269 */         if (addAttachmentResult.hasError()) {
/* 270 */           result.addError("attachments", addAttachmentResult.getError());
/*     */         }
/* 272 */       } catch (Exception e) {
/* 273 */         this.log.error("Error when processing attachments of the issue with id=" + id, e);
/* 274 */         result.addError("attachments", e.getMessage());
/*     */       } 
/*     */     }
/*     */     
/* 278 */     if (transferItem.getValues().containsKey("timetracking.timeSpent"))
/* 279 */       updateTimeSpent(transferItem, oldItem, result); 
/*     */   }
/*     */   
/*     */   private void updateTimeSpent(@NotNull TransferItem transferItem, @Nullable TransferItem oldItem, @NotNull UpdateResult result) {
/*     */     long additionalTimeSpent;
/* 284 */     Duration timeSpent = (Duration)transferItem.getValue("timetracking.timeSpent");
/* 285 */     long timeSpentMinutes = (timeSpent == null) ? 0L : 
/* 286 */       timeSpent
/* 287 */       .getAbsoluteDuration();
/*     */ 
/*     */ 
/*     */     
/* 291 */     Duration oldTimeSpent = (oldItem == null) ? null : 
/* 292 */       (Duration)oldItem
/* 293 */       .getValue("timetracking.timeSpent");
/* 294 */     if (oldTimeSpent == null) {
/* 295 */       additionalTimeSpent = timeSpentMinutes;
/*     */     } else {
/* 297 */       additionalTimeSpent = timeSpentMinutes - 
/* 298 */         oldTimeSpent.getAbsoluteDuration();
/*     */     } 
/*     */     
/* 301 */     Map<String, String> workLog = new HashMap<>();
/* 302 */     workLog.put("timeSpent", String.valueOf(additionalTimeSpent) + "m");
/*     */     
/* 304 */     if (additionalTimeSpent > 0L) {
/* 305 */       Exception exception2, exception1 = null;
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*     */     }
/* 311 */     else if (additionalTimeSpent < 0L) {
/* 312 */       result.addError("timetracking.timeSpent", "Reducing time spent is not supported.");
/*     */     } 
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private Collection<String> updateStatus(@NotNull TransferItem transferItem, @Nullable TransferItem oldItem, @NotNull String status, @NotNull UpdateResult result, @NotNull Collection<String> unprocessedFields) {
/* 318 */     Exception exception1 = null, exception2 = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*     */     try {
/*     */     
/*     */     } finally {
/* 343 */       exception2 = null; if (exception1 == null) { exception1 = exception2; } else if (exception1 != exception2) { exception1.addSuppressed(exception2); }
/*     */     
/*     */     } 
/*     */   } private UpdateResult processAttachments(@NotNull String issueKey, @NotNull CollectionUpdate<Attachment> allAttachments) {
/* 347 */     UpdateResult result = UpdateResult.success();
/*     */     
/* 349 */     deleteAttachments(issueKey, result, allAttachments.getRemoved());
/* 350 */     addAttachments(issueKey, result, allAttachments.getAdded());
/*     */ 
/*     */     
/* 353 */     deleteAttachments(issueKey, result, allAttachments.getUpdated());
/* 354 */     addAttachments(issueKey, result, allAttachments.getUpdated());
/*     */     
/* 356 */     return result;
/*     */   }
/*     */   
/*     */   private void deleteAttachments(@NotNull String issueKey, @NotNull UpdateResult result, @NotNull Collection<Attachment> removed) {
/* 360 */     if (!removed.isEmpty()) {
/* 361 */       Issue issue = this.jiraProxy.getIssue(issueKey, Collections.singleton("attachments"));
/* 362 */       Collection<Map<String, Object>> foundAttachment = (Collection<Map<String, Object>>)issue.getFields().get("attachment");
/*     */       
/* 364 */       for (Map<String, Object> myAttachment : foundAttachment) {
/* 365 */         for (Attachment searchForAttachment : removed) {
/*     */ 
/*     */           
/* 368 */           if (searchForAttachment.getFileName().equals(
/* 369 */               myAttachment.get("filename"))) {
/* 370 */             Exception exception2, exception1 = null;
/*     */           }
/*     */         } 
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void addAttachments(@NotNull String issueKey, @NotNull UpdateResult result, @NotNull Collection<Attachment> added) {
/* 383 */     if (!added.isEmpty()) {
/* 384 */       Exception exception2; MultiPart multiPartInput = new MultiPart();
/* 385 */       added.stream()
/* 386 */         .map(attachment -> new StreamDataBodyPart("file", attachment.getContent(), encode(attachment.getFileName()), MediaType.APPLICATION_OCTET_STREAM_TYPE))
/* 387 */         .forEach(part -> { 
/* 388 */           }); Exception exception1 = null;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   private String encode(@NotNull String name) {
/*     */     try {
/* 400 */       return MimeUtility.encodeText(name, StandardCharsets.UTF_8.name(), null);
/* 401 */     } catch (UnsupportedEncodingException e) {
/* 402 */       throw new RuntimeException("Unexpected error.", e);
/*     */     } 
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   protected final Collection<String> loadEditableFields(@NotNull Collection<String> allFields, @NotNull Map<String, Field> fields) {
/* 408 */     Collection<String> editableFields = new ArrayList<>();
/* 409 */     for (String key : allFields) {
/* 410 */       Field field = fields.get(this.translator.getJiraFieldId(key));
/* 411 */       if (field != null && field.isEditable()) {
/* 412 */         editableFields.add(key);
/*     */       }
/*     */     } 
/* 415 */     return editableFields;
/*     */   }
/*     */   
/*     */   @Nullable
/*     */   private Transition findTransition(@NotNull String targetStatus, @NotNull Response response) {
/* 420 */     for (Transition transition : ((Transitions)response.readEntity(Transitions.class)).getTransitions()) {
/* 421 */       if (targetStatus.equals(transition.getTo().getName())) {
/* 422 */         return transition;
/*     */       }
/*     */     } 
/* 425 */     return null;
/*     */   }
/*     */   
/*     */   private String processComments(@NotNull String id, @NotNull Collection<JiraComment> updateComments) {
/* 429 */     for (JiraComment comment : updateComments) {
/* 430 */       String result = postComment(id, comment);
/* 431 */       if (result != null) {
/* 432 */         return result;
/*     */       }
/*     */     } 
/* 435 */     return null;
/*     */   }
/*     */   
/*     */   private void processRelations(@NotNull String issueKey, @NotNull CollectionUpdate<Relation> issuelinks, @NotNull UpdateResult result) {
/* 439 */     addRelations(issueKey, result, issuelinks.getAdded());
/* 440 */     deleteRelations(issueKey, result, issuelinks.getRemoved());
/*     */   }
/*     */   
/*     */   protected void deleteRelations(@NotNull String id, @NotNull UpdateResult result, @NotNull Collection<Relation> removedRelation) {
/* 444 */     if (!removedRelation.isEmpty()) {
/* 445 */       Issue issue = this.jiraProxy.getIssue(id, Collections.singleton("relations"));
/* 446 */       Object linksObj = issue.getFields().get("issuelinks");
/* 447 */       if (linksObj instanceof Collection) {
/* 448 */         Collection<?> links = (Collection)linksObj;
/* 449 */         for (Object link : links) {
/* 450 */           JiraRelation relation = (JiraRelation)this.translator.convertToRelation(link, true);
/* 451 */           for (Iterator<Relation> iterator = removedRelation.iterator(); iterator.hasNext(); ) {
/* 452 */             Relation deleted = iterator.next();
/* 453 */             if (!deleted.getRole().equals("LINKED_EPIC") && 
/* 454 */               relation != null && relation.equals(deleted)) {
/* 455 */               Exception exception2; iterator.remove();
/* 456 */               Exception exception1 = null;
/*     */             } 
/*     */           } 
/*     */         } 
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected void addRelations(@NotNull String id, @NotNull UpdateResult result, @NotNull Collection<Relation> addedRelation) {
/* 471 */     if (!addedRelation.isEmpty()) {
/* 472 */       for (Relation addRelation : addedRelation) {
/* 473 */         if (!addRelation.getRole().equals("LINKED_EPIC")) {
/* 474 */           Exception exception2, exception1 = null;
/*     */         }
/*     */       } 
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Nullable
/*     */   private String postComment(@NotNull String targetId, @NotNull JiraComment comment) {
/* 488 */     HashMap<String, Object> commentMap = new HashMap<>();
/* 489 */     commentMap.put("body", comment.getContent());
/* 490 */     Exception exception1 = null, exception2 = null; try {  }
/*     */     finally
/* 492 */     { exception2 = null; if (exception1 == null) { exception1 = exception2; } else if (exception1 != exception2) { exception1.addSuppressed(exception2); }
/*     */        }
/*     */   
/*     */   }
/*     */   
/*     */   @Nullable
/*     */   protected abstract TransferItem loadOldItemIfRequired(@NotNull TransferItem paramTransferItem);
/*     */   
/*     */   @Nullable
/*     */   protected abstract TransferItem loadOldVersionIfRequired(@NotNull TransferItem paramTransferItem);
/*     */   
/*     */   @NotNull
/*     */   protected abstract UpdateResult performTransfer(@NotNull UpdateIssue paramUpdateIssue, @NotNull TransferItem paramTransferItem, @NotNull Collection<String> paramCollection);
/*     */   
/*     */   @NotNull
/*     */   protected abstract UpdateResult performTransferVersion(@NotNull Version paramVersion, @NotNull TransferItem paramTransferItem, @NotNull Collection<String> paramCollection);
/*     */   
/*     */   @Nullable
/*     */   protected abstract String loadEditableFields(@NotNull TransferItem paramTransferItem, @NotNull Collection<String> paramCollection1, @NotNull Collection<String> paramCollection2);
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer.proxy.jira_3.22.1/jira.jar!/com/polarion/synchronizer/proxy/jira/AbstractTransferHelper.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */