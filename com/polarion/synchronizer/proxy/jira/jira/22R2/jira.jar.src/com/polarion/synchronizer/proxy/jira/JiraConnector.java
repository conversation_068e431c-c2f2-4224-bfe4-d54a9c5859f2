/*     */ package com.polarion.synchronizer.proxy.jira;
/*     */ 
/*     */ import com.fasterxml.jackson.databind.MapperFeature;
/*     */ import com.fasterxml.jackson.databind.ObjectMapper;
/*     */ import com.fasterxml.jackson.jaxrs.json.JacksonJaxbJsonProvider;
/*     */ import com.google.common.base.Joiner;
/*     */ import com.polarion.core.config.Configuration;
/*     */ import com.polarion.core.config.ConfigurationException;
/*     */ import com.polarion.core.util.exceptions.ExceptionUtils;
/*     */ import com.polarion.platform.internal.security.UserAccountVault;
/*     */ import com.polarion.synchronizer.SynchronizationException;
/*     */ import com.polarion.synchronizer.proxy.jira.json.CreateMetaData;
/*     */ import com.polarion.synchronizer.proxy.jira.json.PageBeanProject;
/*     */ import com.polarion.synchronizer.proxy.jira.json.Project;
/*     */ import com.polarion.synchronizer.proxy.jira.json.UpdateIssue;
/*     */ import com.polarion.synchronizer.proxy.jira.json.Version;
/*     */ import com.polarion.synchronizer.retryactions.BackoffRetryActionStrategy;
/*     */ import com.polarion.synchronizer.retryactions.IRetryActionStrategy;
/*     */ import com.polarion.synchronizer.retryactions.RetryActionExecutionException;
/*     */ import com.polarion.synchronizer.retryactions.RetryActionExecutor;
/*     */ import java.io.InputStream;
/*     */ import java.util.Arrays;
/*     */ import java.util.Base64;
/*     */ import java.util.Collection;
/*     */ import java.util.HashSet;
/*     */ import java.util.Map;
/*     */ import java.util.Optional;
/*     */ import java.util.stream.Stream;
/*     */ import javax.ws.rs.client.Client;
/*     */ import javax.ws.rs.client.ClientBuilder;
/*     */ import javax.ws.rs.client.Entity;
/*     */ import javax.ws.rs.client.Invocation;
/*     */ import javax.ws.rs.client.WebTarget;
/*     */ import javax.ws.rs.core.Configuration;
/*     */ import javax.ws.rs.core.MediaType;
/*     */ import javax.ws.rs.core.Response;
/*     */ import org.codehaus.jettison.json.JSONArray;
/*     */ import org.codehaus.jettison.json.JSONException;
/*     */ import org.glassfish.jersey.client.ClientConfig;
/*     */ import org.glassfish.jersey.media.multipart.MultiPart;
/*     */ import org.glassfish.jersey.media.multipart.MultiPartFeature;
/*     */ import org.glassfish.jersey.media.multipart.MultiPartMediaTypes;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ import org.jetbrains.annotations.Nullable;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class JiraConnector
/*     */ {
/*     */   private static final String RELATIV_REST_URL = "rest/api/latest/";
/*  79 */   private int pageSize = 1000;
/*     */   
/*  81 */   private int projectPageSize = 50;
/*     */   
/*     */   @NotNull
/*     */   private final String user;
/*     */   
/*     */   @NotNull
/*     */   private final String password;
/*     */   
/*     */   @NotNull
/*     */   private final String serverUrl;
/*     */   
/*     */   @NotNull
/*     */   private final String userURL;
/*     */   
/*     */   @NotNull
/*     */   private final Client client;
/*     */   @NotNull
/*     */   private final WebTarget baseResource;
/*     */   @NotNull
/*     */   private final WebTarget fieldResource;
/*     */   @NotNull
/*     */   private final WebTarget issueResource;
/*     */   @NotNull
/*     */   private final WebTarget projectResource;
/*     */   @NotNull
/*     */   private final WebTarget searchResource;
/*     */   @NotNull
/*     */   private final WebTarget issueTypesResource;
/*     */   @NotNull
/*     */   private final WebTarget versionResource;
/*     */   @NotNull
/*     */   private final WebTarget permissionsResource;
/*     */   @NotNull
/*     */   private final WebTarget serverInfoResource;
/*     */   @NotNull
/*     */   private final RetryActionExecutor<Response> retryActionExecutor;
/*     */   
/*     */   public JiraConnector(@NotNull JiraConnection connection) {
/* 119 */     this.user = connection.getUser();
/* 120 */     this.password = connection.getPassword();
/*     */     
/* 122 */     this.serverUrl = connection.getServerUrl();
/* 123 */     this.userURL = String.valueOf(adjustServerUrl(connection.getServerUrl())) + "jira/people/";
/*     */     
/* 125 */     ObjectMapper mapper = (new ObjectMapper())
/* 126 */       .enable(new MapperFeature[] { MapperFeature.BLOCK_UNSAFE_POLYMORPHIC_BASE_TYPES });
/* 127 */     JacksonJaxbJsonProvider provider = new JacksonJaxbJsonProvider();
/* 128 */     provider.setMapper(mapper);
/*     */ 
/*     */     
/* 131 */     ClientConfig cc = new ClientConfig();
/* 132 */     cc.register(provider);
/* 133 */     cc.property("jersey.config.client.connectTimeout", Integer.valueOf(60000));
/* 134 */     cc.register(MultiPartFeature.class);
/*     */     
/* 136 */     ClientBuilder clientBuilder = ClientBuilder.newBuilder().withConfig((Configuration)cc);
/* 137 */     if (existMutualTlsSettings(this.serverUrl)) {
/* 138 */       MutualTlsConfiguration.updateClientConfig(clientBuilder, createMutualTlsSetting(this.serverUrl));
/*     */     }
/* 140 */     this.client = clientBuilder.build();
/*     */ 
/*     */     
/* 143 */     this.baseResource = this.client.target(String.valueOf(adjustServerUrl(this.serverUrl)) + "rest/api/latest/");
/* 144 */     this.searchResource = this.baseResource.path("search");
/* 145 */     this.fieldResource = this.baseResource.path("field");
/* 146 */     this.issueResource = this.baseResource.path("issue");
/* 147 */     this.projectResource = this.baseResource.path("project");
/* 148 */     this.issueTypesResource = this.baseResource.path("issuetype");
/* 149 */     this.permissionsResource = this.baseResource.path("mypermissions");
/* 150 */     this.serverInfoResource = this.baseResource.path("serverInfo");
/* 151 */     this.versionResource = this.baseResource.path("version");
/*     */     
/* 153 */     this.retryActionExecutor = RetryActionExecutor.newBuilder()
/* 154 */       .with((IRetryActionStrategy)BackoffRetryActionStrategy.newBuilder(Configuration.getInstance().connectors().getConnectorsRetryStrategy()).build())
/* 155 */       .failIf(result -> (result.getStatusInfo().getFamily() != Response.Status.Family.SUCCESSFUL && result.getStatus() != Response.Status.NOT_FOUND.getStatusCode()))
/* 156 */       .beforeRetry(response -> response.ifPresent(Response::close))
/* 157 */       .build();
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   public JSONArray getFields() throws JSONException {
/* 162 */     Exception exception1 = null, exception2 = null; try {
/*     */     
/*     */     } finally {
/* 165 */       exception2 = null; if (exception1 == null) { exception1 = exception2; } else if (exception1 != exception2) { exception1.addSuppressed(exception2); }
/*     */     
/*     */     } 
/*     */   } @Nullable
/*     */   public String checkConnection() {
/*     */     
/* 171 */     try { Exception exception1 = null, exception2 = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/*     */       try {  }
/*     */       finally
/* 193 */       { exception2 = null; if (exception1 == null) { exception1 = exception2; } else if (exception1 != exception2) { exception1.addSuppressed(exception2); }  }  } catch (RetryActionExecutionException ex)
/* 194 */     { Throwable e = ex.getCause();
/*     */ 
/*     */       
/* 197 */       SynchronizationException synchException = (SynchronizationException)ExceptionUtils.findExceptionByType(e, SynchronizationException.class);
/* 198 */       if (synchException != null) {
/* 199 */         return synchException.getLocalizedMessage();
/*     */       }
/* 201 */       return e.getLocalizedMessage(); }
/*     */   
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   private static String adjustServerUrl(@NotNull String adaptServerURL) {
/* 208 */     if (!adaptServerURL.endsWith("/")) {
/* 209 */       adaptServerURL = String.valueOf(adaptServerURL) + "/";
/*     */     }
/* 211 */     return adaptServerURL;
/*     */   }
/*     */   
/*     */   private static boolean existMutualTlsSettings(@NotNull String serverURL) {
/*     */     try {
/* 216 */       String mutualTlsSettings = Configuration.getInstance().connectors().getJiraConnectionCheckMutualTls();
/* 217 */       return (mutualTlsSettings == null) ? false : Arrays.<String>stream(mutualTlsSettings.split(";")).anyMatch(s -> s.startsWith(paramString1));
/* 218 */     } catch (ConfigurationException e) {
/* 219 */       return false;
/*     */     } 
/*     */   }
/*     */   
/*     */   private static boolean checkMutualTlsSetting(@NotNull String[] mutualTlsSettingArray) {
/* 224 */     return (mutualTlsSettingArray.length == 4 && Arrays.<String>stream(mutualTlsSettingArray).noneMatch(String::isEmpty));
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private static MutualTlsSetting createMutualTlsSetting(@NotNull String serverURL) {
/* 229 */     String mutualTlsSettings = Configuration.getInstance().connectors().getJiraConnectionCheckMutualTls();
/* 230 */     if (mutualTlsSettings == null) {
/* 231 */       throw new SynchronizationException("Failed to load Mutual TLS Settings from the polarion configuration properties: com.siemens.polarion.jiraconnector.connectionCheckMutualTls");
/*     */     }
/* 233 */     String mutualTlsSetting = Arrays.<String>stream(mutualTlsSettings.split(";")).filter(s -> s.startsWith(paramString1)).findAny().orElse(null);
/* 234 */     if (mutualTlsSetting == null) {
/* 235 */       throw new SynchronizationException("The Mutual TLS Settings for the server " + serverURL + " does not exist in the polarion configuration properties: com.siemens.polarion.jiraconnector.connectionCheckMutualTls");
/*     */     }
/* 237 */     String[] mutualTlsSettingArray = mutualTlsSetting.split(",");
/*     */     
/* 239 */     if (!checkMutualTlsSetting(mutualTlsSettingArray)) {
/* 240 */       throw new SynchronizationException("Failed to create Mutual TLS Settings for the server " + serverURL + " defined in the polarion configuration properties: com.siemens.polarion.jiraconnector.connectionCheckMutualTls");
/*     */     }
/* 242 */     UserAccountVault.Credentials credential = UserAccountVault.getInstance().tryGetCredentialsForKey(mutualTlsSettingArray[3]);
/* 243 */     if (credential == null) {
/* 244 */       throw new SynchronizationException("Failed to create vault-credential for " + mutualTlsSettingArray[3] + " defined in the polarion configuration properties: com.siemens.polarion.jiraconnector.connectionCheckMutualTls");
/*     */     }
/* 246 */     return new MutualTlsSetting(mutualTlsSettingArray[0], mutualTlsSettingArray[1], mutualTlsSettingArray[2], credential);
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   public InputStream loadAttachmentContent(String contentUrl) {
/* 251 */     WebTarget content = this.client.target(contentUrl);
/* 252 */     Exception exception1 = null, exception2 = null;
/*     */ 
/*     */     
/*     */     try {
/*     */     
/*     */     } finally {
/* 258 */       exception2 = null; if (exception1 == null) { exception1 = exception2; } else if (exception1 != exception2) { exception1.addSuppressed(exception2); }
/*     */     
/*     */     } 
/*     */   } @NotNull
/*     */   public Response addAttachment(@NotNull String issueKey, @NotNull MultiPart multiPartInput) {
/* 263 */     return (Response)this.retryActionExecutor.execute(() -> prepareRequest(this.issueResource.path(paramString).path("attachments")).accept(new MediaType[] { MultiPartMediaTypes.createFormData() }).header("X-Atlassian-Token", "nocheck").post(Entity.entity(paramMultiPart, MultiPartMediaTypes.createFormData())));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public Response removeAttachment(@NotNull String attachmentId) {
/* 271 */     return (Response)this.retryActionExecutor.execute(() -> prepareRequest(this.baseResource.path("attachment").path(paramString)).delete());
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   public String getUserURL() {
/* 276 */     return this.userURL;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Response postRelation(@NotNull Map<String, Object> relation) {
/* 287 */     return (Response)this.retryActionExecutor.execute(() -> prepareRequest(this.baseResource.path("issueLink")).post(Entity.json(paramMap)));
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   public Response removeRelation(int id) {
/* 292 */     return (Response)this.retryActionExecutor.execute(() -> prepareRequest(this.baseResource.path("issueLink").path(String.valueOf(paramInt))).delete());
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   public Response getIssue(@NotNull String issueKey, @NotNull Collection<String> keys) {
/* 297 */     WebTarget webResource = this.issueResource.path(issueKey).queryParam("expand", new Object[] { "renderedFields" });
/* 298 */     if (!keys.isEmpty()) {
/* 299 */       webResource = webResource.queryParam("fields", new Object[] { Joiner.on(",").join(keys) });
/*     */     }
/* 301 */     WebTarget webResourceFinal = webResource;
/*     */     
/* 303 */     return (Response)this.retryActionExecutor.execute(() -> prepareRequest(paramWebTarget).get());
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   public Response getIssuePermissions(@NotNull String issueKey) {
/* 308 */     WebTarget webResource = this.permissionsResource.queryParam("issueKey", new Object[] { issueKey });
/* 309 */     return (Response)this.retryActionExecutor.execute(() -> prepareRequest(paramWebTarget).get());
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   public Collection<String> searchIssues(@NotNull String jqlQuery) {
/* 314 */     Collection<String> issueKeys = new HashSet<>();
/*     */     
/* 316 */     boolean itemsRemaining = true;
/* 317 */     while (itemsRemaining) {
/* 318 */       Exception exception2, exception1 = null;
/*     */     }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 341 */     return issueKeys;
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   public Response createIssue(@NotNull UpdateIssue issue) {
/* 346 */     return (Response)this.retryActionExecutor.execute(() -> prepareRequest(this.issueResource).post(Entity.json(paramUpdateIssue)));
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   public Response updateIssue(@NotNull String id, @NotNull UpdateIssue issue) {
/* 351 */     return (Response)this.retryActionExecutor.execute(() -> prepareRequest(this.issueResource.path(paramString)).put(Entity.json(paramUpdateIssue)));
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   public Response deleteIssue(@NotNull String issueKey) {
/* 356 */     return (Response)this.retryActionExecutor.execute(() -> prepareRequest(this.issueResource.path(paramString)).delete());
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   public Response createVersion(@NotNull Version version) {
/* 361 */     return (Response)this.retryActionExecutor.execute(() -> prepareRequest(this.versionResource).post(Entity.json(paramVersion)));
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   public Response updateVersion(@NotNull String versionId, @NotNull Version version) {
/* 366 */     return (Response)this.retryActionExecutor.execute(() -> prepareRequest(this.versionResource.path(paramString)).put(Entity.json(paramVersion)));
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   public Response deleteVersion(@NotNull String versionId) {
/* 371 */     return (Response)this.retryActionExecutor.execute(() -> prepareRequest(this.versionResource.path(paramString).path("removeAndSwap")).post(Entity.json("{}")));
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   public Response getVersion(@NotNull String versionId) {
/* 376 */     return (Response)this.retryActionExecutor.execute(() -> prepareRequest(this.versionResource.path(paramString)).get());
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   public Response getAllVersionsFromProject(@NotNull String projectKey) {
/* 381 */     return (Response)this.retryActionExecutor.execute(() -> prepareRequest(this.projectResource.path(paramString).path("versions")).get());
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   public Response postComment(@NotNull String issueKey, @NotNull Map<String, Object> commentMap) {
/* 386 */     return (Response)this.retryActionExecutor.execute(() -> prepareRequest(this.issueResource.path(paramString).path("comment")).post(Entity.json(paramMap)));
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   public Response postWorklog(@NotNull String id, @NotNull Map<String, String> workLog) {
/* 391 */     WebTarget workLogResource = this.issueResource.path(id).path("worklog").queryParam("adjustEstimate", new Object[] { "leave" });
/* 392 */     return (Response)this.retryActionExecutor.execute(() -> prepareRequest(paramWebTarget).post(Entity.json(paramMap)));
/*     */   }
/*     */   @NotNull
/*     */   public Optional<Project> searchProjectOnJiraCloud(@NotNull String projectNameOrKey) {
/*     */     
/* 397 */     try { Exception exception1 = null, exception2 = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/*     */       try {  }
/*     */       finally
/* 407 */       { exception2 = null; if (exception1 == null) { exception1 = exception2; } else if (exception1 != exception2) { exception1.addSuppressed(exception2); }  }  } catch (Exception e)
/* 408 */     { throw new SynchronizationException(e); }
/*     */   
/*     */   }
/*     */   @NotNull
/*     */   public Optional<Project> searchProjectOnJiraPremises(@NotNull String projectNameOrKey) {
/*     */     
/* 414 */     try { Exception exception1 = null, exception2 = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/*     */       try {  }
/*     */       finally
/* 422 */       { exception2 = null; if (exception1 == null) { exception1 = exception2; } else if (exception1 != exception2) { exception1.addSuppressed(exception2); }  }  } catch (Exception e)
/* 423 */     { throw new SynchronizationException(e); }
/*     */   
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private Optional<Project> filterProjectOnCloud(@NotNull Response response, @NotNull String projectKeyOrName) {
/* 429 */     PageBeanProject page = (PageBeanProject)response.readEntity(PageBeanProject.class);
/* 430 */     Project[] projects = page.getProjects();
/* 431 */     Optional<Project> foundProject = Stream.<Project>of(projects)
/* 432 */       .filter(jiraProject -> !(!paramString.equals(jiraProject.getKey()) && !paramString.equals(jiraProject.getName())))
/* 433 */       .findFirst();
/* 434 */     String nextPageUrl = page.getNextPageUrl();
/* 435 */     if (foundProject.isEmpty() && !page.isLast() && nextPageUrl != null) {
/* 436 */       return filterProjectOnCloud(getNextProjectPage(nextPageUrl), projectKeyOrName);
/*     */     }
/* 438 */     return foundProject;
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private Optional<Project> filterProjectOnPremises(@NotNull Response response, @NotNull String projectKeyOrName) {
/* 443 */     Project[] projects = (Project[])response.readEntity(Project[].class);
/* 444 */     return Stream.<Project>of(projects)
/* 445 */       .filter(jiraProject -> !(!paramString.equals(jiraProject.getKey()) && !paramString.equals(jiraProject.getName())))
/* 446 */       .findFirst();
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   public Response getNextProjectPage(@NotNull String nextPageUri) {
/* 451 */     return (Response)this.retryActionExecutor.execute(() -> prepareRequest(this.client.target(paramString)).get());
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   public Response getIssueTypes() {
/* 456 */     return (Response)this.retryActionExecutor.execute(() -> prepareRequest(this.issueTypesResource).get());
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   public Response getTransitions(@NotNull String issueKey) {
/* 461 */     return (Response)this.retryActionExecutor.execute(() -> prepareRequest(this.issueResource.path(paramString).path("transitions").queryParam("expand", new Object[] { "transitions.fields" })).get());
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   public Response performTransition(@NotNull String issueKey, @NotNull UpdateIssue update) {
/* 466 */     return (Response)this.retryActionExecutor.execute(() -> prepareRequest(this.issueResource.path(paramString).path("transitions")).post(Entity.json(paramUpdateIssue)));
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   public Response getEditMetadata(@NotNull String issueKey) {
/* 471 */     return (Response)this.retryActionExecutor.execute(() -> prepareRequest(this.issueResource.path(paramString).path("editmeta")).get());
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   public Response getServerInfo() {
/* 476 */     return (Response)this.retryActionExecutor.execute(() -> prepareRequest(this.serverInfoResource).get());
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   public CreateMetaData getCreateMetadata(@NotNull String projectKey) {
/* 481 */     Exception exception1 = null, exception2 = null;
/*     */ 
/*     */ 
/*     */     
/*     */     try {
/*     */     
/*     */     } finally {
/* 488 */       exception2 = null; if (exception1 == null) { exception1 = exception2; } else if (exception1 != exception2) { exception1.addSuppressed(exception2); }
/*     */     
/*     */     } 
/*     */   }
/*     */   @NotNull
/*     */   private Invocation.Builder prepareRequest(@NotNull WebTarget setHeader) {
/* 494 */     return prepareRequest(setHeader, this.user, this.password);
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private Invocation.Builder prepareRequest(@NotNull WebTarget resource, @NotNull String user, @NotNull String password) {
/* 499 */     return resource.request()
/* 500 */       .header("Authorization", "Basic " + new String(Base64.getEncoder().encode((String.valueOf(user) + ":" + password).getBytes()))).accept(new String[] { "application/json" });
/*     */   }
/*     */   
/*     */   public void setPageSize(int pageSize) {
/* 504 */     this.pageSize = pageSize;
/*     */   }
/*     */   
/*     */   public void setProjectPageSize(int projectPageSize) {
/* 508 */     this.projectPageSize = projectPageSize;
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer.proxy.jira_3.22.1/jira.jar!/com/polarion/synchronizer/proxy/jira/JiraConnector.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */