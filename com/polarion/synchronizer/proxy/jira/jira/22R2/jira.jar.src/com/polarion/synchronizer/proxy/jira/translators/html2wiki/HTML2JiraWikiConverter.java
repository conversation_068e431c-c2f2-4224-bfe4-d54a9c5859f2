/*     */ package com.polarion.synchronizer.proxy.jira.translators.html2wiki;
/*     */ 
/*     */ import com.polarion.alm.tracker.ITestManagementService;
/*     */ import com.polarion.synchronizer.ILogger;
/*     */ import com.polarion.synchronizer.proxy.htmltranslator.ElementInfo;
/*     */ import com.polarion.synchronizer.proxy.htmltranslator.OpenCloseTag;
/*     */ import com.polarion.synchronizer.proxy.htmltranslator.RecursiveTag;
/*     */ import com.polarion.synchronizer.proxy.htmltranslator.Tag;
/*     */ import com.polarion.synchronizer.proxy.polarion.IPolarionProxy;
/*     */ import java.util.ArrayDeque;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Collections;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ import org.jsoup.Jsoup;
/*     */ import org.jsoup.nodes.Document;
/*     */ import org.jsoup.nodes.Element;
/*     */ import org.jsoup.nodes.Node;
/*     */ import org.jsoup.nodes.TextNode;
/*     */ import org.jsoup.safety.Cleaner;
/*     */ import org.jsoup.safety.Whitelist;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HTML2JiraWikiConverter
/*     */ {
/*     */   @NotNull
/*  50 */   private final String space = " ";
/*     */   
/*     */   @NotNull
/*     */   private final IPolarionProxy polarionProxy;
/*     */   @NotNull
/*     */   private final ITestManagementService testManagementService;
/*     */   @NotNull
/*     */   private final String baseUrl;
/*     */   @NotNull
/*     */   private final Map<String, Tag> tagMap;
/*     */   @NotNull
/*     */   private final Map<String, String> styles;
/*     */   @NotNull
/*     */   private ILogger logger;
/*     */   
/*     */   public HTML2JiraWikiConverter(@NotNull IPolarionProxy polarionProxy, @NotNull ITestManagementService testManagementService, @NotNull String baseUrl, @NotNull ILogger logger) {
/*  66 */     this.polarionProxy = polarionProxy;
/*  67 */     this.testManagementService = testManagementService;
/*  68 */     this.baseUrl = baseUrl;
/*  69 */     this.tagMap = new HashMap<>();
/*  70 */     this.styles = new HashMap<>();
/*  71 */     this.logger = logger;
/*     */     
/*  73 */     init();
/*     */   }
/*     */   
/*     */   private void init() {
/*  77 */     this.tagMap.put("img", new ImgTag());
/*  78 */     this.tagMap.put("br", new BrTag());
/*  79 */     this.tagMap.put("strong", new OpenCloseTag("*", "*"));
/*  80 */     this.tagMap.put("b", new OpenCloseTag("*", "*"));
/*  81 */     this.tagMap.put("span", new SpanTag(new JiraMacroLinkConverter(this.polarionProxy, this.testManagementService, this.baseUrl, this.logger)));
/*  82 */     this.tagMap.put("tr", new OpenCloseTag("", "\n"));
/*  83 */     this.tagMap.put("table", new OpenCloseTag("\n", "\n"));
/*  84 */     this.tagMap.put("tbody", new OpenCloseTag("", ""));
/*  85 */     this.tagMap.put("th", new ThTdTag("||"));
/*  86 */     this.tagMap.put("td", new ThTdTag("|"));
/*  87 */     this.tagMap.put("ul", new UlTag());
/*  88 */     this.tagMap.put("ol", new OlTag());
/*  89 */     this.tagMap.put("li", new LiTag());
/*  90 */     this.tagMap.put("p", new PTag());
/*  91 */     this.tagMap.put("a", new ATag());
/*  92 */     this.tagMap.put("sup", new MultiLineTag("^"));
/*  93 */     this.tagMap.put("sub", new MultiLineTag("~"));
/*  94 */     this.tagMap.put("blockquote", new OpenCloseTag("{quote}", "{quote}"));
/*  95 */     this.tagMap.put("div", new DivTag());
/*     */     
/*  97 */     this.styles.put("bold", "*");
/*  98 */     this.styles.put("italic", "_");
/*  99 */     this.styles.put("underline", "+");
/* 100 */     this.styles.put("line-through", "-");
/* 101 */     this.styles.put("color", "color");
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public String convert(@NotNull String source) {
/* 107 */     StringBuilder sb = new StringBuilder();
/* 108 */     Whitelist whitelist = 
/* 109 */       Whitelist.simpleText()
/* 110 */       .addTags(new String[] { "span", "br", "img", "table", "th", "tr", "td", "ul", "ol", "li", "p", "a", "sub", "sup", "div", "blockquote" }).addProtocols("img", "src", new String[] { "http", "https"
/* 111 */         }).addAttributes(":all", new String[] { "style", "src", "href"
/* 112 */         }).addAttributes("span", new String[] { "class", "data-type", "data-item-id", "data-revision" });
/* 113 */     String cleanHTML = Jsoup.clean(source, this.baseUrl, whitelist, (new Document.OutputSettings()).prettyPrint(false));
/* 114 */     parse((new Cleaner(whitelist)).clean(Jsoup.parse(cleanHTML, this.baseUrl)).body(), sb);
/* 115 */     return sb.toString();
/*     */   }
/*     */   
/*     */   private void parse(@NotNull Element parentElement, @NotNull StringBuilder sb) {
/* 119 */     ArrayDeque<Tag> openTags = new ArrayDeque<>();
/* 120 */     List<JiraRichTextPart> parts = new ArrayList<>();
/* 121 */     for (Node node : parentElement.childNodes()) {
/* 122 */       StringBuilder collectedStyles = new StringBuilder("");
/* 123 */       parts.addAll(parseNode(node, collectedStyles, parts, openTags));
/*     */     } 
/*     */     
/* 126 */     for (int i = 0; i < parts.size(); i++) {
/* 127 */       JiraRichTextPart currentText = parts.get(i);
/* 128 */       JiraRichTextPart previousText = (i > 0) ? parts.get(i - 1) : null;
/* 129 */       JiraRichTextPart nextText = (i < parts.size() - 1) ? parts.get(i + 1) : null;
/*     */       
/* 131 */       if (!currentText.getText().equals(" ") || (previousText != null && previousText.isSpan() && nextText != null && nextText.isSpan())) {
/*     */ 
/*     */ 
/*     */         
/* 135 */         boolean shouldFormat = !(previousText != null && !startsWithWhitespace(currentText) && !endsWithWhitespace(previousText));
/* 136 */         shouldFormat = (shouldFormat && (nextText == null || endsWithWhitespace(currentText) || startsWithWhitespace(nextText)));
/* 137 */         String text = currentText.getFormattedText(shouldFormat);
/*     */         
/* 139 */         if (sb.toString().endsWith("\n") && text.startsWith(" ")) {
/* 140 */           text = text.substring(1);
/*     */         }
/*     */         
/* 143 */         sb.append(text);
/*     */       } 
/*     */     } 
/*     */   }
/*     */   
/*     */   private boolean startsWithWhitespace(@NotNull JiraRichTextPart jiraText) {
/* 149 */     return isDelimiter(jiraText.getText().charAt(0));
/*     */   }
/*     */   
/*     */   private boolean endsWithWhitespace(@NotNull JiraRichTextPart jiraText) {
/* 153 */     String text = jiraText.getText();
/* 154 */     return isDelimiter(text.charAt(text.length() - 1));
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private List<JiraRichTextPart> parseNode(@NotNull Node node, @NotNull StringBuilder styles, @NotNull List<JiraRichTextPart> collected, @NotNull ArrayDeque<Tag> openTags) {
/* 159 */     List<JiraRichTextPart> parts = new ArrayList<>();
/*     */     
/* 161 */     if (node instanceof TextNode) {
/* 162 */       TextNode textNode = (TextNode)node;
/* 163 */       String textNodeText = textNode.text();
/*     */       
/* 165 */       boolean isInSpan = ((Element)node.parent()).tag().getName().equals("span");
/* 166 */       boolean isLinkText = ((Element)node.parent()).tag().getName().equals("a");
/*     */       
/* 168 */       textNodeText = textNodeText.replaceAll(" ", " ");
/* 169 */       if (!textNodeText.equals(" ") || (textNodeText.equals(" ") && isInSpan)) {
/* 170 */         textNodeText = stripText(textNodeText);
/* 171 */         if (!isLinkText) {
/* 172 */           textNodeText = escapeSpecialCharacters(textNodeText);
/*     */         }
/*     */       } 
/* 175 */       if (!textNodeText.isEmpty()) {
/* 176 */         parts.add(new JiraRichTextPart(textNodeText, styles, isInSpan ? ((Element)node.parent()).tag().getName() : null));
/*     */       }
/*     */     }
/* 179 */     else if (node instanceof Element) {
/* 180 */       Element element = (Element)node;
/* 181 */       Tag tag = this.tagMap.get(element.tag().getName());
/* 182 */       if (tag != null) {
/* 183 */         if (element.attr("style") != null) {
/* 184 */           String style = element.attr("style");
/* 185 */           styles = updateStyles(style, styles);
/*     */         } 
/* 187 */         if (tag instanceof RecursiveTag && !collected.isEmpty()) {
/* 188 */           ((RecursiveTag)tag).setPreviousText(((JiraRichTextPart)collected.get(collected.size() - 1)).getText());
/*     */         }
/*     */         
/* 191 */         parts.addAll(processTag(node, styles, collected, openTags, tag));
/*     */       } 
/*     */     } 
/*     */     
/* 195 */     return parts;
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private List<JiraRichTextPart> processTag(@NotNull Node node, @NotNull StringBuilder styles, @NotNull List<JiraRichTextPart> collected, @NotNull ArrayDeque<Tag> openTags, @NotNull Tag tag) {
/* 200 */     List<JiraRichTextPart> parts = new ArrayList<>();
/* 201 */     Element element = (Element)node;
/*     */     
/* 203 */     StringBuilder openText = new StringBuilder();
/* 204 */     ElementInfo elementInfo = new ElementInfo(Collections.emptyMap(), element.tag().getName());
/* 205 */     tag.open(element, elementInfo, openText, Collections.unmodifiableCollection(openTags));
/*     */     
/* 207 */     StringBuilder processedText = new StringBuilder();
/* 208 */     openTags.push(tag);
/* 209 */     tag.process(element, elementInfo, processedText, Collections.unmodifiableCollection(openTags));
/*     */     
/* 211 */     if (tag.isContinue()) {
/* 212 */       if (openText.length() > 0) {
/* 213 */         parts.add(new JiraRichTextPart(openText.toString(), null, element.tag().getName()));
/*     */       }
/* 215 */       if (processedText.length() > 0) {
/* 216 */         parts.add(new JiraRichTextPart(processedText.toString(), null, element.tag().getName()));
/*     */       }
/*     */       
/* 219 */       parts.addAll(parseContinueNode(node, styles, collected, openTags, tag, openText));
/*     */     } else {
/* 221 */       StringBuilder closeText = new StringBuilder();
/* 222 */       tag.close(element, elementInfo, closeText, Collections.unmodifiableCollection(openTags));
/* 223 */       openText.append(processedText.toString()).append(closeText.toString());
/* 224 */       if (openText.length() > 0) {
/* 225 */         parts.add(new JiraRichTextPart(openText.toString(), null, element.tag().getName()));
/*     */       }
/*     */     } 
/* 228 */     openTags.pop();
/* 229 */     return parts;
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private List<JiraRichTextPart> parseContinueNode(@NotNull Node node, @NotNull StringBuilder styles, @NotNull List<JiraRichTextPart> collected, @NotNull ArrayDeque<Tag> openTags, @NotNull Tag tag, @NotNull StringBuilder openText) {
/* 234 */     List<JiraRichTextPart> parts = new ArrayList<>();
/* 235 */     Element element = (Element)node;
/* 236 */     ElementInfo elementInfo = new ElementInfo(Collections.emptyMap(), element.tag().getName());
/* 237 */     StringBuilder closeText = new StringBuilder();
/*     */     
/* 239 */     List<JiraRichTextPart> children = new ArrayList<>();
/*     */     
/* 241 */     for (Node child : node.childNodes()) {
/* 242 */       children.addAll(parseNode(child, styles, collected, openTags));
/*     */     }
/*     */     
/* 245 */     if (tag instanceof ATag) {
/* 246 */       ((ATag)tag).setChildren(children);
/*     */     } else {
/* 248 */       if (tag instanceof MultiLineTag) {
/* 249 */         children.forEach(part -> part.setText(String.valueOf(part.getText()) + paramStringBuilder.toString()));
/*     */       }
/* 251 */       parts.addAll(children);
/*     */     } 
/*     */     
/* 254 */     tag.close(element, elementInfo, closeText, Collections.unmodifiableCollection(openTags));
/* 255 */     if (closeText.length() > 0) {
/* 256 */       parts.add(new JiraRichTextPart(closeText.toString(), null, element.tag().getName()));
/*     */     }
/* 258 */     return parts;
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private StringBuilder updateStyles(@NotNull String styleAttr, @NotNull StringBuilder currentStyles) {
/* 263 */     String currentStyle = currentStyles.toString();
/*     */     
/* 265 */     String[] styleAttrs = styleAttr.split(";");
/* 266 */     if (styleAttrs.length > 0) {
/* 267 */       byte b; int i; String[] arrayOfString; for (i = (arrayOfString = styleAttrs).length, b = 0; b < i; ) { String style = arrayOfString[b];
/* 268 */         if (!style.trim().isEmpty()) {
/* 269 */           String[] styleValue = style.split(":");
/* 270 */           String value = styleValue[1].trim();
/* 271 */           if (value.contains(" ")) {
/* 272 */             byte b1; int j; String[] arrayOfString1; for (j = (arrayOfString1 = value.split(" ")).length, b1 = 0; b1 < j; ) { String v = arrayOfString1[b1];
/* 273 */               String st = this.styles.get(v);
/* 274 */               if (st != null && !currentStyle.contains(st))
/* 275 */                 currentStyle = String.valueOf(currentStyle) + st; 
/*     */               b1++; }
/*     */           
/* 278 */           } else if (this.styles.get(value) != null && !currentStyle.contains(this.styles.get(value))) {
/* 279 */             currentStyle = String.valueOf(currentStyle) + (String)this.styles.get(value);
/*     */           } 
/*     */           
/* 282 */           if ("color".equals(styleValue[0].trim()))
/* 283 */             currentStyle = String.valueOf(currentStyle) + "{color:" + value + "}"; 
/*     */         } 
/*     */         b++; }
/*     */     
/*     */     } 
/* 288 */     return new StringBuilder(currentStyle);
/*     */   }
/*     */   
/*     */   private boolean isDelimiter(char c) {
/* 292 */     switch (c) {
/*     */       case '\t':
/*     */       case '\n':
/*     */       case '\r':
/*     */       case ' ':
/*     */       case '!':
/*     */       case '\\':
/*     */       case '|':
/*     */       case '}':
/* 301 */         return true;
/*     */     } 
/* 303 */     return false;
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private String escapeSpecialCharacters(@NotNull String text) {
/* 308 */     String result = text;
/* 309 */     result = result.replace("\\", "&#92;");
/* 310 */     result = result.replace("*", "\\*");
/* 311 */     result = result.replace("_", "\\_");
/* 312 */     result = result.replace("??", "\\??");
/* 313 */     result = result.replace("-", "\\-");
/* 314 */     result = result.replace("+", "\\+");
/* 315 */     result = result.replace("^", "\\^");
/* 316 */     result = result.replace("~", "\\~");
/* 317 */     result = result.replace("[", "\\[").replace("]", "\\]");
/* 318 */     return result.replace("{", "\\{").replace("}", "\\}");
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private String stripText(@NotNull String text) {
/* 323 */     int length = text.length();
/* 324 */     char[] oldChars = new char[length + 1];
/* 325 */     text.getChars(0, length, oldChars, 0);
/* 326 */     oldChars[length] = Character.MIN_VALUE;
/* 327 */     int newLen = -1; do {  }
/* 328 */     while (oldChars[++newLen] >= ' ');
/*     */ 
/*     */ 
/*     */     
/* 332 */     for (int j = newLen; j < length; j++) {
/* 333 */       char ch = oldChars[j];
/* 334 */       if (ch >= ' ') {
/* 335 */         oldChars[newLen] = ch;
/* 336 */         newLen++;
/*     */       } 
/*     */     } 
/* 339 */     return new String(oldChars, 0, newLen);
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer.proxy.jira_3.22.1/jira.jar!/com/polarion/synchronizer/proxy/jira/translators/html2wiki/HTML2JiraWikiConverter.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */