/*    */ package com.polarion.synchronizer.proxy.jira.json;
/*    */ 
/*    */ import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
/*    */ import com.fasterxml.jackson.annotation.JsonInclude;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ import org.jetbrains.annotations.Nullable;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @JsonIgnoreProperties(ignoreUnknown = true)
/*    */ @JsonInclude(JsonInclude.Include.NON_NULL)
/*    */ public class Option
/*    */ {
/*    */   private String id;
/*    */   @Nullable
/*    */   private String name;
/*    */   
/*    */   @Deprecated
/*    */   public Option() {}
/*    */   
/*    */   public Option(@Nullable Object value) {
/* 47 */     setValue(value);
/*    */   }
/*    */   
/*    */   public Option(@Nullable String id, @Nullable String name) {
/* 51 */     this.id = id;
/* 52 */     this.name = name;
/*    */   }
/*    */   
/*    */   protected void setValue(@Nullable Object value) {
/* 56 */     if (value == null) {
/* 57 */       this.id = "-1";
/* 58 */       this.name = "";
/*    */     } else {
/* 60 */       this.id = null;
/* 61 */       this.name = value.toString();
/*    */     } 
/*    */   }
/*    */   
/*    */   protected void setKey(@Nullable Object value) {
/* 66 */     if (value == null) {
/* 67 */       this.id = "-1";
/* 68 */       this.name = "";
/*    */     } else {
/* 70 */       this.id = value.toString();
/* 71 */       this.name = null;
/*    */     } 
/*    */   }
/*    */   
/*    */   @NotNull
/*    */   public static Option withValue(@Nullable Object value, boolean custom) {
/* 77 */     return custom ? new CustomOption(value) : new Option(value);
/*    */   }
/*    */   
/*    */   @NotNull
/*    */   public static Option withKey(@Nullable Object value, boolean custom) {
/* 82 */     Option option = custom ? new CustomOption(null) : new Option(null);
/* 83 */     option.setKey(value);
/* 84 */     return option;
/*    */   }
/*    */   
/*    */   public String getId() {
/* 88 */     return this.id;
/*    */   }
/*    */   
/*    */   @Nullable
/*    */   public String getName() {
/* 93 */     return this.name;
/*    */   }
/*    */ 
/*    */   
/*    */   public String toString() {
/* 98 */     return "Option [id=" + this.id + ", name=" + this.name + "]";
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer.proxy.jira_3.22.1/jira.jar!/com/polarion/synchronizer/proxy/jira/json/Option.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */