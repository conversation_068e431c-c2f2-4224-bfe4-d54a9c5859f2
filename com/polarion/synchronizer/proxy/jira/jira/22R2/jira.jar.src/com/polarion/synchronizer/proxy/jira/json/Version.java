/*    */ package com.polarion.synchronizer.proxy.jira.json;
/*    */ 
/*    */ import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
/*    */ import com.fasterxml.jackson.annotation.JsonProperty;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ import org.jetbrains.annotations.Nullable;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @JsonIgnoreProperties(ignoreUnknown = true)
/*    */ public class Version
/*    */ {
/*    */   @NotNull
/*    */   private String name;
/*    */   @NotNull
/*    */   private String projectId;
/*    */   
/*    */   public Version(@JsonProperty("name") @NotNull String name, @JsonProperty("projectId") @NotNull String projectId) {
/* 21 */     this.name = name;
/* 22 */     this.projectId = projectId; } @Nullable
/*    */   private String id; @Nullable
/*    */   private String startDate; @Nullable
/*    */   private String releaseDate; @Nullable
/* 26 */   public String getId() { return this.id; }
/*    */ 
/*    */   
/*    */   public void setId(@Nullable String id) {
/* 30 */     this.id = id;
/*    */   }
/*    */   @NotNull
/*    */   public String getName() {
/* 34 */     return this.name;
/*    */   }
/*    */   @Nullable
/*    */   public String getStartDate() {
/* 38 */     return this.startDate;
/*    */   }
/*    */   
/*    */   public void setStartDate(@Nullable String startDate) {
/* 42 */     this.startDate = startDate;
/*    */   }
/*    */   @Nullable
/*    */   public String getReleaseDate() {
/* 46 */     return this.releaseDate;
/*    */   }
/*    */   
/*    */   public void setReleaseDate(@Nullable String releaseDate) {
/* 50 */     this.releaseDate = releaseDate;
/*    */   }
/*    */   @Nullable
/*    */   public String getProjectId() {
/* 54 */     return this.projectId;
/*    */   }
/*    */ 
/*    */   
/*    */   public String toString() {
/* 59 */     return "Version [ id=" + this.id + ", name=" + this.name + ",projectId=" + this.projectId + ", toString()=" + super.toString() + "]";
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer.proxy.jira_3.22.1/jira.jar!/com/polarion/synchronizer/proxy/jira/json/Version.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */