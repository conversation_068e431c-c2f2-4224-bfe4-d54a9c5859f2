/*    */ package com.polarion.synchronizer.proxy.jira;
/*    */ 
/*    */ import com.google.inject.Inject;
/*    */ import com.polarion.synchronizer.IProxyConfiguration;
/*    */ import com.polarion.synchronizer.ISynchronizationContext;
/*    */ import com.polarion.synchronizer.configuration.IConnection;
/*    */ import com.polarion.synchronizer.model.IProxy;
/*    */ import com.polarion.synchronizer.model.IProxyFactory;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ import org.jetbrains.annotations.Nullable;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class JiraProxyFactory
/*    */   implements IProxyFactory
/*    */ {
/*    */   private final ISynchronizationContext context;
/*    */   
/*    */   @Inject
/*    */   public JiraProxyFactory(ISynchronizationContext context) {
/* 40 */     this.context = context;
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   public IProxy createProxy(@NotNull IProxyConfiguration<? extends IConnection> configuration) {
/* 46 */     JiraProxyConfiguration jiraConfiguration = (JiraProxyConfiguration)configuration;
/* 47 */     return new JiraProxy(jiraConfiguration, this.context.getLogger());
/*    */   }
/*    */ 
/*    */   
/*    */   @Nullable
/*    */   public String checkConnection(@NotNull IConnection connection) {
/* 53 */     String configurationErrors = ((JiraConnection)connection).check();
/* 54 */     if (configurationErrors != null) {
/* 55 */       return configurationErrors;
/*    */     }
/* 57 */     JiraConnector connector = new JiraConnector((JiraConnection)connection);
/* 58 */     return connector.checkConnection();
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer.proxy.jira_3.22.1/jira.jar!/com/polarion/synchronizer/proxy/jira/JiraProxyFactory.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */