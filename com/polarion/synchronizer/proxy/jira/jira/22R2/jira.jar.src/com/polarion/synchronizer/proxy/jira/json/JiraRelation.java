/*    */ package com.polarion.synchronizer.proxy.jira.json;
/*    */ 
/*    */ import com.polarion.synchronizer.model.Relation;
/*    */ 
/*    */ public class JiraRelation
/*    */   extends Relation
/*    */ {
/*    */   private int id;
/*    */   
/*    */   public JiraRelation(int id, String role, String targetId) {
/* 11 */     super(role, targetId);
/* 12 */     this.id = id;
/*    */   }
/*    */   
/*    */   public int getId() {
/* 16 */     return this.id;
/*    */   }
/*    */ 
/*    */   
/*    */   public int hashCode() {
/* 21 */     int prime = 31;
/* 22 */     int result = super.hashCode();
/* 23 */     result = 31 * result + this.id;
/* 24 */     return result;
/*    */   }
/*    */ 
/*    */   
/*    */   public boolean equals(Object obj) {
/* 29 */     if (this == obj) {
/* 30 */       return true;
/*    */     }
/* 32 */     Relation other = (Relation)obj;
/* 33 */     if (!getRole().equals(other.getRole())) {
/* 34 */       return false;
/*    */     }
/* 36 */     if (!getTargetId().equals(other.getTargetId())) {
/* 37 */       return false;
/*    */     }
/*    */     
/* 40 */     return true;
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer.proxy.jira_3.22.1/jira.jar!/com/polarion/synchronizer/proxy/jira/json/JiraRelation.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */