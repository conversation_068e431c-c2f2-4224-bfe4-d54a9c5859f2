/*    */ package com.polarion.synchronizer.proxy.jira.json;
/*    */ 
/*    */ import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
/*    */ import org.jetbrains.annotations.Nullable;
/*    */ 
/*    */ 
/*    */ @JsonIgnoreProperties(ignoreUnknown = true)
/*    */ public class IssueType
/*    */ {
/*    */   @Nullable
/*    */   private String name;
/*    */   private boolean subtask;
/*    */   @Nullable
/*    */   private Scope scope;
/*    */   
/*    */   public String getName() {
/* 17 */     return this.name;
/*    */   }
/*    */   
/*    */   public boolean isSubtask() {
/* 21 */     return this.subtask;
/*    */   }
/*    */   @Nullable
/*    */   public Scope getScope() {
/* 25 */     return this.scope;
/*    */   }
/*    */ 
/*    */   
/*    */   public String toString() {
/* 30 */     return "IssueType [name=" + this.name + ", subtask=" + this.subtask + "]";
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer.proxy.jira_3.22.1/jira.jar!/com/polarion/synchronizer/proxy/jira/json/IssueType.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */