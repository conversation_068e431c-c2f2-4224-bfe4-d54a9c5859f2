/*     */ package com.polarion.synchronizer.proxy.jira;
/*     */ 
/*     */ import com.polarion.synchronizer.ILogger;
/*     */ import com.polarion.synchronizer.model.TransferItem;
/*     */ import com.polarion.synchronizer.model.UpdateResult;
/*     */ import com.polarion.synchronizer.proxy.jira.json.Issue;
/*     */ import com.polarion.synchronizer.proxy.jira.json.UpdateIssue;
/*     */ import com.polarion.synchronizer.proxy.jira.json.Version;
/*     */ import java.util.Collection;
/*     */ import java.util.Map;
/*     */ import javax.ws.rs.core.Response;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ import org.jetbrains.annotations.Nullable;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class UpdateHelper
/*     */   extends AbstractTransferHelper
/*     */ {
/*     */   public UpdateHelper(@NotNull JiraProxy jiraProxy, @NotNull JiraConnector jiraConnector, @NotNull JiraProxyTranslator translator, @NotNull ILogger log) {
/*  43 */     super(jiraProxy, jiraConnector, translator, log);
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   protected UpdateResult performTransfer(@NotNull UpdateIssue issue, @NotNull TransferItem transferItem, @NotNull Collection<String> includedFields) {
/*  49 */     Exception exception1 = null, exception2 = null;
/*     */     
/*     */     try {
/*     */     
/*     */     } finally {
/*  54 */       exception2 = null; if (exception1 == null) { exception1 = exception2; } else if (exception1 != exception2) { exception1.addSuppressed(exception2); }
/*     */     
/*     */     } 
/*     */   }
/*     */   @Nullable
/*     */   protected String loadEditableFields(@NotNull TransferItem transferItem, @NotNull Collection<String> allFields, @NotNull Collection<String> result) {
/*  60 */     Exception exception1 = null, exception2 = null;
/*     */ 
/*     */ 
/*     */     
/*     */     try {
/*     */     
/*     */     } finally {
/*  67 */       exception2 = null; if (exception1 == null) { exception1 = exception2; } else if (exception1 != exception2) { exception1.addSuppressed(exception2); }
/*     */     
/*     */     } 
/*     */   } @Nullable
/*     */   private String checkUpdateResponse(@NotNull Map<String, ? extends Object> issueContent, @NotNull Response response) {
/*  72 */     if (response.getStatus() == 204 || response.getStatus() == 201 || 
/*  73 */       response.getStatus() == 200) {
/*  74 */       return null;
/*     */     }
/*  76 */     return "Status " + response.getStatus() + ": " + 
/*  77 */       this.translator.fixErrorMessages(issueContent, (String)response.readEntity(String.class));
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   @Nullable
/*     */   protected TransferItem loadOldItemIfRequired(@NotNull TransferItem transferItem) {
/*  84 */     TransferItem oldItem = null;
/*  85 */     Collection<String> requiredOldValues = this.translator.requiredOldValues(transferItem);
/*  86 */     if (!requiredOldValues.isEmpty()) {
/*  87 */       Issue oldIssue = this.jiraProxy.getIssue(transferItem.getId(), requiredOldValues);
/*  88 */       oldItem = this.translator.issueToTransferItem(oldIssue, requiredOldValues);
/*     */     } 
/*  90 */     return oldItem;
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   protected UpdateResult createErrorResult(@NotNull String error) {
/*  96 */     return new UpdateResult(error);
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   protected UpdateResult performTransferVersion(@NotNull Version version, @NotNull TransferItem transferItem, @NotNull Collection<String> fields) {
/* 102 */     Response response = this.jiraConnector.updateVersion(transferItem.getId(), version);
/* 103 */     String error = checkUpdateResponse(transferItem.getValues(), response);
/* 104 */     UpdateResult result = UpdateResult.success();
/* 105 */     addFieldErrors(result, fields, error);
/* 106 */     return result;
/*     */   }
/*     */ 
/*     */   
/*     */   @Nullable
/*     */   protected TransferItem loadOldVersionIfRequired(@NotNull TransferItem transferItem) {
/* 112 */     TransferItem oldItem = null;
/* 113 */     if (transferItem.getId() != null) {
/* 114 */       Version oldVersion = this.jiraProxy.getVersion(transferItem.getId());
/* 115 */       oldItem = this.translator.versionToTransferItem(oldVersion, JiraProxy.VERSION_KEYS);
/*     */     } 
/* 117 */     return oldItem;
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer.proxy.jira_3.22.1/jira.jar!/com/polarion/synchronizer/proxy/jira/UpdateHelper.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */