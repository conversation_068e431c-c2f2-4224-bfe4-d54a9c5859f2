/*     */ package com.polarion.synchronizer.proxy.jira;
/*     */ 
/*     */ import com.polarion.core.util.ObjectUtils;
/*     */ import com.polarion.platform.i18n.Localization;
/*     */ import com.polarion.synchronizer.ILogger;
/*     */ import com.polarion.synchronizer.SynchronizationException;
/*     */ import com.polarion.synchronizer.model.Duration;
/*     */ import com.polarion.synchronizer.model.FieldDefinition;
/*     */ import com.polarion.synchronizer.model.IProxy;
/*     */ import com.polarion.synchronizer.model.Relation;
/*     */ import com.polarion.synchronizer.model.TransferItem;
/*     */ import com.polarion.synchronizer.proxy.jira.json.Issue;
/*     */ import com.polarion.synchronizer.proxy.jira.json.JiraRelation;
/*     */ import com.polarion.synchronizer.proxy.jira.json.UpdateIssue;
/*     */ import com.polarion.synchronizer.proxy.jira.json.Version;
/*     */ import com.polarion.synchronizer.proxy.jira.model.JiraComment;
/*     */ import java.text.ParseException;
/*     */ import java.text.SimpleDateFormat;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Arrays;
/*     */ import java.util.Collection;
/*     */ import java.util.Collections;
/*     */ import java.util.Date;
/*     */ import java.util.HashMap;
/*     */ import java.util.HashSet;
/*     */ import java.util.LinkedHashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import java.util.Optional;
/*     */ import java.util.stream.Collectors;
/*     */ import org.codehaus.jettison.json.JSONArray;
/*     */ import org.codehaus.jettison.json.JSONException;
/*     */ import org.codehaus.jettison.json.JSONObject;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ import org.jetbrains.annotations.Nullable;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class JiraProxyTranslator
/*     */   implements IMetadataHelper
/*     */ {
/*     */   private static final String CASCADINGSELECT = "com.atlassian.jira.plugin.system.customfieldtypes:cascadingselect";
/*     */   private static final String JIRA_SPRINT_CUSTOM_TYPE = "com.pyxis.greenhopper.jira:gh-sprint";
/*     */   private static final String JIRA_PROJECT_CUSTOM_TYPE = "com.atlassian.jira.plugin.system.customfieldtypes:project";
/*  67 */   static final Collection<String> JIRA_SELECT_TYPES = Collections.unmodifiableCollection(new HashSet<>(
/*  68 */         Arrays.asList(new String[] { "com.atlassian.jira.plugin.system.customfieldtypes:select", 
/*  69 */             "com.atlassian.jira.plugin.system.customfieldtypes:multicheckboxes", 
/*  70 */             "com.atlassian.jira.plugin.system.customfieldtypes:multiselect", "com.atlassian.jira.plugin.system.customfieldtypes:cascadingselect" })));
/*     */   
/*  72 */   private static final Collection<String> OPTION_FIELDS = Collections.unmodifiableCollection(new HashSet<>(
/*  73 */         Arrays.asList(new String[] { "priority", "resolution", "component", "version", "issuetype", "status" })));
/*     */   
/*  75 */   private static final Collection<String> RICH_TEXT_FIELDS = Collections.unmodifiableCollection(new HashSet<>(
/*  76 */         Arrays.asList(new String[] { "description", "environment" })));
/*     */   
/*  78 */   private static final Collection<String> STRING_FIELDS = Collections.unmodifiableCollection(new HashSet<>(
/*  79 */         Arrays.asList(new String[] { "summary", "labels" })));
/*     */ 
/*     */   
/*  82 */   private static final Collection<String> USER_FIELDS = Collections.unmodifiableCollection(new HashSet<>(Arrays.asList(new String[] { "user" })));
/*     */   
/*  84 */   private Map<String, FieldDefinition> definedFields = new HashMap<>();
/*     */   
/*     */   @NotNull
/*  87 */   private Map<String, Map<String, FieldDefinition>> fieldDefinitions = new HashMap<>();
/*     */   
/*  89 */   private Map<String, String> keyToFieldMap = new HashMap<>();
/*     */   
/*  91 */   private Map<String, String> customTypeMap = new HashMap<>();
/*     */   
/*  93 */   private static final Collection<String> versionIds = new ArrayList<>();
/*     */ 
/*     */   
/*     */   private ILogger log;
/*     */   
/*     */   @NotNull
/*     */   private String serverUrl;
/*     */   
/*     */   @NotNull
/*     */   private final TransferItemToIssueConverter itemToIssue;
/*     */   
/*     */   @NotNull
/*     */   private String epicLinkFieldKey;
/*     */   
/*     */   private boolean isCloudDeployment;
/*     */ 
/*     */   
/*     */   public JiraProxyTranslator(@NotNull JSONArray fieldsArray, @NotNull ILogger log, @NotNull String serverUrl, boolean isCloudDeployment) throws JSONException {
/* 111 */     this.log = log;
/* 112 */     this.serverUrl = serverUrl;
/* 113 */     this.isCloudDeployment = isCloudDeployment;
/* 114 */     loadDefinedFields(fieldsArray);
/* 115 */     this.epicLinkFieldKey = this.definedFields.values().stream().filter(fieldDefinition -> fieldDefinition.getLabel().equals("Epic Link"))
/* 116 */       .map(fieldDefinition -> fieldDefinition.getKey()).findFirst().orElseGet(() -> "customfield_10900");
/*     */     
/* 118 */     log.debug("If this sync is configured to synchronize Epic Links, we will try to receive these Epic Links from Jira using the field with id " + this.epicLinkFieldKey);
/*     */     
/* 120 */     this.itemToIssue = new TransferItemToIssueConverter(this, isCloudDeployment);
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   public UpdateIssue transferItemToIssue(@NotNull TransferItem transferItem, @Nullable TransferItem oldItem, @NotNull Collection<String> includeKeys, @NotNull Map<String, String> conversionErrors) {
/* 125 */     return this.itemToIssue.convertForUpdate(transferItem, oldItem, includeKeys, conversionErrors);
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   public Version transferItemToVersion(@NotNull TransferItem transferItem, @NotNull Map<String, String> conversionErrors, @NotNull String projectId) {
/* 130 */     return this.itemToIssue.convertForUpdateVersion(transferItem, conversionErrors, projectId);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public TransferItem issueToTransferItem(Issue mapIssue, Collection<String> keys) {
/* 140 */     TransferItem transferItem = null;
/* 141 */     if (mapIssue != null) {
/* 142 */       transferItem = new TransferItem(mapIssue.getKey());
/* 143 */       for (String key : keys) {
/* 144 */         if ("com.polarion.synchronizer:jira:virtual-type:name".equals(key) || "com.polarion.synchronizer:jira:virtual-type:endDate".equals(key) || "com.polarion.synchronizer:jira:virtual-type:startDate".equals(key) || "fixVersionIds".equals(key)) {
/*     */           continue;
/*     */         }
/* 147 */         FieldDefinition fieldDefinition = this.definedFields.get(key);
/*     */         
/* 149 */         String jiraFieldId = getJiraFieldId(key);
/* 150 */         if (jiraFieldId == null) {
/* 151 */           throw new SynchronizationException("Jira field not found for key " + key);
/*     */         }
/* 153 */         Object sourceValue = mapIssue.getFields().get(jiraFieldId);
/* 154 */         transferItem.put(key, convertValue(key, fieldDefinition, sourceValue, mapIssue.getRenderedFields().get(key)));
/*     */       } 
/* 156 */       if (keys.contains("com.polarion.synchronizer:item-url")) {
/* 157 */         transferItem.put("com.polarion.synchronizer:item-url", String.valueOf(this.serverUrl) + "/browse/" + mapIssue.getKey());
/*     */       }
/* 159 */       if (keys.contains("com.polarion.synchronizer:jira:item-id")) {
/* 160 */         transferItem.put("com.polarion.synchronizer:jira:item-id", mapIssue.getKey());
/*     */       }
/* 162 */       if (keys.contains("relations")) {
/* 163 */         ArrayList<Relation> relations = (ArrayList<Relation>)transferItem.getValue("relations");
/* 164 */         Object epicLinkObject = mapIssue.getFields().get(this.epicLinkFieldKey);
/* 165 */         if (epicLinkObject != null) {
/* 166 */           relations.add(new Relation("LINKED_EPIC", (String)epicLinkObject, String.valueOf(this.serverUrl) + "/browse/" + epicLinkObject));
/* 167 */           transferItem.put("relations", relations);
/*     */         } 
/*     */       } 
/*     */     } 
/* 171 */     return transferItem;
/*     */   }
/*     */ 
/*     */   
/*     */   @Nullable
/*     */   public String getJiraFieldId(@NotNull String key) {
/* 177 */     return this.keyToFieldMap.get(key);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private Object convertValue(String key, FieldDefinition fieldDefinition, Object sourceValue, Object renderedValue) {
/*     */     Object convertedValue;
/* 191 */     if (fieldDefinition.isMultiValued()) {
/* 192 */       Collection<Object> values = Collections.EMPTY_LIST;
/* 193 */       if (sourceValue instanceof Collection) {
/* 194 */         values = (Collection<Object>)sourceValue;
/* 195 */       } else if (key.equals("comments")) {
/* 196 */         values = (Collection<Object>)((Map)sourceValue).get("comments");
/* 197 */       } else if (sourceValue == null) {
/* 198 */         values = Collections.EMPTY_LIST;
/*     */       } else {
/* 200 */         this.log.error("Failed to convert data for key " + key + "; source value is " + sourceValue);
/*     */       } 
/*     */       
/* 203 */       Collection<Object> convertedValues = new ArrayList(values.size());
/* 204 */       for (Object value : values) {
/* 205 */         Object convertArrayValue = convertSingleValue(value, null, fieldDefinition.getKey(), fieldDefinition.getType());
/* 206 */         if (convertArrayValue != null) {
/* 207 */           convertedValues.add(convertArrayValue);
/*     */         }
/*     */       } 
/* 210 */       convertedValue = convertedValues;
/*     */     } else {
/* 212 */       convertedValue = convertSingleValue(sourceValue, renderedValue, fieldDefinition.getKey(), fieldDefinition.getType());
/*     */     } 
/* 214 */     return convertedValue;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private Object convertSingleValue(@Nullable Object sourceValue, @Nullable Object renderedValue, @NotNull String fdKey, @NotNull String fdType) {
/* 226 */     Object convertedValue = null;
/* 227 */     if (sourceValue == null) {
/* 228 */       return null;
/*     */     }
/*     */     
/* 231 */     if (isJiraSprintField(fdKey)) {
/* 232 */       return convertFromSprint(sourceValue);
/*     */     }
/*     */     
/* 235 */     if (isJiraProjectField(fdKey)) {
/* 236 */       return convertFromProject(sourceValue);
/*     */     }
/*     */     
/* 239 */     String jiraId = (String)ObjectUtils.notNull(getJiraFieldId(fdKey));
/* 240 */     IMetadataHelper.CascadingSelectType cascadingSelectType = getCascadingSelectType(fdKey);
/* 241 */     if (cascadingSelectType != null) {
/* 242 */       Map<String, Object> cascadingSelect = (Map<String, Object>)sourceValue;
/* 243 */       if (cascadingSelectType == IMetadataHelper.CascadingSelectType.ROOT) {
/* 244 */         convertedValue = getOptionValue(sourceValue, jiraId, false);
/* 245 */       } else if (cascadingSelectType == IMetadataHelper.CascadingSelectType.CHILD) {
/* 246 */         convertedValue = getOptionValue(cascadingSelect.get("child"), jiraId, false);
/*     */       } 
/* 248 */     } else if (fdType.equals("option") || fdType.equals("user")) {
/* 249 */       convertedValue = getOptionValue(sourceValue, fdKey, fdType.equals("user"));
/* 250 */     } else if (fdType.equals("string") || fdType.equals("text")) {
/* 251 */       convertedValue = sourceValue;
/* 252 */     } else if (fdType.equals("date-time")) {
/* 253 */       convertedValue = convertToDate(sourceValue, "yyyy-MM-dd'T'HH:mm:ss.SSSZ");
/* 254 */     } else if (fdType.equals("date")) {
/* 255 */       convertedValue = convertToDate(sourceValue, "yyyy-MM-dd");
/* 256 */     } else if (fdType.equals("jira:rich-text")) {
/* 257 */       convertedValue = ((String)sourceValue).isEmpty() ? null : new JiraRichText((String)sourceValue, (String)renderedValue);
/* 258 */     } else if (fdType.equals("jira:relation")) {
/* 259 */       convertedValue = convertToRelation(sourceValue, false);
/* 260 */     } else if (fdType.equals("jira:comment")) {
/* 261 */       convertedValue = convertToComment(sourceValue);
/* 262 */     } else if (fdType.equals("attachment")) {
/* 263 */       convertedValue = convertToAttachment(sourceValue);
/* 264 */     } else if (fdType.equals("jira:duration")) {
/* 265 */       String subkey = fdKey.substring("timetracking.".length());
/* 266 */       Map<String, Object> sourceMap = (Map<String, Object>)sourceValue;
/* 267 */       String stringRepresentation = (String)sourceMap.get(subkey);
/* 268 */       if (stringRepresentation != null) {
/* 269 */         long minutes = ((Number)sourceMap.get(String.valueOf(subkey) + "Seconds")).longValue() / 60L;
/* 270 */         convertedValue = new Duration(stringRepresentation, minutes);
/*     */       } 
/* 272 */     } else if (fdType.equals("float")) {
/* 273 */       convertedValue = Float.valueOf(((Number)sourceValue).floatValue());
/*     */     } else {
/* 275 */       throw new SynchronizationException("Error while converting Jira single value for field " + jiraId + ". Unsupported type " + fdType);
/*     */     } 
/*     */     
/* 278 */     return convertedValue;
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private String getOptionValue(@Nullable Object sourceValue, @NotNull String key, boolean isUserField) {
/* 283 */     if (isUserField && this.isCloudDeployment) {
/* 284 */       return getStringFromMap(sourceValue, new String[] { "accountId" });
/*     */     }
/* 286 */     return getStringFromMap(sourceValue, new String[] { getOptionValueKey(key) });
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private Date convertToDate(Object sourceValue, String jiraDateFormat) {
/* 297 */     SimpleDateFormat sdf = new SimpleDateFormat(jiraDateFormat);
/* 298 */     Date parsedDate = null;
/*     */     try {
/* 300 */       parsedDate = sdf.parse((String)sourceValue);
/* 301 */     } catch (ParseException e) {
/* 302 */       this.log.error("Error while converting value " + sourceValue + " to date", e);
/*     */     } 
/* 304 */     return parsedDate;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Nullable
/*     */   public Object convertToRelation(@NotNull Object sourceValue, boolean extended) {
/* 315 */     Relation convertedValue = null;
/* 316 */     Map<String, Object> sourceLink = (Map<String, Object>)sourceValue;
/* 317 */     if (sourceLink.containsKey("outwardIssue")) {
/* 318 */       String typeName = getStringFromMap(sourceValue, new String[] { "type", "name" });
/* 319 */       String key = getStringFromMap(sourceValue, new String[] { "outwardIssue", "key" });
/* 320 */       if (extended) {
/* 321 */         JiraRelation jiraRelation = new JiraRelation(Integer.valueOf(getStringFromMap(sourceValue, new String[] { "id" })).intValue(), typeName, key);
/*     */       } else {
/* 323 */         convertedValue = new Relation(typeName, key, String.valueOf(this.serverUrl) + "/browse/" + key);
/*     */       } 
/*     */     } 
/* 326 */     return convertedValue;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private Object convertToComment(Object sourceValue) {
/*     */     String authorName;
/* 336 */     if (this.isCloudDeployment) {
/* 337 */       authorName = getStringFromMap(sourceValue, new String[] { "author", "accountId" });
/*     */     } else {
/* 339 */       authorName = getStringFromMap(sourceValue, new String[] { "author", "name" });
/*     */     } 
/* 341 */     String body = getStringFromMap(sourceValue, new String[] { "body" });
/* 342 */     Date created = parseDateFromMap(sourceValue, "created");
/* 343 */     return new JiraComment(authorName, body, created);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private Object convertToAttachment(Object sourceValue) {
/* 353 */     String attachmentId = getStringFromMap(sourceValue, new String[] { "id" });
/* 354 */     String attachmentName = getStringFromMap(sourceValue, new String[] { "filename" });
/* 355 */     String contentUrl = getStringFromMap(sourceValue, new String[] { "content" });
/* 356 */     return new JiraAttachment(attachmentId, attachmentName, contentUrl);
/*     */   }
/*     */   
/*     */   @Nullable
/*     */   private String convertFromSprint(@NotNull Object sourceValue) {
/* 361 */     if (sourceValue instanceof ArrayList && !((ArrayList)sourceValue).isEmpty()) {
/* 362 */       if (((ArrayList)sourceValue).get(0) instanceof String) {
/* 363 */         String str = ((ArrayList<String>)sourceValue).get(0);
/* 364 */         int idx = str.indexOf("id=");
/* 365 */         return str.substring(idx + 3, str.indexOf(",", idx));
/* 366 */       }  if (((ArrayList)sourceValue).get(0) instanceof LinkedHashMap) {
/* 367 */         return ((Integer)((LinkedHashMap)((ArrayList<LinkedHashMap>)sourceValue).get(0)).get("id")).toString();
/*     */       }
/*     */     } 
/* 370 */     return null;
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private String convertFromProject(@NotNull Object sourceValue) {
/* 375 */     return (String)((Map)sourceValue).get("id");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private Date parseDateFromMap(Object sourceValue, String mapValue) {
/* 386 */     String jiraDateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSZ";
/* 387 */     SimpleDateFormat sdf = new SimpleDateFormat(jiraDateFormat);
/* 388 */     Date parsedDate = null;
/*     */     
/* 390 */     String valueToParse = getStringFromMap(sourceValue, new String[] { mapValue });
/*     */     try {
/* 392 */       parsedDate = sdf.parse(valueToParse);
/* 393 */     } catch (ParseException e) {
/* 394 */       this.log.error("Error while converting value " + valueToParse + " to date");
/*     */     } 
/* 396 */     return parsedDate;
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   public Collection<FieldDefinition> getDefinedFields() {
/* 401 */     return this.definedFields.values();
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public Collection<FieldDefinition> getDefinedFields(@Nullable String issueTypeId) {
/* 407 */     if (issueTypeId != null && this.fieldDefinitions.containsKey(issueTypeId)) {
/* 408 */       return ((Map)this.fieldDefinitions.get(issueTypeId)).values();
/*     */     }
/* 410 */     return this.definedFields.values();
/*     */   }
/*     */   
/*     */   private void generateMessages(Map<String, ? extends Object> checkIssue, StringBuilder errorMessages) {
/* 414 */     if (errorMessages.indexOf("customfield_") > -1) {
/* 415 */       generateCustomfieldMessage(checkIssue, errorMessages);
/*     */     }
/* 417 */     replaceStandardOutput(errorMessages, (String)checkIssue.get("type"));
/*     */   }
/*     */   
/*     */   public String fixErrorMessages(Map<String, ? extends Object> checkIssue, String errorMessages) {
/* 421 */     StringBuilder builder = new StringBuilder(errorMessages);
/* 422 */     generateMessages(checkIssue, builder);
/* 423 */     return builder.toString();
/*     */   }
/*     */   
/*     */   private void generateCustomfieldMessage(Map<String, ? extends Object> checkIssue, StringBuilder errorMessages) {
/* 427 */     int index = errorMessages.indexOf("customfield_");
/*     */     
/* 429 */     String customfieldKey = errorMessages.substring(index, index + 17);
/* 430 */     String customfieldLabel = getFieldDefinitionLabel(customfieldKey);
/*     */     
/* 432 */     String oldMessage = "Option id 'null' is not valid";
/* 433 */     String newMessage = "Value '" + checkIssue.get(customfieldKey) + "' is not valid for field '" + customfieldLabel + "'.";
/* 434 */     replaceAllSB(errorMessages, oldMessage, newMessage);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String getFieldDefinitionLabel(String fieldId) {
/* 442 */     if (this.definedFields.containsKey(fieldId)) {
/* 443 */       FieldDefinition fieldDefinition = this.definedFields.get(fieldId);
/* 444 */       return String.valueOf(fieldDefinition.getKey()) + " (" + fieldDefinition.getLabel() + ")";
/*     */     } 
/* 446 */     return String.valueOf(fieldId) + " NOT FOUND IN THE FIELD DEFINITION ";
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   private void replaceStandardOutput(StringBuilder errorMessages, String typeName) {
/* 452 */     String newMessage = "Value '" + typeName + "' is not valid for field 'type'.";
/* 453 */     replaceAllSB(errorMessages, "issue type is required", newMessage);
/* 454 */     replaceAllSB(errorMessages, "Could not find issuetype by id or name.", newMessage);
/* 455 */     replaceAllSB(errorMessages, "}", "");
/* 456 */     replaceAllSB(errorMessages, "\"", "");
/* 457 */     replaceAllSB(errorMessages, "{errorMessages:[],errors:{", "");
/*     */   }
/*     */   
/*     */   private void replaceAllSB(StringBuilder sb, String from, String to) {
/* 461 */     int index = sb.indexOf(from);
/* 462 */     while (index != -1) {
/* 463 */       sb.replace(index, index + from.length(), to);
/* 464 */       index += to.length();
/* 465 */       index = sb.indexOf(from, index);
/*     */     } 
/*     */   }
/*     */   
/*     */   public Collection<String> getMappedKeys(Collection<String> keys) {
/* 470 */     Collection<String> mappedKeys = new HashSet<>();
/* 471 */     for (String key : keys) {
/* 472 */       String mappedKey = getJiraFieldId(key);
/* 473 */       if (mappedKey != null) {
/* 474 */         mappedKeys.add(mappedKey);
/*     */       }
/*     */     } 
/* 477 */     return mappedKeys;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String getStringFromMap(@Nullable Object mapObj, String... keys) {
/* 487 */     if (mapObj == null) {
/* 488 */       return null;
/*     */     }
/*     */     
/* 491 */     Map<String, Object> map = (Map<String, Object>)mapObj;
/* 492 */     Object value = map.get(keys[0]);
/* 493 */     if (keys.length > 1)
/* 494 */       return getStringFromMap(value, Arrays.<String>copyOfRange(keys, 1, keys.length)); 
/* 495 */     if (value != null) {
/* 496 */       return value.toString().replaceAll("\r", "");
/*     */     }
/* 498 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Object> relationToMap(String issueKey, Relation relation) {
/* 509 */     Map<String, Object> result = new HashMap<>();
/* 510 */     result.put("type", Collections.singletonMap("name", relation.getRole()));
/* 511 */     result.put("inwardIssue", Collections.singletonMap("key", issueKey));
/* 512 */     result.put("outwardIssue", Collections.singletonMap("key", relation.getTargetId()));
/* 513 */     return result;
/*     */   }
/*     */   
/*     */   private void loadDefinedFields(JSONArray fields) throws JSONException {
/* 517 */     for (int fieldIndex = 0; fieldIndex < fields.length(); fieldIndex++) {
/* 518 */       JSONObject field = fields.getJSONObject(fieldIndex);
/* 519 */       loadDefinedField(field);
/*     */     } 
/* 521 */     addTimeTrackingField("timetracking.originalEstimate", "Original Estimate");
/* 522 */     addTimeTrackingField("timetracking.timeSpent", "Time Spent");
/* 523 */     addTimeTrackingField("timetracking.remainingEstimate", "Remaining Estimate");
/* 524 */     addURLField(IProxy.FIELD_ITEM_URL.getKey());
/* 525 */     addItemIdField(JiraProxy.FIELD_ITEM_ID.getKey());
/* 526 */     addFixVersionIdsField(JiraProxy.FIELD_FIX_VERSIONS_IDS.getKey());
/* 527 */     addVirtualFields("version");
/* 528 */     updateVirtualKeyFieldMap();
/*     */   }
/*     */   
/*     */   private void addVirtualFields(@NotNull String optionType) {
/* 532 */     Map<String, FieldDefinition> fieldMap = new HashMap<>();
/* 533 */     boolean isReadOnly = false;
/* 534 */     boolean isMultiValued = false;
/*     */     
/* 536 */     addVirtualTypeFields("com.polarion.synchronizer:jira:virtual-type:name", Localization.getString("synchronizer.ui.jira.field.name"), "string", isReadOnly, isMultiValued, fieldMap);
/* 537 */     addVirtualTypeFields("com.polarion.synchronizer:jira:virtual-type:startDate", Localization.getString("synchronizer.ui.jira.field.startDate"), "date", isReadOnly, isMultiValued, fieldMap);
/* 538 */     addVirtualTypeFields("com.polarion.synchronizer:jira:virtual-type:endDate", Localization.getString("synchronizer.ui.jira.field.endDate"), "date", isReadOnly, isMultiValued, fieldMap);
/* 539 */     this.fieldDefinitions.put(optionType, fieldMap);
/*     */   }
/*     */ 
/*     */   
/*     */   private void updateVirtualKeyFieldMap() {
/* 544 */     this.keyToFieldMap.put("com.polarion.synchronizer:jira:virtual-type:name", Localization.getString("synchronizer.ui.jira.field.name"));
/* 545 */     this.keyToFieldMap.put("com.polarion.synchronizer:jira:virtual-type:startDate", Localization.getString("synchronizer.ui.jira.field.startDate"));
/* 546 */     this.keyToFieldMap.put("com.polarion.synchronizer:jira:virtual-type:endDate", Localization.getString("synchronizer.ui.jira.field.endDate"));
/*     */   }
/*     */   
/*     */   private void addVirtualTypeFields(@NotNull String key, @NotNull String label, @NotNull String type, boolean readOnly, boolean multi, @NotNull Map<String, FieldDefinition> fieldMap) {
/* 550 */     FieldDefinition newFieldDefinition = new FieldDefinition(key, label, type, readOnly, multi);
/* 551 */     fieldMap.put(newFieldDefinition.getKey(), newFieldDefinition);
/* 552 */     this.definedFields.putIfAbsent(newFieldDefinition.getKey(), newFieldDefinition);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void addFixVersionIdsField(@NotNull String mappedKey) {
/* 559 */     this.keyToFieldMap.put(mappedKey, JiraProxy.FIELD_FIX_VERSIONS_IDS.getLabel());
/* 560 */     this.definedFields.put(JiraProxy.FIELD_FIX_VERSIONS_IDS.getKey(), JiraProxy.FIELD_FIX_VERSIONS_IDS);
/*     */   }
/*     */   
/*     */   private void loadDefinedField(@NotNull JSONObject field) throws JSONException {
/* 564 */     if (field.has("schema")) {
/* 565 */       String fieldType; JSONObject schema = field.getJSONObject("schema");
/* 566 */       String fieldKey = field.getString("id");
/* 567 */       String fieldLabel = field.getString("name");
/*     */       
/* 569 */       boolean isReadOnly = false;
/* 570 */       String schemaType = schema.getString("type");
/* 571 */       boolean isMultiValued = "array".equals(schemaType);
/*     */ 
/*     */       
/* 574 */       if ("comments-page".equals(schemaType)) {
/*     */         
/* 576 */         fieldType = "jira:comment";
/* 577 */         isMultiValued = true;
/* 578 */       } else if ("option-with-child".equals(schemaType)) {
/*     */         
/* 580 */         fieldType = loadCustomStringField(schema, fieldKey);
/* 581 */         isMultiValued = true;
/*     */       } else {
/* 583 */         String jiraType = isMultiValued ? (schema.has("items") ? schema.getString("items") : null) : schemaType;
/* 584 */         if (jiraType != null) {
/* 585 */           fieldType = findFieldType(schema, fieldKey, jiraType);
/*     */         } else {
/* 587 */           fieldType = null;
/*     */         } 
/*     */       } 
/*     */       
/* 591 */       if (fieldType == null) {
/* 592 */         this.log.debug("Unsupported field type for field '" + fieldKey + "'. Field data:" + field.toString());
/*     */         
/*     */         return;
/*     */       } 
/* 596 */       if (isCascadingSelect(fieldKey)) {
/* 597 */         String rootKey = String.valueOf(fieldKey) + "_root";
/* 598 */         String childKey = String.valueOf(fieldKey) + "_child";
/*     */         
/* 600 */         this.keyToFieldMap.put(rootKey, fieldKey);
/* 601 */         this.keyToFieldMap.put(childKey, fieldKey);
/*     */         
/* 603 */         addFieldDefinition(rootKey, Localization.getString("synchronizer.jira.cascadingSelect.root", new String[] { fieldLabel }), "option", isReadOnly, false);
/* 604 */         addFieldDefinition(childKey, Localization.getString("synchronizer.jira.cascadingSelect.child", new String[] { fieldLabel }), "option", isReadOnly, false);
/*     */       } else {
/* 606 */         this.keyToFieldMap.put(findMappedKey(fieldKey), fieldKey);
/* 607 */         if (isJiraSprintField(fieldKey)) {
/* 608 */           addFieldDefinition(findMappedKey(fieldKey), fieldLabel, fieldType, isReadOnly, false);
/* 609 */         } else if (fieldKey.equals("issuelinks")) {
/* 610 */           addFieldDefinition(findMappedKey(fieldKey), Localization.getString("synchronizer.jira.linkedIssues"), fieldType, isReadOnly, isMultiValued);
/*     */         } else {
/* 612 */           addFieldDefinition(findMappedKey(fieldKey), fieldLabel, fieldType, isReadOnly, isMultiValued);
/*     */         } 
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   @Nullable
/*     */   public IMetadataHelper.CascadingSelectType getCascadingSelectType(@NotNull String key) {
/* 622 */     String jiraId = getJiraFieldId(key);
/* 623 */     if (jiraId != null && isCascadingSelect(jiraId)) {
/* 624 */       return key.endsWith("_root") ? IMetadataHelper.CascadingSelectType.ROOT : IMetadataHelper.CascadingSelectType.CHILD;
/*     */     }
/* 626 */     return null;
/*     */   }
/*     */   
/*     */   private boolean isCascadingSelect(@NotNull String jiraId) {
/* 630 */     return "com.atlassian.jira.plugin.system.customfieldtypes:cascadingselect".equals(this.customTypeMap.get(jiraId));
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private String findMappedKey(@NotNull String fieldKey) {
/*     */     String mappedKey;
/* 636 */     if (fieldKey.equals("comment")) {
/* 637 */       mappedKey = "comments";
/* 638 */     } else if (fieldKey.equals("issuelinks")) {
/* 639 */       mappedKey = "relations";
/* 640 */     } else if (fieldKey.equals("attachment")) {
/* 641 */       mappedKey = "attachments";
/* 642 */     } else if (fieldKey.equals("issuetype")) {
/* 643 */       mappedKey = "type";
/*     */     } else {
/* 645 */       mappedKey = fieldKey;
/*     */     } 
/* 647 */     return mappedKey;
/*     */   }
/*     */   
/*     */   private String findFieldType(@NotNull JSONObject schema, @NotNull String fieldKey, @NotNull String jiraType) throws JSONException {
/* 651 */     String fieldType = null;
/* 652 */     if (jiraType.equals("string")) {
/* 653 */       if (schema.has("custom")) {
/* 654 */         fieldType = loadCustomStringField(schema, fieldKey);
/*     */       } else {
/* 656 */         fieldType = loadSystemStringField(schema);
/*     */       } 
/* 658 */     } else if ("option".equals(jiraType) || "json".equals(jiraType)) {
/* 659 */       fieldType = loadCustomStringField(schema, fieldKey);
/* 660 */     } else if (OPTION_FIELDS.contains(jiraType)) {
/* 661 */       fieldType = "option";
/* 662 */     } else if (USER_FIELDS.contains(jiraType)) {
/* 663 */       fieldType = "user";
/* 664 */     } else if (jiraType.equals("status") || jiraType.equals("issuetype")) {
/* 665 */       fieldType = "option";
/* 666 */     } else if (jiraType.equals("string")) {
/* 667 */       fieldType = "string";
/* 668 */     } else if (jiraType.equals("comment")) {
/* 669 */       fieldType = "jira:comment";
/* 670 */     } else if (jiraType.equals("attachment")) {
/* 671 */       fieldType = "attachment";
/* 672 */     } else if (jiraType.equals("issuelinks") && fieldKey.equals("issuelinks")) {
/* 673 */       fieldType = "jira:relation";
/* 674 */     } else if (jiraType.equals("date")) {
/* 675 */       fieldType = "date";
/* 676 */     } else if (jiraType.equals("datetime")) {
/* 677 */       fieldType = "date-time";
/* 678 */     } else if ("number".equals(jiraType)) {
/* 679 */       if (schema.has("custom")) {
/* 680 */         fieldType = loadCustomNumberField(schema, fieldKey);
/*     */       }
/* 682 */     } else if ("any".equals(jiraType)) {
/* 683 */       if (schema.has("custom")) {
/* 684 */         fieldType = loadCustomAnyField(schema, fieldKey);
/*     */       }
/* 686 */     } else if ("project".equals(jiraType) && 
/* 687 */       schema.has("custom")) {
/* 688 */       fieldType = loadCustomProjectField(schema, fieldKey);
/*     */     } 
/*     */     
/* 691 */     return fieldType;
/*     */   }
/*     */   
/*     */   @Nullable
/*     */   private String loadSystemStringField(@NotNull JSONObject schema) throws JSONException {
/* 696 */     Object system = schema.get("system");
/* 697 */     if (RICH_TEXT_FIELDS.contains(system))
/* 698 */       return "jira:rich-text"; 
/* 699 */     if (STRING_FIELDS.contains(system)) {
/* 700 */       return "string";
/*     */     }
/* 702 */     return null;
/*     */   }
/*     */   
/*     */   @Nullable
/*     */   private String loadCustomStringField(@NotNull JSONObject schema, @NotNull String fieldKey) throws JSONException {
/* 707 */     String customType = schema.getString("custom");
/* 708 */     this.customTypeMap.put(fieldKey, customType);
/* 709 */     if (JIRA_SELECT_TYPES.contains(customType)) {
/* 710 */       return "option";
/*     */     }
/*     */     String str1;
/* 713 */     switch ((str1 = customType).hashCode()) { case -2138599174: if (!str1.equals("com.atlassian.jira.plugin.system.customfieldtypes:textfield")) {
/*     */           break;
/*     */         }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 722 */         return "string";case -761864051: if (!str1.equals("com.atlassian.jira.plugin.system.customfieldtypes:textarea")) break;  return "text";case -289053069: if (!str1.equals("com.pyxis.greenhopper.jira:gh-sprint")) break;  return "string";case -134457740: if (!str1.equals("com.pyxis.greenhopper.jira:gh-epic-color")) break;  return "string";case -126573051: if (!str1.equals("com.pyxis.greenhopper.jira:gh-epic-label")) break;  return "string";case 1803797010: if (!str1.equals("com.atlassian.jira.plugin.system.customfieldtypes:labels")) break;  return "string"; }
/*     */ 
/*     */     
/* 725 */     return null;
/*     */   }
/*     */   
/*     */   @Nullable
/*     */   private String loadCustomNumberField(@NotNull JSONObject schema, @NotNull String fieldKey) throws JSONException {
/* 730 */     String customType = schema.getString("custom");
/* 731 */     this.customTypeMap.put(fieldKey, customType);
/*     */     String str1;
/* 733 */     switch ((str1 = customType).hashCode()) { case 468627945: if (!str1.equals("com.atlassian.jira.plugin.system.customfieldtypes:float"))
/*     */           break; 
/* 735 */         return "float"; }
/*     */ 
/*     */     
/* 738 */     return null;
/*     */   }
/*     */   
/*     */   @Nullable
/*     */   private String loadCustomAnyField(@NotNull JSONObject schema, @NotNull String fieldKey) throws JSONException {
/* 743 */     String customType = schema.getString("custom");
/* 744 */     this.customTypeMap.put(fieldKey, customType);
/*     */     String str1;
/* 746 */     switch ((str1 = customType).hashCode()) { case -852477264: if (!str1.equals("com.pyxis.greenhopper.jira:gh-lexo-rank")) {
/*     */           break;
/*     */         }
/* 749 */         return "string";case 134472393: if (!str1.equals("com.pyxis.greenhopper.jira:gh-epic-link")) break;  return "string"; }
/*     */ 
/*     */     
/* 752 */     return null;
/*     */   }
/*     */   
/*     */   @Nullable
/*     */   private String loadCustomProjectField(@NotNull JSONObject schema, @NotNull String fieldKey) throws JSONException {
/* 757 */     String customType = schema.getString("custom");
/* 758 */     this.customTypeMap.put(fieldKey, customType);
/*     */     String str1;
/* 760 */     switch ((str1 = customType).hashCode()) { case -162976922: if (!str1.equals("com.atlassian.jira.plugin.system.customfieldtypes:project"))
/*     */           break; 
/* 762 */         return "string"; }
/*     */ 
/*     */     
/* 765 */     return null;
/*     */   }
/*     */   
/*     */   private void addFieldDefinition(String key, String label, String type, boolean readOnly, boolean multi) {
/* 769 */     FieldDefinition newFieldDefinition = new FieldDefinition(key, label, type, readOnly, multi);
/* 770 */     this.definedFields.put(newFieldDefinition.getKey(), newFieldDefinition);
/*     */   }
/*     */   
/*     */   private void addTimeTrackingField(@NotNull String mappedKey, @NotNull String label) {
/* 774 */     this.keyToFieldMap.put(mappedKey, "timetracking");
/* 775 */     addFieldDefinition(mappedKey, label, "jira:duration", false, false);
/*     */   }
/*     */   
/*     */   private void addURLField(@NotNull String mappedKey) {
/* 779 */     this.keyToFieldMap.put(mappedKey, IProxy.FIELD_ITEM_URL.getLabel());
/* 780 */     this.definedFields.put(IProxy.FIELD_ITEM_URL.getKey(), IProxy.FIELD_ITEM_URL);
/*     */   }
/*     */   
/*     */   private void addItemIdField(@NotNull String mappedKey) {
/* 784 */     this.keyToFieldMap.put(mappedKey, JiraProxy.FIELD_ITEM_ID.getLabel());
/* 785 */     this.definedFields.put(JiraProxy.FIELD_ITEM_ID.getKey(), JiraProxy.FIELD_ITEM_ID);
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public String getOptionValueKey(@NotNull String key) {
/* 791 */     return JIRA_SELECT_TYPES.contains(this.customTypeMap.get(key)) ? "value" : "name";
/*     */   }
/*     */ 
/*     */   
/*     */   @Nullable
/*     */   public FieldDefinition getDefinedFieldDefinition(@NotNull String key) {
/* 797 */     return this.definedFields.get(key);
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean isCustomOption(@NotNull String jiraId) {
/* 802 */     return JIRA_SELECT_TYPES.contains(this.customTypeMap.get(jiraId));
/*     */   }
/*     */   
/*     */   public Collection<String> requiredOldValues(TransferItem transferItem) {
/* 806 */     return this.itemToIssue.requiredOldValues(transferItem);
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean isJiraSprintField(@NotNull String jiraId) {
/* 811 */     return "com.pyxis.greenhopper.jira:gh-sprint".equals(this.customTypeMap.get(jiraId));
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean isJiraProjectField(@NotNull String jiraId) {
/* 816 */     return "com.atlassian.jira.plugin.system.customfieldtypes:project".equals(this.customTypeMap.get(jiraId));
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public String getEpicLinkFieldId() {
/* 822 */     return this.epicLinkFieldKey;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public List<TransferItem> addVersionsToTransferItems(@NotNull Issue issue, @NotNull TransferItem transferItemForIssue, @NotNull List<Version> allVersions, @NotNull Collection<String> fieldKeys, @NotNull List<TransferItem> existingTransferItems) {
/* 835 */     List<TransferItem> transferItemsForVersions = new ArrayList<>();
/* 836 */     List<String> fixVersionids = new ArrayList<>();
/* 837 */     String jiraFieldId = getJiraFieldId("fixVersions");
/* 838 */     Object sourceValue = issue.getFields().get(jiraFieldId);
/* 839 */     List<String> virtualTypeFieldKeys = (List<String>)fieldKeys.stream().filter(key -> !(!"com.polarion.synchronizer:jira:virtual-type:name".equals(key) && !"com.polarion.synchronizer:jira:virtual-type:endDate".equals(key) && !"com.polarion.synchronizer:jira:virtual-type:startDate".equals(key)))
/* 840 */       .collect(Collectors.toList());
/* 841 */     if (sourceValue instanceof Collection) {
/* 842 */       Collection<Object> jiraVersionValues = (Collection<Object>)sourceValue;
/* 843 */       for (Object versionValue : jiraVersionValues) {
/* 844 */         Map<String, Object> versionValueMap = (Map<String, Object>)versionValue;
/* 845 */         fixVersionids.add((String)versionValueMap.get("id"));
/* 846 */         if (!versionValueMap.isEmpty() && existingTransferItems.stream().noneMatch(item -> paramMap.get("id").equals(item.getId()))) {
/* 847 */           Optional<Version> matchedVersion = allVersions.stream().filter(version -> paramMap.get("id").equals(version.getId())).findFirst();
/* 848 */           if (matchedVersion.isPresent()) {
/* 849 */             TransferItem transferItem = versionToTransferItem(matchedVersion.get(), virtualTypeFieldKeys);
/* 850 */             transferItemsForVersions.add(transferItem);
/* 851 */             versionIds.add(((Version)matchedVersion.get()).getId());
/*     */           } 
/*     */         } 
/*     */       } 
/*     */     } 
/* 856 */     transferItemForIssue.put("fixVersionIds", fixVersionids);
/* 857 */     return transferItemsForVersions;
/*     */   }
/*     */   
/*     */   @Nullable
/*     */   public TransferItem versionToTransferItem(@Nullable Version version, @NotNull Collection<String> keys) {
/* 862 */     TransferItem transferItem = null;
/* 863 */     if (version != null) {
/* 864 */       transferItem = new TransferItem(version.getId());
/* 865 */       transferItem.put("type", "version");
/* 866 */       for (String key : keys) {
/* 867 */         FieldDefinition fieldDefinition = this.definedFields.get(key);
/* 868 */         String jiraFieldId = getJiraFieldId(key);
/* 869 */         if (jiraFieldId == null) {
/* 870 */           throw new SynchronizationException("Jira field not found for key " + key);
/*     */         }
/* 872 */         String sourceValue = null;
/* 873 */         boolean isVirtualTypeAttribute = false;
/* 874 */         if ("com.polarion.synchronizer:jira:virtual-type:name".equals(key)) {
/* 875 */           sourceValue = version.getName();
/* 876 */           isVirtualTypeAttribute = true;
/* 877 */         } else if ("com.polarion.synchronizer:jira:virtual-type:endDate".equals(key)) {
/* 878 */           sourceValue = version.getReleaseDate();
/* 879 */           isVirtualTypeAttribute = true;
/* 880 */         } else if ("com.polarion.synchronizer:jira:virtual-type:startDate".equals(key)) {
/* 881 */           sourceValue = version.getStartDate();
/* 882 */           isVirtualTypeAttribute = true;
/*     */         } 
/* 884 */         if (isVirtualTypeAttribute) {
/* 885 */           transferItem.put(key, convertSingleValue(sourceValue, null, fieldDefinition.getKey(), fieldDefinition.getType()));
/*     */         }
/*     */       } 
/*     */     } 
/* 889 */     return transferItem;
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   public Collection<String> getVersionIds() {
/* 894 */     return versionIds;
/*     */   }
/*     */   
/*     */   public void addVersionIds(@NotNull String versionId) {
/* 898 */     versionIds.add(versionId);
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer.proxy.jira_3.22.1/jira.jar!/com/polarion/synchronizer/proxy/jira/JiraProxyTranslator.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */