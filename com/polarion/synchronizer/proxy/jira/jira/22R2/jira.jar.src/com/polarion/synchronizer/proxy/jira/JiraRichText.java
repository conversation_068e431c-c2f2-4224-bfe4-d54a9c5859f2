/*    */ package com.polarion.synchronizer.proxy.jira;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ 
/*    */ public class JiraRichText
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 2086806987644238516L;
/*    */   private String source;
/*    */   private transient String rendered;
/*    */   
/*    */   public JiraRichText(String source, String rendered) {
/* 14 */     this.source = source;
/* 15 */     this.rendered = rendered;
/*    */   }
/*    */   
/*    */   public String getSource() {
/* 19 */     return this.source;
/*    */   }
/*    */   
/*    */   public String getRendered() {
/* 23 */     return this.rendered;
/*    */   }
/*    */ 
/*    */   
/*    */   public int hashCode() {
/* 28 */     int prime = 31;
/* 29 */     int result = 1;
/* 30 */     result = 31 * result + ((this.source == null) ? 0 : this.source.hashCode());
/* 31 */     return result;
/*    */   }
/*    */ 
/*    */   
/*    */   public boolean equals(Object obj) {
/* 36 */     if (this == obj) {
/* 37 */       return true;
/*    */     }
/* 39 */     if (obj == null) {
/* 40 */       return false;
/*    */     }
/* 42 */     if (getClass() != obj.getClass()) {
/* 43 */       return false;
/*    */     }
/* 45 */     JiraRichText other = (JiraRichText)obj;
/* 46 */     if (this.source == null) {
/* 47 */       if (other.source != null) {
/* 48 */         return false;
/*    */       }
/* 50 */     } else if (!this.source.equals(other.source)) {
/* 51 */       return false;
/*    */     } 
/* 53 */     return true;
/*    */   }
/*    */ 
/*    */   
/*    */   public String toString() {
/* 58 */     return "JiraRichText [source=" + this.source + ", rendered=" + this.rendered + "]";
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer.proxy.jira_3.22.1/jira.jar!/com/polarion/synchronizer/proxy/jira/JiraRichText.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */