package com.polarion.synchronizer.proxy.jira;

import com.polarion.synchronizer.model.IProxy;
import org.jetbrains.annotations.NotNull;

public interface IJiraProxy extends IProxy {
  boolean isCloudDeployment();
  
  @NotNull
  String getUserURL();
}


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer.proxy.jira_3.22.1/jira.jar!/com/polarion/synchronizer/proxy/jira/IJiraProxy.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */