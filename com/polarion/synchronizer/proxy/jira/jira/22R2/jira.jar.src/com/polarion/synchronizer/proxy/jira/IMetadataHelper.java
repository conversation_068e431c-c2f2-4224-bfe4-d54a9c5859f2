/*    */ package com.polarion.synchronizer.proxy.jira;
/*    */ 
/*    */ import com.polarion.synchronizer.model.FieldDefinition;
/*    */ import java.util.Collection;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ import org.jetbrains.annotations.Nullable;
/*    */ 
/*    */ 
/*    */ 
/*    */ public interface IMetadataHelper
/*    */ {
/*    */   public static final String JIRA_KEY_REMAINING_ESTIMATE = "remainingEstimate";
/*    */   public static final String JIRA_KEY_ORIGINAL_ESTIMATE = "originalEstimate";
/*    */   public static final String JIRA_KEY_TIME_SPENT = "timeSpent";
/*    */   public static final String PREFIX_TIMETRACKING = "timetracking.";
/*    */   public static final String CASCADING_ROOT_SUFFIX = "_root";
/*    */   public static final String CASCADING_CHILD_SUFFIX = "_child";
/*    */   
/*    */   @Nullable
/*    */   CascadingSelectType getCascadingSelectType(@NotNull String paramString);
/*    */   
/*    */   @Nullable
/*    */   FieldDefinition getDefinedFieldDefinition(@NotNull String paramString);
/*    */   
/*    */   @Nullable
/*    */   String getJiraFieldId(@NotNull String paramString);
/*    */   
/*    */   @NotNull
/*    */   String getOptionValueKey(@NotNull String paramString);
/*    */   
/*    */   boolean isCustomOption(@NotNull String paramString);
/*    */   
/*    */   boolean isJiraSprintField(@NotNull String paramString);
/*    */   
/*    */   boolean isJiraProjectField(@NotNull String paramString);
/*    */   
/*    */   @NotNull
/*    */   String getEpicLinkFieldId();
/*    */   
/*    */   @NotNull
/*    */   Collection<FieldDefinition> getDefinedFields(@Nullable String paramString);
/*    */   
/*    */   public enum CascadingSelectType
/*    */   {
/* 45 */     ROOT, CHILD;
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer.proxy.jira_3.22.1/jira.jar!/com/polarion/synchronizer/proxy/jira/IMetadataHelper.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */