/*    */ package com.polarion.synchronizer.proxy.jira;
/*    */ 
/*    */ public class JiraAttachment
/*    */ {
/*    */   private final String id;
/*    */   private final String fileName;
/*    */   private final String contentUrl;
/*    */   
/*    */   public JiraAttachment(String id, String fileName, String contentUrl) {
/* 10 */     this.id = id;
/* 11 */     this.fileName = fileName;
/* 12 */     this.contentUrl = contentUrl;
/*    */   }
/*    */   
/*    */   public String getId() {
/* 16 */     return this.id;
/*    */   }
/*    */   
/*    */   public String getFileName() {
/* 20 */     return this.fileName;
/*    */   }
/*    */   
/*    */   public String getContentUrl() {
/* 24 */     return this.contentUrl;
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer.proxy.jira_3.22.1/jira.jar!/com/polarion/synchronizer/proxy/jira/JiraAttachment.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */