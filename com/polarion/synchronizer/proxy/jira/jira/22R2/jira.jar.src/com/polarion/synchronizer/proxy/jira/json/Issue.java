/*    */ package com.polarion.synchronizer.proxy.jira.json;
/*    */ 
/*    */ import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
/*    */ import com.fasterxml.jackson.annotation.JsonInclude;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import javax.xml.bind.annotation.XmlRootElement;
/*    */ 
/*    */ 
/*    */ 
/*    */ @XmlRootElement
/*    */ @JsonIgnoreProperties(ignoreUnknown = true)
/*    */ @JsonInclude(JsonInclude.Include.NON_DEFAULT)
/*    */ public class Issue
/*    */ {
/*    */   private String Id;
/*    */   private String Self;
/*    */   private String Key;
/* 19 */   private Map<String, Object> fields = new HashMap<>();
/*    */ 
/*    */   
/*    */   private Map<String, ? extends Object> renderedFields;
/*    */ 
/*    */   
/*    */   public String getId() {
/* 26 */     return this.Id;
/*    */   }
/*    */   
/*    */   public void setId(String id) {
/* 30 */     this.Id = id;
/*    */   }
/*    */   
/*    */   public String getSelf() {
/* 34 */     return this.Self;
/*    */   }
/*    */   
/*    */   public void setSelf(String self) {
/* 38 */     this.Self = self;
/*    */   }
/*    */   
/*    */   public String getKey() {
/* 42 */     return this.Key;
/*    */   }
/*    */   
/*    */   public void setKey(String key) {
/* 46 */     this.Key = key;
/*    */   }
/*    */   
/*    */   public Map<String, Object> getFields() {
/* 50 */     return this.fields;
/*    */   }
/*    */   
/*    */   public Map<String, ? extends Object> getRenderedFields() {
/* 54 */     return this.renderedFields;
/*    */   }
/*    */   
/*    */   public void setRenderedFields(Map<String, ? extends Object> renderedFields) {
/* 58 */     this.renderedFields = renderedFields;
/*    */   }
/*    */ 
/*    */   
/*    */   public String toString() {
/* 63 */     return "Issue [Id=" + this.Id + ", Self=" + this.Self + ", Key=" + this.Key + ", fields=" + this.fields + "]";
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer.proxy.jira_3.22.1/jira.jar!/com/polarion/synchronizer/proxy/jira/json/Issue.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */