/*    */ package com.polarion.synchronizer.proxy.jira.json;
/*    */ 
/*    */ import java.util.Collections;
/*    */ import java.util.Map;
/*    */ import org.jetbrains.annotations.Nullable;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class CascadingSelect
/*    */ {
/*    */   @Nullable
/*    */   private Object value;
/*    */   @Nullable
/*    */   private Object childValue;
/*    */   
/*    */   public CascadingSelect() {}
/*    */   
/*    */   public CascadingSelect(Object value, Object childValue) {
/* 40 */     this.value = value;
/* 41 */     this.childValue = childValue;
/*    */   }
/*    */   
/*    */   @Nullable
/*    */   public Map<String, Object> getChild() {
/* 46 */     return (this.childValue == null) ? null : Collections.<String, Object>singletonMap("value", this.childValue);
/*    */   }
/*    */   
/*    */   public Object getValue() {
/* 50 */     return this.value;
/*    */   }
/*    */   
/*    */   public void setValue(@Nullable Object value) {
/* 54 */     this.value = value;
/*    */   }
/*    */   
/*    */   public void setChildValue(@Nullable Object childValue) {
/* 58 */     this.childValue = childValue;
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer.proxy.jira_3.22.1/jira.jar!/com/polarion/synchronizer/proxy/jira/json/CascadingSelect.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */