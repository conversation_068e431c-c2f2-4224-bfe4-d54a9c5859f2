/*    */ package com.polarion.synchronizer.proxy.jira.translators;
/*    */ 
/*    */ import com.polarion.synchronizer.mapping.TranslationResult;
/*    */ import com.polarion.synchronizer.model.Duration;
/*    */ import com.polarion.synchronizer.spi.translators.TypesafeTranslator;
/*    */ 
/*    */ public class PolarionToJiraDurationTranslator
/*    */   extends TypesafeTranslator<Duration, Duration, Duration> {
/*    */   public PolarionToJiraDurationTranslator() {
/* 10 */     super(Duration.class, Duration.class);
/*    */   }
/*    */ 
/*    */   
/*    */   public TranslationResult<Duration> translateBidirectionalTypesafe(Duration sourceBaseline, Duration sourceValue, Duration targetBaseline, Duration targetValue) {
/* 15 */     return createBidirectionalResult(sourceBaseline, sourceValue, getMappedValue(sourceValue), targetBaseline, targetValue);
/*    */   }
/*    */   
/*    */   private Duration getMappedValue(Duration sourceValue) {
/* 19 */     if (sourceValue == null) {
/* 20 */       return null;
/*    */     }
/* 22 */     long absolute = sourceValue.getAbsoluteDuration() / 60000L;
/* 23 */     String stringRepresentation = String.valueOf(absolute) + "m";
/* 24 */     Duration mappedValue = new Duration(stringRepresentation, absolute);
/* 25 */     return mappedValue;
/*    */   }
/*    */ 
/*    */   
/*    */   public TranslationResult<Duration> translateUnidirectionalTypesafe(Duration sourceValue, Duration targetValue) {
/* 30 */     return createUnidirectionalResult(getMappedValue(sourceValue), targetValue);
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer.proxy.jira_3.22.1/jira.jar!/com/polarion/synchronizer/proxy/jira/translators/PolarionToJiraDurationTranslator.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */