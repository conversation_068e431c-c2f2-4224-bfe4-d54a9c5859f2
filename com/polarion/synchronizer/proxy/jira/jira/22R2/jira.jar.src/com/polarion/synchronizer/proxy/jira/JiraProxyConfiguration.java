/*     */ package com.polarion.synchronizer.proxy.jira;
/*     */ 
/*     */ import com.fasterxml.jackson.annotation.JsonIgnore;
/*     */ import com.polarion.platform.i18n.Localization;
/*     */ import com.polarion.synchronizer.configuration.IConnection;
/*     */ import com.polarion.synchronizer.spi.AbstractProxyConfiguration;
/*     */ import javax.xml.bind.annotation.XmlAccessType;
/*     */ import javax.xml.bind.annotation.XmlAccessorType;
/*     */ import javax.xml.bind.annotation.XmlAttribute;
/*     */ import javax.xml.bind.annotation.XmlTransient;
/*     */ import org.jetbrains.annotations.Nullable;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @XmlAccessorType(XmlAccessType.FIELD)
/*     */ public class JiraProxyConfiguration
/*     */   extends AbstractProxyConfiguration<JiraConnection>
/*     */ {
/*     */   private static final String SYSTEM_ID_PREFIX = "JIRA-";
/*     */   @XmlAttribute
/*     */   private String project;
/*     */   @XmlAttribute
/*     */   private String query;
/*     */   @XmlAttribute
/*     */   @Deprecated
/*     */   private String issueType;
/*     */   @XmlTransient
/*  29 */   private int pageSize = 1000;
/*     */   
/*     */   @XmlTransient
/*  32 */   private int projectPageSize = 50;
/*     */ 
/*     */   
/*     */   public JiraProxyConfiguration() {}
/*     */ 
/*     */   
/*     */   public JiraProxyConfiguration(String project, String query, JiraConnection connection) {
/*  39 */     this.project = project;
/*  40 */     this.query = query;
/*  41 */     setConnection((IConnection)connection);
/*     */   }
/*     */   
/*     */   public String getProject() {
/*  45 */     return this.project;
/*     */   }
/*     */   
/*     */   @Nullable
/*     */   private JiraConnection getJiraConnection() {
/*  50 */     IConnection connection = getConnection();
/*  51 */     if (connection == null) {
/*  52 */       return null;
/*     */     }
/*  54 */     if (connection instanceof JiraConnection) {
/*  55 */       return (JiraConnection)connection;
/*     */     }
/*  57 */     throw new IllegalStateException("Connection is not a JiraConnection.");
/*     */   }
/*     */ 
/*     */   
/*     */   @JsonIgnore
/*     */   public String getServerUrl() {
/*  63 */     JiraConnection jiraConnection = getJiraConnection();
/*  64 */     return (jiraConnection == null) ? null : jiraConnection.getServerUrl();
/*     */   }
/*     */   
/*     */   public String getQuery() {
/*  68 */     return this.query;
/*     */   }
/*     */   
/*     */   @Deprecated
/*     */   public String getIssueType() {
/*  73 */     return this.issueType;
/*     */   }
/*     */   
/*     */   @JsonIgnore
/*     */   public String getUser() {
/*  78 */     JiraConnection jiraConnection = getJiraConnection();
/*  79 */     return (jiraConnection == null) ? null : jiraConnection.getUser();
/*     */   }
/*     */   
/*     */   @JsonIgnore
/*     */   public String getPassword() {
/*  84 */     JiraConnection jiraConnection = getJiraConnection();
/*  85 */     return (jiraConnection == null) ? null : jiraConnection.getPassword();
/*     */   }
/*     */ 
/*     */   
/*     */   public String getSystemIdentifier() {
/*  90 */     JiraConnection jiraConnection = getJiraConnection();
/*  91 */     return "JIRA-" + ((jiraConnection == null) ? null : jiraConnection.getId());
/*     */   }
/*     */   
/*     */   @Nullable
/*     */   public String checkConfiguration() {
/*  96 */     JiraConnection connection = getJiraConnection();
/*  97 */     if (connection != null) {
/*  98 */       return connection.check();
/*     */     }
/* 100 */     return Localization.getString("synchronizer.jira.noConnection");
/*     */   }
/*     */ 
/*     */   
/*     */   public int getPageSize() {
/* 105 */     return this.pageSize;
/*     */   }
/*     */   
/*     */   public void setPageSize(int pageSize) {
/* 109 */     this.pageSize = pageSize;
/*     */   }
/*     */   
/*     */   public int getProjectPageSize() {
/* 113 */     return this.projectPageSize;
/*     */   }
/*     */   
/*     */   public void setProjectPageSize(int projectPageSize) {
/* 117 */     this.projectPageSize = projectPageSize;
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer.proxy.jira_3.22.1/jira.jar!/com/polarion/synchronizer/proxy/jira/JiraProxyConfiguration.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */