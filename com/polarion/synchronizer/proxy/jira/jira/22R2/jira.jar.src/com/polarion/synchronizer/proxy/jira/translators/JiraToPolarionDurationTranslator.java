/*    */ package com.polarion.synchronizer.proxy.jira.translators;
/*    */ 
/*    */ import com.polarion.synchronizer.mapping.TranslationResult;
/*    */ import com.polarion.synchronizer.model.Duration;
/*    */ import com.polarion.synchronizer.proxy.polarion.model.PolarionDurationHelper;
/*    */ import com.polarion.synchronizer.spi.translators.TypesafeTranslator;
/*    */ import javax.inject.Inject;
/*    */ 
/*    */ 
/*    */ public class JiraToPolarionDurationTranslator
/*    */   extends TypesafeTranslator<Duration, Duration, Duration>
/*    */ {
/*    */   public static final long MINUTE = 60000L;
/*    */   private final PolarionDurationHelper durationHelper;
/*    */   
/*    */   @Inject
/*    */   public JiraToPolarionDurationTranslator(PolarionDurationHelper durationHelper) {
/* 18 */     super(Duration.class, Duration.class);
/* 19 */     this.durationHelper = durationHelper;
/*    */   }
/*    */ 
/*    */   
/*    */   public TranslationResult<Duration> translateBidirectionalTypesafe(Duration sourceBaseline, Duration sourceValue, Duration targetBaseline, Duration targetValue) {
/* 24 */     return createBidirectionalResult(sourceBaseline, sourceValue, getMappedValue(sourceValue), targetBaseline, targetValue);
/*    */   }
/*    */   
/*    */   private Duration getMappedValue(Duration sourceValue) {
/* 28 */     if (sourceValue == null) {
/* 29 */       return null;
/*    */     }
/* 31 */     long absoluteMillis = sourceValue.getAbsoluteDuration() * 60000L;
/*    */     
/* 33 */     Duration mappedValue = new Duration(this.durationHelper.loadDurationFromMillis(absoluteMillis).toString(), absoluteMillis);
/* 34 */     return mappedValue;
/*    */   }
/*    */ 
/*    */   
/*    */   public TranslationResult<Duration> translateUnidirectionalTypesafe(Duration sourceValue, Duration targetValue) {
/* 39 */     return createUnidirectionalResult(getMappedValue(sourceValue), targetValue);
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer.proxy.jira_3.22.1/jira.jar!/com/polarion/synchronizer/proxy/jira/translators/JiraToPolarionDurationTranslator.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */