/*    */ package com.polarion.synchronizer.proxy.jira.translators.html2wiki;
/*    */ 
/*    */ import com.polarion.alm.tracker.ITestManagementService;
/*    */ import com.polarion.synchronizer.ILogger;
/*    */ import com.polarion.synchronizer.model.ITransactionProxy;
/*    */ import com.polarion.synchronizer.proxy.polarion.IPolarionProxy;
/*    */ import com.polaron.synchronizer.macrolink.AbstractMacroLinkConverter;
/*    */ import com.polaron.synchronizer.macrolink.MacroLinkHelper;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ import org.jsoup.nodes.Element;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class JiraMacroLinkConverter
/*    */   extends AbstractMacroLinkConverter
/*    */ {
/*    */   public JiraMacroLinkConverter(@NotNull IPolarionProxy polarionProxy, @NotNull ITestManagementService testManagementService, @NotNull String baseUrl, @NotNull ILogger logger) {
/* 37 */     super((ITransactionProxy)polarionProxy, testManagementService, baseUrl, logger);
/*    */   }
/*    */ 
/*    */   
/*    */   protected void createTestRunMacroLink(@NotNull MacroLinkHelper.TestRunMacroLinkData data, @NotNull Element element, @NotNull StringBuilder sb) {
/* 42 */     if (data.getTestRunImgPath() != null || data.getTestRunLabelStr() != null) {
/* 43 */       sb.append("!");
/* 44 */       sb.append(data.getTestRunImgPath());
/* 45 */       sb.append("!");
/* 46 */       sb.append("[");
/* 47 */       sb.append(data.getTestRunLabelStr());
/* 48 */       sb.append("|");
/*    */     } else {
/* 50 */       sb.append("[");
/*    */     } 
/* 52 */     sb.append(data.getTestRunPath());
/* 53 */     sb.append("]");
/*    */   }
/*    */ 
/*    */   
/*    */   protected void createWorkItemMacroLink(@NotNull MacroLinkHelper.WorkItemMacroLinkData data, @NotNull Element element, @NotNull StringBuilder sb) {
/* 58 */     if (data.getRevisionImgPath() != null) {
/* 59 */       sb.append("!");
/* 60 */       sb.append(data.getRevisionImgPath());
/* 61 */       sb.append("!");
/*    */     } 
/* 63 */     if (data.getWorkItemImgPath() != null) {
/* 64 */       sb.append("!");
/* 65 */       sb.append(data.getWorkItemImgPath());
/* 66 */       sb.append("!");
/*    */     } 
/* 68 */     sb.append("[");
/* 69 */     sb.append(data.getWorkTitleStr());
/* 70 */     sb.append("|");
/* 71 */     sb.append(data.getWorkItemPath());
/* 72 */     sb.append("]");
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer.proxy.jira_3.22.1/jira.jar!/com/polarion/synchronizer/proxy/jira/translators/html2wiki/JiraMacroLinkConverter.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */