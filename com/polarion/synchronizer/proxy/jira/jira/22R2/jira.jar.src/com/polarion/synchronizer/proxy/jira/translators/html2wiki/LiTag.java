/*    */ package com.polarion.synchronizer.proxy.jira.translators.html2wiki;
/*    */ 
/*    */ import com.polarion.synchronizer.proxy.htmltranslator.ElementInfo;
/*    */ import com.polarion.synchronizer.proxy.htmltranslator.Tag;
/*    */ import java.util.Collection;
/*    */ import java.util.Collections;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ import org.jsoup.nodes.Element;
/*    */ import org.jsoup.nodes.Node;
/*    */ 
/*    */ 
/*    */ 
/*    */ public class LiTag
/*    */   implements Tag, ListTag
/*    */ {
/*    */   public void open(@NotNull Element element, @NotNull ElementInfo elementInfo, @NotNull StringBuilder sb, @NotNull Collection<Tag> openTags) {
/* 17 */     String prefix = "";
/* 18 */     int level = (int)getLevel(openTags) + 1;
/* 19 */     if (UlTag.isInUlTag(openTags)) {
/* 20 */       if (element.attributes().size() == 0 && level > 0) {
/* 21 */         prefix = String.join("", Collections.nCopies(level, "*"));
/*    */       }
/*    */     }
/* 24 */     else if (OlTag.isInOlTag(openTags) && 
/* 25 */       element.attributes().size() == 0 && level > 0) {
/* 26 */       prefix = String.join("", Collections.nCopies(level, "#"));
/*    */     } 
/*    */ 
/*    */ 
/*    */     
/* 31 */     sb.append(prefix.isEmpty() ? prefix : (String.valueOf(prefix) + " "));
/*    */   }
/*    */ 
/*    */   
/*    */   public void close(@NotNull Element element, @NotNull ElementInfo elementInfo, @NotNull StringBuilder sb, @NotNull Collection<Tag> openTags) {
/* 36 */     if (element.attributes().size() == 0) {
/* 37 */       boolean hasSublists = element.childNodes().stream().filter(n -> {
/*    */             if (n instanceof Element) {
/*    */               String tag = ((Element)n).tag().getName();
/* 40 */               return !(!"ol".equals(tag) && !"ul".equals(tag));
/*    */             } 
/*    */             return false;
/* 43 */           }).findFirst().isPresent();
/* 44 */       if (!sb.toString().endsWith("\n") && !hasSublists) {
/* 45 */         sb.append("\n");
/*    */       }
/*    */     } 
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public void process(@NotNull Element element, @NotNull ElementInfo elementInfo, @NotNull StringBuilder sb, @NotNull Collection<Tag> openTags) {}
/*    */ 
/*    */ 
/*    */   
/*    */   public boolean isContinue() {
/* 57 */     return true;
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer.proxy.jira_3.22.1/jira.jar!/com/polarion/synchronizer/proxy/jira/translators/html2wiki/LiTag.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */