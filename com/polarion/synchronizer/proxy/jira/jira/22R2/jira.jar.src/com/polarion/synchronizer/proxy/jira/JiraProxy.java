/*     */ package com.polarion.synchronizer.proxy.jira;
/*     */ 
/*     */ import com.polarion.core.util.ObjectUtils;
/*     */ import com.polarion.platform.i18n.Localization;
/*     */ import com.polarion.synchronizer.ILogger;
/*     */ import com.polarion.synchronizer.SynchronizationException;
/*     */ import com.polarion.synchronizer.model.Attachment;
/*     */ import com.polarion.synchronizer.model.CollectionUpdate;
/*     */ import com.polarion.synchronizer.model.FieldDefinition;
/*     */ import com.polarion.synchronizer.model.Option;
/*     */ import com.polarion.synchronizer.model.TransferItem;
/*     */ import com.polarion.synchronizer.model.UpdateResult;
/*     */ import com.polarion.synchronizer.proxy.jira.json.Issue;
/*     */ import com.polarion.synchronizer.proxy.jira.json.IssueType;
/*     */ import com.polarion.synchronizer.proxy.jira.json.Project;
/*     */ import com.polarion.synchronizer.proxy.jira.json.Scope;
/*     */ import com.polarion.synchronizer.proxy.jira.json.Version;
/*     */ import com.polarion.synchronizer.spi.AbstractProxy;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Arrays;
/*     */ import java.util.Collection;
/*     */ import java.util.HashSet;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import java.util.Optional;
/*     */ import java.util.function.Predicate;
/*     */ import java.util.stream.Collectors;
/*     */ import java.util.stream.Stream;
/*     */ import javax.ws.rs.core.Response;
/*     */ import org.apache.commons.io.IOUtils;
/*     */ import org.codehaus.jettison.json.JSONArray;
/*     */ import org.codehaus.jettison.json.JSONException;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ import org.jetbrains.annotations.Nullable;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class JiraProxy
/*     */   extends AbstractProxy
/*     */   implements IJiraProxy
/*     */ {
/*     */   public static final String EPIC_LINK_ROLE = "LINKED_EPIC";
/*     */   public static final String KEY_ITEM_ID = "com.polarion.synchronizer:jira:item-id";
/*     */   public static final String KEY_STATUS = "status";
/*     */   public static final String TYPE_JIRA_RICH_TEXT = "jira:rich-text";
/*     */   public static final String TYPE_JIRA_COMMENT = "jira:comment";
/*     */   public static final String TYPE_JIRA_DURATION = "jira:duration";
/*     */   public static final String TYPE_JIRA_RELATION = "jira:relation";
/*     */   public static final String TYPE_JIRA_SPRINT = "jira:sprint";
/*     */   public static final String TYPE_VERSION = "version";
/*     */   public static final String KEY_REMAINING_ESTIMATE = "timetracking.remainingEstimate";
/*     */   public static final String KEY_ORIGINAL_ESTIMATE = "timetracking.originalEstimate";
/*     */   public static final String KEY_TIME_SPENT = "timetracking.timeSpent";
/*  94 */   private static final boolean DEBUG = "true".equals(System.getProperty("jiraproxy.debug"));
/*     */   
/*     */   public static final String KEY_LINKS = "issuelinks";
/*  97 */   public static final FieldDefinition FIELD_ITEM_ID = new FieldDefinition("com.polarion.synchronizer:jira:item-id", Localization.getString("synchronizer.jira.key.itemId"), "string", true, false);
/*     */   
/*     */   public static final String KEY_FIX_VERSIONS = "fixVersions";
/*     */   
/*     */   public static final String KEY_FIX_VERSIONS_IDS = "fixVersionIds";
/*     */   
/*     */   public static final String KEY_VERSION_NAME = "com.polarion.synchronizer:jira:virtual-type:name";
/*     */   
/*     */   public static final String KEY_VERSION_START_DATE = "com.polarion.synchronizer:jira:virtual-type:startDate";
/*     */   
/*     */   public static final String KEY_VERSION_END_DATE = "com.polarion.synchronizer:jira:virtual-type:endDate";
/*     */   
/* 109 */   public static final FieldDefinition FIELD_FIX_VERSIONS_IDS = new FieldDefinition("fixVersionIds", "Fix versions (Ids)", "string", false, true);
/*     */   
/*     */   @NotNull
/* 112 */   protected static final Collection<String> VERSION_KEYS = new HashSet<>(Arrays.asList(new String[] { "com.polarion.synchronizer:jira:virtual-type:name", "com.polarion.synchronizer:jira:virtual-type:startDate", "com.polarion.synchronizer:jira:virtual-type:endDate" }));
/*     */ 
/*     */   
/*     */   private final String itemsJql;
/*     */   
/*     */   private final Project project;
/*     */   
/*     */   private final JiraProxyTranslator translator;
/*     */   
/*     */   private ILogger log;
/*     */   
/*     */   @NotNull
/*     */   private final JiraConnector jiraConnector;
/*     */   
/*     */   @NotNull
/*     */   private final AbstractTransferHelper updateHelper;
/*     */   
/*     */   @NotNull
/*     */   private final AbstractTransferHelper createHelper;
/*     */   
/*     */   private boolean isCloudDeployment;
/*     */   
/*     */   @NotNull
/*     */   private String projectKey;
/*     */   
/*     */   @NotNull
/* 138 */   List<Version> queriedJiraVersions = new ArrayList<>();
/*     */   public JiraProxy(JiraProxyConfiguration configuration, ILogger log) {
/* 140 */     this.virtualTypeField = (key -> key.contains("jira:virtual-type"));
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 149 */     this.log = log;
/*     */     
/* 151 */     String configurationError = configuration.checkConfiguration();
/* 152 */     if (configurationError != null) {
/* 153 */       throw new SynchronizationException("Jira configuration error :" + configurationError + ". Please check Jira connection properties.");
/*     */     }
/* 155 */     this.jiraConnector = new JiraConnector((JiraConnection)ObjectUtils.notNull(configuration.getConnection()));
/* 156 */     this.jiraConnector.setProjectPageSize(configuration.getProjectPageSize());
/* 157 */     this.jiraConnector.setPageSize(configuration.getPageSize());
/* 158 */     this.isCloudDeployment = checkIfIsCloudDeployment();
/* 159 */     String projectKeyOrName = (String)Optional.<String>ofNullable(configuration.getProject())
/* 160 */       .flatMap(project -> project.isEmpty() ? Optional.empty() : Optional.<String>of(project))
/* 161 */       .orElseThrow(() -> new SynchronizationException(Localization.getString("synchronizer.jira.projectNotSet")));
/*     */     
/* 163 */     this.project = findProject(projectKeyOrName)
/* 164 */       .<Throwable>orElseThrow(() -> new SynchronizationException(Localization.getString("synchronizer.jira.unknownProject", new String[] { paramString })));
/*     */     
/* 166 */     this.projectKey = (String)Optional.<String>ofNullable(this.project.getKey())
/* 167 */       .orElseThrow(() -> new SynchronizationException(Localization.getString("synchronizer.jira.projectWithoutKey")));
/*     */ 
/*     */     
/* 170 */     String itemsJql = "project=" + this.projectKey;
/*     */     
/* 172 */     String query = configuration.getQuery();
/* 173 */     if (query == null || query.isEmpty()) {
/* 174 */       this.itemsJql = itemsJql;
/*     */     } else {
/* 176 */       this.itemsJql = String.valueOf(itemsJql) + " AND (" + query + ")";
/*     */     } 
/*     */     
/* 179 */     JSONArray definedFields = null;
/*     */     try {
/* 181 */       definedFields = this.jiraConnector.getFields();
/* 182 */       this.translator = new JiraProxyTranslator(definedFields, log, configuration.getServerUrl(), this.isCloudDeployment);
/* 183 */     } catch (JSONException e) {
/* 184 */       throw new SynchronizationException("Failed to load Jira defined fields: " + definedFields, e);
/*     */     } 
/*     */     
/* 187 */     this.updateHelper = new UpdateHelper(this, this.jiraConnector, this.translator, log);
/* 188 */     this.createHelper = new CreateHelper(this.projectKey, this, this.jiraConnector, this.translator, log);
/*     */   }
/*     */   @NotNull
/*     */   private Predicate<String> virtualTypeField;
/*     */   public Collection<FieldDefinition> getDefinedFields(@Nullable String typeId) {
/* 193 */     return this.translator.getDefinedFields(typeId);
/*     */   }
/*     */   
/*     */   Issue getIssue(String issueKey, Collection<String> keys) {
/* 197 */     Collection<String> mappedKeys = this.translator.getMappedKeys(keys);
/* 198 */     if (mappedKeys.contains("issuelinks")) {
/* 199 */       mappedKeys.add(this.translator.getEpicLinkFieldId());
/*     */     }
/* 201 */     Exception exception1 = null, exception2 = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*     */     try {
/*     */     
/*     */     } finally {
/* 225 */       exception2 = null; if (exception1 == null) { exception1 = exception2; } else if (exception1 != exception2) { exception1.addSuppressed(exception2); }
/*     */     
/*     */     } 
/*     */   } @Nullable
/*     */   public Version getVersion(@NotNull String versionId) {
/* 230 */     Response response = this.jiraConnector.getVersion(versionId);
/* 231 */     int versionStatusCode = response.getStatus();
/* 232 */     if (versionStatusCode == Response.Status.OK.getStatusCode()) {
/* 233 */       return (Version)response.readEntity(Version.class);
/*     */     }
/* 235 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void checkKeys(Issue issue, Collection<String> keys) {
/* 247 */     if (DEBUG) {
/* 248 */       Map<String, ? extends Object> fieldsMap = issue.getFields();
/* 249 */       if (fieldsMap != null) {
/* 250 */         Collection<String> fields = new HashSet<>(fieldsMap.keySet());
/* 251 */         fields.removeAll(keys);
/* 252 */         if (!fields.isEmpty()) {
/* 253 */           this.log.error("Jira returned the following unexpected fields for the issue " + issue.getId() + ": " + String.join(",", (Iterable)fields));
/*     */         }
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public Collection<TransferItem> getItems(@NotNull Collection<String> ids, @NotNull Collection<String> keys) {
/* 262 */     this.queriedJiraVersions.clear();
/* 263 */     List<TransferItem> allTransferItems = new ArrayList<>();
/* 264 */     boolean isVersionType = keys.contains("com.polarion.synchronizer:jira:virtual-type:name");
/* 265 */     boolean isFixVersionFieldAdded = false;
/* 266 */     if (isVersionType) {
/* 267 */       this.queriedJiraVersions = getAllVersionsFromProject();
/* 268 */       if (keys.stream().noneMatch(key -> "fixVersions".equals(key))) {
/* 269 */         keys.add("fixVersions");
/* 270 */         isFixVersionFieldAdded = true;
/*     */       } 
/*     */     } 
/* 273 */     for (String issueKey : ids) {
/* 274 */       Issue foundIssue = getIssue(issueKey, keys);
/* 275 */       if (foundIssue != null && foundIssue.getKey().equals(issueKey)) {
/* 276 */         TransferItem transferItem = this.translator.issueToTransferItem(foundIssue, keys);
/* 277 */         fillAttachments(transferItem);
/* 278 */         allTransferItems.add(transferItem);
/*     */         
/* 280 */         if (isVersionType) {
/* 281 */           List<TransferItem> transferItemsForVersions = this.translator.addVersionsToTransferItems(foundIssue, transferItem, this.queriedJiraVersions, keys, allTransferItems);
/* 282 */           allTransferItems.addAll(transferItemsForVersions);
/*     */         } 
/* 284 */         if (isFixVersionFieldAdded)
/* 285 */           transferItem.getValues().remove("fixVersions"); 
/*     */         continue;
/*     */       } 
/* 288 */       Version jiraVersion = getVersion(issueKey);
/* 289 */       if (jiraVersion != null) {
/* 290 */         TransferItem transferItem = this.translator.versionToTransferItem(jiraVersion, keys);
/* 291 */         allTransferItems.add(transferItem);
/* 292 */         this.translator.addVersionIds(issueKey);
/*     */       } 
/*     */     } 
/*     */     
/* 296 */     return allTransferItems;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   private List<Version> getAllVersionsFromProject() {
/* 304 */     Response response = this.jiraConnector.getAllVersionsFromProject(this.projectKey);
/* 305 */     if (response.getStatusInfo().getFamily() == Response.Status.Family.SUCCESSFUL) {
/* 306 */       return (List<Version>)Stream.<Version>of((Version[])response.readEntity(Version[].class)).collect(Collectors.toList());
/*     */     }
/* 308 */     throw new SynchronizationException(
/* 309 */         Localization.getString("synchronizer.jira.findVersionsFromProjectError", new String[] { this.projectKey, 
/* 310 */             String.valueOf(String.valueOf(response.getStatus())) + " " + (String)response.readEntity(String.class) }));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void fillAttachments(@NotNull TransferItem transferItem) {
/* 323 */     Collection<JiraAttachment> attachments = (Collection<JiraAttachment>)transferItem.getValue("attachments");
/* 324 */     if (attachments != null) {
/* 325 */       Collection<Attachment> withContent = new ArrayList<>(attachments.size());
/* 326 */       for (JiraAttachment attachment : attachments) {
/* 327 */         Attachment updatedAttachment = new Attachment(attachment.getFileName(), () -> IOUtils.toByteArray(this.jiraConnector.loadAttachmentContent(paramJiraAttachment.getContentUrl())));
/*     */ 
/*     */         
/* 330 */         withContent.add(updatedAttachment);
/*     */       } 
/* 332 */       transferItem.put("attachments", withContent);
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public Collection<TransferItem> getScopeItems(@NotNull Collection<String> keys) {
/* 339 */     Collection<String> issueKeys = this.jiraConnector.searchIssues(this.itemsJql);
/* 340 */     return getItems(issueKeys, keys);
/*     */   }
/*     */   @NotNull
/*     */   private Optional<Project> findProject(@NotNull String projectKeyOrName) {
/*     */     try {
/* 345 */       if (this.isCloudDeployment) {
/* 346 */         return this.jiraConnector.searchProjectOnJiraCloud(projectKeyOrName);
/*     */       }
/* 348 */       return this.jiraConnector.searchProjectOnJiraPremises(projectKeyOrName);
/*     */     }
/* 350 */     catch (Exception e) {
/* 351 */       this.log.error("Unexpected error while loading Jira project " + projectKeyOrName, e);
/* 352 */       throw new SynchronizationException(
/* 353 */           Localization.getString("synchronizer.jira.findProjectError", new String[] { projectKeyOrName, e.getLocalizedMessage() }));
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Nullable
/*     */   static String checkResponse(@NotNull Response response) {
/* 367 */     if (response.getStatus() == 204 || response.getStatus() == 201 || response.getStatus() == 200) {
/* 368 */       return null;
/*     */     }
/* 370 */     return "\nStatus " + response.getStatus() + ": " + (String)response.readEntity(String.class);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public List<UpdateResult> update(@NotNull List<TransferItem> items) {
/* 378 */     List<TransferItem> issueTypeTransferItem = (List<TransferItem>)items.stream().filter(item -> item.getValues().keySet().stream().noneMatch(this.virtualTypeField)).collect(Collectors.toList());
/*     */     
/* 380 */     translateJiraVersionFieldValues(issueTypeTransferItem);
/* 381 */     return transfer(items);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void translateJiraVersionFieldValues(@NotNull List<TransferItem> issueTypeTransferItem) {
/* 389 */     for (TransferItem transferItem : issueTypeTransferItem) {
/* 390 */       Map<String, Object> attributesMap = transferItem.getValues();
/* 391 */       if (attributesMap.containsKey("fixVersionIds")) {
/* 392 */         CollectionUpdate<?> values = (CollectionUpdate)transferItem.getValue("fixVersionIds");
/* 393 */         Predicate<? super Version> matchedAddedVersion = version -> paramCollectionUpdate.getAdded().stream().anyMatch(());
/* 394 */         List<String> addedVersionNames = new ArrayList<>();
/* 395 */         this.queriedJiraVersions.stream().filter(matchedAddedVersion).forEach(version -> {
/*     */             
/* 397 */             }); Predicate<? super Version> matchedRemovedVersion = version -> paramCollectionUpdate.getRemoved().stream().anyMatch(());
/* 398 */         List<String> removedVersionNames = new ArrayList<>();
/* 399 */         this.queriedJiraVersions.stream().filter(matchedRemovedVersion).forEach(version -> { 
/* 400 */             }); transferItem.getValues().remove("fixVersionIds");
/* 401 */         transferItem.getValues().put("fixVersionIds", new CollectionUpdate(addedVersionNames, removedVersionNames));
/*     */       } 
/*     */     } 
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private List<UpdateResult> transfer(@NotNull List<TransferItem> items) {
/* 408 */     List<UpdateResult> transferResults = new ArrayList<>(items.size());
/* 409 */     for (TransferItem transferItem : items) {
/* 410 */       if (transferItem.getValues().keySet().stream().noneMatch(this.virtualTypeField)) {
/* 411 */         if (transferItem.getId() == null) {
/* 412 */           transferResults.add(this.createHelper.transfer(transferItem)); continue;
/*     */         } 
/* 414 */         transferResults.add(this.updateHelper.transfer(transferItem));
/*     */         continue;
/*     */       } 
/* 417 */       String projectId = this.project.getId();
/* 418 */       if (projectId != null) {
/* 419 */         if (transferItem.getId() == null) {
/* 420 */           transferResults.add(this.createHelper.transferVirtualItems(transferItem, projectId)); continue;
/*     */         } 
/* 422 */         transferResults.add(this.updateHelper.transferVirtualItems(transferItem, projectId));
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/* 427 */     return transferResults;
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public List<UpdateResult> delete(@NotNull List<String> ids) {
/* 433 */     List<UpdateResult> allResults = new ArrayList<>(ids.size());
/* 434 */     List<String> versionKeys = (List<String>)ids.stream().filter(id -> this.translator.getVersionIds().contains(id)).collect(Collectors.toList());
/* 435 */     List<String> issueKeys = (List<String>)ids.stream().filter(id -> !this.translator.getVersionIds().contains(id)).collect(Collectors.toList());
/*     */     
/* 437 */     List<UpdateResult> deleteVersionResult = new ArrayList<>(versionKeys.size());
/* 438 */     for (String versionKey : versionKeys) {
/* 439 */       UpdateResult updateResult = new UpdateResult(checkResponse(this.jiraConnector.deleteVersion(versionKey)));
/* 440 */       deleteVersionResult.add(updateResult);
/*     */     } 
/*     */     
/* 443 */     for (String issueKey : issueKeys) {
/* 444 */       UpdateResult updateResult = new UpdateResult(checkResponse(this.jiraConnector.deleteIssue(issueKey)));
/* 445 */       allResults.add(updateResult);
/*     */     } 
/* 447 */     allResults.addAll(deleteVersionResult);
/* 448 */     return allResults;
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public String getUserURL() {
/* 454 */     return this.jiraConnector.getUserURL();
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean isHierarchySupported() {
/* 459 */     return false;
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public Collection<Option> getDefinedTypes() {
/* 465 */     Exception exception1 = null, exception2 = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*     */     try {
/*     */     
/*     */     } finally {
/* 476 */       exception2 = null; if (exception1 == null) { exception1 = exception2; } else if (exception1 != exception2) { exception1.addSuppressed(exception2); }
/*     */     
/*     */     } 
/*     */   }
/*     */   public String getContentScope() {
/* 481 */     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean hasNonSynchronizableFields() {
/* 486 */     return false;
/*     */   }
/*     */   
/*     */   private boolean checkIfIsCloudDeployment() {
/* 490 */     Exception exception1 = null, exception2 = null;
/*     */ 
/*     */     
/*     */     try {
/*     */     
/*     */     } finally {
/* 496 */       exception2 = null; if (exception1 == null) { exception1 = exception2; } else if (exception1 != exception2) { exception1.addSuppressed(exception2); }
/*     */     
/*     */     } 
/*     */   }
/*     */   public boolean isCloudDeployment() {
/* 501 */     return this.isCloudDeployment;
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer.proxy.jira_3.22.1/jira.jar!/com/polarion/synchronizer/proxy/jira/JiraProxy.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */