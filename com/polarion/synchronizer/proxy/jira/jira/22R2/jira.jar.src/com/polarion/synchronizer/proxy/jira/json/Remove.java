/*    */ package com.polarion.synchronizer.proxy.jira.json;
/*    */ 
/*    */ public class Remove
/*    */   extends Operation {
/*    */   private Object remove;
/*    */   
/*    */   public Remove(Object value) {
/*  8 */     this.remove = value;
/*    */   }
/*    */   
/*    */   public Object getRemove() {
/* 12 */     return this.remove;
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer.proxy.jira_3.22.1/jira.jar!/com/polarion/synchronizer/proxy/jira/json/Remove.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */