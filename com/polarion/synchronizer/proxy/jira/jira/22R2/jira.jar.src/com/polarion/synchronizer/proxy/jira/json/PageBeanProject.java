/*    */ package com.polarion.synchronizer.proxy.jira.json;
/*    */ 
/*    */ import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
/*    */ import com.fasterxml.jackson.annotation.JsonProperty;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ import org.jetbrains.annotations.Nullable;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @JsonIgnoreProperties(ignoreUnknown = true)
/*    */ public class PageBeanProject
/*    */ {
/*    */   @Nullable
/*    */   private final String currentPageUrl;
/*    */   @Nullable
/*    */   private final String nextPageUrl;
/*    */   private final int maxResults;
/*    */   private final int startAt;
/*    */   private final int total;
/*    */   private final boolean isLast;
/*    */   @NotNull
/*    */   private final Project[] projects;
/*    */   
/*    */   public PageBeanProject(@JsonProperty("self") @Nullable String currentPageUrl, @JsonProperty("nextPage") @Nullable String nextPageUrl, @JsonProperty("maxResults") int maxResults, @JsonProperty("startAt") int startAt, @JsonProperty("total") int total, @JsonProperty("isLast") boolean isLast, @JsonProperty("values") @NotNull Project[] projects) {
/* 27 */     this.currentPageUrl = currentPageUrl;
/* 28 */     this.nextPageUrl = nextPageUrl;
/* 29 */     this.maxResults = maxResults;
/* 30 */     this.startAt = startAt;
/* 31 */     this.total = total;
/* 32 */     this.isLast = isLast;
/* 33 */     this.projects = projects;
/*    */   }
/*    */   
/*    */   @Nullable
/*    */   public String getCurrentPageUrl() {
/* 38 */     return this.currentPageUrl;
/*    */   }
/*    */   
/*    */   @Nullable
/*    */   public String getNextPageUrl() {
/* 43 */     return this.nextPageUrl;
/*    */   }
/*    */   
/*    */   public int getMaxResults() {
/* 47 */     return this.maxResults;
/*    */   }
/*    */   
/*    */   public int getStartAt() {
/* 51 */     return this.startAt;
/*    */   }
/*    */   
/*    */   public int getTotal() {
/* 55 */     return this.total;
/*    */   }
/*    */   
/*    */   public boolean isLast() {
/* 59 */     return this.isLast;
/*    */   }
/*    */   
/*    */   @NotNull
/*    */   public Project[] getProjects() {
/* 64 */     return this.projects;
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer.proxy.jira_3.22.1/jira.jar!/com/polarion/synchronizer/proxy/jira/json/PageBeanProject.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */