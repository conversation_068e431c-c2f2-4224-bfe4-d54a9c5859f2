/*     */ package com.polarion.synchronizer.proxy.jira;
/*     */ 
/*     */ import com.polarion.synchronizer.ILogger;
/*     */ import com.polarion.synchronizer.SynchronizationException;
/*     */ import com.polarion.synchronizer.model.CreateResult;
/*     */ import com.polarion.synchronizer.model.TransferItem;
/*     */ import com.polarion.synchronizer.model.UpdateResult;
/*     */ import com.polarion.synchronizer.proxy.jira.json.CreateMetaData;
/*     */ import com.polarion.synchronizer.proxy.jira.json.CreateMetaDataIssueType;
/*     */ import com.polarion.synchronizer.proxy.jira.json.CreateMetaDataProject;
/*     */ import com.polarion.synchronizer.proxy.jira.json.Fields;
/*     */ import com.polarion.synchronizer.proxy.jira.json.Issue;
/*     */ import com.polarion.synchronizer.proxy.jira.json.UpdateIssue;
/*     */ import com.polarion.synchronizer.proxy.jira.json.Version;
/*     */ import java.util.Collection;
/*     */ import java.util.Collections;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import javax.ws.rs.core.Response;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ import org.jetbrains.annotations.Nullable;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CreateHelper
/*     */   extends AbstractTransferHelper
/*     */ {
/*     */   @NotNull
/*     */   private final String projectKey;
/*     */   @NotNull
/*  53 */   private final Map<String, Fields> createMetadataMap = new HashMap<>();
/*     */   
/*     */   public CreateHelper(@NotNull String projectKey, @NotNull JiraProxy jiraProxy, @NotNull JiraConnector jiraConnector, @NotNull JiraProxyTranslator translator, @NotNull ILogger log) {
/*  56 */     super(jiraProxy, jiraConnector, translator, log);
/*  57 */     this.projectKey = projectKey;
/*  58 */     loadCreateMetadata(projectKey);
/*     */   }
/*     */   
/*     */   private void loadCreateMetadata(@NotNull String projectKey) {
/*     */     try {
/*  63 */       CreateMetaData createMetadata = this.jiraConnector.getCreateMetadata(projectKey);
/*  64 */       Collection<CreateMetaDataProject> projects = createMetadata.getProjects();
/*  65 */       if (projects.size() == 1) {
/*  66 */         CreateMetaDataProject project = projects.iterator().next();
/*  67 */         Collection<CreateMetaDataIssueType> issuetypes = project.getIssuetypes();
/*  68 */         for (CreateMetaDataIssueType issueType : issuetypes) {
/*  69 */           this.createMetadataMap.put(issueType.getName(), issueType);
/*     */         }
/*     */       } else {
/*  72 */         throw new SynchronizationException("Expected to find exactly one instance of create metadata for project " + projectKey + " but found " + projects.size());
/*     */       }
/*     */     
/*  75 */     } catch (Exception e) {
/*  76 */       throw new SynchronizationException("No metadata for the creation of items in Jira project " + projectKey + " found. Check if user has access permissions to the project.", e);
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   protected UpdateResult performTransfer(@NotNull UpdateIssue issue, @NotNull TransferItem transferItem, @NotNull Collection<String> updateFields) {
/*  83 */     issue.getFields().put("project", Collections.singletonMap("key", this.projectKey));
/*  84 */     Exception exception1 = null, exception2 = null;
/*     */     try {
/*     */     
/*     */     } finally {
/*  88 */       exception2 = null; if (exception1 == null) { exception1 = exception2; } else if (exception1 != exception2) { exception1.addSuppressed(exception2); }
/*     */     
/*     */     } 
/*     */   } @NotNull
/*     */   private CreateResult checkCreateResponse(@NotNull Map<String, ? extends Object> checkIssue, @NotNull Response response) {
/*  93 */     if (response.getStatus() == 204 || response.getStatus() == 201) {
/*  94 */       Issue createdIssue = (Issue)response.readEntity(Issue.class);
/*  95 */       return new CreateResult(createdIssue.getKey(), null);
/*     */     } 
/*  97 */     return new CreateResult(null, "Status " + 
/*  98 */         response.getStatus() + ": " + this.translator.fixErrorMessages(checkIssue, (String)response.readEntity(String.class)));
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   @Nullable
/*     */   protected String loadEditableFields(@NotNull TransferItem transferItem, @NotNull Collection<String> allFields, @NotNull Collection<String> result) {
/* 105 */     String type = transferItem.getType();
/* 106 */     Fields fields = this.createMetadataMap.get(type);
/* 107 */     if (fields == null) {
/* 108 */       return "Create metadata for type '" + type + "' is not found in project. Check if all Work Item types that are mapped in the Sync Pair are present in Jira project. Failed item: " + transferItem;
/*     */     }
/* 110 */     result.addAll(loadEditableFields(allFields, fields.getFields()));
/* 111 */     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   @Nullable
/*     */   protected TransferItem loadOldItemIfRequired(@NotNull TransferItem transferItem) {
/* 117 */     if (transferItem.getId() != null) {
/* 118 */       TransferItem oldItem = null;
/* 119 */       Collection<String> requiredOldValues = this.translator.requiredOldValues(transferItem);
/* 120 */       if (!requiredOldValues.isEmpty()) {
/* 121 */         Issue oldIssue = this.jiraProxy.getIssue(transferItem.getId(), requiredOldValues);
/* 122 */         oldItem = this.translator.issueToTransferItem(oldIssue, requiredOldValues);
/*     */       } 
/* 124 */       return oldItem;
/*     */     } 
/* 126 */     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   protected UpdateResult createErrorResult(@NotNull String error) {
/* 132 */     return (UpdateResult)new CreateResult(null, error);
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   protected UpdateResult performTransferVersion(@NotNull Version version, @NotNull TransferItem transferItem, @NotNull Collection<String> fields) {
/* 138 */     Response response = this.jiraConnector.createVersion(version);
/* 139 */     CreateResult createResult = checkCreateVersionResponse(transferItem.getValues(), response);
/* 140 */     transferItem.setId(createResult.getCreatedId());
/* 141 */     return (UpdateResult)createResult;
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private CreateResult checkCreateVersionResponse(@NotNull Map<String, ? extends Object> checkVersion, @NotNull Response response) {
/* 146 */     if (response.getStatus() == 204 || response.getStatus() == 201) {
/* 147 */       Version createdVersion = (Version)response.readEntity(Version.class);
/* 148 */       return new CreateResult(createdVersion.getId(), null);
/*     */     } 
/* 150 */     return new CreateResult(null, "Status " + 
/* 151 */         response.getStatus() + ": " + this.translator.fixErrorMessages(checkVersion, (String)response.readEntity(String.class)));
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   @Nullable
/*     */   protected TransferItem loadOldVersionIfRequired(@NotNull TransferItem transferItem) {
/* 158 */     TransferItem oldItem = null;
/* 159 */     if (transferItem.getId() != null) {
/* 160 */       Version oldVersion = this.jiraProxy.getVersion(transferItem.getId());
/* 161 */       oldItem = this.translator.versionToTransferItem(oldVersion, JiraProxy.VERSION_KEYS);
/*     */     } 
/* 163 */     return oldItem;
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer.proxy.jira_3.22.1/jira.jar!/com/polarion/synchronizer/proxy/jira/CreateHelper.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */