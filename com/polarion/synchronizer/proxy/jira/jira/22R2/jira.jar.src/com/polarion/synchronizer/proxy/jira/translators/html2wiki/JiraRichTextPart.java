/*    */ package com.polarion.synchronizer.proxy.jira.translators.html2wiki;
/*    */ 
/*    */ import com.polarion.core.util.ObjectUtils;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ import org.jetbrains.annotations.Nullable;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class JiraRichTextPart
/*    */ {
/*    */   @NotNull
/*    */   private String text;
/*    */   @Nullable
/*    */   private StringBuilder style;
/*    */   @Nullable
/*    */   private String tagName;
/*    */   @NotNull
/* 19 */   private String color = "";
/*    */   
/*    */   public JiraRichTextPart(@NotNull String text, @Nullable StringBuilder style, @Nullable String tagName) {
/* 22 */     this.text = text;
/* 23 */     this.style = style;
/* 24 */     this.tagName = tagName;
/* 25 */     if (style != null) {
/* 26 */       extractColor(style.toString());
/*    */     }
/*    */   }
/*    */   
/*    */   private void extractColor(String styleAsString) {
/* 31 */     int colorIdx = styleAsString.indexOf("{color");
/* 32 */     if (colorIdx >= 0) {
/* 33 */       int endColorIdx = styleAsString.indexOf("}");
/* 34 */       this.color = styleAsString.substring(colorIdx, endColorIdx + 1);
/* 35 */       styleAsString = styleAsString.replace(this.color, "");
/* 36 */       this.style = new StringBuilder(styleAsString);
/*    */     } 
/*    */   }
/*    */   
/*    */   @NotNull
/*    */   public String getFormattedText(boolean shouldFormat) {
/* 42 */     if (this.style != null && shouldFormat && 
/* 43 */       !this.text.equals(" ")) {
/* 44 */       String trimmedText = this.text.trim();
/* 45 */       String withFormat = "";
/*    */       
/* 47 */       withFormat = String.valueOf(((StringBuilder)ObjectUtils.notNull(this.style)).toString()) + this.text.trim() + ((StringBuilder)ObjectUtils.notNull(this.style)).reverse().toString();
/* 48 */       if (!this.color.isEmpty()) {
/* 49 */         withFormat = applyColor(withFormat);
/*    */       }
/* 51 */       if (withFormat.contains("_") && withFormat.contains("— ")) {
/* 52 */         withFormat = withFormat.replaceAll("— ", "??");
/* 53 */         withFormat = String.valueOf(withFormat) + "??";
/* 54 */         withFormat = withFormat.replaceAll("_", "");
/*    */       } 
/*    */       
/* 57 */       return this.text.replace(trimmedText, withFormat);
/*    */     } 
/*    */     
/* 60 */     if (!this.color.isEmpty()) {
/* 61 */       this.text = applyColor(this.text);
/*    */     }
/*    */     
/* 64 */     return this.text;
/*    */   }
/*    */   
/*    */   @NotNull
/*    */   private String applyColor(@NotNull String text) {
/* 69 */     return String.valueOf(this.color) + text + "{color}";
/*    */   }
/*    */   
/*    */   public boolean isSpan() {
/* 73 */     return (this.tagName != null && "span".equals(this.tagName));
/*    */   }
/*    */   
/*    */   @NotNull
/*    */   public String getText() {
/* 78 */     return this.text;
/*    */   }
/*    */   
/*    */   public void setText(@NotNull String text) {
/* 82 */     this.text = text;
/*    */   }
/*    */   
/*    */   @Nullable
/*    */   public StringBuilder getStyle() {
/* 87 */     return this.style;
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer.proxy.jira_3.22.1/jira.jar!/com/polarion/synchronizer/proxy/jira/translators/html2wiki/JiraRichTextPart.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */