/*    */ package com.polarion.synchronizer.proxy.jira;
/*    */ 
/*    */ import com.polarion.platform.i18n.Localization;
/*    */ import com.polarion.synchronizer.spi.Connection;
/*    */ import java.util.ArrayList;
/*    */ import java.util.Collection;
/*    */ import javax.xml.bind.annotation.XmlAttribute;
/*    */ import org.jetbrains.annotations.Nullable;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class JiraConnection
/*    */   extends Connection
/*    */ {
/*    */   @XmlAttribute
/*    */   private String serverUrl;
/*    */   
/*    */   @Deprecated
/*    */   public JiraConnection() {}
/*    */   
/*    */   public JiraConnection(String id, String serverUrl, String user, String password) {
/* 24 */     super(id, user, password);
/* 25 */     this.serverUrl = serverUrl;
/*    */   }
/*    */   
/*    */   public String getServerUrl() {
/* 29 */     return this.serverUrl;
/*    */   }
/*    */   
/*    */   @Nullable
/*    */   public String check() {
/* 34 */     Collection<String> missing = new ArrayList<>();
/* 35 */     if (getUser() == null || getUser().isEmpty()) {
/* 36 */       missing.add(Localization.getString("definition.user"));
/*    */     }
/* 38 */     if (getPassword() == null || getPassword().isEmpty()) {
/* 39 */       missing.add(Localization.getString("synchronizer.ui.password"));
/*    */     }
/* 41 */     if (getServerUrl() == null || getServerUrl().isEmpty()) {
/* 42 */       missing.add(Localization.getString("synchronizer.ui.serverUrl"));
/*    */     }
/* 44 */     return missing.isEmpty() ? null : Localization.getString("synchronizer.ui.missingParameters", new String[] { missing.toString().replaceAll("^\\[(.*)\\]$", "$1") });
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer.proxy.jira_3.22.1/jira.jar!/com/polarion/synchronizer/proxy/jira/JiraConnection.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */