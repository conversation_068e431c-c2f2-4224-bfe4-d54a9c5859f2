/*    */ package com.polarion.synchronizer.proxy.jira.json;
/*    */ 
/*    */ import com.fasterxml.jackson.annotation.JsonIgnore;
/*    */ import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
/*    */ import java.util.Collection;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @JsonIgnoreProperties(ignoreUnknown = true)
/*    */ public class Field
/*    */ {
/*    */   private Collection<String> operations;
/*    */   
/*    */   @NotNull
/*    */   public Collection<String> getOperations() {
/* 39 */     return this.operations;
/*    */   }
/*    */   
/*    */   @JsonIgnore
/*    */   public boolean isEditable() {
/* 44 */     return this.operations.contains("set");
/*    */   }
/*    */ 
/*    */   
/*    */   public String toString() {
/* 49 */     return "Field [operations=" + this.operations + "]";
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer.proxy.jira_3.22.1/jira.jar!/com/polarion/synchronizer/proxy/jira/json/Field.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */