/*    */ package com.polarion.synchronizer.proxy.jira.json;
/*    */ 
/*    */ public class Add
/*    */   extends Operation {
/*    */   private Object add;
/*    */   
/*    */   public Add(Object value) {
/*  8 */     this.add = value;
/*    */   }
/*    */   
/*    */   public Object getAdd() {
/* 12 */     return this.add;
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer.proxy.jira_3.22.1/jira.jar!/com/polarion/synchronizer/proxy/jira/json/Add.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */