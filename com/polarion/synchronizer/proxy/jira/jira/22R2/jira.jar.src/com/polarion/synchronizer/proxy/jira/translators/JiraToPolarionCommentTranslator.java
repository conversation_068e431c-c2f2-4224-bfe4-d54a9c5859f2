/*    */ package com.polarion.synchronizer.proxy.jira.translators;
/*    */ 
/*    */ import com.google.inject.assistedinject.Assisted;
/*    */ import com.polarion.synchronizer.mapping.ValueMapping;
/*    */ import com.polarion.synchronizer.model.Side;
/*    */ import com.polarion.synchronizer.proxy.jira.model.JiraComment;
/*    */ import com.polarion.synchronizer.proxy.polarion.model.Comment;
/*    */ import com.polarion.synchronizer.spi.translators.AbstractCollectionTranslator;
/*    */ import java.util.ArrayList;
/*    */ import java.util.Collection;
/*    */ import java.util.Collections;
/*    */ import java.util.LinkedList;
/*    */ import javax.inject.Inject;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ import org.jetbrains.annotations.Nullable;
/*    */ 
/*    */ 
/*    */ 
/*    */ public class JiraToPolarionCommentTranslator
/*    */   extends AbstractCollectionTranslator<JiraComment, Comment>
/*    */ {
/*    */   @Inject
/*    */   public JiraToPolarionCommentTranslator(@Assisted Collection<ValueMapping> valueMappings, @Assisted Side fromSide) {
/* 24 */     super(valueMappings, fromSide);
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   protected Collection<Comment> mapCollection(@Nullable Collection<JiraComment> sourceValue, @Nullable Collection<Comment> otherValue, boolean explode) {
/* 30 */     Collection<Comment> result = new LinkedList<>();
/* 31 */     if (sourceValue != null) {
/* 32 */       for (JiraComment jiraComment : sourceValue) {
/* 33 */         Collection<String> potentialMatches = getPotentialMatches(jiraComment.getAuthor());
/* 34 */         Comment possibleExistingPolarionComment = getPossibleExistingComment(jiraComment, potentialMatches, otherValue);
/* 35 */         if (possibleExistingPolarionComment == null) {
/* 36 */           result.add(new Comment(loadBestMatch(Collections.emptyList(), jiraComment.getAuthor()), null, jiraComment.getContent().trim(), jiraComment.getCreated())); continue;
/*    */         } 
/* 38 */         result.add(possibleExistingPolarionComment);
/*    */       } 
/*    */     }
/*    */ 
/*    */     
/* 43 */     return result;
/*    */   }
/*    */   
/*    */   @Nullable
/*    */   private Comment getPossibleExistingComment(@NotNull JiraComment jiraComment, @NotNull Collection<String> potentialMatches, @Nullable Collection<Comment> otherValue) {
/* 48 */     if (otherValue == null || otherValue.isEmpty()) {
/* 49 */       return null;
/*    */     }
/* 51 */     for (String possibleAuthor : potentialMatches) {
/* 52 */       Comment possibleExistingPolarionComment = new Comment(possibleAuthor, null, jiraComment.getContent(), jiraComment.getCreated());
/* 53 */       if (otherValue.contains(possibleExistingPolarionComment)) {
/* 54 */         return possibleExistingPolarionComment;
/*    */       }
/*    */     } 
/* 57 */     return null;
/*    */   }
/*    */   
/*    */   @NotNull
/*    */   private Collection<String> getPotentialMatches(@NotNull String author) {
/* 62 */     Collection<String> potentialMatches = new ArrayList<>();
/* 63 */     String polarionUserId = loadBestMatch(Collections.emptyList(), author);
/* 64 */     potentialMatches.add(polarionUserId);
/* 65 */     potentialMatches.addAll(loadPotentialMatches(polarionUserId, Side.LEFT));
/* 66 */     return potentialMatches;
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer.proxy.jira_3.22.1/jira.jar!/com/polarion/synchronizer/proxy/jira/translators/JiraToPolarionCommentTranslator.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */