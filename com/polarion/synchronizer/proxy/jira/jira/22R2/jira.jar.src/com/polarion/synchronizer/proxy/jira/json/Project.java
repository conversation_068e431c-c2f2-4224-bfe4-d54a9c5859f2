/*    */ package com.polarion.synchronizer.proxy.jira.json;
/*    */ 
/*    */ import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
/*    */ import com.fasterxml.jackson.annotation.JsonProperty;
/*    */ import com.google.common.base.Objects;
/*    */ import org.jetbrains.annotations.Nullable;
/*    */ 
/*    */ 
/*    */ @JsonIgnoreProperties(ignoreUnknown = true)
/*    */ public class Project
/*    */ {
/*    */   @Nullable
/*    */   private final String id;
/*    */   @Nullable
/*    */   private final String name;
/*    */   @Nullable
/*    */   private final String key;
/*    */   @Nullable
/*    */   private final String style;
/*    */   
/*    */   public Project(@JsonProperty("id") @Nullable String id, @JsonProperty("name") @Nullable String name, @JsonProperty("key") @Nullable String key, @JsonProperty("style") @Nullable String style) {
/* 22 */     this.id = id;
/* 23 */     this.name = name;
/* 24 */     this.key = key;
/* 25 */     this.style = style;
/*    */   }
/*    */   @Nullable
/*    */   public String getId() {
/* 29 */     return this.id;
/*    */   }
/*    */   @Nullable
/*    */   public String getName() {
/* 33 */     return this.name;
/*    */   }
/*    */   @Nullable
/*    */   public String getKey() {
/* 37 */     return this.key;
/*    */   }
/*    */   
/*    */   public boolean isNextGen() {
/* 41 */     return Objects.equal(this.style, "next-gen");
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer.proxy.jira_3.22.1/jira.jar!/com/polarion/synchronizer/proxy/jira/json/Project.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */