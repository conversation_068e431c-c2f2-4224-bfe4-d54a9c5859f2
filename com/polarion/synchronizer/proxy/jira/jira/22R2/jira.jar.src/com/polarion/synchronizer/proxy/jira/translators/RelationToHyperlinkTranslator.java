/*    */ package com.polarion.synchronizer.proxy.jira.translators;
/*    */ 
/*    */ import com.google.inject.assistedinject.Assisted;
/*    */ import com.polarion.synchronizer.mapping.ValueMapping;
/*    */ import com.polarion.synchronizer.model.Relation;
/*    */ import com.polarion.synchronizer.model.Side;
/*    */ import com.polarion.synchronizer.proxy.polarion.model.Hyperlink;
/*    */ import com.polarion.synchronizer.spi.translators.AbstractCollectionTranslator;
/*    */ import java.util.ArrayList;
/*    */ import java.util.Collection;
/*    */ import java.util.List;
/*    */ import javax.inject.Inject;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ import org.jetbrains.annotations.Nullable;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class RelationToHyperlinkTranslator
/*    */   extends AbstractCollectionTranslator<Relation, Hyperlink>
/*    */ {
/*    */   @Inject
/*    */   public RelationToHyperlinkTranslator(@Assisted Collection<ValueMapping> valueMappings, @Assisted Side fromSide) {
/* 26 */     super(valueMappings, fromSide);
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   protected Collection<Hyperlink> mapCollection(@Nullable Collection<Relation> sourceValue, @Nullable Collection<Hyperlink> otherValue, boolean explode) {
/* 32 */     List<Hyperlink> links = new ArrayList<>();
/* 33 */     if (sourceValue != null) {
/* 34 */       for (Relation relation : sourceValue) {
/* 35 */         Collection<String> matches = loadPotentialMatches(relation.getRole());
/* 36 */         if (matches.size() == 1 && relation.getUrl() != null) {
/* 37 */           links.add(new Hyperlink(relation.getUrl(), matches.iterator().next()));
/*    */         }
/*    */       } 
/*    */     }
/*    */     
/* 42 */     return links;
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer.proxy.jira_3.22.1/jira.jar!/com/polarion/synchronizer/proxy/jira/translators/RelationToHyperlinkTranslator.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */