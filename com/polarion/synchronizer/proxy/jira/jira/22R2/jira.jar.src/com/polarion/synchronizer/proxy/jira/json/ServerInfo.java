/*    */ package com.polarion.synchronizer.proxy.jira.json;
/*    */ 
/*    */ import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
/*    */ import com.fasterxml.jackson.annotation.JsonProperty;
/*    */ import org.jetbrains.annotations.Nullable;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @JsonIgnoreProperties(ignoreUnknown = true)
/*    */ public class ServerInfo
/*    */ {
/*    */   @Nullable
/*    */   private final String deploymentType;
/*    */   
/*    */   public ServerInfo(@JsonProperty("deploymentType") @Nullable String deploymentType) {
/* 17 */     this.deploymentType = deploymentType;
/*    */   }
/*    */   
/*    */   public boolean isCloudDeployment() {
/* 21 */     return (this.deploymentType != null && "Cloud".equals(this.deploymentType));
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer.proxy.jira_3.22.1/jira.jar!/com/polarion/synchronizer/proxy/jira/json/ServerInfo.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */