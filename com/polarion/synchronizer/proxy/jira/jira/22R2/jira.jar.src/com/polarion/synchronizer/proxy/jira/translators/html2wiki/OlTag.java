/*    */ package com.polarion.synchronizer.proxy.jira.translators.html2wiki;
/*    */ 
/*    */ import com.polarion.synchronizer.proxy.htmltranslator.ElementInfo;
/*    */ import com.polarion.synchronizer.proxy.htmltranslator.RecursiveTag;
/*    */ import com.polarion.synchronizer.proxy.htmltranslator.Tag;
/*    */ import java.util.Collection;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ import org.jsoup.nodes.Element;
/*    */ 
/*    */ public class OlTag
/*    */   extends RecursiveTag
/*    */   implements ListTag
/*    */ {
/* 14 */   private long level = 0L;
/*    */ 
/*    */   
/*    */   public void open(@NotNull Element element, @NotNull ElementInfo elementInfo, @NotNull StringBuilder sb, @NotNull Collection<Tag> openTags) {
/* 18 */     this.level = getLevel(openTags) + 1L;
/*    */     
/* 20 */     if ((this.level > 1L || (this.previousText.length() != 0 && !this.previousText.toString().endsWith("\n"))) && parentIsRendered(element)) {
/* 21 */       sb.append("\n");
/*    */     }
/*    */   }
/*    */ 
/*    */   
/*    */   public void close(@NotNull Element element, @NotNull ElementInfo elementInfo, @NotNull StringBuilder sb, @NotNull Collection<Tag> openTags) {
/* 27 */     this.level = getLevel(openTags);
/* 28 */     if (this.level == 0L && 
/* 29 */       ThTdTag.isInThTdTag(openTags)) {
/* 30 */       sb.append("\\\\ ");
/*    */     }
/*    */   }
/*    */ 
/*    */   
/*    */   public static boolean isInOlTag(@NotNull Collection<Tag> openTags) {
/* 36 */     return openTags.stream().filter(o -> o instanceof OlTag).findFirst().isPresent();
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer.proxy.jira_3.22.1/jira.jar!/com/polarion/synchronizer/proxy/jira/translators/html2wiki/OlTag.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */