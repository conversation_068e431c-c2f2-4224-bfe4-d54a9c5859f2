/*    */ package com.polarion.synchronizer.proxy.jira.json;
/*    */ 
/*    */ import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
/*    */ import java.util.Collections;
/*    */ import java.util.Map;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @JsonIgnoreProperties(ignoreUnknown = true)
/*    */ public class Transition
/*    */ {
/*    */   private int id;
/*    */   private Option to;
/*    */   private Map<String, Field> fields;
/*    */   
/*    */   @Deprecated
/*    */   public Transition() {}
/*    */   
/*    */   public Transition(int id) {
/* 46 */     this.id = id;
/* 47 */     this.fields = Collections.EMPTY_MAP;
/*    */   }
/*    */   
/*    */   public int getId() {
/* 51 */     return this.id;
/*    */   }
/*    */   
/*    */   @NotNull
/*    */   public Option getTo() {
/* 56 */     return this.to;
/*    */   }
/*    */   
/*    */   @NotNull
/*    */   public Map<String, Field> getFields() {
/* 61 */     return this.fields;
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer.proxy.jira_3.22.1/jira.jar!/com/polarion/synchronizer/proxy/jira/json/Transition.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */