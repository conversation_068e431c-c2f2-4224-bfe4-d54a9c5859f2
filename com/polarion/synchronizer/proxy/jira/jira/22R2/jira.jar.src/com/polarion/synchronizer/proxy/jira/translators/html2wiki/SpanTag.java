/*    */ package com.polarion.synchronizer.proxy.jira.translators.html2wiki;
/*    */ 
/*    */ import com.polarion.synchronizer.proxy.htmltranslator.ElementInfo;
/*    */ import com.polarion.synchronizer.proxy.htmltranslator.RecursiveTag;
/*    */ import com.polarion.synchronizer.proxy.htmltranslator.Tag;
/*    */ import java.util.Collection;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ import org.jsoup.nodes.Element;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class SpanTag
/*    */   extends RecursiveTag
/*    */ {
/*    */   @NotNull
/*    */   private JiraMacroLinkConverter macroLinkConverter;
/*    */   
/*    */   public SpanTag(@NotNull JiraMacroLinkConverter macroLinkConverter) {
/* 39 */     this.macroLinkConverter = macroLinkConverter;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void open(@NotNull Element element, @NotNull ElementInfo elementInfo, @NotNull StringBuilder sb, @NotNull Collection<Tag> openTags) {}
/*    */ 
/*    */ 
/*    */   
/*    */   public void close(@NotNull Element element, @NotNull ElementInfo elementInfo, @NotNull StringBuilder sb, @NotNull Collection<Tag> openTags) {}
/*    */ 
/*    */ 
/*    */   
/*    */   public void process(@NotNull Element element, @NotNull ElementInfo elementInfo, @NotNull StringBuilder sb, @NotNull Collection<Tag> openTags) {
/* 54 */     if (!this.macroLinkConverter.processMacroLinks(element, sb))
/* 55 */       super.process(element, elementInfo, sb, openTags); 
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer.proxy.jira_3.22.1/jira.jar!/com/polarion/synchronizer/proxy/jira/translators/html2wiki/SpanTag.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */