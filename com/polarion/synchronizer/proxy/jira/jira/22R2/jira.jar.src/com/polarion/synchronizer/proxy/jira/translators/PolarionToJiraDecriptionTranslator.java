/*     */ package com.polarion.synchronizer.proxy.jira.translators;
/*     */ 
/*     */ import com.google.inject.Inject;
/*     */ import com.google.inject.assistedinject.Assisted;
/*     */ import com.polarion.alm.tracker.ITestManagementService;
/*     */ import com.polarion.core.util.ObjectUtils;
/*     */ import com.polarion.core.util.StringUtils;
/*     */ import com.polarion.synchronizer.ISynchronizationContext;
/*     */ import com.polarion.synchronizer.mapping.TranslationResult;
/*     */ import com.polarion.synchronizer.model.IProxy;
/*     */ import com.polarion.synchronizer.proxy.jira.JiraRichText;
/*     */ import com.polarion.synchronizer.proxy.jira.translators.html2wiki.HTML2JiraWikiConverter;
/*     */ import com.polarion.synchronizer.proxy.polarion.IPolarionProxy;
/*     */ import com.polarion.synchronizer.spi.translators.TypesafeTranslator;
/*     */ import java.net.URLDecoder;
/*     */ import java.net.URLEncoder;
/*     */ import java.nio.charset.StandardCharsets;
/*     */ import java.util.ArrayList;
/*     */ import java.util.List;
/*     */ import java.util.regex.Matcher;
/*     */ import java.util.regex.Pattern;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ import org.jetbrains.annotations.Nullable;
/*     */ 
/*     */ public class PolarionToJiraDecriptionTranslator
/*     */   extends TypesafeTranslator<String, JiraRichText, JiraRichText>
/*     */ {
/*     */   @NotNull
/*     */   private final IPolarionProxy fromProxy;
/*     */   @NotNull
/*     */   private final ITestManagementService testManagementService;
/*     */   @NotNull
/*     */   private final String baseUrl;
/*     */   @NotNull
/*     */   private final ISynchronizationContext context;
/*     */   
/*     */   @Inject
/*     */   public PolarionToJiraDecriptionTranslator(@Assisted("fromProxy") @NotNull IProxy fromProxy, @NotNull ITestManagementService testManagementService, @NotNull ISynchronizationContext context) {
/*  39 */     super(String.class, JiraRichText.class);
/*  40 */     this.fromProxy = (IPolarionProxy)fromProxy;
/*  41 */     this.testManagementService = testManagementService;
/*  42 */     this.baseUrl = StringUtils.removeSuffix(System.getProperty("base.url").trim(), "/");
/*  43 */     this.context = context;
/*     */   }
/*     */ 
/*     */   
/*     */   public TranslationResult<JiraRichText> translateUnidirectionalTypesafe(String source, JiraRichText target) {
/*  48 */     return createUnidirectionalResult(convert(source), target);
/*     */   }
/*     */ 
/*     */   
/*     */   public TranslationResult<JiraRichText> translateBidirectionalTypesafe(String sourceBaseline, String sourceValue, JiraRichText targetBaseline, JiraRichText targetValue) {
/*  53 */     return createBidirectionalResult(sourceBaseline, sourceValue, convert(sourceValue), targetBaseline, targetValue);
/*     */   }
/*     */   
/*     */   private JiraRichText convert(@Nullable String source) {
/*  57 */     JiraRichText result = null;
/*  58 */     if (source != null) {
/*  59 */       HTML2JiraWikiConverter conv = new HTML2JiraWikiConverter(this.fromProxy, this.testManagementService, this.baseUrl, this.context.getLogger());
/*  60 */       String res = conv.convert(source);
/*  61 */       result = new JiraRichText(res, null);
/*     */     } 
/*  63 */     return result;
/*     */   }
/*     */ 
/*     */   
/*     */   protected boolean isSourceModified(@Nullable String sourceValue, @Nullable String sourceBaseline) {
/*  68 */     if (sourceBaseline != null && sourceValue != null) {
/*  69 */       List<String> baselineAttachments = new ArrayList<>();
/*     */       
/*  71 */       String cleanedBaseline = removeAttachments(sourceBaseline, baselineAttachments);
/*     */       
/*  73 */       List<String> sourceAttachments = new ArrayList<>();
/*  74 */       String cleanedSource = removeAttachments(sourceValue, sourceAttachments);
/*     */       
/*  76 */       if (!ObjectUtils.equalsWithNull(cleanedSource, cleanedBaseline)) {
/*  77 */         return true;
/*     */       }
/*     */       
/*  80 */       return checkAttachmentsChanged(baselineAttachments, sourceAttachments);
/*     */     } 
/*  82 */     return super.isSourceModified(sourceValue, sourceBaseline);
/*     */   }
/*     */   
/*     */   private boolean checkAttachmentsChanged(@NotNull List<String> baselineAttachments, @NotNull List<String> sourceAttachments) {
/*  86 */     if (baselineAttachments.size() != sourceAttachments.size()) {
/*  87 */       return false;
/*     */     }
/*  89 */     sourceAttachments.removeIf(anchor -> paramList.stream().map(()).filter(anchor::contains).findAny().isPresent());
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  98 */     return !sourceAttachments.isEmpty();
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private String removeAttachments(@NotNull String source, @NotNull List<String> collectedAttachments) {
/* 103 */     String cleanedSource = source;
/*     */     
/* 105 */     int attachmentIndex = cleanedSource.indexOf("<a href");
/* 106 */     while (attachmentIndex > -1) {
/* 107 */       String attachment = cleanedSource.substring(attachmentIndex);
/* 108 */       int endIndex = attachment.indexOf(">") + 1;
/* 109 */       attachment = attachment.substring(0, endIndex);
/* 110 */       collectedAttachments.add(getHref(attachment));
/* 111 */       cleanedSource = cleanedSource.replace(attachment, "");
/* 112 */       attachmentIndex = cleanedSource.indexOf("<a href");
/*     */     } 
/* 114 */     return cleanedSource;
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private String getHref(@NotNull String link) {
/* 119 */     Matcher m = Pattern.compile("href=\"\\S+\"").matcher(link);
/* 120 */     if (m.find()) {
/* 121 */       return link.substring(m.start() + 6, m.end() - 1);
/*     */     }
/* 123 */     return "";
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private String replaceInvalidChars(@NotNull String name) {
/* 128 */     String escapedString = URLDecoder.decode(name, StandardCharsets.UTF_8);
/* 129 */     String invalidChars = "\\/:*?\"<>|;`~'#%$ ";
/* 130 */     for (int i = 0, s = invalidChars.length(); i < s; i++) {
/* 131 */       escapedString = escapedString.replace(invalidChars.charAt(i), '_');
/*     */     }
/* 133 */     return URLEncoder.encode(escapedString, StandardCharsets.UTF_8);
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer.proxy.jira_3.22.1/jira.jar!/com/polarion/synchronizer/proxy/jira/translators/PolarionToJiraDecriptionTranslator.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */