/*    */ package com.polarion.synchronizer.proxy.jira.translators;
/*    */ 
/*    */ import com.google.inject.assistedinject.Assisted;
/*    */ import com.polarion.platform.i18n.Localization;
/*    */ import com.polarion.synchronizer.mapping.ValueMapping;
/*    */ import com.polarion.synchronizer.model.IProxy;
/*    */ import com.polarion.synchronizer.model.Side;
/*    */ import com.polarion.synchronizer.proxy.jira.IJiraProxy;
/*    */ import com.polarion.synchronizer.proxy.jira.model.JiraComment;
/*    */ import com.polarion.synchronizer.proxy.polarion.model.Comment;
/*    */ import com.polarion.synchronizer.spi.translators.AbstractCollectionTranslator;
/*    */ import java.util.Collection;
/*    */ import java.util.Collections;
/*    */ import java.util.LinkedList;
/*    */ import javax.inject.Inject;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ import org.jetbrains.annotations.Nullable;
/*    */ 
/*    */ 
/*    */ 
/*    */ public class PolarionToJiraCommentTranslator
/*    */   extends AbstractCollectionTranslator<Comment, JiraComment>
/*    */ {
/*    */   @NotNull
/*    */   private final IJiraProxy jiraProxy;
/*    */   
/*    */   @Inject
/*    */   public PolarionToJiraCommentTranslator(@Assisted("toProxy") @NotNull IProxy jiraProxy, @Assisted Collection<ValueMapping> valueMappings, @Assisted Side fromSide) {
/* 29 */     super(valueMappings, fromSide);
/* 30 */     this.jiraProxy = (IJiraProxy)jiraProxy;
/*    */   }
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   protected Collection<JiraComment> mapCollection(@Nullable Collection<Comment> sourceValue, @Nullable Collection<JiraComment> otherValue, boolean explode) {
/* 36 */     Collection<JiraComment> result = new LinkedList<>();
/* 37 */     if (sourceValue != null) {
/* 38 */       for (Comment comment : sourceValue) {
/* 39 */         Collection<String> potentialMatches = loadPotentialMatches(comment.getAuthor());
/* 40 */         potentialMatches.add(comment.getAuthor());
/* 41 */         JiraComment possibleExistingJiraComment = getPossibleExistingComment(comment, potentialMatches, otherValue);
/* 42 */         if (possibleExistingJiraComment == null) {
/* 43 */           String text = getCommentText(comment);
/* 44 */           result.add(new JiraComment(loadBestMatch(Collections.emptyList(), comment.getAuthor()), text, comment.getCreated())); continue;
/*    */         } 
/* 46 */         result.add(possibleExistingJiraComment);
/*    */       } 
/*    */     }
/*    */ 
/*    */ 
/*    */     
/* 52 */     return result;
/*    */   }
/*    */   
/*    */   @Nullable
/*    */   private JiraComment getPossibleExistingComment(@NotNull Comment polarionComment, @NotNull Collection<String> potentialMatches, @Nullable Collection<JiraComment> otherValue) {
/* 57 */     if (otherValue == null || otherValue.isEmpty()) {
/* 58 */       return null;
/*    */     }
/* 60 */     for (String possibleAuthor : potentialMatches) {
/* 61 */       JiraComment possibleExistingJiraComment = new JiraComment(possibleAuthor, compileCommentTextForJira(polarionComment, possibleAuthor), polarionComment.getCreated());
/* 62 */       if (otherValue.contains(possibleExistingJiraComment)) {
/* 63 */         return possibleExistingJiraComment;
/*    */       }
/*    */     } 
/* 66 */     return null;
/*    */   }
/*    */   
/*    */   @NotNull
/*    */   private String getCommentText(@NotNull Comment comment) {
/* 71 */     if (this.jiraProxy.isCloudDeployment()) {
/* 72 */       String jiraAuthor = loadBestMatch(Collections.emptyList(), comment.getAuthor());
/* 73 */       String jiraAuthorWithLink = "[" + jiraAuthor + "|" + this.jiraProxy.getUserURL() + jiraAuthor + "]";
/* 74 */       return compileCommentTextForJira(comment, jiraAuthorWithLink);
/*    */     } 
/* 76 */     return compileCommentTextForJira(comment, loadBestMatch(Collections.emptyList(), comment.getAuthor()));
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   @NotNull
/*    */   private String compileCommentTextForJira(@NotNull Comment comment, @NotNull String authortext) {
/* 83 */     String header = Localization.getString("synchronizer.jira.commentHeader", new String[] {
/* 84 */           comment.getSubject(), authortext, comment.getCreated().toString() });
/* 85 */     return String.valueOf(header) + ((comment.getText() == null) ? "" : ("\n" + comment.getText()));
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer.proxy.jira_3.22.1/jira.jar!/com/polarion/synchronizer/proxy/jira/translators/PolarionToJiraCommentTranslator.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */