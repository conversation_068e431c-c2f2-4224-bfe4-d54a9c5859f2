/*     */ package com.polarion.synchronizer.proxy.jira.translators;
/*     */ 
/*     */ import com.polarion.platform.i18n.Localization;
/*     */ import com.polarion.synchronizer.proxy.jira.JiraIcons;
/*     */ import com.polarion.synchronizer.proxy.jira.translators.html2wiki.ImgTag;
/*     */ import java.net.URLDecoder;
/*     */ import java.nio.charset.StandardCharsets;
/*     */ import java.util.List;
/*     */ import java.util.regex.Matcher;
/*     */ import java.util.regex.Pattern;
/*     */ import java.util.stream.Collectors;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ import org.jsoup.Jsoup;
/*     */ import org.jsoup.nodes.Document;
/*     */ import org.jsoup.nodes.Element;
/*     */ import org.jsoup.nodes.Node;
/*     */ import org.jsoup.parser.Tag;
/*     */ import org.jsoup.select.Elements;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class JiraToPolarionHtmlConverter
/*     */ {
/*     */   private static final String ITALIC_STYLE = "font-style: italic;";
/*     */   private static final String UNDERLINED_STYLE = "text-decoration: underline;";
/*     */   private static final String STRIKED_STYLE = "text-decoration: line-through;";
/*     */   private static final String ICON_STYLE = "vertical-align: sub;";
/*     */   private static final String SPAN_TAG = "span";
/*     */   private static final String IMG_TAG = "img";
/*     */   private static final String EM_TAG = "em";
/*     */   private static final String INS_TAG = "ins";
/*     */   private static final String DEL_TAG = "del";
/*     */   private static final String ANCHOR_TAG = "a";
/*     */   private static final String FONT_TAG = "font";
/*     */   private static final String P_TAG = "p";
/*     */   private static final String BR_TAG = "br";
/*     */   private static final String CITE_TAG = "cite";
/*     */   private static final String STYLE_ATTRIBUTE = "style";
/*     */   private static final String HREF_ATTRIBUTE = "href";
/*     */   private static final String SRC_ATTRIBUTE = "src";
/*     */   private static final String CLASS_ATTRIBUTE = "class";
/*     */   private static final String COLOR_ATTRIBUTE = "color";
/*     */   
/*     */   @NotNull
/*     */   public String convert(@NotNull String renderedHTML) {
/*  49 */     Document doc = Jsoup.parse(renderedHTML);
/*  50 */     processStyleTags(doc);
/*  51 */     processReferences(doc);
/*  52 */     replacePtagsWithBrTags(doc);
/*  53 */     replaceIcons(doc);
/*  54 */     replaceImageRefsWithLinks(doc);
/*  55 */     processFontTags(doc);
/*     */     
/*  57 */     doc.outputSettings().prettyPrint(false);
/*  58 */     return doc.body().html();
/*     */   }
/*     */   
/*     */   private void processFontTags(@NotNull Document doc) {
/*  62 */     for (Element element : doc.getElementsByTag("font")) {
/*  63 */       if (element.hasAttr("color")) {
/*  64 */         String color = element.attr("color");
/*  65 */         element.tagName("span").attr("style", "color: " + color + ";");
/*  66 */         element.removeAttr("color");
/*     */       } 
/*     */     } 
/*     */   }
/*     */   
/*     */   private void processStyleTags(@NotNull Document doc) {
/*  72 */     for (Element element : doc.getElementsByTag("em")) {
/*  73 */       element.tagName("span").attr("style", "font-style: italic;");
/*     */     }
/*  75 */     for (Element element : doc.getElementsByTag("ins")) {
/*  76 */       element.tagName("span").attr("style", "text-decoration: underline;");
/*     */     }
/*  78 */     for (Element element : doc.getElementsByTag("del")) {
/*  79 */       element.tagName("span").attr("style", "text-decoration: line-through;");
/*     */     }
/*  81 */     for (Element element : doc.getElementsByTag("cite")) {
/*  82 */       element.tagName("span").attr("style", "font-style: italic;");
/*  83 */       element.html("— " + element.html());
/*     */     } 
/*     */   }
/*     */   
/*     */   private void processReferences(@NotNull Document doc) {
/*  88 */     Pattern referencePattern = Pattern.compile(".*/secure/attachment/\\d+/\\d+_(.[^\"]+)");
/*  89 */     for (Element element : doc.getElementsByTag("img")) {
/*  90 */       String src = element.hasAttr("src") ? element.attr("src") : "";
/*  91 */       Matcher referenceMatcher = referencePattern.matcher(src);
/*  92 */       if (referenceMatcher.find()) {
/*  93 */         String attachmentName = referenceMatcher.group(1);
/*  94 */         element.attr("src", "attachment:" + attachmentName);
/*     */       } 
/*     */     } 
/*  97 */     for (Element element : doc.getElementsByTag("a")) {
/*  98 */       String ref = element.hasAttr("href") ? element.attr("href") : "";
/*  99 */       Matcher referenceMatcher = referencePattern.matcher(ref);
/* 100 */       if (referenceMatcher.find()) {
/* 101 */         Elements thumbnailElement = element.getElementsByTag("img");
/* 102 */         String attachmentName = referenceMatcher.group(1);
/* 103 */         Element newImgElement = doc.createElement("img");
/* 104 */         newImgElement.attr("src", "attachment:" + attachmentName);
/* 105 */         if (thumbnailElement.size() == 1) {
/* 106 */           newImgElement.attr("style", "max-width: 200px;");
/*     */         }
/* 108 */         element.replaceWith((Node)newImgElement);
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   private void replacePtagsWithBrTags(@NotNull Document doc) {
/* 115 */     List<Node> nodes = doc.body().childNodes();
/* 116 */     for (Element element : doc.getElementsByTag("p")) {
/* 117 */       Node parent = element.parentNode();
/* 118 */       Node prev = element.previousSibling();
/* 119 */       if (prev != null) {
/* 120 */         if (!element.equals(nodes.get(nodes.size() - 1))) {
/* 121 */           element.tagName("br"); continue;
/*     */         } 
/* 123 */         List<Node> childNodes = element.childNodes();
/* 124 */         element.before((Node)new Element(Tag.valueOf("br"), ""));
/* 125 */         doc.body().insertChildren(doc.body().childNodeSize(), childNodes);
/* 126 */         element.remove();
/*     */         continue;
/*     */       } 
/* 129 */       List<Node> chilNodes = element.childNodes();
/* 130 */       if (parent != null) {
/* 131 */         ((Element)parent).insertChildren(0, chilNodes);
/*     */       } else {
/* 133 */         doc.body().insertChildren(0, chilNodes);
/*     */       } 
/* 135 */       if (doc.body().children().last() != element) {
/* 136 */         element.before((Node)new Element(Tag.valueOf("br"), ""));
/*     */       }
/* 138 */       element.remove();
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   private void replaceIcons(@NotNull Document doc) {
/* 144 */     Elements elements = doc.getElementsByTag("img");
/* 145 */     String src = "src";
/* 146 */     String prefix = "/polarion/ria/images/actions/";
/* 147 */     for (Element imgTag : elements) {
/* 148 */       if ("emoticon".equals(imgTag.attr("class"))) {
/* 149 */         String source = imgTag.attr(src);
/* 150 */         if (source.contains("check.png")) {
/* 151 */           imgTag.attr(src, String.valueOf(prefix) + JiraIcons.OK.inPolarion());
/* 152 */         } else if (source.contains("error.png")) {
/* 153 */           imgTag.attr(src, String.valueOf(prefix) + JiraIcons.ERROR.inPolarion());
/* 154 */         } else if (source.contains("information.png")) {
/* 155 */           imgTag.attr(src, String.valueOf(prefix) + JiraIcons.INFO.inPolarion());
/*     */         } 
/* 157 */         imgTag.attr("style", "vertical-align: sub;");
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   private void replaceImageRefsWithLinks(@NotNull Document doc) {
/* 164 */     List<Element> imgRefs = (List<Element>)doc.getElementsByTag("span").stream().filter(e -> "nobr".equals(e.attr("class"))).collect(Collectors.toList());
/* 165 */     if (!imgRefs.isEmpty())
/* 166 */       for (Element imgRef : imgRefs) {
/* 167 */         String linkTitle = imgRef.child(0).attr("src");
/* 168 */         String fileName = URLDecoder.decode(linkTitle.replace("attachment:", ""), StandardCharsets.UTF_8);
/* 169 */         if (ImgTag.isImage(fileName)) {
/* 170 */           Element link = new Element(Tag.valueOf("a"), "");
/* 171 */           link.attr("href", linkTitle);
/* 172 */           link.text(fileName);
/* 173 */           link.attr("title", Localization.getString("synchronizer.jira.referencedImageAttachment.tooltip", new String[] { fileName }));
/* 174 */           imgRef.replaceWith((Node)link);
/*     */         } 
/*     */       }  
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer.proxy.jira_3.22.1/jira.jar!/com/polarion/synchronizer/proxy/jira/translators/JiraToPolarionHtmlConverter.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */