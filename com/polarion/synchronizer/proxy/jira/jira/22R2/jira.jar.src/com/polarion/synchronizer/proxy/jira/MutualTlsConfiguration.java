/*     */ package com.polarion.synchronizer.proxy.jira;
/*     */ 
/*     */ import com.polarion.synchronizer.SynchronizationException;
/*     */ import java.io.IOException;
/*     */ import java.net.Socket;
/*     */ import java.security.Principal;
/*     */ import java.security.PrivateKey;
/*     */ import java.security.cert.X509Certificate;
/*     */ import javax.net.ssl.HostnameVerifier;
/*     */ import javax.net.ssl.KeyManager;
/*     */ import javax.net.ssl.KeyManagerFactory;
/*     */ import javax.net.ssl.SSLSession;
/*     */ import javax.net.ssl.X509KeyManager;
/*     */ import javax.ws.rs.client.ClientBuilder;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class MutualTlsConfiguration
/*     */ {
/*     */   public static void updateClientConfig(@NotNull ClientBuilder clientBuilder, @NotNull MutualTlsSetting mutualTlsSetting) {
/*  34 */     char[] keyStorePassword = mutualTlsSetting.getVaultCredential().getPassword().toCharArray();
/*     */     try {
/*  36 */       Exception exception2, exception1 = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*     */     }
/*  50 */     catch (IOException|java.security.NoSuchAlgorithmException|java.security.KeyStoreException|java.security.cert.CertificateException|java.security.UnrecoverableKeyException|java.security.KeyManagementException e) {
/*  51 */       throw new SynchronizationException("Error while connecting to Jira using mutual TLS. Please check validity of certificates in Java keystore on Polarion server.", e);
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   private static KeyManager createKeyManager(@NotNull KeyManagerFactory kmf, @NotNull final MutualTlsSetting mutualTlsSetting) {
/*  58 */     final X509KeyManager origKm = (X509KeyManager)kmf.getKeyManagers()[0];
/*  59 */     return new X509KeyManager()
/*     */       {
/*     */         public String chooseClientAlias(String[] keyType, Principal[] issuers, Socket socket) {
/*  62 */           return mutualTlsSetting.getCertificateAlias();
/*     */         }
/*     */ 
/*     */         
/*     */         public X509Certificate[] getCertificateChain(String alias) {
/*  67 */           X509Certificate[] x509Certificates = origKm.getCertificateChain(alias);
/*  68 */           if (x509Certificates == null || x509Certificates.length == 0) {
/*  69 */             throwException();
/*     */           }
/*  71 */           return x509Certificates;
/*     */         }
/*     */ 
/*     */         
/*     */         public String[] getClientAliases(String keyType, Principal[] issuers) {
/*  76 */           return origKm.getClientAliases(keyType, issuers);
/*     */         }
/*     */ 
/*     */         
/*     */         public String[] getServerAliases(String keyType, Principal[] issuers) {
/*  81 */           return origKm.getServerAliases(keyType, issuers);
/*     */         }
/*     */ 
/*     */         
/*     */         public String chooseServerAlias(String keyType, Principal[] issuers, Socket socket) {
/*  86 */           return origKm.chooseServerAlias(keyType, issuers, socket);
/*     */         }
/*     */ 
/*     */         
/*     */         public PrivateKey getPrivateKey(String alias) {
/*  91 */           PrivateKey privateKey = origKm.getPrivateKey(alias);
/*  92 */           if (privateKey == null) {
/*  93 */             throwException();
/*     */           }
/*  95 */           return privateKey;
/*     */         }
/*     */         
/*     */         private void throwException() {
/*  99 */           throw new SynchronizationException("The certificate-alias " + mutualTlsSetting.getCertificateAlias() + " does not exist in Java keystore " + mutualTlsSetting.getKeyStorePath() + " on Polarion server");
/*     */         }
/*     */       };
/*     */   }
/*     */   
/*     */   private static class TrustAllHostnamesVerifier
/*     */     implements HostnameVerifier {
/*     */     public boolean verify(String hostname, SSLSession session) {
/* 107 */       return true;
/*     */     }
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer.proxy.jira_3.22.1/jira.jar!/com/polarion/synchronizer/proxy/jira/MutualTlsConfiguration.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */