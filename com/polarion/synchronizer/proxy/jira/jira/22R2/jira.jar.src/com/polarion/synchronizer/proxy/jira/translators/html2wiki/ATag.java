/*    */ package com.polarion.synchronizer.proxy.jira.translators.html2wiki;
/*    */ 
/*    */ import com.polarion.core.util.StringUtils;
/*    */ import com.polarion.synchronizer.proxy.htmltranslator.ElementInfo;
/*    */ import com.polarion.synchronizer.proxy.htmltranslator.Tag;
/*    */ import java.util.Collection;
/*    */ import java.util.List;
/*    */ import java.util.Map;
/*    */ import java.util.stream.Collectors;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ import org.jsoup.nodes.Element;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class ATag
/*    */   implements Tag
/*    */ {
/*    */   private List<JiraRichTextPart> children;
/*    */   
/*    */   public void open(@NotNull Element element, @NotNull ElementInfo elementInfo, @NotNull StringBuilder sb, @NotNull Collection<Tag> openTags) {
/* 24 */     String href = getHref(element);
/* 25 */     if (isLinkToAttachment(href, element.text())) {
/* 26 */       sb.append("[^");
/* 27 */     } else if (isExternal(href, element.text())) {
/* 28 */       sb.append("[");
/*    */     } 
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public void close(@NotNull Element element, @NotNull ElementInfo elementInfo, @NotNull StringBuilder sb, @NotNull Collection<Tag> openTags) {
/* 35 */     String href = getHref(element);
/*    */     
/* 37 */     Map<Boolean, List<JiraRichTextPart>> partionedParts = (Map<Boolean, List<JiraRichTextPart>>)this.children.stream().collect(Collectors.partitioningBy(n -> "\n".equals(n.getText())));
/* 38 */     for (JiraRichTextPart noBr : partionedParts.get(Boolean.FALSE)) {
/* 39 */       if (!noBr.getText().trim().isEmpty()) {
/* 40 */         sb.append(noBr.getText());
/*    */       }
/*    */     } 
/*    */     
/* 44 */     if (isLinkToAttachment(href, element.text()) || element.text().startsWith("http")) {
/* 45 */       sb.append("]");
/* 46 */     } else if (isExternal(href, element.text())) {
/* 47 */       sb.append("|" + href + "]");
/*    */     } 
/* 49 */     for (JiraRichTextPart br : partionedParts.get(Boolean.TRUE)) {
/* 50 */       sb.append(br.getText());
/*    */     }
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void process(@NotNull Element element, @NotNull ElementInfo elementInfo, @NotNull StringBuilder sb, @NotNull Collection<Tag> openTags) {}
/*    */ 
/*    */ 
/*    */   
/*    */   public boolean isContinue() {
/* 62 */     return true;
/*    */   }
/*    */   
/*    */   @NotNull
/*    */   private String getHref(@NotNull Element element) {
/* 67 */     return StringUtils.getEmptyIfNull(element.attr("href"));
/*    */   }
/*    */   
/*    */   private boolean isExternal(@NotNull String href, @NotNull String text) {
/* 71 */     return !(!href.startsWith("http") && !text.startsWith("http"));
/*    */   }
/*    */   
/*    */   private boolean isLinkToAttachment(@NotNull String href, @NotNull String text) {
/* 75 */     boolean checkHref = !(!href.startsWith("/repo/") && !href.contains("/polarion/wi-attachment/"));
/* 76 */     return (checkHref && !text.startsWith("http") && text.matches("(.*)\\.(\\w{3,4})"));
/*    */   }
/*    */   
/*    */   public void setChildren(List<JiraRichTextPart> children) {
/* 80 */     this.children = children;
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer.proxy.jira_3.22.1/jira.jar!/com/polarion/synchronizer/proxy/jira/translators/html2wiki/ATag.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */