/*     */ package com.polarion.synchronizer.proxy.jira;
/*     */ 
/*     */ import com.polarion.core.util.ObjectUtils;
/*     */ import com.polarion.synchronizer.model.CollectionUpdate;
/*     */ import com.polarion.synchronizer.model.Duration;
/*     */ import com.polarion.synchronizer.model.FieldDefinition;
/*     */ import com.polarion.synchronizer.model.Relation;
/*     */ import com.polarion.synchronizer.model.TransferItem;
/*     */ import com.polarion.synchronizer.proxy.jira.json.Add;
/*     */ import com.polarion.synchronizer.proxy.jira.json.CascadingSelect;
/*     */ import com.polarion.synchronizer.proxy.jira.json.Operation;
/*     */ import com.polarion.synchronizer.proxy.jira.json.Option;
/*     */ import com.polarion.synchronizer.proxy.jira.json.Remove;
/*     */ import com.polarion.synchronizer.proxy.jira.json.Set;
/*     */ import com.polarion.synchronizer.proxy.jira.json.UpdateIssue;
/*     */ import com.polarion.synchronizer.proxy.jira.json.Version;
/*     */ import java.text.SimpleDateFormat;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Collection;
/*     */ import java.util.HashMap;
/*     */ import java.util.HashSet;
/*     */ import java.util.Map;
/*     */ import java.util.Optional;
/*     */ import java.util.stream.Collectors;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ import org.jetbrains.annotations.Nullable;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class TransferItemToIssueConverter
/*     */ {
/*     */   private final IMetadataHelper metadataHelper;
/*     */   @NotNull
/*     */   private String epicLinkFieldId;
/*     */   @NotNull
/*  73 */   private SimpleDateFormat simpleDateFormat = new SimpleDateFormat();
/*     */   
/*     */   private boolean isCloudDeployment;
/*     */   
/*     */   public TransferItemToIssueConverter(@NotNull IMetadataHelper metadataHelper, boolean isCloudDeployment) {
/*  78 */     this.metadataHelper = metadataHelper;
/*  79 */     this.isCloudDeployment = isCloudDeployment;
/*  80 */     this.epicLinkFieldId = metadataHelper.getEpicLinkFieldId();
/*  81 */     this.simpleDateFormat.applyPattern("yyyy-MM-dd");
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public UpdateIssue convertForUpdate(@NotNull TransferItem transferItem, @Nullable TransferItem oldItem, @NotNull Collection<String> includeKeys, @NotNull Map<String, String> conversionErrors) {
/*  87 */     UpdateIssue issue = new UpdateIssue();
/*     */     
/*  89 */     Collection<String> withoutTimeTracking = new ArrayList<>(includeKeys);
/*  90 */     int i = withoutTimeTracking.remove("timetracking.originalEstimate") | withoutTimeTracking.remove("timetracking.remainingEstimate");
/*     */     
/*  92 */     processCascadingSelects(transferItem, oldItem, withoutTimeTracking, issue, conversionErrors);
/*  93 */     processRegularValues(transferItem, oldItem, issue, withoutTimeTracking, conversionErrors);
/*  94 */     if (i != 0) {
/*  95 */       addTimeTrackingValues(transferItem, issue, oldItem);
/*     */     }
/*     */     
/*  98 */     return issue;
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   public Version convertForUpdateVersion(@NotNull TransferItem transferItem, @NotNull Map<String, String> conversionErrors, @NotNull String projectId) {
/* 103 */     String name = (String)transferItem.getValues().get("com.polarion.synchronizer:jira:virtual-type:name");
/* 104 */     Version version = new Version(name, projectId);
/* 105 */     processVersionValues(transferItem, version, conversionErrors);
/* 106 */     return version;
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   public Version processVersionValues(@NotNull TransferItem transferItem, @NotNull Version version, @NotNull Map<String, String> conversionErrors) {
/* 111 */     for (Map.Entry<String, Object> entry : (Iterable<Map.Entry<String, Object>>)transferItem.getValues().entrySet()) {
/* 112 */       String key = entry.getKey();
/*     */       
/* 114 */       String mappedKey = this.metadataHelper.getJiraFieldId(key);
/* 115 */       if (mappedKey == null) {
/* 116 */         conversionErrors.put(key, "Unknown field.");
/*     */         
/*     */         continue;
/*     */       } 
/* 120 */       FieldDefinition fieldDefinition = this.metadataHelper.getDefinedFieldDefinition(key);
/* 121 */       if (fieldDefinition == null) {
/* 122 */         conversionErrors.put(key, "Definiton of field not found.");
/*     */         
/*     */         continue;
/*     */       } 
/* 126 */       if (key.equals("com.polarion.synchronizer:jira:virtual-type:startDate") && fieldDefinition.getType().equals("date") && entry.getValue() != null) {
/* 127 */         version.setStartDate(this.simpleDateFormat.format(entry.getValue())); continue;
/* 128 */       }  if (key.equals("com.polarion.synchronizer:jira:virtual-type:endDate") && fieldDefinition.getType().equals("date") && entry.getValue() != null) {
/* 129 */         version.setReleaseDate(this.simpleDateFormat.format(entry.getValue()));
/*     */       }
/*     */     } 
/* 132 */     return version;
/*     */   }
/*     */   
/*     */   private void processRegularValues(@NotNull TransferItem transferItem, @Nullable TransferItem oldItem, @NotNull UpdateIssue issue, @NotNull Collection<String> withoutTimeTracking, @NotNull Map<String, String> conversionErrors) {
/* 136 */     for (Map.Entry<String, Object> entry : (Iterable<Map.Entry<String, Object>>)transferItem.getValues().entrySet()) {
/* 137 */       String key = entry.getKey();
/* 138 */       if (!withoutTimeTracking.contains(key) || "fixVersionIds".equals(key)) {
/*     */         continue;
/*     */       }
/* 141 */       String mappedKey = this.metadataHelper.getJiraFieldId(key);
/* 142 */       if (mappedKey == null) {
/* 143 */         conversionErrors.put(key, "Unknown field.");
/*     */         
/*     */         continue;
/*     */       } 
/* 147 */       FieldDefinition fieldDefinition = this.metadataHelper.getDefinedFieldDefinition(key);
/* 148 */       if (fieldDefinition == null) {
/* 149 */         conversionErrors.put(key, "Definiton of field not found.");
/*     */         
/*     */         continue;
/*     */       } 
/* 153 */       processField(key, entry.getValue(), mappedKey, issue, fieldDefinition, conversionErrors);
/*     */     } 
/*     */     
/* 156 */     if (transferItem.getValues().containsKey("relations")) {
/* 157 */       processEpicLinkField(transferItem, oldItem, issue, conversionErrors);
/*     */     }
/* 159 */     if (transferItem.getValues().containsKey("fixVersionIds")) {
/* 160 */       processFixVersionIdsField(transferItem, issue, conversionErrors);
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void processFixVersionIdsField(@NotNull TransferItem transferItem, @NotNull UpdateIssue issue, @NotNull Map<String, String> conversionErrors) {
/* 170 */     String mappedKey = this.metadataHelper.getJiraFieldId("fixVersions");
/* 171 */     if (mappedKey == null) {
/* 172 */       conversionErrors.put("fixVersions", "Unknown field.");
/*     */       
/*     */       return;
/*     */     } 
/* 176 */     FieldDefinition fieldDefinition = this.metadataHelper.getDefinedFieldDefinition("fixVersions");
/* 177 */     if (fieldDefinition == null) {
/* 178 */       conversionErrors.put("fixVersions", "Definiton of field not found.");
/*     */       return;
/*     */     } 
/* 181 */     processField("fixVersions", transferItem.getValue("fixVersionIds"), mappedKey, issue, fieldDefinition, conversionErrors);
/*     */   }
/*     */   
/*     */   private void processEpicLinkField(@NotNull TransferItem transferItem, @Nullable TransferItem oldItem, @NotNull UpdateIssue issue, @NotNull Map<String, String> conversionErrors) {
/* 185 */     Collection<Relation> currentEpicLinks = getCurrentEpicLinks(oldItem);
/* 186 */     CollectionUpdate<Relation> relations = (CollectionUpdate<Relation>)transferItem.getValue("relations");
/* 187 */     Collection<Relation> addedEpicRelations = (Collection<Relation>)relations.getAdded().stream().filter(relation -> relation.getRole().equals("LINKED_EPIC")).collect(Collectors.toSet());
/* 188 */     Collection<Relation> removedEpicRelations = (Collection<Relation>)relations.getRemoved().stream().filter(relation -> relation.getRole().equals("LINKED_EPIC")).collect(Collectors.toSet());
/* 189 */     if (!removedEpicRelations.isEmpty() && !currentEpicLinks.removeAll(removedEpicRelations)) {
/* 190 */       String ids = removedEpicRelations.stream().map(rel -> rel.getTargetId()).collect(Collectors.joining(","));
/* 191 */       conversionErrors.put("relations", "Failed to remove epic link to item(s) " + ids + ".");
/*     */     } 
/*     */     
/* 194 */     if (addedEpicRelations.isEmpty()) {
/* 195 */       if (!removedEpicRelations.isEmpty()) {
/* 196 */         issue.getFields().put(this.epicLinkFieldId, null);
/*     */       }
/* 198 */     } else if (addedEpicRelations.size() == 1) {
/* 199 */       Optional<Relation> addedEpicOptional = addedEpicRelations.stream().findAny();
/* 200 */       if (addedEpicOptional.isPresent()) {
/* 201 */         Relation newEpicLink = addedEpicOptional.get();
/* 202 */         if (!currentEpicLinks.contains(newEpicLink)) {
/* 203 */           currentEpicLinks.add(newEpicLink);
/* 204 */           if (currentEpicLinks.size() == 1) {
/* 205 */             issue.getFields().put(this.epicLinkFieldId, newEpicLink.getTargetId());
/*     */           } else {
/* 207 */             conversionErrors.put("relations", "Can't add multiple epic links to a Jira issue.");
/*     */           }
/*     */         
/*     */         } 
/*     */       } 
/*     */     } else {
/*     */       
/* 214 */       conversionErrors.put("relations", "Can't add multiple epic links to a Jira issue.");
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   private Collection<Relation> getCurrentEpicLinks(@Nullable TransferItem oldItem) {
/* 220 */     Collection<Relation> currentrelations = (oldItem != null) ? (Collection<Relation>)oldItem.getValue("relations") : new HashSet<>();
/* 221 */     return (Collection<Relation>)currentrelations.stream().filter(relation -> relation.getRole().equals("LINKED_EPIC")).collect(Collectors.toSet());
/*     */   }
/*     */ 
/*     */   
/*     */   private void processCascadingSelects(@NotNull TransferItem transferItem, @Nullable TransferItem oldItem, @NotNull Collection<String> includeKeys, @NotNull UpdateIssue issue, @NotNull Map<String, String> conversionErrors) {
/* 226 */     Collection<String> cascadingSelectFields = loadCascadingSelectFields(includeKeys);
/*     */     
/* 228 */     for (String key : cascadingSelectFields) {
/* 229 */       String rootKey = String.valueOf(key) + "_root";
/* 230 */       Object value = transferItem.getValue(rootKey);
/* 231 */       includeKeys.remove(rootKey);
/* 232 */       if (value == null && oldItem != null) {
/* 233 */         value = oldItem.getValue(rootKey);
/*     */       }
/* 235 */       boolean clearRootValue = (transferItem.getValues().containsKey(rootKey) && value == null);
/*     */       
/* 237 */       String childKey = String.valueOf(key) + "_child";
/* 238 */       Object childValue = transferItem.getValue(childKey);
/* 239 */       includeKeys.remove(childKey);
/* 240 */       if (childValue == null && oldItem != null) {
/* 241 */         childValue = oldItem.getValue(childKey);
/*     */       }
/*     */       
/* 244 */       if (value == null && childValue != null) {
/* 245 */         if (clearRootValue) {
/* 246 */           conversionErrors.put(rootKey, "Can't clear root value without clearing child value."); continue;
/*     */         } 
/* 248 */         conversionErrors.put(childKey, "Can't set child value without root value.");
/*     */         
/*     */         continue;
/*     */       } 
/* 252 */       CascadingSelect cascadingSelect = new CascadingSelect(value, childValue);
/*     */       
/* 254 */       Set operation = (cascadingSelect.getValue() == null) ? new Set(null) : new Set(cascadingSelect);
/* 255 */       issue.addOperation(key, (Operation)operation);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   private Collection<String> loadCascadingSelectFields(@NotNull Collection<String> keys) {
/* 264 */     Collection<String> cascadingSelectFields = new HashSet<>();
/* 265 */     for (String key : keys) {
/* 266 */       IMetadataHelper.CascadingSelectType cascadingSelectType = this.metadataHelper.getCascadingSelectType(key);
/* 267 */       if (cascadingSelectType != null) {
/* 268 */         cascadingSelectFields.add((String)ObjectUtils.notNull(this.metadataHelper.getJiraFieldId(key)));
/*     */       }
/*     */     } 
/* 271 */     return cascadingSelectFields;
/*     */   }
/*     */   
/*     */   private void processField(@NotNull String key, @Nullable Object value, @NotNull String mappedKey, @NotNull UpdateIssue issue, @NotNull FieldDefinition fieldDefinition, @NotNull Map<String, String> conversionErrors) {
/* 275 */     if (mappedKey.equals("issuetype")) {
/* 276 */       if (value != null) {
/* 277 */         issue.getFields().put(mappedKey, Option.withValue(value, false));
/*     */       }
/*     */     }
/* 280 */     else if (fieldDefinition.isMultiValued()) {
/* 281 */       processMultiValued(key, value, issue, fieldDefinition, conversionErrors);
/*     */     } else {
/* 283 */       processSingleValued(key, value, issue, fieldDefinition, conversionErrors);
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   private void processSingleValued(@NotNull String key, @Nullable Object value, @NotNull UpdateIssue issue, @NotNull FieldDefinition fieldDefinition, @NotNull Map<String, String> conversionErrors) {
/* 289 */     String fieldType = fieldDefinition.getType();
/* 290 */     if (fieldType.equals("option")) {
/* 291 */       processOption(key, value, issue);
/* 292 */     } else if (fieldType.equals("user")) {
/* 293 */       processUser(key, value, issue);
/* 294 */     } else if (fieldType.equals("string") || fieldType.equals("text")) {
/* 295 */       if (this.metadataHelper.isJiraSprintField(fieldDefinition.getKey())) {
/*     */         try {
/* 297 */           value = (value != null) ? Long.valueOf(Long.parseLong((String)value)) : null;
/* 298 */         } catch (NumberFormatException ex) {
/* 299 */           throw new RuntimeException("Unable to parse id value to Integer", ex);
/*     */         } 
/* 301 */       } else if (this.metadataHelper.isJiraProjectField(fieldDefinition.getKey())) {
/* 302 */         processProject(key, value, issue);
/*     */         
/*     */         return;
/*     */       } 
/* 306 */       issue.addOperation(key, (Operation)new Set((value == null) ? "" : value));
/* 307 */     } else if (fieldType.equals("jira:rich-text")) {
/* 308 */       JiraRichText text = (JiraRichText)value;
/* 309 */       issue.addOperation(key, (Operation)new Set((text == null) ? "" : text.getSource()));
/* 310 */     } else if (fieldType.equals("date") || fieldType.equals("date-time")) {
/* 311 */       SimpleDateFormat sdf = new SimpleDateFormat();
/* 312 */       sdf.applyPattern("yyyy-MM-dd'T'HH:mm:ss.SSSZ");
/*     */       
/* 314 */       issue.addOperation(key, (Operation)new Set((value == null) ? null : sdf.format(value)));
/* 315 */     } else if (!fieldType.equals("jira:duration") || !key.startsWith("timetracking.")) {
/*     */       
/* 317 */       if (fieldType.equals("float")) {
/* 318 */         issue.addOperation(key, (Operation)new Set(value));
/*     */       } else {
/* 320 */         conversionErrors.put(key, String.format("Unsupported type '%s'.", new Object[] { fieldType }));
/*     */       } 
/*     */     } 
/*     */   } private void processUser(@NotNull String key, @Nullable Object value, @NotNull UpdateIssue issue) {
/*     */     Option option;
/* 325 */     String val = Optional.<Object>ofNullable(value).map(Object::toString).orElse("-1");
/*     */     
/* 327 */     if (this.isCloudDeployment) {
/* 328 */       option = new Option(val, null);
/*     */     } else {
/* 330 */       option = new Option(null, Optional.<Object>ofNullable(value).map(Object::toString).orElse("-1"));
/*     */     } 
/* 332 */     issue.addOperation(key, (Operation)new Set(option));
/*     */   }
/*     */   
/*     */   private void processOption(@NotNull String key, @Nullable Object value, @NotNull UpdateIssue issue) {
/* 336 */     issue.addOperation(key, (Operation)new Set(Option.withValue(value, this.metadataHelper.isCustomOption(key))));
/*     */   }
/*     */   
/*     */   private void processProject(@NotNull String key, @Nullable Object value, @NotNull UpdateIssue issue) {
/* 340 */     issue.addOperation(key, (Operation)new Set(Option.withKey(value, false)));
/*     */   }
/*     */   
/*     */   private void processMultiValued(@NotNull String key, @Nullable Object value, @NotNull UpdateIssue issue, @NotNull FieldDefinition fieldDefinition, @NotNull Map<String, String> conversionErrors) {
/* 344 */     String type = fieldDefinition.getType();
/* 345 */     if (value instanceof CollectionUpdate) {
/* 346 */       if ("option".equals(type) || "user".equals(type)) {
/* 347 */         processCollectionUpdate(issue, key, true, (CollectionUpdate)value);
/* 348 */       } else if ("string".equals(type)) {
/* 349 */         processCollectionUpdate(issue, key, false, (CollectionUpdate)value);
/*     */       } else {
/* 351 */         conversionErrors.put(key, String.format("Unsupported multi-value type '%s'.", new Object[] { type }));
/*     */       } 
/*     */     } else {
/*     */       
/* 355 */       conversionErrors.put(key, "Attempted to update multi-value field with invalid data: " + value);
/*     */     } 
/*     */   }
/*     */   
/*     */   private void processCollectionUpdate(@NotNull UpdateIssue issue, @NotNull String key, boolean isOptionValue, @NotNull CollectionUpdate<?> collectionUpdate) {
/* 360 */     for (Object addedValue : collectionUpdate.getAdded()) {
/* 361 */       if (isOptionValue) {
/* 362 */         issue.addOperation(key, (Operation)new Add(Option.withValue(addedValue, this.metadataHelper.isCustomOption(key)))); continue;
/*     */       } 
/* 364 */       issue.addOperation(key, (Operation)new Add(addedValue));
/*     */     } 
/*     */     
/* 367 */     for (Object removeValue : collectionUpdate.getRemoved()) {
/* 368 */       if (isOptionValue) {
/* 369 */         issue.addOperation(key, (Operation)new Remove(Option.withValue(removeValue, this.metadataHelper.isCustomOption(key)))); continue;
/*     */       } 
/* 371 */       issue.addOperation(key, (Operation)new Remove(removeValue));
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void addTimeTrackingValues(TransferItem transferItem, UpdateIssue issue, TransferItem targetItem) {
/* 378 */     if (transferItem.getValues().containsKey("timetracking.originalEstimate") || 
/* 379 */       transferItem.getValues().containsKey("timetracking.remainingEstimate")) {
/*     */       
/* 381 */       Map<String, String> timetracking = new HashMap<>();
/*     */       
/* 383 */       setTimeTrackingField("originalEstimate", transferItem, targetItem, timetracking);
/* 384 */       setTimeTrackingField("remainingEstimate", transferItem, targetItem, timetracking);
/*     */       
/* 386 */       issue.addOperation("timetracking", (Operation)new Set(timetracking));
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   private void setTimeTrackingField(String timetrackingKey, TransferItem transferItem, TransferItem targetItem, Map<String, String> timetracking) {
/* 393 */     String transferItemKey = "timetracking." + timetrackingKey;
/* 394 */     if (transferItem.getValues().containsKey(transferItemKey)) {
/* 395 */       setTimeTrackingField(transferItemKey, timetrackingKey, transferItem, timetracking);
/* 396 */     } else if (targetItem != null) {
/* 397 */       setTimeTrackingField(transferItemKey, timetrackingKey, targetItem, timetracking);
/*     */     } 
/*     */   }
/*     */   
/*     */   private void setTimeTrackingField(String transferItemKey, String timetrackingKey, TransferItem transferItem, Map<String, String> timetracking) {
/* 402 */     Duration duration = (Duration)transferItem.getValue(transferItemKey);
/* 403 */     if (duration == null) {
/* 404 */       timetracking.put(timetrackingKey, "");
/*     */     } else {
/* 406 */       timetracking.put(timetrackingKey, duration.getStringRepresentation());
/*     */     } 
/*     */   }
/*     */   
/*     */   public Collection<String> requiredOldValues(TransferItem transferItem) {
/* 411 */     Collection<String> requiredAdditionalFields = new ArrayList<>(3);
/*     */     
/* 413 */     if (transferItem.getValues().containsKey("timetracking.originalEstimate") && 
/* 414 */       !transferItem.getValues().containsKey("timetracking.remainingEstimate")) {
/* 415 */       requiredAdditionalFields.add("timetracking.remainingEstimate");
/*     */     }
/*     */     
/* 418 */     if (!transferItem.getValues().containsKey("timetracking.originalEstimate") && 
/* 419 */       transferItem.getValues().containsKey("timetracking.remainingEstimate")) {
/* 420 */       requiredAdditionalFields.add("timetracking.originalEstimate");
/*     */     }
/*     */     
/* 423 */     if (transferItem.getValues().containsKey("timetracking.timeSpent")) {
/* 424 */       requiredAdditionalFields.add("timetracking.timeSpent");
/*     */     }
/*     */     
/* 427 */     if (transferItem.getValues().containsKey("relations")) {
/* 428 */       requiredAdditionalFields.add("relations");
/*     */     }
/*     */     
/* 431 */     for (String fieldId : loadCascadingSelectFields(transferItem.getValues().keySet())) {
/* 432 */       addRequired(String.valueOf(fieldId) + "_child", transferItem, requiredAdditionalFields);
/* 433 */       addRequired(String.valueOf(fieldId) + "_root", transferItem, requiredAdditionalFields);
/*     */     } 
/*     */     
/* 436 */     return requiredAdditionalFields;
/*     */   }
/*     */   
/*     */   private void addRequired(@NotNull String requiredKey, @NotNull TransferItem transferItem, @NotNull Collection<String> requiredAdditionalField) {
/* 440 */     if (!transferItem.getValues().containsKey(requiredKey))
/* 441 */       requiredAdditionalField.add(requiredKey); 
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer.proxy.jira_3.22.1/jira.jar!/com/polarion/synchronizer/proxy/jira/TransferItemToIssueConverter.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */