/*    */ package com.polarion.synchronizer.proxy.jira.json;
/*    */ 
/*    */ public class Set
/*    */   extends Operation {
/*    */   private Object set;
/*    */   
/*    */   public Set(Object value) {
/*  8 */     this.set = value;
/*    */   }
/*    */   
/*    */   public Object getSet() {
/* 12 */     return this.set;
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer.proxy.jira_3.22.1/jira.jar!/com/polarion/synchronizer/proxy/jira/json/Set.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */