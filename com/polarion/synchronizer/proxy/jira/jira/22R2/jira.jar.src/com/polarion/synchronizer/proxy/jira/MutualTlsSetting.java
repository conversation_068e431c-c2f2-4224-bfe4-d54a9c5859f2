/*    */ package com.polarion.synchronizer.proxy.jira;
/*    */ 
/*    */ import com.polarion.platform.internal.security.UserAccountVault;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class MutualTlsSetting
/*    */ {
/*    */   @NotNull
/*    */   private final String serverUrl;
/*    */   @NotNull
/*    */   private final String keyStorePath;
/*    */   @NotNull
/*    */   private final String certificateAlias;
/*    */   @NotNull
/*    */   private final UserAccountVault.Credentials vaultCredential;
/*    */   
/*    */   public MutualTlsSetting(@NotNull String serverUrl, @NotNull String keyStorePath, @NotNull String certificateAlias, @NotNull UserAccountVault.Credentials vaultCredential) {
/* 23 */     this.serverUrl = serverUrl;
/* 24 */     this.keyStorePath = keyStorePath;
/* 25 */     this.certificateAlias = certificateAlias;
/* 26 */     this.vaultCredential = vaultCredential;
/*    */   }
/*    */   
/*    */   @NotNull
/*    */   public String getServerUrl() {
/* 31 */     return this.serverUrl;
/*    */   }
/*    */   
/*    */   @NotNull
/*    */   public String getKeyStorePath() {
/* 36 */     return this.keyStorePath;
/*    */   }
/*    */   
/*    */   @NotNull
/*    */   public String getCertificateAlias() {
/* 41 */     return this.certificateAlias;
/*    */   }
/*    */   
/*    */   @NotNull
/*    */   public UserAccountVault.Credentials getVaultCredential() {
/* 46 */     return this.vaultCredential;
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer.proxy.jira_3.22.1/jira.jar!/com/polarion/synchronizer/proxy/jira/MutualTlsSetting.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */