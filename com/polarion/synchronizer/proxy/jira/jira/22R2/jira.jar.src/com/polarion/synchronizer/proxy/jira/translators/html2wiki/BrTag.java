/*    */ package com.polarion.synchronizer.proxy.jira.translators.html2wiki;
/*    */ 
/*    */ import com.polarion.synchronizer.proxy.htmltranslator.ElementInfo;
/*    */ import com.polarion.synchronizer.proxy.htmltranslator.OpenCloseTag;
/*    */ import com.polarion.synchronizer.proxy.htmltranslator.Tag;
/*    */ import java.util.Collection;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ import org.jsoup.nodes.Element;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class BrTag
/*    */   extends OpenCloseTag
/*    */ {
/*    */   public BrTag() {
/* 18 */     super("\n", "");
/*    */   }
/*    */ 
/*    */   
/*    */   public void open(@NotNull Element element, @NotNull ElementInfo elementInfo, @NotNull StringBuilder sb, @NotNull Collection<Tag> openTags) {
/* 23 */     if (ThTdTag.isInThTdTag(openTags)) {
/* 24 */       sb.append("\\\\ ");
/*    */     } else {
/* 26 */       sb.append("\n");
/*    */     } 
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer.proxy.jira_3.22.1/jira.jar!/com/polarion/synchronizer/proxy/jira/translators/html2wiki/BrTag.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */