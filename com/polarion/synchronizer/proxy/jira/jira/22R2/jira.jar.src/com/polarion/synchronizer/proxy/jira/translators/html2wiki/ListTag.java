/*    */ package com.polarion.synchronizer.proxy.jira.translators.html2wiki;
/*    */ 
/*    */ import com.polarion.synchronizer.proxy.htmltranslator.Tag;
/*    */ import java.util.Collection;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ import org.jsoup.nodes.Element;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public interface ListTag
/*    */ {
/*    */   default long getLevel(@NotNull Collection<Tag> openTags) {
/* 16 */     return openTags.stream().filter(tag -> tag instanceof LiTag).count();
/*    */   }
/*    */   
/*    */   default boolean parentIsRendered(@NotNull Element element) {
/* 20 */     if ("li".equals(element.parent().tag().getName())) {
/* 21 */       return (element.parent().attributes().size() == 0);
/*    */     }
/* 23 */     return true;
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer.proxy.jira_3.22.1/jira.jar!/com/polarion/synchronizer/proxy/jira/translators/html2wiki/ListTag.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */