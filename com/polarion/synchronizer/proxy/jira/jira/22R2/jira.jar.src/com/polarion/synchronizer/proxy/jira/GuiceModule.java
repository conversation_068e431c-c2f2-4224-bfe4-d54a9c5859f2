/*    */ package com.polarion.synchronizer.proxy.jira;
/*    */ 
/*    */ import com.google.inject.AbstractModule;
/*    */ import com.google.inject.Module;
/*    */ import com.polarion.synchronizer.ProxyContribution;
/*    */ import com.polarion.synchronizer.internal.mapping.RelationTranslator;
/*    */ import com.polarion.synchronizer.mapping.ITranslator;
/*    */ import com.polarion.synchronizer.model.CollectionUpdate;
/*    */ import com.polarion.synchronizer.model.Duration;
/*    */ import com.polarion.synchronizer.model.Relation;
/*    */ import com.polarion.synchronizer.proxy.jira.model.JiraComment;
/*    */ import com.polarion.synchronizer.proxy.jira.translators.JiraToPolarionCommentTranslator;
/*    */ import com.polarion.synchronizer.proxy.jira.translators.JiraToPolarionDescriptionTranslator;
/*    */ import com.polarion.synchronizer.proxy.jira.translators.JiraToPolarionDurationTranslator;
/*    */ import com.polarion.synchronizer.proxy.jira.translators.PolarionToJiraCommentTranslator;
/*    */ import com.polarion.synchronizer.proxy.jira.translators.PolarionToJiraDecriptionTranslator;
/*    */ import com.polarion.synchronizer.proxy.jira.translators.PolarionToJiraDurationTranslator;
/*    */ import com.polarion.synchronizer.proxy.jira.translators.RelationToHyperlinkTranslator;
/*    */ import com.polarion.synchronizer.proxy.polarion.model.Comment;
/*    */ import com.polarion.synchronizer.proxy.polarion.model.Hyperlink;
/*    */ import com.polarion.synchronizer.spi.ProxyBinder;
/*    */ import com.polarion.synchronizer.spi.translators.CollectionTranslatorModule;
/*    */ import com.polarion.synchronizer.spi.translators.TranslatorModule;
/*    */ 
/*    */ public class GuiceModule
/*    */   extends AbstractModule {
/*    */   protected void configure() {
/* 28 */     ProxyBinder.bindProxy(binder(), 
/* 29 */         new ProxyContribution("Jira", JiraProxyConfiguration.class, JiraConnection.class), JiraProxyFactory.class);
/*    */     
/* 31 */     install((Module)new CollectionTranslatorModule<Comment>(
/* 32 */           "jira:comment", "comment", JiraToPolarionCommentTranslator.class) {  }
/*    */       );
/* 34 */     install((Module)new CollectionTranslatorModule<JiraComment>(
/* 35 */           "comment", "jira:comment", PolarionToJiraCommentTranslator.class) {
/*    */         
/*    */         });
/* 38 */     install((Module)new TranslatorModule<JiraRichText>(
/* 39 */           "rich-text", "jira:rich-text", PolarionToJiraDecriptionTranslator.class) {  }
/*    */       );
/* 41 */     install((Module)new TranslatorModule<String>(
/* 42 */           "jira:rich-text", "rich-text", JiraToPolarionDescriptionTranslator.class) {
/*    */         
/*    */         });
/* 45 */     install((Module)new TranslatorModule<Duration>(
/* 46 */           "duration", "jira:duration", PolarionToJiraDurationTranslator.class) {  }
/*    */       );
/* 48 */     install((Module)new TranslatorModule<Duration>(
/* 49 */           "jira:duration", "duration", JiraToPolarionDurationTranslator.class) {
/*    */         
/*    */         });
/* 52 */     install((Module)new CollectionTranslatorModule<Hyperlink>("jira:relation", "polarion:hyperlink", RelationToHyperlinkTranslator.class) {
/*    */         
/*    */         });
/* 55 */     install((Module)new CollectionTranslatorModule<Relation>("jira:relation", "relation", RelationTranslator.class) {
/*    */         
/*    */         });
/* 58 */     install((Module)new CollectionTranslatorModule<Relation>("relation", "jira:relation", RelationTranslator.class) {
/*    */         
/*    */         });
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer.proxy.jira_3.22.1/jira.jar!/com/polarion/synchronizer/proxy/jira/GuiceModule.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */