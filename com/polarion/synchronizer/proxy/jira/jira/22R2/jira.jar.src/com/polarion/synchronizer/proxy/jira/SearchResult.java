/*    */ package com.polarion.synchronizer.proxy.jira;
/*    */ 
/*    */ import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
/*    */ import com.polarion.synchronizer.proxy.jira.json.Issue;
/*    */ import java.util.Collection;
/*    */ 
/*    */ 
/*    */ 
/*    */ @JsonIgnoreProperties(ignoreUnknown = true)
/*    */ public class SearchResult
/*    */ {
/*    */   private Collection<Issue> issues;
/*    */   private Integer total;
/*    */   
/*    */   public Collection<Issue> getIssues() {
/* 16 */     return this.issues;
/*    */   }
/*    */   
/*    */   public Integer getTotal() {
/* 20 */     return this.total;
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer.proxy.jira_3.22.1/jira.jar!/com/polarion/synchronizer/proxy/jira/SearchResult.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */