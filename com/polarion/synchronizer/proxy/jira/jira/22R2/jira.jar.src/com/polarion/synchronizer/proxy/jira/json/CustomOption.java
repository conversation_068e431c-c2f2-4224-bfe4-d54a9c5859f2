/*    */ package com.polarion.synchronizer.proxy.jira.json;
/*    */ 
/*    */ import com.fasterxml.jackson.annotation.JsonIgnore;
/*    */ import org.jetbrains.annotations.Nullable;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class CustomOption
/*    */   extends Option
/*    */ {
/*    */   public CustomOption(@Nullable Object value) {
/* 31 */     super(value);
/*    */   }
/*    */ 
/*    */   
/*    */   @JsonIgnore
/*    */   @Nullable
/*    */   public String getName() {
/* 38 */     return super.getName();
/*    */   }
/*    */   
/*    */   @Nullable
/*    */   public String getValue() {
/* 43 */     return super.getName();
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer.proxy.jira_3.22.1/jira.jar!/com/polarion/synchronizer/proxy/jira/json/CustomOption.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */