/*    */ package com.polarion.synchronizer.proxy.jira.json;
/*    */ 
/*    */ import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
/*    */ import com.fasterxml.jackson.annotation.JsonProperty;
/*    */ import com.google.common.base.Objects;
/*    */ import java.util.Optional;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ import org.jetbrains.annotations.Nullable;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @JsonIgnoreProperties(ignoreUnknown = true)
/*    */ public class Scope
/*    */ {
/*    */   @Nullable
/*    */   private String type;
/*    */   @Nullable
/*    */   private Project project;
/*    */   
/*    */   public Scope(@JsonProperty("type") @Nullable String type, @JsonProperty("project") @Nullable Project project) {
/* 23 */     this.type = type;
/* 24 */     this.project = project;
/*    */   }
/*    */   
/*    */   public boolean isProjectType() {
/* 28 */     return "project".equals(this.type);
/*    */   }
/*    */   @Nullable
/*    */   private String getProjectId() {
/* 32 */     return Optional.<Project>ofNullable(this.project).map(project -> project.getId()).orElse(null);
/*    */   }
/*    */   
/*    */   public boolean matches(@NotNull Project project) {
/* 36 */     return Objects.equal(getProjectId(), project.getId());
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer.proxy.jira_3.22.1/jira.jar!/com/polarion/synchronizer/proxy/jira/json/Scope.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */