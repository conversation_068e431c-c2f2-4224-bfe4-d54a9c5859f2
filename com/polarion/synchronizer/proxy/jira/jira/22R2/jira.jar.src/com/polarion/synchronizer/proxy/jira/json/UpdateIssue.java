/*    */ package com.polarion.synchronizer.proxy.jira.json;
/*    */ 
/*    */ import java.util.Collection;
/*    */ import java.util.HashMap;
/*    */ import java.util.LinkedList;
/*    */ import java.util.Map;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ import org.jetbrains.annotations.Nullable;
/*    */ 
/*    */ public class UpdateIssue
/*    */   extends Issue
/*    */ {
/* 13 */   private Map<String, Collection<Operation>> update = new HashMap<>();
/*    */   
/*    */   @Nullable
/*    */   private Transition transition;
/*    */   
/*    */   public void addOperation(String field, Operation operation) {
/* 19 */     Collection<Operation> operations = this.update.get(field);
/* 20 */     if (operations == null) {
/* 21 */       operations = new LinkedList<>();
/* 22 */       this.update.put(field, operations);
/*    */     } 
/* 24 */     operations.add(operation);
/*    */   }
/*    */   
/*    */   public Map<String, Collection<Operation>> getUpdate() {
/* 28 */     return this.update;
/*    */   }
/*    */   
/*    */   @Nullable
/*    */   public Transition getTransition() {
/* 33 */     return this.transition;
/*    */   }
/*    */   
/*    */   public void setTransition(@NotNull Transition transition) {
/* 37 */     this.transition = transition;
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer.proxy.jira_3.22.1/jira.jar!/com/polarion/synchronizer/proxy/jira/json/UpdateIssue.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */