/*    */ package com.polarion.synchronizer.proxy.jira;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public enum JiraIcons
/*    */ {
/* 10 */   INFO("i", "information.png"), ERROR("x", "cross.png"), OK("/", "tick.png");
/*    */   
/*    */   private final String inPolarion;
/*    */   private final String inJira;
/*    */   
/*    */   JiraIcons(String inJira, String inPolarion) {
/* 16 */     this.inJira = inJira;
/* 17 */     this.inPolarion = inPolarion;
/*    */   }
/*    */   
/*    */   public String inJira() {
/* 21 */     return this.inJira;
/*    */   }
/*    */   
/*    */   public String inPolarion() {
/* 25 */     return this.inPolarion;
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer.proxy.jira_3.22.1/jira.jar!/com/polarion/synchronizer/proxy/jira/JiraIcons.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */