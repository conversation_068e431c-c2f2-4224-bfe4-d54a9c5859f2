/*    */ package com.polarion.synchronizer.proxy.jira.translators.html2wiki;
/*    */ 
/*    */ import com.polarion.synchronizer.proxy.htmltranslator.ElementInfo;
/*    */ import com.polarion.synchronizer.proxy.htmltranslator.RecursiveTag;
/*    */ import com.polarion.synchronizer.proxy.htmltranslator.Tag;
/*    */ import java.util.Collection;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ import org.jsoup.nodes.Element;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class DivTag
/*    */   extends RecursiveTag
/*    */ {
/*    */   public void open(@NotNull Element element, @NotNull ElementInfo elementInfo, @NotNull StringBuilder sb, @NotNull Collection<Tag> openTags) {
/* 19 */     if (!element.text().equals("") && element.attr("style").equals("margin-left: 40px;")) {
/* 20 */       sb.append("{quote}");
/*    */     }
/*    */   }
/*    */ 
/*    */   
/*    */   public void close(@NotNull Element element, @NotNull ElementInfo elementInfo, @NotNull StringBuilder sb, @NotNull Collection<Tag> openTags) {
/* 26 */     if (!element.text().equals("") && element.attr("style").equals("margin-left: 40px;"))
/* 27 */       sb.append("{quote}"); 
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer.proxy.jira_3.22.1/jira.jar!/com/polarion/synchronizer/proxy/jira/translators/html2wiki/DivTag.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */