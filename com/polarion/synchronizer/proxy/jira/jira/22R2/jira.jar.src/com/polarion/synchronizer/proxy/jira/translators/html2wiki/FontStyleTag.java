/*    */ package com.polarion.synchronizer.proxy.jira.translators.html2wiki;
/*    */ 
/*    */ import com.polarion.synchronizer.proxy.htmltranslator.ElementInfo;
/*    */ import com.polarion.synchronizer.proxy.htmltranslator.StyleTag;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class FontStyleTag
/*    */   implements StyleTag
/*    */ {
/*    */   public static final String KEY = "font-style";
/*    */   public static final String CITATION_PREFIX = "— ";
/*    */   
/*    */   public void open(@NotNull ElementInfo elementInfo, @NotNull String text, @NotNull StringBuilder sb) {
/* 36 */     if (!text.isEmpty()) {
/* 37 */       String fontStyle = (String)elementInfo.getStyle().get("font-style");
/* 38 */       if ("italic".equals(fontStyle)) {
/* 39 */         if (text.startsWith("— ")) {
/* 40 */           sb.append("??");
/*    */         } else {
/* 42 */           sb.append("_");
/*    */         } 
/*    */       }
/*    */     } 
/*    */   }
/*    */ 
/*    */   
/*    */   public void close(@NotNull ElementInfo elementInfo, @NotNull String text, @NotNull StringBuilder sb) {
/* 50 */     if (!text.isEmpty()) {
/* 51 */       String fontStyle = (String)elementInfo.getStyle().get("font-style");
/* 52 */       if ("italic".equals(fontStyle))
/* 53 */         if (text.startsWith("— ")) {
/* 54 */           sb.append("??");
/*    */         } else {
/* 56 */           sb.append("_");
/*    */         }  
/*    */     } 
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer.proxy.jira_3.22.1/jira.jar!/com/polarion/synchronizer/proxy/jira/translators/html2wiki/FontStyleTag.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */