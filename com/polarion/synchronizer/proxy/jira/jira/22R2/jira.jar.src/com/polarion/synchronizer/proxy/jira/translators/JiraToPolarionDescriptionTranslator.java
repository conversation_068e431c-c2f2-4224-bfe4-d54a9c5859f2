/*    */ package com.polarion.synchronizer.proxy.jira.translators;
/*    */ 
/*    */ import com.polarion.core.util.xml.HTMLHelper;
/*    */ import com.polarion.synchronizer.helper.RichTextHelper;
/*    */ import com.polarion.synchronizer.mapping.TranslationResult;
/*    */ import com.polarion.synchronizer.proxy.jira.JiraRichText;
/*    */ import com.polarion.synchronizer.spi.translators.TypesafeTranslator;
/*    */ import javax.inject.Inject;
/*    */ import org.w3c.dom.Document;
/*    */ 
/*    */ 
/*    */ public class JiraToPolarionDescriptionTranslator
/*    */   extends TypesafeTranslator<JiraRichText, String, String>
/*    */ {
/* 15 */   private RichTextHelper richTextHelper = new RichTextHelper();
/*    */   
/*    */   @Inject
/*    */   public JiraToPolarionDescriptionTranslator() {
/* 19 */     super(JiraRichText.class, String.class);
/*    */   }
/*    */ 
/*    */   
/*    */   public TranslationResult<String> translateUnidirectionalTypesafe(JiraRichText source, String target) {
/* 24 */     return createUnidirectionalResult(convert(source), target);
/*    */   }
/*    */ 
/*    */   
/*    */   public TranslationResult<String> translateBidirectionalTypesafe(JiraRichText sourceBaseline, JiraRichText sourceValue, String targetBaseline, String targetValue) {
/* 29 */     return createBidirectionalResult(sourceBaseline, sourceValue, convert(sourceValue), targetBaseline, targetValue);
/*    */   }
/*    */   
/*    */   private String convert(JiraRichText source) {
/* 33 */     String htmlText = null;
/* 34 */     if (source != null && source.getRendered() != null) {
/* 35 */       String convertedHtml = (new JiraToPolarionHtmlConverter()).convert(source.getRendered());
/* 36 */       Document document = HTMLHelper.parseHTMLAsDOM(convertedHtml);
/* 37 */       RichTextHelper.alignImageStyleAttributes(document.getDocumentElement());
/* 38 */       htmlText = this.richTextHelper.sortAttributes(document);
/*    */     } 
/* 40 */     return (htmlText == null) ? null : htmlText.replaceAll("\\r", "");
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer.proxy.jira_3.22.1/jira.jar!/com/polarion/synchronizer/proxy/jira/translators/JiraToPolarionDescriptionTranslator.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */