/*    */ package com.polarion.synchronizer.proxy.jira.translators.html2wiki;
/*    */ 
/*    */ import com.polarion.synchronizer.proxy.htmltranslator.ElementInfo;
/*    */ import com.polarion.synchronizer.proxy.htmltranslator.RecursiveTag;
/*    */ import com.polarion.synchronizer.proxy.htmltranslator.Tag;
/*    */ import java.util.Collection;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ import org.jsoup.nodes.Element;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class ThTdTag
/*    */   extends RecursiveTag
/*    */ {
/*    */   @NotNull
/*    */   private final String separator;
/*    */   
/*    */   public ThTdTag(@NotNull String separator) {
/* 39 */     this.separator = separator;
/*    */   }
/*    */ 
/*    */   
/*    */   public void open(@NotNull Element element, @NotNull ElementInfo elementInfo, @NotNull StringBuilder sb, @NotNull Collection<Tag> openTags) {
/* 44 */     sb.append(this.separator);
/* 45 */     if (element.ownText().isEmpty()) {
/* 46 */       sb.append(" ");
/*    */     }
/*    */   }
/*    */ 
/*    */   
/*    */   public void close(@NotNull Element element, @NotNull ElementInfo elementInfo, @NotNull StringBuilder sb, @NotNull Collection<Tag> openTags) {
/* 52 */     if (element.nextElementSibling() == null) {
/* 53 */       sb.append(this.separator);
/*    */     }
/*    */   }
/*    */ 
/*    */   
/*    */   public void process(@NotNull Element element, @NotNull ElementInfo elementInfo, @NotNull StringBuilder sb, @NotNull Collection<Tag> openTags) {
/* 59 */     if (element.ownText().isEmpty()) {
/* 60 */       sb.append(" ");
/*    */     }
/*    */   }
/*    */   
/*    */   public static boolean isInThTdTag(@NotNull Collection<Tag> openTags) {
/* 65 */     return openTags.stream().filter(o -> o instanceof ThTdTag).findFirst().isPresent();
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer.proxy.jira_3.22.1/jira.jar!/com/polarion/synchronizer/proxy/jira/translators/html2wiki/ThTdTag.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */