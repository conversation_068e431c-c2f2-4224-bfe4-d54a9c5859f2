/*     */ package com.polarion.synchronizer.proxy.jira.translators.html2wiki;
/*     */ 
/*     */ import com.polarion.core.util.StringUtils;
/*     */ import com.polarion.synchronizer.proxy.htmltranslator.ElementInfo;
/*     */ import com.polarion.synchronizer.proxy.htmltranslator.ImageSize;
/*     */ import com.polarion.synchronizer.proxy.htmltranslator.Tag;
/*     */ import com.polarion.synchronizer.proxy.jira.JiraIcons;
/*     */ import com.polarion.synchronizer.spi.translators.RichTextUtils;
/*     */ import java.util.Arrays;
/*     */ import java.util.Collection;
/*     */ import org.apache.commons.io.FilenameUtils;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ import org.jsoup.nodes.Element;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ImgTag
/*     */   implements Tag
/*     */ {
/*     */   private static final int MAX_THUMBNAIL_WIDTH = 200;
/*     */   private static final int MAX_THUMBNAIL_HEIGHT = 200;
/*     */   private static final String ICON_PREFIX = "/polarion/ria/images/actions/";
/*     */   
/*     */   public static boolean isImage(@NotNull String fileName) {
/*  44 */     String[] validImageExtensions = { "bmp", "jpg", "jpeg", "jpe", "gif", "png" };
/*  45 */     return Arrays.<String>asList(validImageExtensions).stream().anyMatch(x -> x.equalsIgnoreCase(FilenameUtils.getExtension(paramString1)));
/*     */   }
/*     */ 
/*     */   
/*     */   public void open(@NotNull Element element, @NotNull ElementInfo elementInfo, @NotNull StringBuilder sb, @NotNull Collection<Tag> openTags) {
/*  50 */     String src = getSrc(element);
/*  51 */     String nameFromUrl = RichTextUtils.nameFromUrl(src);
/*  52 */     if (nameFromUrl == null || isImage(nameFromUrl)) {
/*  53 */       if (isIcon(src)) {
/*  54 */         sb.append("(");
/*     */       } else {
/*  56 */         sb.append("!");
/*     */       } 
/*     */     } else {
/*  59 */       sb.append("[^");
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void close(@NotNull Element element, @NotNull ElementInfo elementInfo, @NotNull StringBuilder sb, @NotNull Collection<Tag> openTags) {
/*  66 */     String src = getSrc(element);
/*  67 */     String nameFromUrl = RichTextUtils.nameFromUrl(src);
/*  68 */     String style = StringUtils.getEmptyIfNull(element.attr("style"));
/*  69 */     if (nameFromUrl == null || isImage(nameFromUrl)) {
/*  70 */       if (isIcon(src)) {
/*  71 */         sb.append(")");
/*  72 */       } else if (isThumbnail(style)) {
/*  73 */         sb.append("|thumbnail! ");
/*     */       } else {
/*  75 */         sb.append("!");
/*     */       } 
/*     */     } else {
/*     */       
/*  79 */       sb.append("]");
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   public void process(@NotNull Element element, @NotNull ElementInfo elementInfo, @NotNull StringBuilder sb, @NotNull Collection<Tag> openTags) {
/*  85 */     String src = getSrc(element);
/*  86 */     String nameFromUrl = RichTextUtils.nameFromUrl(src);
/*  87 */     if (nameFromUrl != null) {
/*  88 */       sb.append(nameFromUrl);
/*     */     }
/*  90 */     else if (isIcon(src)) {
/*  91 */       sb.append(getIconReplacement(src));
/*     */     } else {
/*  93 */       sb.append(element.absUrl("src"));
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   private String getIconReplacement(String src) {
/* 100 */     if (src.endsWith(JiraIcons.INFO.inPolarion()))
/* 101 */       return JiraIcons.INFO.inJira(); 
/* 102 */     if (src.endsWith(JiraIcons.OK.inPolarion()))
/* 103 */       return JiraIcons.OK.inJira(); 
/* 104 */     if (src.endsWith(JiraIcons.ERROR.inPolarion())) {
/* 105 */       return JiraIcons.ERROR.inJira();
/*     */     }
/* 107 */     return src;
/*     */   }
/*     */   
/*     */   @NotNull
/*     */   private String getSrc(@NotNull Element element) {
/* 112 */     return StringUtils.getEmptyIfNull(element.attr("src"));
/*     */   }
/*     */   
/*     */   private boolean isIcon(@NotNull String src) {
/* 116 */     return src.startsWith("/polarion/ria/images/actions/");
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean isContinue() {
/* 121 */     return false;
/*     */   }
/*     */   
/*     */   private boolean isThumbnail(@NotNull String styleAttributes) {
/* 125 */     if (styleAttributes.contains("max-width")) {
/* 126 */       return true;
/*     */     }
/* 128 */     ImageSize size = ImageSize.getDimensions(styleAttributes);
/* 129 */     int width = size.getWidth();
/* 130 */     int height = size.getHeight();
/* 131 */     if (width != 0 && height != 0 && width <= 200 && height < 200) {
/* 132 */       return true;
/*     */     }
/* 134 */     return false;
/*     */   }
/*     */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer.proxy.jira_3.22.1/jira.jar!/com/polarion/synchronizer/proxy/jira/translators/html2wiki/ImgTag.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */