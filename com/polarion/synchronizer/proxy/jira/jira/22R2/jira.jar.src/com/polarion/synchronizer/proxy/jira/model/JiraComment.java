/*    */ package com.polarion.synchronizer.proxy.jira.model;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ import java.util.Date;
/*    */ 
/*    */ 
/*    */ public class JiraComment
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1883637055046399217L;
/*    */   private transient String author;
/*    */   private String content;
/*    */   private Date created;
/*    */   
/*    */   public JiraComment(String author, String content, Date created) {
/* 16 */     this.author = author;
/* 17 */     this.content = content;
/* 18 */     this.created = created;
/*    */   }
/*    */ 
/*    */   
/*    */   public String getAuthor() {
/* 23 */     return this.author;
/*    */   }
/*    */ 
/*    */   
/*    */   public String getContent() {
/* 28 */     return this.content;
/*    */   }
/*    */ 
/*    */   
/*    */   public Date getCreated() {
/* 33 */     return this.created;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String toString() {
/* 41 */     return "Comment [author=" + this.author + ", content=" + this.content + ", created=" + this.created + "]";
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public int hashCode() {
/* 47 */     int prime = 31;
/* 48 */     int result = 1;
/* 49 */     result = 31 * result + ((this.content == null) ? 0 : this.content.hashCode());
/* 50 */     return result;
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public boolean equals(Object obj) {
/* 56 */     if (this == obj)
/* 57 */       return true; 
/* 58 */     if (obj == null)
/* 59 */       return false; 
/* 60 */     if (getClass() != obj.getClass())
/* 61 */       return false; 
/* 62 */     JiraComment other = (JiraComment)obj;
/* 63 */     if (this.content == null) {
/* 64 */       if (other.content != null)
/* 65 */         return false; 
/* 66 */     } else if (!this.content.equals(other.content)) {
/* 67 */       return false;
/* 68 */     }  return true;
/*    */   }
/*    */ }


/* Location:              /opt/polarion/polarion/plugins/com.polarion.synchronizer.proxy.jira_3.22.1/jira.jar!/com/polarion/synchronizer/proxy/jira/model/JiraComment.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */