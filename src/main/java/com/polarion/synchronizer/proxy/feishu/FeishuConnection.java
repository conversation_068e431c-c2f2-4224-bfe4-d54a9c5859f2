package com.polarion.synchronizer.proxy.feishu;

import com.polarion.synchronizer.spi.Connection;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.XmlTransient;
import java.util.ArrayList;
import java.util.Collection;

/**
 * 飞书项目连接器配置类
 * 定义连接到飞书项目所需的基本参数
 */
@XmlRootElement
@XmlType
public class FeishuConnection extends Connection {
    
    private String serverUrl;
    private String authMode = "plugin_access_token"; // 访问凭证类型：plugin_access_token, virtual_plugin_token, user_access_token
    private String pluginId;  // App ID
    @XmlTransient  // 不序列化到XML，使用保险库存储
    private String pluginSecret;  // App Secret (for plugin_access_token)
    @XmlTransient  // 不序列化到XML，使用保险库存储
    private String accessToken;  // 直接的访问凭证 (for virtual_plugin_token, user_access_token)
    //用于 xml 配置回显赋值
    public FeishuConnection() {
        super();
        // 设置默认用户名，密码用于存储accessToken
        this.setUser("feishu-access-token");
    }
    
    /**
     * 构造函数
     * @param id 连接ID
     * @param serverUrl 服务器地址
     * @param authMode 访问凭证类型
     * @param accessToken 访问凭证
     */
    public FeishuConnection(String id, String serverUrl, String authMode, String accessToken) {
        super(id, "feishu-access-token", accessToken != null ? accessToken : "");
        this.serverUrl = serverUrl != null ? serverUrl : "https://project.feishu.cn";
        this.authMode = authMode != null ? authMode : "plugin_access_token";
        this.accessToken = accessToken;
    }
    
    public String getServerUrl() {
        return serverUrl;
    }
    
    public void setServerUrl(String serverUrl) {
        this.serverUrl = serverUrl;
    }
    
    public String getAuthMode() {
        return authMode != null ? authMode : "plugin_access_token";
    }

    public void setAuthMode(String authMode) {
        this.authMode = authMode;
    }

    public String getPluginId() {
        return pluginId;
    }

    public void setPluginId(String pluginId) {
        this.pluginId = pluginId;
    }

    public String getPluginSecret() {
        // 从保险库中读取secret，如果没有则返回XML中的值
        if ("plugin_access_token".equals(getAuthMode())) {
            String vaultSecret = this.getPassword();
            return (vaultSecret != null && !vaultSecret.isEmpty() && !"Not required.".equals(vaultSecret))
                   ? vaultSecret : pluginSecret;
        }
        return pluginSecret;
    }

    public void setPluginSecret(String pluginSecret) {
        this.pluginSecret = pluginSecret;
        // 将secret存储到保险库中
        if ("plugin_access_token".equals(getAuthMode()) && pluginSecret != null && !pluginSecret.isEmpty()) {
            this.setPassword(pluginSecret);
        }
    }

    public String getAccessToken() {
        // 从保险库中读取token，如果没有则返回XML中的值
        if (!"plugin_access_token".equals(getAuthMode())) {
            String vaultToken = this.getPassword();
            return (vaultToken != null && !vaultToken.isEmpty() && !"Not required.".equals(vaultToken))
                   ? vaultToken : accessToken;
        }
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
        // 将token存储到保险库中
        if (!"plugin_access_token".equals(getAuthMode()) && accessToken != null && !accessToken.isEmpty()) {
            this.setPassword(accessToken);
        }
    }
    
    /**
     * 检查连接配置的有效性
     * @return 错误信息，如果配置有效则返回null
     */
    @Nullable
    public String check() {
        Collection<String> missing = new ArrayList<>();

        if (getServerUrl() == null || getServerUrl().isEmpty()) {
            missing.add("服务器地址");
        }

        String authMode = getAuthMode();
        switch (authMode) {
            case "plugin_access_token":
                if (getPluginId() == null || getPluginId().isEmpty()) {
                    missing.add("App ID");
                }
                if (getPluginSecret() == null || getPluginSecret().isEmpty()) {
                    missing.add("App Secret");
                }
                break;
            case "virtual_plugin_token":
            case "user_access_token":
                if (getAccessToken() == null || getAccessToken().isEmpty()) {
                    missing.add("访问凭证");
                }
                break;
            default:
                return "不支持的访问凭证类型: " + authMode;
        }

        if (!missing.isEmpty()) {
            return "缺少必需的配置项: " + String.join(", ", missing);
        }

        // 验证服务器地址格式
        if (!getServerUrl().startsWith("http://") && !getServerUrl().startsWith("https://")) {
            return "服务器地址必须以 http:// 或 https:// 开头";
        }

        return null;
    }
    
    /**
     * 获取完整的API基础URL
     * @return API基础URL
     */
    @NotNull
    public String getApiBaseUrl() {
        String baseUrl = getServerUrl();
        if (baseUrl.endsWith("/")) {
            baseUrl = baseUrl.substring(0, baseUrl.length() - 1);
        }
        return baseUrl + "/open_api";
    }
    
    /**
     * 测试连接是否可用
     * @return 连接测试结果
     */
    public boolean testConnection() {
        // TODO: 实现连接测试逻辑
        // 可以调用飞书项目的健康检查接口或获取用户信息接口
        return true;
    }
}
