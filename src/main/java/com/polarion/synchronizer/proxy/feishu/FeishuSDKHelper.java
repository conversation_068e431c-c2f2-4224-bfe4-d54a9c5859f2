package com.polarion.synchronizer.proxy.feishu;

import com.lark.project.Client;
import com.lark.project.core.request.RequestOptions;
import com.lark.project.service.project.builder.ListProjectWorkItemTypeReq;
import com.lark.project.service.project.builder.ListProjectWorkItemTypeResp;
import com.polarion.core.util.logging.Logger;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

/**
 * 飞书项目SDK辅助类
 * 封装常用的SDK操作和客户端调用
 */
public class FeishuSDKHelper {

    private static final Logger logger = Logger.getLogger(FeishuSDKHelper.class);

    private final FeishuConnection connection;
    private Client client;
    private RequestOptions defaultOptions;

    public FeishuSDKHelper(@NotNull FeishuConnection connection) {
        this.connection = connection;
        this.client = createClient();
        this.defaultOptions = createRequestOptions();
    }
    
    /**
     * 创建SDK客户端
     */
    private Client createClient() {
        String baseUrl = getBaseUrl();
        String authMode = connection.getAuthMode();

        switch (authMode) {
            case "plugin_access_token":
                // 插件身份凭证模式 - 使用App ID和Secret，SDK自动管理token
                return Client.newBuilder(connection.getPluginId(), connection.getPluginSecret())
                    .openBaseUrl(baseUrl)
                    .requestTimeout(30000)
                    .logReqAtDebug(true)
                    .build();

            case "virtual_plugin_token":
            case "user_access_token":
                // 禁用token管理，需要手动传递token
                return Client.newBuilder("", "")
                    .openBaseUrl(baseUrl)
                    .requestTimeout(30000)
                    .disableTokenCache()
                    .logReqAtDebug(true)
                    .build();

            default:
                // 默认使用插件模式
                return Client.newBuilder("", "")
                    .openBaseUrl(baseUrl)
                    .requestTimeout(30000)
                    .disableTokenCache()
                    .logReqAtDebug(true)
                    .build();
        }
    }

    /**
     * 根据访问凭证类型创建请求选项
     */
    private RequestOptions createRequestOptions() {
        String authMode = connection.getAuthMode();

        switch (authMode) {
            case "plugin_access_token":
                // 插件模式由SDK自动管理token，只需要设置X-User-Key
                RequestOptions.Builder builder = RequestOptions.newBuilder();
                String userKey = connection.getUserKey();
                if (userKey != null && !userKey.isEmpty()) {
                	builder.userKey(userKey);
                }
                return builder.build();

            case "virtual_plugin_token":
            case "user_access_token":
                // 手动传递token
                String accessToken = connection.getAccessToken();
                if (accessToken != null && !accessToken.isEmpty()) {
                    return RequestOptions.newBuilder()
                        .accessToken(accessToken)
                        .build();
                }
                break;
        }

        return RequestOptions.newBuilder().build();
    }



    /**
     * 获取基础URL
     */
    private String getBaseUrl() {
        String baseUrl = connection.getServerUrl();
        return (baseUrl != null && !baseUrl.isEmpty()) ? baseUrl : "https://project.feishu.cn";
    }
    
    /**
     * 刷新客户端和请求选项
     * 当连接参数发生变化时调用此方法重新创建客户端
     */
    public void refreshClient() {
        this.client = createClient();
        this.defaultOptions = createRequestOptions();
        logger.debug("已刷新飞书SDK客户端和请求选项");
    }

    /**
     * 测试连接 - 使用SDK调用
     * 创建临时客户端进行测试，不影响实例的主客户端
     */
    @Nullable
    public String testConnection() {
        Client tempClient = null;
        RequestOptions tempOptions = null;

        try {
            logger.info("测试飞书连接，认证模式: " + connection.getAuthMode());

            // 创建临时客户端和请求选项用于测试，不影响主客户端
            tempClient = createClient();
            tempOptions = createRequestOptions();

            ListProjectWorkItemTypeReq req = ListProjectWorkItemTypeReq.newBuilder()
                .build();

            ListProjectWorkItemTypeResp resp = tempClient.getProjectService().listProjectWorkItemType(req, tempOptions);

            if (resp.success()) {
                logger.info("飞书连接测试成功");
                return null;
            } else {
                String errorMsg = String.format("飞书连接测试失败: %s (错误码: %s)",
                    resp.getErrMsg(), resp.getErrCode());
                logger.warn(errorMsg);
                return errorMsg;
            }

        } catch (Exception e) {
            String errorMsg = "飞书连接测试出现异常: " + e.getMessage();
            logger.error(errorMsg, e);
            return errorMsg;
        } finally {
            // 清理临时客户端资源（如果需要的话）
            if (tempClient != null) {
                try {
                    // 这里可以添加客户端清理逻辑，如果SDK提供了相应方法
                    logger.debug("临时测试客户端已清理");
                } catch (Exception e) {
                    logger.warn("清理临时测试客户端时出现异常: " + e.getMessage());
                }
            }
        }
    }
    
    /**
     * 获取项目的工作项类型
     */
    public ListProjectWorkItemTypeResp getWorkItemTypes(@NotNull String projectKey) throws Exception {
        ListProjectWorkItemTypeReq req = ListProjectWorkItemTypeReq.newBuilder()
            .projectKey(projectKey)
            .build();

        return client.getProjectService().listProjectWorkItemType(req, defaultOptions);
    }

    /**
     * 获取SDK客户端（用于高级操作）
     */
    public Client getClient() {
        return client;
    }

    /**
     * 获取默认请求选项（用于高级操作）
     */
    public RequestOptions getDefaultOptions() {
        return defaultOptions;
    }
}
