2025-07-28 11:14:04 CST  LOG:  starting PostgreSQL 14.18 (Ubuntu 14.18-1.pgdg22.04+1) on aarch64-unknown-linux-gnu, compiled by gcc (Ubuntu 11.4.0-1ubuntu1~22.04) 11.4.0, 64-bit
2025-07-28 11:14:04 CST  LOG:  listening on IPv4 address "127.0.0.1", port 5434
2025-07-28 11:14:04 CST  LOG:  listening on IPv6 address "::1", port 5434
2025-07-28 11:14:04 CST  LOG:  listening on Unix socket "/var/run/postgresql/.s.PGSQL.5434"
2025-07-28 11:14:04 CST  LOG:  database system was interrupted; last known up at 2025-07-27 21:11:16 CST
2025-07-28 11:14:04 CST  LOG:  database system was not properly shut down; automatic recovery in progress
2025-07-28 11:14:04 CST  LOG:  redo starts at 0/8758738
2025-07-28 11:14:04 CST  LOG:  invalid record length at 0/8758820: wanted 24, got 0
2025-07-28 11:14:04 CST  LOG:  redo done at 0/87587E8 system usage: CPU: user: 0.00 s, system: 0.00 s, elapsed: 0.00 s
2025-07-28 11:14:04 CST  LOG:  database system is ready to accept connections
2025-07-28 15:23:54 CST polarion ERROR:  column "c_id" does not exist at character 43
2025-07-28 15:23:54 CST polarion STATEMENT:  -- 查看所有可能的测试步骤字段名
	SELECT DISTINCT C_name ,c_id
	FROM CF_WorkItem 
	WHERE C_text_value IS NOT NULL
	  AND C_name::text LIKE '%"steps"%'
	  AND C_name::text LIKE '%"keys"%'
	ORDER BY C_name
2025-07-28 15:50:49 CST polarion ERROR:  syntax error at or near "cf" at character 125
2025-07-28 15:50:49 CST polarion STATEMENT:  -- 获取测试步骤数据
	SELECT 
	    wi.C_ID as test_case_id,
	    wi.C_TITLE as test_case_title,
			wi.c_description as c_description
	    cf.C_name as field_name,
	    cf.C_text_value as test_steps_json
	FROM WorkItem wi
	INNER JOIN CF_WorkItem cf ON wi.C_PK = cf.FK_WorkItem
	WHERE 
	  wi.c_id = 'WBS-9781'
