2025-07-28 11:57:48,615 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0371 s [64% update (144x), 35% query (12x)] (221x), svn: 0.0109 s [60% getLatestRevision (2x), 32% testConnection (1x)] (4x)
2025-07-28 11:57:48,731 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0452 s [61% getDir2 content (2x), 33% info (3x)] (6x)
2025-07-28 11:57:49,597 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.864 s, CPU [user: 0.23 s, system: 0.25 s], Allocated memory: 53.1 MB, transactions: 0, ObjectMaps: 0.113 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-28 11:57:49,597 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.864 s, CPU [user: 0.283 s, system: 0.317 s], Allocated memory: 70.3 MB, transactions: 0, ObjectMaps: 0.123 s [99% getAllPrimaryObjects (1x)] (14x)
2025-07-28 11:57:49,598 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.863 s, CPU [user: 0.0506 s, system: 0.0684 s], Allocated memory: 6.8 MB, transactions: 0, ObjectMaps: 0.0596 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-28 11:57:49,598 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.863 s, CPU [user: 0.0704 s, system: 0.0742 s], Allocated memory: 8.6 MB, transactions: 0, svn: 0.091 s [78% log2 (10x), 15% getLatestRevision (2x)] (13x), ObjectMaps: 0.0584 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-28 11:57:49,598 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.864 s, CPU [user: 0.107 s, system: 0.129 s], Allocated memory: 14.3 MB, transactions: 0, ObjectMaps: 0.12 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.073 s [78% log2 (10x), 15% getLatestRevision (2x)] (13x)
2025-07-28 11:57:49,598 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.864 s, CPU [user: 0.159 s, system: 0.189 s], Allocated memory: 26.4 MB, transactions: 0, ObjectMaps: 0.166 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0961 s [36% log2 (5x), 22% info (5x), 20% log (1x), 11% getLatestRevision (2x)] (18x)
2025-07-28 11:57:49,598 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.641 s [100% getAllPrimaryObjects (8x)] (63x), svn: 0.335 s [63% log2 (36x), 14% getLatestRevision (9x), 10% testConnection (6x)] (61x)
2025-07-28 11:57:49,807 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.176 s [100% getReadConfiguration (48x)] (48x), svn: 0.0694 s [81% info (18x)] (38x)
2025-07-28 11:57:50,091 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.213 s [76% info (94x), 17% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.163 s [100% getReadConfiguration (54x)] (54x)
2025-07-28 11:57:50,322 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.216 s [100% doFinishStartup (1x)] (1x), commit: 0.0622 s [100% Revision (1x)] (1x), Lucene: 0.0278 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.0161 s [100% objectsToInv (1x)] (1x), DB: 0.0108 s [40% query (1x), 39% update (3x), 13% execute (1x)] (8x)
2025-07-28 11:57:52,503 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.272 s [90% info (158x)] (168x), GlobalHandler: 0.0154 s [98% applyTxChanges (1x)] (7x)
2025-07-28 11:57:53,236 [DBHistoryCreator-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.68 s, CPU [user: 0.00327 s, system: 0.00125 s], Allocated memory: 230.7 kB, transactions: 1
2025-07-28 11:57:53,236 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.68 s, CPU [user: 0.00372 s, system: 0.0012 s], Allocated memory: 486.3 kB, transactions: 1
2025-07-28 11:57:53,237 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 26, Lucene: 0.0313 s [83% add (1x)] (3x), Incremental Baseline: 0.0162 s [100% WorkItem (19x)] (19x), persistence listener: 0.0155 s [75% indexRefreshPersistenceListener (1x), 15% WorkItemActivityCreator (1x)] (7x), notification worker: 0.0111 s [54% RevisionActivityCreator (2x), 25% WorkItemActivityCreator (1x), 11% TestRunActivityCreator (1x)] (6x), resolve: 0.0105 s [77% User (1x), 23% Revision (2x)] (3x), DB: 0.00327 s [70% update (2x), 30% commit (2x)] (4x), ObjectMaps: 0.00265 s [100% getPrimaryObjectLocation (1x)] (1x)
2025-07-28 11:57:53,238 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.737 s, CPU [user: 0.129 s, system: 0.0226 s], Allocated memory: 17.9 MB, transactions: 21, svn: 0.631 s [99% getDatedRevision (181x)] (183x)
2025-07-28 11:57:53,648 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6613cb80ff440_0_6613cb80ff440_0_: finished. Total: 1.14 s, CPU [user: 0.361 s, system: 0.0805 s], Allocated memory: 53.2 MB, svn: 0.676 s [57% getDatedRevision (181x), 25% getDir2 content (25x)] (307x), resolve: 0.385 s [100% Category (96x)] (96x), ObjectMaps: 0.119 s [45% getPrimaryObjectProperty (96x), 32% getPrimaryObjectLocation (96x), 23% getLastPromoted (96x)] (388x)
2025-07-28 11:57:54,017 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6613cb824fc4a_0_6613cb824fc4a_0_: finished. Total: 0.162 s, CPU [user: 0.0589 s, system: 0.00592 s], Allocated memory: 19.9 MB, svn: 0.124 s [72% getDir2 content (17x), 27% getFile content (44x)] (62x), RepositoryConfigService: 0.0556 s [98% getReadConfiguration (170x)] (192x)
2025-07-28 11:57:54,658 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6613cb827844b_0_6613cb827844b_0_: finished. Total: 0.641 s, CPU [user: 0.286 s, system: 0.0124 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.433 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.358 s [53% getFile content (412x), 47% getDir2 content (21x)] (434x)
2025-07-28 11:57:54,997 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6613cb832fc4e_0_6613cb832fc4e_0_: finished. Total: 0.246 s, CPU [user: 0.1 s, system: 0.00602 s], Allocated memory: 387.2 MB, RepositoryConfigService: 0.166 s [96% getReadConfiguration (2787x)] (3025x), svn: 0.153 s [57% getFile content (185x), 43% getDir2 content (20x)] (206x)
2025-07-28 11:57:54,997 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 2.49 s, CPU [user: 0.928 s, system: 0.123 s], Allocated memory: 1.6 GB, transactions: 10, svn: 1.47 s [39% getDir2 content (114x), 32% getFile content (809x), 26% getDatedRevision (181x)] (1144x), RepositoryConfigService: 0.746 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.453 s [85% Category (96x)] (117x), ObjectMaps: 0.147 s [47% getPrimaryObjectProperty (110x), 32% getPrimaryObjectLocation (116x), 21% getLastPromoted (110x)] (452x)
2025-07-28 11:57:54,997 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 51, svn: 2.11 s [48% getDatedRevision (362x), 27% getDir2 content (114x), 22% getFile content (809x)] (1329x), RepositoryConfigService: 0.746 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.453 s [85% Category (96x)] (118x), ObjectMaps: 0.147 s [47% getPrimaryObjectProperty (110x), 32% getPrimaryObjectLocation (116x), 21% getLastPromoted (110x)] (452x)
