2025-07-28 11:58:39,671 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.039 s [60% update (144x), 40% query (12x)] (221x), svn: 0.00985 s [52% getLatestRevision (2x), 35% testConnection (1x)] (4x)
2025-07-28 11:58:39,772 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.03 s [59% getDir2 content (2x), 32% info (3x)] (6x)
2025-07-28 11:58:40,470 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.695 s, CPU [user: 0.105 s, system: 0.22 s], Allocated memory: 23.7 MB, transactions: 0, ObjectMaps: 0.0657 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-28 11:58:40,470 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.695 s, CPU [user: 0.182 s, system: 0.285 s], Allocated memory: 52.8 MB, transactions: 0, ObjectMaps: 0.102 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-28 11:58:40,470 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.694 s, CPU [user: 0.0553 s, system: 0.102 s], Allocated memory: 8.5 MB, transactions: 0, svn: 0.0812 s [64% log2 (10x), 29% getLatestRevision (2x)] (13x), ObjectMaps: 0.0481 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-28 11:58:40,470 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.694 s, CPU [user: 0.0637 s, system: 0.0976 s], Allocated memory: 9.4 MB, transactions: 0, svn: 0.0896 s [36% log2 (5x), 27% log (1x), 18% info (5x)] (18x), ObjectMaps: 0.0418 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-28 11:58:40,470 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.694 s, CPU [user: 0.22 s, system: 0.343 s], Allocated memory: 70.2 MB, transactions: 0, ObjectMaps: 0.12 s [98% getAllPrimaryObjects (1x)] (14x)
2025-07-28 11:58:40,470 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.695 s, CPU [user: 0.0876 s, system: 0.164 s], Allocated memory: 14.6 MB, transactions: 0, ObjectMaps: 0.0673 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.0559 s [78% log2 (10x), 15% getLatestRevision (2x)] (13x)
2025-07-28 11:58:40,470 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.444 s [99% getAllPrimaryObjects (8x)] (63x), svn: 0.296 s [58% log2 (36x), 17% getLatestRevision (9x), 10% testConnection (6x)] (61x)
2025-07-28 11:58:40,664 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.167 s [100% getReadConfiguration (48x)] (48x), svn: 0.0597 s [85% info (18x)] (38x)
2025-07-28 11:58:40,917 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.193 s [77% info (94x), 16% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.148 s [100% getReadConfiguration (54x)] (54x)
2025-07-28 11:58:41,123 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.191 s [100% doFinishStartup (1x)] (1x), commit: 0.047 s [100% Revision (1x)] (1x), Lucene: 0.0287 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.0125 s [100% objectsToInv (1x)] (1x)
2025-07-28 12:00:09,341 [PullingJob-default] INFO  TXLOGGER - Summary for 'PullingJob': Total: 1.09 s, CPU [user: 0.00276 s, system: 0.00518 s], Allocated memory: 130.5 kB, transactions: 0, PullingJob: 0.587 s [100% collectChanges (1x)] (1x), svn: 0.586 s [100% getLatestRevision (1x)] (1x)
2025-07-28 13:05:01,489 [PullingJob-default] INFO  TXLOGGER - Summary for 'PullingJob': Total: 0.781 s, CPU [user: 0.00236 s, system: 0.0147 s], Allocated memory: 129.9 kB, transactions: 0, PullingJob: 0.776 s [100% collectChanges (1x)] (1x), svn: 0.775 s [100% getLatestRevision (1x)] (1x)
2025-07-28 13:05:14,266 [PullingJob-default] INFO  TXLOGGER - Summary for 'PullingJob': Total: 1.26 s, CPU [user: 0.00328 s, system: 0.0122 s], Allocated memory: 139.6 kB, transactions: 0, PullingJob: 0.422 s [100% collectChanges (1x)] (1x), svn: 0.185 s [100% getLatestRevision (1x)] (1x)
2025-07-28 13:05:14,956 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 3.06162109375
2025-07-28 13:05:24,950 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 2.87783203125
2025-07-28 13:05:34,952 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 2.52568359375
2025-07-28 13:05:44,947 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 2.152685546875
2025-07-28 13:05:54,947 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.8517578125
2025-07-28 13:06:04,950 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.619873046875
2025-07-28 13:06:14,949 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.40087890625
2025-07-28 13:06:24,947 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.264892578125
2025-07-28 13:06:34,944 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.1232421875
2025-07-28 13:06:44,947 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.004638671875
