2025-07-28 15:33:33,676 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0373 s [63% update (144x), 37% query (12x)] (221x), svn: 0.0164 s [55% getLatestRevision (2x), 32% testConnection (1x)] (4x)
2025-07-28 15:33:33,788 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0327 s [67% getDir2 content (2x), 26% info (3x)] (6x)
2025-07-28 15:33:34,623 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.83 s, CPU [user: 0.0678 s, system: 0.0615 s], Allocated memory: 8.5 MB, transactions: 0, svn: 0.0975 s [78% log2 (10x), 17% getLatestRevision (2x)] (13x), ObjectMaps: 0.0452 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-28 15:33:34,623 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.831 s, CPU [user: 0.113 s, system: 0.136 s], Allocated memory: 16.7 MB, transactions: 0, svn: 0.113 s [43% log2 (10x), 19% info (5x), 16% log (1x), 13% getLatestRevision (3x)] (24x), ObjectMaps: 0.0781 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-28 15:33:34,624 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.832 s, CPU [user: 0.244 s, system: 0.265 s], Allocated memory: 53.1 MB, transactions: 0, ObjectMaps: 0.178 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-28 15:33:34,624 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.831 s, CPU [user: 0.131 s, system: 0.189 s], Allocated memory: 23.7 MB, transactions: 0, ObjectMaps: 0.0981 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-28 15:33:34,626 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.831 s, CPU [user: 0.32 s, system: 0.338 s], Allocated memory: 70.2 MB, transactions: 0, ObjectMaps: 0.179 s [98% getAllPrimaryObjects (1x)] (14x)
2025-07-28 15:33:34,622 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.831 s, CPU [user: 0.0498 s, system: 0.0646 s], Allocated memory: 6.8 MB, transactions: 0
2025-07-28 15:33:34,630 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.617 s [99% getAllPrimaryObjects (8x)] (63x), svn: 0.326 s [60% log2 (36x), 16% getLatestRevision (9x), 10% testConnection (6x)] (61x)
2025-07-28 15:33:34,927 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.225 s [100% getReadConfiguration (48x)] (48x), svn: 0.0789 s [83% info (18x)] (38x)
2025-07-28 15:33:35,271 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.274 s [75% info (94x), 18% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.203 s [100% getReadConfiguration (54x)] (54x)
2025-07-28 15:33:35,543 [PolarionDocIdCreator-5] INFO  TXLOGGER - Tx fillBloomFilter [Attachment]: finished. Total: 0.147 s, CPU [user: 0.00314 s, system: 0.000936 s], Allocated memory: 241.5 kB, GC: 0.031 s [100% G1 Young Generation (1x)] (1x)
2025-07-28 15:33:35,546 [PolarionDocIdCreator-2] INFO  TXLOGGER - Tx fillBloomFilter [User]: finished. Total: 0.15 s, CPU [user: 0.0288 s, system: 0.00541 s], Allocated memory: 2.4 MB, GC: 0.031 s [100% G1 Young Generation (1x)] (1x)
2025-07-28 15:33:35,549 [PolarionDocIdCreator-4] INFO  TXLOGGER - Tx fillBloomFilter [WikiPage]: finished. Total: 0.154 s, CPU [user: 0.0367 s, system: 0.00625 s], Allocated memory: 3.0 MB, GC: 0.031 s [100% G1 Young Generation (1x)] (1x)
2025-07-28 15:33:35,556 [PolarionDocIdCreator-3] INFO  TXLOGGER - Tx fillBloomFilter [Category]: finished. Total: 0.161 s, CPU [user: 0.00513 s, system: 0.00249 s], Allocated memory: 824.5 kB, GC: 0.031 s [100% G1 Young Generation (1x)] (1x)
2025-07-28 15:33:35,604 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.208 s, CPU [user: 0.0462 s, system: 0.0129 s], Allocated memory: 11.1 MB, GC: 0.031 s [100% G1 Young Generation (1x)] (1x)
2025-07-28 15:33:35,629 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.34 s [100% doFinishStartup (1x)] (1x), commit: 0.0657 s [100% Revision (1x)] (1x), Lucene: 0.0614 s [100% refresh (1x)] (1x), DB: 0.0246 s [35% update (3x), 29% query (1x), 26% commit (2x)] (8x), SubterraURITable: 0.0198 s [100% addIfNotExistsDB (1x)] (1x)
2025-07-28 15:33:38,083 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 7, svn: 0.287 s [90% info (158x)] (168x)
2025-07-28 15:33:38,817 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.668 s, CPU [user: 0.00702 s, system: 0.00162 s], Allocated memory: 552.8 kB, transactions: 1
2025-07-28 15:33:38,818 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 20, Incremental Baseline: 0.0158 s [100% WorkItem (19x)] (19x), resolve: 0.0148 s [100% User (1x)] (1x), PullingJob: 0.00695 s [100% collectChanges (1x)] (1x), svn: 0.00662 s [56% testConnection (1x), 44% getLatestRevision (1x)] (2x), Lucene: 0.00466 s [100% refresh (1x)] (1x), ObjectMaps: 0.00384 s [100% getPrimaryObjectLocation (1x)] (1x), DB: 0.000883 s [62% update (1x), 38% commit (1x)] (2x)
2025-07-28 15:33:38,818 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.749 s, CPU [user: 0.139 s, system: 0.0237 s], Allocated memory: 17.9 MB, transactions: 21, svn: 0.601 s [99% getDatedRevision (181x)] (183x)
2025-07-28 15:33:39,152 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6613fce32a445_0_6613fce32a445_0_: finished. Total: 1.06 s, CPU [user: 0.342 s, system: 0.0742 s], Allocated memory: 53.3 MB, svn: 0.609 s [51% getDatedRevision (181x), 30% getDir2 content (25x)] (307x), resolve: 0.351 s [100% Category (96x)] (96x), ObjectMaps: 0.118 s [46% getPrimaryObjectProperty (96x), 31% getPrimaryObjectLocation (96x), 23% getLastPromoted (96x)] (388x)
2025-07-28 15:33:39,345 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6613fce44ac48_0_6613fce44ac48_0_: finished. Total: 0.102 s, CPU [user: 0.0504 s, system: 0.00828 s], Allocated memory: 8.5 MB, RepositoryConfigService: 0.0439 s [58% getReadUserConfiguration (10x), 42% getReadConfiguration (162x)] (172x), svn: 0.0424 s [56% info (19x), 37% getFile content (16x)] (37x), resolve: 0.0333 s [100% User (9x)] (9x), ObjectMaps: 0.0171 s [47% getPrimaryObjectProperty (9x), 34% getPrimaryObjectLocation (9x)] (37x)
2025-07-28 15:33:39,559 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6613fce46d04a_0_6613fce46d04a_0_: finished. Total: 0.179 s, CPU [user: 0.073 s, system: 0.00624 s], Allocated memory: 19.9 MB, svn: 0.121 s [58% getDir2 content (17x), 42% getFile content (44x)] (62x), RepositoryConfigService: 0.0823 s [98% getReadConfiguration (170x)] (192x)
2025-07-28 15:33:40,208 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6613fce499c4b_0_6613fce499c4b_0_: finished. Total: 0.649 s, CPU [user: 0.304 s, system: 0.0258 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.479 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.33 s [62% getFile content (412x), 38% getDir2 content (21x)] (434x), GC: 0.033 s [100% G1 Young Generation (4x)] (4x)
2025-07-28 15:33:40,564 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6613fce55404e_0_6613fce55404e_0_: finished. Total: 0.26 s, CPU [user: 0.109 s, system: 0.00693 s], Allocated memory: 384.7 MB, RepositoryConfigService: 0.179 s [96% getReadConfiguration (2787x)] (3025x), svn: 0.163 s [59% getFile content (185x), 41% getDir2 content (20x)] (206x)
2025-07-28 15:33:40,564 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 2.48 s, CPU [user: 0.957 s, system: 0.132 s], Allocated memory: 1.6 GB, transactions: 10, svn: 1.4 s [37% getDir2 content (114x), 36% getFile content (809x), 22% getDatedRevision (181x)] (1144x), RepositoryConfigService: 0.842 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.435 s [81% Category (96x)] (117x), ObjectMaps: 0.156 s [48% getPrimaryObjectProperty (110x), 31% getPrimaryObjectLocation (116x), 22% getLastPromoted (110x)] (452x)
2025-07-28 15:33:40,564 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 51, svn: 2 s [45% getDatedRevision (362x), 26% getDir2 content (114x), 25% getFile content (809x)] (1327x), RepositoryConfigService: 0.842 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.435 s [81% Category (96x)] (118x), ObjectMaps: 0.156 s [48% getPrimaryObjectProperty (110x), 31% getPrimaryObjectLocation (116x), 22% getLastPromoted (110x)] (452x)
