2025-07-29 18:34:08,306 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 18:34:08,306 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-29 18:34:08,306 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 18:34:08,306 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-29 18:34:08,306 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-29 18:34:08,306 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:34:08,306 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-29 18:34:12,708 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-29 18:34:12,849 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.14 s. ]
2025-07-29 18:34:12,849 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-29 18:34:12,888 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0394 s. ]
2025-07-29 18:34:12,939 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-29 18:34:13,041 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 121 s. ]
2025-07-29 18:34:13,350 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.05 s. ]
2025-07-29 18:34:13,481 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:34:13,481 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-29 18:34:13,543 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.19 s. ]
2025-07-29 18:34:13,543 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:34:13,543 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-29 18:34:13,549 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (1/9)
2025-07-29 18:34:13,549 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (4/9)
2025-07-29 18:34:13,549 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (3/9)
2025-07-29 18:34:13,549 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (2/9)
2025-07-29 18:34:13,549 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (6/9)
2025-07-29 18:34:13,549 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (5/9)
2025-07-29 18:34:13,556 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-07-29 18:34:13,690 [LowLevelDataService-contextInitializer-4 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-29 18:34:13,803 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-29 18:34:14,545 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 1.0 s. ]
2025-07-29 18:34:14,560 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:34:14,560 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-29 18:34:14,794 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-29 18:34:14,808 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.26 s. ]
2025-07-29 18:34:14,831 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:34:14,831 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-29 18:34:14,834 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-29 18:34:14,883 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-29 18:34:14,933 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-07-29 18:34:14,962 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (4/9)
2025-07-29 18:34:14,985 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (5/9)
2025-07-29 18:34:15,021 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (6/9)
2025-07-29 18:34:15,057 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-07-29 18:34:15,090 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-29 18:34:15,125 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-29 18:34:15,125 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.32 s. ]
2025-07-29 18:34:15,125 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:34:15,125 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-29 18:34:15,151 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.03 s. ]
2025-07-29 18:34:15,151 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:34:15,151 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-29 18:34:15,272 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-29 18:34:15,275 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-29 18:34:15,398 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.25 s. ]
2025-07-29 18:34:15,398 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:34:15,398 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-29 18:34:15,404 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-29 18:34:15,404 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:34:15,404 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-29 18:34:18,179 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.77 s. ]
2025-07-29 18:34:18,180 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 18:34:18,180 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 9.86 s. ]
2025-07-29 18:34:18,180 [main] INFO  com.polarion.platform.startup - ****************************************************************
