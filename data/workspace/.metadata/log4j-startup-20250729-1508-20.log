2025-07-29 15:08:20,693 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 15:08:20,693 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-29 15:08:20,693 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 15:08:20,693 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-29 15:08:20,693 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-29 15:08:20,693 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 15:08:20,693 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-29 15:08:25,895 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-29 15:08:26,082 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.187 s. ]
2025-07-29 15:08:26,082 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-29 15:08:26,140 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0579 s. ]
2025-07-29 15:08:26,203 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-29 15:08:26,323 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 35 s. ]
2025-07-29 15:08:26,546 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.86 s. ]
2025-07-29 15:08:26,674 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 15:08:26,674 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-29 15:08:26,702 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.16 s. ]
2025-07-29 15:08:26,702 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 15:08:26,702 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-29 15:08:26,708 [LowLevelDataService-contextInitializer-5 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (2/9)
2025-07-29 15:08:26,708 [LowLevelDataService-contextInitializer-6 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (1/9)
2025-07-29 15:08:26,708 [LowLevelDataService-contextInitializer-4 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (4/9)
2025-07-29 15:08:26,708 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (3/9)
2025-07-29 15:08:26,708 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (5/9)
2025-07-29 15:08:26,708 [LowLevelDataService-contextInitializer-3 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (6/9)
2025-07-29 15:08:26,715 [LowLevelDataService-contextInitializer-1 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (7/9)
2025-07-29 15:08:26,850 [LowLevelDataService-contextInitializer-1 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-07-29 15:08:26,961 [LowLevelDataService-contextInitializer-3 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-07-29 15:08:27,452 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.75 s. ]
2025-07-29 15:08:27,463 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 15:08:27,463 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-29 15:08:27,713 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-29 15:08:27,729 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.28 s. ]
2025-07-29 15:08:27,760 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 15:08:27,760 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-29 15:08:27,765 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-29 15:08:27,836 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-29 15:08:27,873 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (3/9)
2025-07-29 15:08:27,920 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (4/9)
2025-07-29 15:08:27,965 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (5/9)
2025-07-29 15:08:28,013 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (6/9)
2025-07-29 15:08:28,036 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (7/9)
2025-07-29 15:08:28,100 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-07-29 15:08:28,151 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-07-29 15:08:28,151 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.42 s. ]
2025-07-29 15:08:28,151 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 15:08:28,151 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-29 15:08:28,166 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-07-29 15:08:28,166 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 15:08:28,167 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-29 15:08:28,286 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-29 15:08:28,292 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-29 15:08:28,405 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.24 s. ]
2025-07-29 15:08:28,406 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 15:08:28,406 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-29 15:08:28,417 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-29 15:08:28,417 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 15:08:28,417 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-29 15:08:31,502 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 3.09 s. ]
2025-07-29 15:08:31,503 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 15:08:31,503 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 10.8 s. ]
2025-07-29 15:08:31,503 [main] INFO  com.polarion.platform.startup - ****************************************************************
