2025-07-29 18:08:02,348 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Using logging context STANDALONE
2025-07-29 18:08:02,349 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Launchers manager started...
2025-07-29 18:08:02,349 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Using home directory /opt/polarion/polarion
2025-07-29 18:08:02,349 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Using root directory /opt/polarion
2025-07-29 18:08:02,349 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Using workspace directory /opt/polarion/data/workspace
2025-07-29 18:08:02,349 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Using config directory /Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion
2025-07-29 18:08:02,349 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Loading external properties ...
2025-07-29 18:08:02,349 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Using external property file /opt/polarion/etc/polarion.properties
2025-07-29 18:08:02,350 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Loading internal properties ...
2025-07-29 18:08:02,352 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Host: zhangwentiandeMac-mini-2.local (*************)
2025-07-29 18:08:02,354 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Product: com.polarion.alm
2025-07-29 18:08:02,354 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Version: 3.22.1
2025-07-29 18:08:02,354 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Build: 20220419-1528-22_R1-be3adceb
2025-07-29 18:08:02,355 [main] WARN  com.polarion.core.boot.impl.AppLaunchersManager - missing subfolder 'eclipse' under extension directory: /opt/polarion/polarion/extensions/fasnote
2025-07-29 18:08:02,355 [main] WARN  com.polarion.core.boot.impl.AppLaunchersManager - missing subfolder 'eclipse' under extension directory: /opt/polarion/polarion/extensions/2404
2025-07-29 18:08:02,355 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Extensions: [exts]
2025-07-29 18:08:02,359 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Workspace location: /opt/polarion/data/workspace
2025-07-29 18:08:02,359 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Workspace lock acquired
2025-07-29 18:08:02,359 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Found applications: [polarion.server, polarion.coordinator, polarion.rt]
2025-07-29 18:08:02,359 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Starting application: polarion.server
2025-07-29 18:08:02,364 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Application extension successfully read
2025-07-29 18:08:02,370 [main] INFO  com.polarion.platform.internal.SystemStatistics - Initializing monitoring, isThreadCpuTimeSupported: true, isThreadContentionMonitoringSupported: true, isThreadAllocatedMemorySupported: true
2025-07-29 18:08:02,370 [main] INFO  com.polarion.platform.internal.SystemStatistics - State before enabling: isThreadCpuTimeEnabled: true, isThreadContentionMonitoringEnabled: false, isThreadAllocatedMemoryEnabled: true
2025-07-29 18:08:02,370 [main] INFO  com.polarion.platform.internal.SystemStatistics - State after enabling: isThreadCpuTimeEnabled: true, isThreadContentionMonitoringEnabled: false, isThreadAllocatedMemoryEnabled: true
2025-07-29 18:08:02,373 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 18:08:02,373 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-29 18:08:02,373 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 18:08:02,373 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-29 18:08:02,373 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-29 18:08:02,373 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:08:02,374 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-29 18:08:02,374 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - **** Java system properties listing: 
2025-07-29 18:08:02,394 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - adminPasswd = admin
2025-07-29 18:08:02,395 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - adminUser = admin
2025-07-29 18:08:02,395 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - announcer.smtp.auth = false
2025-07-29 18:08:02,395 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - announcer.smtp.host = 
2025-07-29 18:08:02,395 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - announcer.smtp.password = **PASSWORD**HIDDEN**
2025-07-29 18:08:02,395 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - announcer.smtp.port = 25
2025-07-29 18:08:02,395 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - announcer.smtp.user = 
2025-07-29 18:08:02,395 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - awt.toolkit = sun.lwawt.macosx.LWCToolkit
2025-07-29 18:08:02,395 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - base.url = http://localhost
2025-07-29 18:08:02,395 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - bfh.jobs.workdir = /opt/polarion/data/workspace/polarion-data/jobs
2025-07-29 18:08:02,395 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - BIRDir = /opt/polarion/data/BIR
2025-07-29 18:08:02,395 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - calculated.fields.mode = async
2025-07-29 18:08:02,395 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.activation.activationHelpLink = https://polarion.plm.automation.siemens.com/getlicense
2025-07-29 18:08:02,395 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.activation.server = https://license.polarion.com/licenseGenerator/generator/generate
2025-07-29 18:08:02,395 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.alm.ui.gravatar.enabled = false
2025-07-29 18:08:02,395 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.alm.ui.gravatar.url = http://www.gravatar.com/avatar/$emailHash$?d=identicon&s=50
2025-07-29 18:08:02,395 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.application = polarion.server
2025-07-29 18:08:02,395 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.config = /Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion
2025-07-29 18:08:02,395 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.data = /opt/polarion/data
2025-07-29 18:08:02,395 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.eclipse = /opt/polarion/polarion
2025-07-29 18:08:02,395 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.home = /opt/polarion/polarion
2025-07-29 18:08:02,395 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.licenseDir = /opt/polarion/polarion/license
2025-07-29 18:08:02,395 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.platform.internalPG = polarion:polarion@localhost:5434
2025-07-29 18:08:02,395 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.platform.monitoring.notifications.disabled = true
2025-07-29 18:08:02,395 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.platform.monitoring.notifications.receivers = 
2025-07-29 18:08:02,395 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.platform.monitoring.notifications.sender = 
2025-07-29 18:08:02,395 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.platform.monitoring.notifications.subject.prefix = 
2025-07-29 18:08:02,395 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.platform.persistence.notifications.disabled = true
2025-07-29 18:08:02,395 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.propertyFile = /opt/polarion/etc/polarion.properties
2025-07-29 18:08:02,395 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.root = /opt/polarion
2025-07-29 18:08:02,395 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.workspace = /opt/polarion/data/workspace
2025-07-29 18:08:02,395 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.collaborationNotifications.enabled = true
2025-07-29 18:08:02,395 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.document.listStyle = 1ai
2025-07-29 18:08:02,395 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.loggingContext = STANDALONE
2025-07-29 18:08:02,395 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.preview.thumbnailsDataDir = /opt/polarion/data/workspace/previews-data/thumbnails
2025-07-29 18:08:02,395 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.rest.cors.allowedOrigins = *
2025-07-29 18:08:02,395 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.rest.enabled = true
2025-07-29 18:08:02,395 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.rest.swaggerUi.enabled = true
2025-07-29 18:08:02,395 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.xcelerator.accEndpointUrl = https://acc.collab.sws.siemens.com
2025-07-29 18:08:02,395 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.xcelerator.baseDomain = sws.siemens.com
2025-07-29 18:08:02,395 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.xcelerator.shareEndpointUrl = https://share.sws.siemens.com
2025-07-29 18:08:02,395 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - content.types.user.table = /opt/polarion/polarion/plugins/com.polarion.core.boot_3.22.1/content-types.properties
2025-07-29 18:08:02,395 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - controlHostname = localhost
2025-07-29 18:08:02,395 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - controlPort = 8887
2025-07-29 18:08:02,396 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - createproject.default.location = Sandbox/
2025-07-29 18:08:02,396 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - createproject.default.useUserId = true
2025-07-29 18:08:02,396 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - createproject.limitedAccessMessage = You may create a project in the Sandbox project group (only). Please fill in the required properties below. For example:<br/><table><tr><td>Location:</td><td>Sandbox/MyFirstProject</td></tr><tr><td>ID:</td><td>MyFirstProject</td></tr></table><br/>Or use the suggested defaults.
2025-07-29 18:08:02,396 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - debug = false
2025-07-29 18:08:02,396 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - debug.license.validation = true
2025-07-29 18:08:02,396 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - debug.machine.code.generation = true
2025-07-29 18:08:02,396 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - debug.security.validation = true
2025-07-29 18:08:02,396 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - default.project.template.ALM = alm_vmodel
2025-07-29 18:08:02,396 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - default.project.template.Pro = alm_vmodel
2025-07-29 18:08:02,396 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - default.project.template.QA = qa_vmodel
2025-07-29 18:08:02,396 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - default.project.template.Requirements = req_vmodel
2025-07-29 18:08:02,396 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - default.project.template.XBase = alm_vmodel
2025-07-29 18:08:02,396 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - default.project.template.XEnterprise = alm_vmodel
2025-07-29 18:08:02,396 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - default.project.template.XPro = alm_vmodel
2025-07-29 18:08:02,396 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - derby.system.home = /opt/polarion/data/logs/derby
2025-07-29 18:08:02,396 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - eclipse.application = com.polarion.core.boot.app
2025-07-29 18:08:02,396 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - eclipse.commands = -application
com.polarion.core.boot.app
-data
/opt/polarion/data/workspace
-configuration
file:///Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/
-dev
file:///Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/dev.properties
-os
linux
-ws
linux
-arch
arm64
-appId
polarion.server

2025-07-29 18:08:02,396 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - eclipse.home.location = file:/opt/polarion/polarion/plugins/
2025-07-29 18:08:02,396 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - eclipse.p2.data.area = @config.dir/.p2
2025-07-29 18:08:02,396 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - eclipse.pde.launch = true
2025-07-29 18:08:02,396 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - eclipse.startTime = *************
2025-07-29 18:08:02,396 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - eclipse.stateSaveDelayInterval = 30000
2025-07-29 18:08:02,396 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - enableCreateAccountForm = false
2025-07-29 18:08:02,396 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - equinox.init.uuid = true
2025-07-29 18:08:02,396 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - error.report.email = 
2025-07-29 18:08:02,396 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - file.encoding = UTF-8
2025-07-29 18:08:02,396 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - file.separator = /
2025-07-29 18:08:02,396 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - ftp.nonProxyHosts = 127.0.0.1|***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|**********/16|*.**********/16|localhost|*.localhost|local|*.local|crashlytics.com|*.crashlytics.com|<local>|*.<local>
2025-07-29 18:08:02,396 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - gopherProxySet = false
2025-07-29 18:08:02,396 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - gosh.args = --nointeractive
2025-07-29 18:08:02,396 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - htpasswd.path = htpasswd
2025-07-29 18:08:02,396 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - http.nonProxyHosts = 127.0.0.1|***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|**********/16|*.**********/16|localhost|*.localhost|local|*.local|crashlytics.com|*.crashlytics.com|<local>|*.<local>
2025-07-29 18:08:02,396 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - index.activities = /opt/polarion/data/workspace/polarion-data/index
2025-07-29 18:08:02,396 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.awt.graphicsenv = sun.awt.CGraphicsEnvironment
2025-07-29 18:08:02,396 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.awt.headless = true
2025-07-29 18:08:02,396 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.awt.printerjob = sun.lwawt.macosx.CPrinterJob
2025-07-29 18:08:02,396 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.class.path = /opt/polarion/polarion/plugins/org.eclipse.equinox.launcher_1.5.0.v20180512-1130.jar:
2025-07-29 18:08:02,396 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.class.version = 55.0
2025-07-29 18:08:02,396 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.home = /Users/<USER>/Library/Java/JavaVirtualMachines/ms-11.0.27/Contents/Home
2025-07-29 18:08:02,396 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.io.tmpdir = /var/folders/z_/shw6wc7d7ps_fjvv781t4gt80000gn/T/
2025-07-29 18:08:02,396 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.library.path = /Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:.
2025-07-29 18:08:02,396 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.runtime.name = OpenJDK Runtime Environment
2025-07-29 18:08:02,396 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.runtime.version = 11.0.27+6-LTS
2025-07-29 18:08:02,396 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.security.policy = /opt/polarion/polarion/policy
2025-07-29 18:08:02,396 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.specification.maintenance.version = 3
2025-07-29 18:08:02,396 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.specification.name = Java Platform API Specification
2025-07-29 18:08:02,396 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.specification.vendor = Oracle Corporation
2025-07-29 18:08:02,396 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.specification.version = 11
2025-07-29 18:08:02,396 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vendor = Microsoft
2025-07-29 18:08:02,396 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vendor.url = https://www.microsoft.com
2025-07-29 18:08:02,396 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vendor.url.bug = https://github.com/microsoft/openjdk/issues
2025-07-29 18:08:02,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vendor.version = Microsoft-11367290
2025-07-29 18:08:02,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.version = 11.0.27
2025-07-29 18:08:02,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.version.date = 2025-04-15
2025-07-29 18:08:02,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.compressedOopsMode = Zero based
2025-07-29 18:08:02,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.info = mixed mode
2025-07-29 18:08:02,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.name = OpenJDK 64-Bit Server VM
2025-07-29 18:08:02,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.specification.name = Java Virtual Machine Specification
2025-07-29 18:08:02,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.specification.vendor = Oracle Corporation
2025-07-29 18:08:02,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.specification.version = 11
2025-07-29 18:08:02,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.vendor = Microsoft
2025-07-29 18:08:02,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.version = 11.0.27+6-LTS
2025-07-29 18:08:02,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - javasvn.timeout = 10000
2025-07-29 18:08:02,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - jdk.debug = release
2025-07-29 18:08:02,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - ldap.bind.password = **PASSWORD**HIDDEN**
2025-07-29 18:08:02,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.audit.enabled = true
2025-07-29 18:08:02,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.auto.scan.enabled = true
2025-07-29 18:08:02,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.cache.size = 100
2025-07-29 18:08:02,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.cache.ttl = 1800
2025-07-29 18:08:02,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.check.interval = 0
2025-07-29 18:08:02,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.allow.expired = true
2025-07-29 18:08:02,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.allow.local.files = true
2025-07-29 18:08:02,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.default.features = all
2025-07-29 18:08:02,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.default.max.users = 10
2025-07-29 18:08:02,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.default.plugin.id = com.fasnote.alm.plugin.manage
2025-07-29 18:08:02,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.mode = true
2025-07-29 18:08:02,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.show.machine.code = true
2025-07-29 18:08:02,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.skip.machine.binding = true
2025-07-29 18:08:02,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.skip.network.validation = true
2025-07-29 18:08:02,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.directory = dev-licenses
2025-07-29 18:08:02,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.encryption.enabled = false
2025-07-29 18:08:02,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.hot.reload.enabled = true
2025-07-29 18:08:02,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.log.level = DEBUG
2025-07-29 18:08:02,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.machine.binding.enabled = false
2025-07-29 18:08:02,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.max.plugins = 1000
2025-07-29 18:08:02,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.scan.interval = 60
2025-07-29 18:08:02,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.signature.validation.enabled = false
2025-07-29 18:08:02,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.validation.timeout = 1000
2025-07-29 18:08:02,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - licenseForNewUserAccount = 
2025-07-29 18:08:02,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - line.separator = 

2025-07-29 18:08:02,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - log4j2.contextSelector = org.apache.logging.log4j.core.selector.BasicContextSelector
2025-07-29 18:08:02,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - log4j2.loggerContextFactory = org.apache.logging.log4j.core.impl.Log4jContextFactory
2025-07-29 18:08:02,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - logDir = /opt/polarion/data/workspace/.metadata/
2025-07-29 18:08:02,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - login = polarion
2025-07-29 18:08:02,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - mavenConfigDir = /opt/polarion/polarion/../maven
2025-07-29 18:08:02,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - minimalPasswordLength = **PASSWORD**HIDDEN**
2025-07-29 18:08:02,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.eclipse.equinox.simpleconfigurator.configUrl = file:/Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/org.eclipse.equinox.simpleconfigurator/bundles.info
2025-07-29 18:08:02,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.eclipse.lyo.oslc4j.strictDatatypes = false
2025-07-29 18:08:02,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.executionenvironment = OSGi/Minimum-1.0, OSGi/Minimum-1.1, OSGi/Minimum-1.2, JavaSE/compact1-1.8, JavaSE/compact2-1.8, JavaSE/compact3-1.8, JRE-1.1, J2SE-1.2, J2SE-1.3, J2SE-1.4, J2SE-1.5, JavaSE-1.6, JavaSE-1.7, JavaSE-1.8, JavaSE-9, JavaSE-10, JavaSE-11
2025-07-29 18:08:02,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.language = zh
2025-07-29 18:08:02,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.os.name = MacOSX
2025-07-29 18:08:02,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.os.version = 15.5.0
2025-07-29 18:08:02,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.processor = aarch64
2025-07-29 18:08:02,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.storage = /Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion
2025-07-29 18:08:02,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.system.capabilities = osgi.ee; osgi.ee="OSGi/Minimum"; version:List<Version>="1.0, 1.1, 1.2", osgi.ee; osgi.ee="JRE"; version:List<Version>="1.0, 1.1", osgi.ee; osgi.ee="JavaSE"; version:List<Version>="1.0, 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 1.7, 1.8, 9.0, 10.0, 11.0",osgi.ee; osgi.ee="JavaSE/compact1"; version:List<Version>="1.8, 9.0, 10.0, 11.0",osgi.ee; osgi.ee="JavaSE/compact2"; version:List<Version>="1.8, 9.0, 10.0, 11.0",osgi.ee; osgi.ee="JavaSE/compact3"; version:List<Version>="1.8, 9.0, 10.0, 11.0"
2025-07-29 18:08:02,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.system.packages = com.sun.jarsigner, com.sun.java.accessibility.util, com.sun.javadoc, com.sun.jdi, com.sun.jdi.connect, com.sun.jdi.connect.spi, com.sun.jdi.event, com.sun.jdi.request, com.sun.jndi.ldap.spi, com.sun.management, com.sun.net.httpserver, com.sun.net.httpserver.spi, com.sun.nio.file, com.sun.nio.sctp, com.sun.security.auth, com.sun.security.auth.callback, com.sun.security.auth.login, com.sun.security.auth.module, com.sun.security.jgss, com.sun.source.doctree, com.sun.source.tree, com.sun.source.util, com.sun.tools.attach, com.sun.tools.attach.spi, com.sun.tools.javac, com.sun.tools.javadoc, com.sun.tools.jconsole, java.applet, java.awt, java.awt.color, java.awt.datatransfer, java.awt.desktop, java.awt.dnd, java.awt.event, java.awt.font, java.awt.geom, java.awt.im, java.awt.im.spi, java.awt.image, java.awt.image.renderable, java.awt.print, java.beans, java.beans.beancontext, java.io, java.lang, java.lang.annotation, java.lang.instrument, java.lang.invoke, java.lang.management, java.lang.module, java.lang.ref, java.lang.reflect, java.math, java.net, java.net.http, java.net.spi, java.nio, java.nio.channels, java.nio.channels.spi, java.nio.charset, java.nio.charset.spi, java.nio.file, java.nio.file.attribute, java.nio.file.spi, java.rmi, java.rmi.activation, java.rmi.dgc, java.rmi.registry, java.rmi.server, java.security, java.security.acl, java.security.cert, java.security.interfaces, java.security.spec, java.sql, java.text, java.text.spi, java.time, java.time.chrono, java.time.format, java.time.temporal, java.time.zone, java.util, java.util.concurrent, java.util.concurrent.atomic, java.util.concurrent.locks, java.util.function, java.util.jar, java.util.logging, java.util.prefs, java.util.regex, java.util.spi, java.util.stream, java.util.zip, javax.accessibility, javax.annotation.processing, javax.crypto, javax.crypto.interfaces, javax.crypto.spec, javax.imageio, javax.imageio.event, javax.imageio.metadata, javax.imageio.plugins.bmp, javax.imageio.plugins.jpeg, javax.imageio.plugins.tiff, javax.imageio.spi, javax.imageio.stream, javax.lang.model, javax.lang.model.element, javax.lang.model.type, javax.lang.model.util, javax.management, javax.management.loading, javax.management.modelmbean, javax.management.monitor, javax.management.openmbean, javax.management.relation, javax.management.remote, javax.management.remote.rmi, javax.management.timer, javax.naming, javax.naming.directory, javax.naming.event, javax.naming.ldap, javax.naming.spi, javax.net, javax.net.ssl, javax.print, javax.print.attribute, javax.print.attribute.standard, javax.print.event, javax.rmi.ssl, javax.script, javax.security.auth, javax.security.auth.callback, javax.security.auth.kerberos, javax.security.auth.login, javax.security.auth.spi, javax.security.auth.x500, javax.security.cert, javax.security.sasl, javax.smartcardio, javax.sound.midi, javax.sound.midi.spi, javax.sound.sampled, javax.sound.sampled.spi, javax.sql, javax.sql.rowset, javax.sql.rowset.serial, javax.sql.rowset.spi, javax.swing, javax.swing.border, javax.swing.colorchooser, javax.swing.event, javax.swing.filechooser, javax.swing.plaf, javax.swing.plaf.basic, javax.swing.plaf.metal, javax.swing.plaf.multi, javax.swing.plaf.nimbus, javax.swing.plaf.synth, javax.swing.table, javax.swing.text, javax.swing.text.html, javax.swing.text.html.parser, javax.swing.text.rtf, javax.swing.tree, javax.swing.undo, javax.tools, javax.transaction.xa, javax.xml, javax.xml.catalog, javax.xml.crypto, javax.xml.crypto.dom, javax.xml.crypto.dsig, javax.xml.crypto.dsig.dom, javax.xml.crypto.dsig.keyinfo, javax.xml.crypto.dsig.spec, javax.xml.datatype, javax.xml.namespace, javax.xml.parsers, javax.xml.stream, javax.xml.stream.events, javax.xml.stream.util, javax.xml.transform, javax.xml.transform.dom, javax.xml.transform.sax, javax.xml.transform.stax, javax.xml.transform.stream, javax.xml.validation, javax.xml.xpath, jdk.dynalink, jdk.dynalink.beans, jdk.dynalink.linker, jdk.dynalink.linker.support, jdk.dynalink.support, jdk.javadoc.doclet, jdk.jfr, jdk.jfr.consumer, jdk.jshell, jdk.jshell.execution, jdk.jshell.spi, jdk.jshell.tool, jdk.management.jfr, jdk.nashorn.api.scripting, jdk.nashorn.api.tree, jdk.net, jdk.nio, jdk.security.jarsigner, jdk.swing.interop, netscape.javascript, org.ietf.jgss, org.w3c.dom, org.w3c.dom.bootstrap, org.w3c.dom.css, org.w3c.dom.events, org.w3c.dom.html, org.w3c.dom.ls, org.w3c.dom.ranges, org.w3c.dom.stylesheets, org.w3c.dom.traversal, org.w3c.dom.views, org.w3c.dom.xpath, org.xml.sax, org.xml.sax.ext, org.xml.sax.helpers, sun.misc, sun.reflect
2025-07-29 18:08:02,398 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.uuid = cd026560-86ab-4902-88d3-1a3844a30557
2025-07-29 18:08:02,398 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.vendor = Eclipse
2025-07-29 18:08:02,398 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.version = 1.9.0
2025-07-29 18:08:02,398 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.supports.framework.extension = true
2025-07-29 18:08:02,398 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.supports.framework.fragment = true
2025-07-29 18:08:02,398 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.supports.framework.requirebundle = true
2025-07-29 18:08:02,398 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.xsocket.connection.client.readbuffer.usedirect = true
2025-07-29 18:08:02,398 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.xsocket.connection.server.readbuffer.usedirect = true
2025-07-29 18:08:02,398 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - os.arch = aarch64
2025-07-29 18:08:02,398 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - os.name = Mac OS X
2025-07-29 18:08:02,398 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - os.version = 15.5
2025-07-29 18:08:02,398 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.arch = arm64
2025-07-29 18:08:02,398 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.bundles = reference:file:/opt/polarion/polarion/plugins/org.eclipse.equinox.simpleconfigurator_1.3.0.v20180502-1828.jar@1:start
2025-07-29 18:08:02,398 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.bundles.defaultStartLevel = 4
2025-07-29 18:08:02,398 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.checkConfiguration = true
2025-07-29 18:08:02,398 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.compatibility.bootdelegation = true
2025-07-29 18:08:02,398 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.compatibility.bootdelegation.default = true
2025-07-29 18:08:02,398 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.configuration.area = file:/Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/
2025-07-29 18:08:02,398 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.configuration.cascaded = false
2025-07-29 18:08:02,398 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.dev = file:///Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/dev.properties
2025-07-29 18:08:02,398 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.framework = file:/opt/polarion/polarion/plugins/org.eclipse.osgi_3.13.0.v20180409-1500.jar
2025-07-29 18:08:02,398 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.framework.shape = jar
2025-07-29 18:08:02,398 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.framework.useSystemProperties = true
2025-07-29 18:08:02,398 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.install.area = file:/opt/polarion/polarion/plugins/
2025-07-29 18:08:02,398 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.instance.area = file:/opt/polarion/data/workspace/
2025-07-29 18:08:02,398 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.logfile = /opt/polarion/data/workspace/.metadata/.log
2025-07-29 18:08:02,398 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.nl = zh_CN_#Hans
2025-07-29 18:08:02,398 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.os = linux
2025-07-29 18:08:02,398 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.syspath = /opt/polarion/polarion/plugins
2025-07-29 18:08:02,398 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.tracefile = /opt/polarion/data/workspace/.metadata/trace.log
2025-07-29 18:08:02,398 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.ws = linux
2025-07-29 18:08:02,398 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - password = **PASSWORD**HIDDEN**
2025-07-29 18:08:02,398 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - path.separator = :
2025-07-29 18:08:02,398 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - pdfbox.fontcache = /opt/polarion/data/workspace/polarion-data
2025-07-29 18:08:02,398 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - pdfexport.config = /opt/polarion/polarion/configuration/pdfexport.xml
2025-07-29 18:08:02,398 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - polarion.build.default.deploy.repository.id = polarion-shared
2025-07-29 18:08:02,398 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - polarion.build.default.deploy.repository.url = file:///opt/polarion/data/shared-maven-repo
2025-07-29 18:08:02,398 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - polarion.build.maven.location.maven2 = /opt/polarion/polarion/../maven/distribution
2025-07-29 18:08:02,398 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - polarion.global.doc.cache.size = 100
2025-07-29 18:08:02,398 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - polarion.global.doc.cache.with.history = false
2025-07-29 18:08:02,398 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - polarion.tx.doc.cache.size = 100
2025-07-29 18:08:02,398 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - productLink.com.polarion.alm = https://polarion.plm.automation.siemens.com/products/polarion-alm
2025-07-29 18:08:02,398 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - productLink.com.polarion.qa = https://polarion.plm.automation.siemens.com/products/polarion-qa
2025-07-29 18:08:02,398 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - productLink.com.polarion.requirements = https://polarion.plm.automation.siemens.com/products/polarion-requirements
2025-07-29 18:08:02,398 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - repo = http://localhost/repo
2025-07-29 18:08:02,398 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - rolesForNewUserAccount = user
2025-07-29 18:08:02,398 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - RRDir = /opt/polarion/data/RR
2025-07-29 18:08:02,398 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - SDKDir = /opt/polarion/polarion/SDK
2025-07-29 18:08:02,398 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - secure.approvals = false
2025-07-29 18:08:02,398 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - shutdownCatchPhrase = shutdown
2025-07-29 18:08:02,398 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - simple.profiler.enabled = false
2025-07-29 18:08:02,398 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - skip.data.preloading = false
2025-07-29 18:08:02,398 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - socksNonProxyHosts = 127.0.0.1|***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|**********/16|*.**********/16|localhost|*.localhost|local|*.local|crashlytics.com|*.crashlytics.com|<local>|*.<local>
2025-07-29 18:08:02,398 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - stderr.encoding = UTF-8
2025-07-29 18:08:02,398 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - stdout.encoding = UTF-8
2025-07-29 18:08:02,398 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - storeUrl.com.polarion.requirements = https://polarion.plm.automation.siemens.com/products/licensing?product=REQUIREMENTS
2025-07-29 18:08:02,398 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.arch.data.model = 64
2025-07-29 18:08:02,398 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.boot.library.path = /Users/<USER>/Library/Java/JavaVirtualMachines/ms-11.0.27/Contents/Home/lib
2025-07-29 18:08:02,398 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.cpu.endian = little
2025-07-29 18:08:02,398 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.cpu.isalist = 
2025-07-29 18:08:02,398 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.io.unicode.encoding = UnicodeBig
2025-07-29 18:08:02,398 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.java.command = org.eclipse.equinox.launcher.Main -application com.polarion.core.boot.app -data /opt/polarion/data/workspace -configuration file:///Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/ -dev file:///Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/dev.properties -os linux -ws linux -arch arm64 -appId polarion.server
2025-07-29 18:08:02,398 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.java.launcher = SUN_STANDARD
2025-07-29 18:08:02,398 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.jnu.encoding = UTF-8
2025-07-29 18:08:02,398 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.management.compiler = HotSpot 64-Bit Tiered Compilers
2025-07-29 18:08:02,398 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.os.patch.level = unknown
2025-07-29 18:08:02,399 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - support.contact = https://polarion.plm.automation.siemens.com/techsupport/resources
2025-07-29 18:08:02,399 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - support.license.email = <EMAIL>
2025-07-29 18:08:02,399 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - support.sales.email = <EMAIL>
2025-07-29 18:08:02,399 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - svn.access.file = /opt/polarion/data/svn/access
2025-07-29 18:08:02,399 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - svn.passwd.file = /opt/polarion/data/svn/passwd
2025-07-29 18:08:02,399 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - svnkit.http.encoding = UTF-8
2025-07-29 18:08:02,399 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - svnkit.library.gnome-keyring.enabled = false
2025-07-29 18:08:02,399 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - TomcatService.ajp13-port = 8889
2025-07-29 18:08:02,399 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - TomcatService.request.safeListedHosts = 0.0.0.0
2025-07-29 18:08:02,399 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - user.country = CN
2025-07-29 18:08:02,399 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - user.dir = /Applications/Eclipse JEE.app/Contents/MacOS
2025-07-29 18:08:02,399 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - user.home = /Users/<USER>
2025-07-29 18:08:02,399 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - user.language = zh
2025-07-29 18:08:02,399 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - user.name = zhangwentian
2025-07-29 18:08:02,399 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - user.script = Hans
2025-07-29 18:08:02,399 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - user.timezone = Asia/Shanghai
2025-07-29 18:08:02,399 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - userAccountVault = /opt/polarion/data/workspace/user-account-vault
2025-07-29 18:08:02,399 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - workDir = /opt/polarion/data/workspace/polarion-data
2025-07-29 18:08:02,399 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - **** END of Java system properties
2025-07-29 18:08:02,400 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - XML parsers factory: com.sun.org.apache.xerces.internal.jaxp.DocumentBuilderFactoryImpl
2025-07-29 18:08:02,401 [main] INFO  com.polarion.psvn.launcher.internal.platform.PlatformService - Starting Platform...
2025-07-29 18:08:02,410 [main] INFO  PolarionLicensing - Searching for valid license file in /opt/polarion/polarion/license
2025-07-29 18:08:02,411 [main] INFO  PolarionLicensing - Trying to load license file polarion.lic
2025-07-29 18:08:02,413 [main] INFO  PolarionLicensing - The license file contains the following fields:
2025-07-29 18:08:02,413 [main] INFO  PolarionLicensing - *** License fields ***
2025-07-29 18:08:02,413 [main] INFO  PolarionLicensing - VariantsNamedUsers = 3
2025-07-29 18:08:02,413 [main] INFO  PolarionLicensing - almNamedUsers = 3
2025-07-29 18:08:02,413 [main] INFO  PolarionLicensing - dateCreated = 23.07.2025
2025-07-29 18:08:02,413 [main] INFO  PolarionLicensing - expirationDate = 21.08.2025
2025-07-29 18:08:02,413 [main] INFO  PolarionLicensing - hardwareKey = 8AG9-261C-1962
2025-07-29 18:08:02,413 [main] INFO  PolarionLicensing - licenseFormat = 2022
2025-07-29 18:08:02,413 [main] INFO  PolarionLicensing - licenseType = EVAL
2025-07-29 18:08:02,413 [main] INFO  PolarionLicensing - multiInstanceRunningInstances = 3
2025-07-29 18:08:02,413 [main] INFO  PolarionLicensing - userCompany = Polarion Eval
2025-07-29 18:08:02,413 [main] INFO  PolarionLicensing - *** License fields END ***
2025-07-29 18:08:02,429 [main] INFO  PolarionLicensing - Removing allocations by null
2025-07-29 18:08:02,430 [main] INFO  PolarionLicensing - STATS:concurrentVariantsUser,current:0,peak:0,limit:0
2025-07-29 18:08:02,430 [main] INFO  PolarionLicensing - 0 namedReviewerUser assignments (out of 0) loaded: []
2025-07-29 18:08:02,430 [main] INFO  PolarionLicensing - 0 concurrentReviewerUser assignments (out of 0) loaded: []
2025-07-29 18:08:02,430 [main] INFO  PolarionLicensing - STATS:concurrentReviewerUser,current:0,peak:0,limit:0
2025-07-29 18:08:02,430 [main] INFO  PolarionLicensing - 0 namedXBaseUser assignments (out of 0) loaded: []
2025-07-29 18:08:02,430 [main] INFO  PolarionLicensing - 0 concurrentXBaseUser assignments (out of 0) loaded: []
2025-07-29 18:08:02,430 [main] INFO  PolarionLicensing - STATS:concurrentXBaseUser,current:0,peak:0,limit:0
2025-07-29 18:08:02,430 [main] INFO  PolarionLicensing - 0 namedXProUser assignments (out of 0) loaded: []
2025-07-29 18:08:02,430 [main] INFO  PolarionLicensing - 0 concurrentXProUser assignments (out of 0) loaded: []
2025-07-29 18:08:02,430 [main] INFO  PolarionLicensing - STATS:concurrentXProUser,current:0,peak:0,limit:0
2025-07-29 18:08:02,430 [main] INFO  PolarionLicensing - 0 namedXEnterpriseUser assignments (out of 0) loaded: []
2025-07-29 18:08:02,430 [main] INFO  PolarionLicensing - 0 concurrentXEnterpriseUser assignments (out of 0) loaded: []
2025-07-29 18:08:02,430 [main] INFO  PolarionLicensing - STATS:concurrentXEnterpriseUser,current:0,peak:0,limit:0
2025-07-29 18:08:02,430 [main] INFO  PolarionLicensing - 0 namedProUser assignments (out of 0) loaded: []
2025-07-29 18:08:02,430 [main] INFO  PolarionLicensing - 0 concurrentProUser assignments (out of 0) loaded: []
2025-07-29 18:08:02,430 [main] INFO  PolarionLicensing - STATS:concurrentProUser,current:0,peak:0,limit:0
2025-07-29 18:08:02,430 [main] INFO  PolarionLicensing - 0 namedRequirementsUser assignments (out of 0) loaded: []
2025-07-29 18:08:02,430 [main] INFO  PolarionLicensing - 0 concurrentRequirementsUser assignments (out of 0) loaded: []
2025-07-29 18:08:02,430 [main] INFO  PolarionLicensing - STATS:concurrentRequirementsUser,current:0,peak:0,limit:0
2025-07-29 18:08:02,430 [main] INFO  PolarionLicensing - 0 namedQAUser assignments (out of 0) loaded: []
2025-07-29 18:08:02,430 [main] INFO  PolarionLicensing - 0 concurrentQAUser assignments (out of 0) loaded: []
2025-07-29 18:08:02,431 [main] INFO  PolarionLicensing - STATS:concurrentQAUser,current:0,peak:0,limit:0
2025-07-29 18:08:02,431 [main] INFO  PolarionLicensing - 3 namedALMUser assignments (out of 3) loaded: [admin, ou_d6f3139d36fb2978b33a8f870096b9e3, mTest]
2025-07-29 18:08:02,431 [main] INFO  PolarionLicensing - 0 concurrentALMUser assignments (out of 0) loaded: []
2025-07-29 18:08:02,431 [main] INFO  PolarionLicensing - STATS:concurrentALMUser,current:0,peak:0,limit:0
2025-07-29 18:08:02,431 [main] INFO  PolarionLicensing - 
*******************************************************************
 Polarion successfully activated
*******************************************************************
2025-07-29 18:08:02,490 [main] INFO  com.polarion.platform.internal.i18n.LanguageContributor - Localization file /META-INF/messages_en.properties read successfully (7789 messages)
2025-07-29 18:08:02,514 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Processing bundles:
2025-07-29 18:08:02,514 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [0] - org.eclipse.osgi
2025-07-29 18:08:02,514 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [1] - org.eclipse.equinox.simpleconfigurator
2025-07-29 18:08:02,514 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [2] - antlr
2025-07-29 18:08:02,514 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [3] - antlr4
2025-07-29 18:08:02,515 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [4] - antlr4-runtime
2025-07-29 18:08:02,515 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [5] - bcprov
2025-07-29 18:08:02,515 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [6] - com.auth0.java-jwt
2025-07-29 18:08:02,515 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [7] - com.fasnote.alm.auth.feishu
2025-07-29 18:08:02,516 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.fasnote.alm.auth.feishu to HiveMind
2025-07-29 18:08:02,516 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [8] - com.fasnote.alm.injection
2025-07-29 18:08:02,516 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [9] - com.fasnote.alm.plugin.manage
2025-07-29 18:08:02,517 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [10] - com.fasnote.alm.queryexpander
2025-07-29 18:08:02,517 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.fasnote.alm.queryexpander to HiveMind
2025-07-29 18:08:02,517 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [11] - com.fasnote.alm.test
2025-07-29 18:08:02,518 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [12] - com.fasterxml.classmate
2025-07-29 18:08:02,518 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [13] - com.fasterxml.jackson
2025-07-29 18:08:02,519 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [14] - com.fasterxml.jackson.dataformat.jackson-dataformat-yaml
2025-07-29 18:08:02,519 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [15] - com.fasterxml.jackson.jaxrs
2025-07-29 18:08:02,519 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [16] - com.fasterxml.woodstox
2025-07-29 18:08:02,519 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [17] - com.google.gson
2025-07-29 18:08:02,519 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [18] - com.google.guava
2025-07-29 18:08:02,519 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [19] - com.google.guava.failureaccess
2025-07-29 18:08:02,519 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [20] - com.ibm.icu.icu4j
2025-07-29 18:08:02,520 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [21] - com.icl.saxon
2025-07-29 18:08:02,520 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [22] - com.jayway.jsonpath.json-path
2025-07-29 18:08:02,520 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [23] - com.jcraft.jsch
2025-07-29 18:08:02,520 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [24] - com.networknt.json-schema-validator
2025-07-29 18:08:02,520 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [25] - com.nimbusds.content-type
2025-07-29 18:08:02,520 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [26] - com.nimbusds.nimbus-jose-jwt
2025-07-29 18:08:02,520 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [27] - com.opensymphony.quartz
2025-07-29 18:08:02,520 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [28] - com.polarion.alm.ProjectPlanGantt_new
2025-07-29 18:08:02,521 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.ProjectPlanGantt_new to HiveMind
2025-07-29 18:08:02,521 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [29] - com.polarion.alm.builder
2025-07-29 18:08:02,521 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.builder to HiveMind
2025-07-29 18:08:02,521 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [30] - com.polarion.alm.checker
2025-07-29 18:08:02,521 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.checker to HiveMind
2025-07-29 18:08:02,521 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [31] - com.polarion.alm.extension.vcontext
2025-07-29 18:08:02,521 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.extension.vcontext to HiveMind
2025-07-29 18:08:02,521 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [32] - com.polarion.alm.impex
2025-07-29 18:08:02,522 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.impex to HiveMind
2025-07-29 18:08:02,522 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [33] - com.polarion.alm.install
2025-07-29 18:08:02,522 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [34] - com.polarion.alm.oslc
2025-07-29 18:08:02,524 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.oslc to HiveMind
2025-07-29 18:08:02,524 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [35] - com.polarion.alm.projects
2025-07-29 18:08:02,524 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.projects to HiveMind
2025-07-29 18:08:02,524 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [36] - com.polarion.alm.qcentre
2025-07-29 18:08:02,524 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.qcentre to HiveMind
2025-07-29 18:08:02,524 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [37] - com.polarion.alm.tracker
2025-07-29 18:08:02,524 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.tracker to HiveMind
2025-07-29 18:08:02,524 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [38] - com.polarion.alm.ui
2025-07-29 18:08:02,528 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.ui to HiveMind
2025-07-29 18:08:02,528 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [39] - com.polarion.alm.ui.diagrams.mxgraph
2025-07-29 18:08:02,528 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [40] - com.polarion.alm.wiki
2025-07-29 18:08:02,529 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.wiki to HiveMind
2025-07-29 18:08:02,529 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [41] - com.polarion.alm.ws
2025-07-29 18:08:02,529 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [42] - com.polarion.alm.ws.client
2025-07-29 18:08:02,530 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [43] - com.polarion.cluster
2025-07-29 18:08:02,530 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.cluster to HiveMind
2025-07-29 18:08:02,530 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [44] - com.polarion.core.boot
2025-07-29 18:08:02,530 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [45] - com.polarion.core.util
2025-07-29 18:08:02,531 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [46] - com.polarion.fop
2025-07-29 18:08:02,531 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [47] - com.polarion.platform
2025-07-29 18:08:02,531 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform to HiveMind
2025-07-29 18:08:02,531 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [48] - com.polarion.platform.guice
2025-07-29 18:08:02,531 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [49] - com.polarion.platform.hivemind
2025-07-29 18:08:02,532 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.hivemind to HiveMind
2025-07-29 18:08:02,532 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [50] - com.polarion.platform.jobs
2025-07-29 18:08:02,532 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.jobs to HiveMind
2025-07-29 18:08:02,532 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [51] - com.polarion.platform.monitoring
2025-07-29 18:08:02,533 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.monitoring to HiveMind
2025-07-29 18:08:02,533 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [52] - com.polarion.platform.persistence
2025-07-29 18:08:02,533 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.persistence to HiveMind
2025-07-29 18:08:02,533 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [53] - com.polarion.platform.repository
2025-07-29 18:08:02,533 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.repository to HiveMind
2025-07-29 18:08:02,533 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [54] - com.polarion.platform.repository.driver.svn
2025-07-29 18:08:02,533 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.repository.driver.svn to HiveMind
2025-07-29 18:08:02,533 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [55] - com.polarion.platform.repository.external
2025-07-29 18:08:02,533 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.repository.external to HiveMind
2025-07-29 18:08:02,533 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [56] - com.polarion.platform.repository.external.git
2025-07-29 18:08:02,534 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.repository.external.git to HiveMind
2025-07-29 18:08:02,534 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [57] - com.polarion.platform.repository.external.svn
2025-07-29 18:08:02,534 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.repository.external.svn to HiveMind
2025-07-29 18:08:02,534 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [58] - com.polarion.platform.sql
2025-07-29 18:08:02,534 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [59] - com.polarion.portal.tomcat
2025-07-29 18:08:02,537 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [60] - com.polarion.psvn.launcher
2025-07-29 18:08:02,537 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.psvn.launcher to HiveMind
2025-07-29 18:08:02,537 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [61] - com.polarion.psvn.translations.en
2025-07-29 18:08:02,537 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [62] - com.polarion.purevariants
2025-07-29 18:08:02,538 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.purevariants to HiveMind
2025-07-29 18:08:02,538 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [63] - com.polarion.qcentre
2025-07-29 18:08:02,538 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [64] - com.polarion.scripting
2025-07-29 18:08:02,546 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.scripting to HiveMind
2025-07-29 18:08:02,546 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [65] - com.polarion.scripting.servlet
2025-07-29 18:08:02,546 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [66] - com.polarion.subterra.base
2025-07-29 18:08:02,546 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [67] - com.polarion.subterra.index
2025-07-29 18:08:02,547 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.subterra.index to HiveMind
2025-07-29 18:08:02,547 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [68] - com.polarion.subterra.persistence
2025-07-29 18:08:02,547 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.subterra.persistence to HiveMind
2025-07-29 18:08:02,547 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [69] - com.polarion.subterra.persistence.document
2025-07-29 18:08:02,548 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.subterra.persistence.document to HiveMind
2025-07-29 18:08:02,548 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [70] - com.polarion.synchronizer
2025-07-29 18:08:02,548 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.synchronizer to HiveMind
2025-07-29 18:08:02,548 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [71] - com.polarion.synchronizer.proxy.feishu
2025-07-29 18:08:02,548 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [72] - com.polarion.synchronizer.proxy.hpalm
2025-07-29 18:08:02,552 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [73] - com.polarion.synchronizer.proxy.jira
2025-07-29 18:08:02,552 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [74] - com.polarion.synchronizer.proxy.polarion
2025-07-29 18:08:02,553 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [75] - com.polarion.synchronizer.proxy.reqif
2025-07-29 18:08:02,561 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [76] - com.polarion.synchronizer.ui
2025-07-29 18:08:02,561 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [77] - com.polarion.usdp.persistence
2025-07-29 18:08:02,562 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.usdp.persistence to HiveMind
2025-07-29 18:08:02,562 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [78] - com.polarion.xray.doc.user
2025-07-29 18:08:02,562 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [79] - com.siemens.des.logger.api
2025-07-29 18:08:02,562 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [80] - com.siemens.plm.bitools.analytics
2025-07-29 18:08:02,562 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [81] - com.siemens.polarion.ct.collectors.git
2025-07-29 18:08:02,562 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.siemens.polarion.ct.collectors.git to HiveMind
2025-07-29 18:08:02,562 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [82] - com.siemens.polarion.eclipse.configurator
2025-07-29 18:08:02,563 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [83] - com.siemens.polarion.integration.ci
2025-07-29 18:08:02,563 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.siemens.polarion.integration.ci to HiveMind
2025-07-29 18:08:02,563 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [84] - com.siemens.polarion.previewer
2025-07-29 18:08:02,564 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.siemens.polarion.previewer to HiveMind
2025-07-29 18:08:02,564 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [85] - com.siemens.polarion.previewer.external
2025-07-29 18:08:02,564 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.siemens.polarion.previewer.external to HiveMind
2025-07-29 18:08:02,564 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [86] - com.siemens.polarion.rest
2025-07-29 18:08:02,564 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [87] - com.siemens.polarion.rt
2025-07-29 18:08:02,564 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [88] - com.siemens.polarion.rt.api
2025-07-29 18:08:02,565 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [89] - com.siemens.polarion.rt.collectors.git
2025-07-29 18:08:02,565 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [90] - com.siemens.polarion.rt.communication.common
2025-07-29 18:08:02,565 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [91] - com.siemens.polarion.rt.communication.polarion
2025-07-29 18:08:02,565 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.siemens.polarion.rt.communication.polarion to HiveMind
2025-07-29 18:08:02,565 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [92] - com.siemens.polarion.rt.communication.rt
2025-07-29 18:08:02,566 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [93] - com.siemens.polarion.rt.parsers.c
2025-07-29 18:08:02,566 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [94] - com.siemens.polarion.rt.ui
2025-07-29 18:08:02,566 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [95] - com.siemens.polarion.synchronizer.proxy.tfs
2025-07-29 18:08:02,567 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [96] - com.sun.activation.javax.activation
2025-07-29 18:08:02,567 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [97] - com.sun.istack.commons-runtime
2025-07-29 18:08:02,567 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [98] - com.sun.jna
2025-07-29 18:08:02,567 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [99] - com.sun.jna.platform
2025-07-29 18:08:02,567 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [100] - com.sun.xml.bind.jaxb-impl
2025-07-29 18:08:02,567 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [101] - com.trilead.ssh2
2025-07-29 18:08:02,567 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [102] - com.zaxxer.hikariCP
2025-07-29 18:08:02,567 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [103] - des-sdk-core
2025-07-29 18:08:02,568 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [104] - des-sdk-dss
2025-07-29 18:08:02,568 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [105] - io.github.resilience4j.circuitbreaker
2025-07-29 18:08:02,568 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [106] - io.github.resilience4j.core
2025-07-29 18:08:02,568 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [107] - io.github.resilience4j.retry
2025-07-29 18:08:02,568 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [108] - io.swagger
2025-07-29 18:08:02,568 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [109] - io.vavr
2025-07-29 18:08:02,568 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [110] - jakaroma
2025-07-29 18:08:02,568 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [111] - jakarta.validation.validation-api
2025-07-29 18:08:02,568 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [112] - javassist
2025-07-29 18:08:02,568 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [113] - javax.annotation-api
2025-07-29 18:08:02,568 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [114] - javax.cache
2025-07-29 18:08:02,568 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [115] - javax.el
2025-07-29 18:08:02,568 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [116] - javax.inject
2025-07-29 18:08:02,568 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [117] - javax.servlet
2025-07-29 18:08:02,569 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [118] - javax.servlet.jsp
2025-07-29 18:08:02,569 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [119] - javax.transaction
2025-07-29 18:08:02,569 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [120] - jaxb-api
2025-07-29 18:08:02,569 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [121] - jcip-annotations
2025-07-29 18:08:02,569 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [122] - jcl.over.slf4j
2025-07-29 18:08:02,569 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [123] - jul.to.slf4j
2025-07-29 18:08:02,569 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [124] - kuromoji-core
2025-07-29 18:08:02,569 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [125] - kuromoji-ipadic
2025-07-29 18:08:02,569 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [126] - lang-tag
2025-07-29 18:08:02,569 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [127] - net.htmlparser.jericho
2025-07-29 18:08:02,569 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [128] - net.java.dev.jna
2025-07-29 18:08:02,569 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [129] - net.minidev.accessors-smart
2025-07-29 18:08:02,570 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [130] - net.minidev.asm
2025-07-29 18:08:02,570 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [131] - net.minidev.json-smart
2025-07-29 18:08:02,570 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [132] - net.n3.nanoxml
2025-07-29 18:08:02,570 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [133] - net.sourceforge.cssparser
2025-07-29 18:08:02,570 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [134] - nu.xom
2025-07-29 18:08:02,570 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [135] - oauth2-oidc-sdk
2025-07-29 18:08:02,570 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [136] - org.apache.ant
2025-07-29 18:08:02,577 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [137] - org.apache.avro
2025-07-29 18:08:02,578 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [138] - org.apache.axis
2025-07-29 18:08:02,581 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [139] - org.apache.batik
2025-07-29 18:08:02,581 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [140] - org.apache.commons.codec
2025-07-29 18:08:02,581 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [141] - org.apache.commons.collections
2025-07-29 18:08:02,581 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [142] - org.apache.commons.commons-beanutils
2025-07-29 18:08:02,581 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [143] - org.apache.commons.commons-collections4
2025-07-29 18:08:02,581 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [144] - org.apache.commons.commons-compress
2025-07-29 18:08:02,582 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [145] - org.apache.commons.commons-fileupload
2025-07-29 18:08:02,582 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [146] - org.apache.commons.digester
2025-07-29 18:08:02,582 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [147] - org.apache.commons.exec
2025-07-29 18:08:02,582 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [148] - org.apache.commons.io
2025-07-29 18:08:02,582 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [149] - org.apache.commons.lang
2025-07-29 18:08:02,582 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [150] - org.apache.commons.lang3
2025-07-29 18:08:02,582 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [151] - org.apache.commons.logging
2025-07-29 18:08:02,582 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [152] - org.apache.curator
2025-07-29 18:08:02,587 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [153] - org.apache.fop
2025-07-29 18:08:02,587 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [154] - org.apache.hivemind
2025-07-29 18:08:02,587 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle org.apache.hivemind to HiveMind
2025-07-29 18:08:02,587 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [155] - org.apache.httpcomponents.httpclient
2025-07-29 18:08:02,588 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [156] - org.apache.httpcomponents.httpcore
2025-07-29 18:08:02,588 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [157] - org.apache.jasper.glassfish
2025-07-29 18:08:02,588 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [158] - org.apache.kafka.clients
2025-07-29 18:08:02,588 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [159] - org.apache.kafka.streams
2025-07-29 18:08:02,588 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [160] - org.apache.logging.log4j.1.2-api
2025-07-29 18:08:02,588 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [161] - org.apache.logging.log4j.api
2025-07-29 18:08:02,589 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [162] - org.apache.logging.log4j.apiconf
2025-07-29 18:08:02,589 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [163] - org.apache.logging.log4j.core
2025-07-29 18:08:02,589 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [164] - org.apache.logging.log4j.slf4j-impl
2025-07-29 18:08:02,589 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [165] - org.apache.lucene.analyzers-common
2025-07-29 18:08:02,589 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [166] - org.apache.lucene.analyzers-common
2025-07-29 18:08:02,590 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [167] - org.apache.lucene.analyzers-smartcn
2025-07-29 18:08:02,590 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [168] - org.apache.lucene.core
2025-07-29 18:08:02,590 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [169] - org.apache.lucene.core
2025-07-29 18:08:02,592 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [170] - org.apache.lucene.grouping
2025-07-29 18:08:02,592 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [171] - org.apache.lucene.queryparser
2025-07-29 18:08:02,592 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [172] - org.apache.oro
2025-07-29 18:08:02,592 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [173] - org.apache.pdfbox.fontbox
2025-07-29 18:08:02,592 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [174] - org.apache.poi
2025-07-29 18:08:02,601 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [175] - org.apache.tika
2025-07-29 18:08:03,114 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [176] - org.apache.xalan
2025-07-29 18:08:03,114 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [177] - org.apache.xercesImpl
2025-07-29 18:08:03,115 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [178] - org.apache.xml.serializer
2025-07-29 18:08:03,115 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [179] - org.apache.xmlgraphics.commons
2025-07-29 18:08:03,115 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [180] - org.apache.zookeeper
2025-07-29 18:08:03,115 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [181] - org.codehaus.groovy
2025-07-29 18:08:03,116 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [182] - org.codehaus.jettison
2025-07-29 18:08:03,116 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [183] - org.dom4j
2025-07-29 18:08:03,116 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [184] - org.eclipse.core.contenttype
2025-07-29 18:08:03,116 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [185] - org.eclipse.core.expressions
2025-07-29 18:08:03,116 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [186] - org.eclipse.core.filesystem
2025-07-29 18:08:03,116 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [187] - org.eclipse.core.jobs
2025-07-29 18:08:03,116 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [188] - org.eclipse.core.net
2025-07-29 18:08:03,116 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [189] - org.eclipse.core.resources
2025-07-29 18:08:03,116 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [190] - org.eclipse.core.runtime
2025-07-29 18:08:03,116 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [191] - org.eclipse.equinox.app
2025-07-29 18:08:03,116 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [192] - org.eclipse.equinox.common
2025-07-29 18:08:03,116 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [193] - org.eclipse.equinox.event
2025-07-29 18:08:03,117 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [194] - org.eclipse.equinox.http.registry
2025-07-29 18:08:03,117 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [195] - org.eclipse.equinox.http.servlet
2025-07-29 18:08:03,117 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [196] - org.eclipse.equinox.jsp.jasper
2025-07-29 18:08:03,117 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [197] - org.eclipse.equinox.jsp.jasper.registry
2025-07-29 18:08:03,117 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [198] - org.eclipse.equinox.launcher
2025-07-29 18:08:03,117 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [199] - org.eclipse.equinox.preferences
2025-07-29 18:08:03,117 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [200] - org.eclipse.equinox.registry
2025-07-29 18:08:03,117 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [201] - org.eclipse.equinox.security
2025-07-29 18:08:03,117 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [202] - org.eclipse.help
2025-07-29 18:08:03,117 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [203] - org.eclipse.help.base
2025-07-29 18:08:03,118 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [204] - org.eclipse.help.webapp
2025-07-29 18:08:03,118 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [205] - org.eclipse.jgit
2025-07-29 18:08:03,118 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [206] - org.eclipse.osgi.services
2025-07-29 18:08:03,118 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [207] - org.eclipse.osgi.util
2025-07-29 18:08:03,118 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [208] - org.ehcache
2025-07-29 18:08:03,119 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [209] - org.gitlab.java-gitlab-api
2025-07-29 18:08:03,119 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [210] - org.glassfish.jersey
2025-07-29 18:08:03,119 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [211] - org.hibernate.annotations
2025-07-29 18:08:03,120 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [212] - org.hibernate.core
2025-07-29 18:08:03,120 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [213] - org.hibernate.entitymanager
2025-07-29 18:08:03,120 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [214] - org.hibernate.hikaricp
2025-07-29 18:08:03,120 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [215] - org.hibernate.jpa.2.1.api
2025-07-29 18:08:03,120 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [216] - org.jboss.logging
2025-07-29 18:08:03,120 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [217] - org.jvnet.mimepull
2025-07-29 18:08:03,120 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [218] - org.objectweb.asm
2025-07-29 18:08:03,120 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [219] - org.objectweb.jotm
2025-07-29 18:08:03,121 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [220] - org.opensaml
2025-07-29 18:08:03,136 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [221] - org.polarion.svncommons
2025-07-29 18:08:03,137 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [222] - org.polarion.svnwebclient
2025-07-29 18:08:03,138 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [223] - org.postgesql
2025-07-29 18:08:03,138 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [224] - org.projectlombok.lombok
2025-07-29 18:08:03,152 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [225] - org.rocksdb.rocksdbjni
2025-07-29 18:08:03,152 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [226] - org.springframework.data.core
2025-07-29 18:08:03,152 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [227] - org.springframework.data.jpa
2025-07-29 18:08:03,153 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [228] - org.springframework.spring-aop
2025-07-29 18:08:03,153 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [229] - org.springframework.spring-beans
2025-07-29 18:08:03,153 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [230] - org.springframework.spring-context
2025-07-29 18:08:03,153 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [231] - org.springframework.spring-core
2025-07-29 18:08:03,153 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [232] - org.springframework.spring-expression
2025-07-29 18:08:03,153 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [233] - org.springframework.spring-jdbc
2025-07-29 18:08:03,154 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [234] - org.springframework.spring-orm
2025-07-29 18:08:03,154 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [235] - org.springframework.spring-test
2025-07-29 18:08:03,154 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [236] - org.springframework.spring-tx
2025-07-29 18:08:03,154 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [237] - org.springframework.spring-web
2025-07-29 18:08:03,154 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [238] - org.springframework.spring-webmvc
2025-07-29 18:08:03,154 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [239] - org.tmatesoft.sqljet
2025-07-29 18:08:03,154 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [240] - org.tmatesoft.svnkit
2025-07-29 18:08:03,154 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [241] - saaj-api
2025-07-29 18:08:03,155 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [242] - sdk-lifecycle-collab
2025-07-29 18:08:03,155 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [243] - sdk-lifecycle-docmgmt
2025-07-29 18:08:03,155 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [244] - siemens.des.clientsecurity
2025-07-29 18:08:03,155 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [245] - slf4j.api
2025-07-29 18:08:03,155 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [246] - xml-apis
2025-07-29 18:08:03,155 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [247] - xml.apis.ext
2025-07-29 18:08:03,155 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [248] - xstream
2025-07-29 18:08:03,370 [main] INFO  com.polarion.core.util.remote.server.SocketRemoteControlServer - Remote control server socket is ready to listen on localhost/127.0.0.1:8887
2025-07-29 18:08:03,371 [xServer:8887] INFO  org.xsocket.connection.Server - server listening on localhost:8887 (xSocket 2.5.3)
2025-07-29 18:08:03,584 [main] INFO  com.polarion.platform.sql.internal.SqlModule - Initializing database...
2025-07-29 18:08:03,655 [main] INFO  com.polarion.platform.sql.internal.PgServerInfo - PG server listening on localhost:5435
2025-07-29 18:08:05,806 [main] INFO  com.polarion.platform.internal.cache.CacheConfigurator - EHCache uses internal configuration
2025-07-29 18:08:06,097 [main] WARN  org.ehcache.impl.internal.executor.PooledExecutionService - No default pool configured, services requiring thread pools must be configured explicitly using named thread pools
2025-07-29 18:08:06,170 [main] INFO  org.ehcache.sizeof.filters.AnnotationSizeOfFilter - Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-07-29 18:08:06,174 [main] INFO  org.ehcache.sizeof.impl.JvmInformation - Detected JVM data model settings of: 64-Bit OpenJDK JVM with Compressed OOPs
2025-07-29 18:08:06,186 [main] INFO  org.ehcache.sizeof.impl.AgentLoader - Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-07-29 18:08:06,278 [main] INFO  com.polarion.platform.internal.cache.CachingProviderHandler - All the caches have been destroyed because of not clean shutdown. You can ignore this message if Polarion started in reindex mode.
2025-07-29 18:08:06,310 [main] INFO  com.polarion.platform.sql.internal.PgConnection - JDBC url of database 'polarion' is: *****************************************
2025-07-29 18:08:06,315 [main] INFO  com.polarion.platform.sql.internal.PgConnection - JDBC url of database 'polarion' is: *****************************************
2025-07-29 18:08:06,316 [main] INFO  com.polarion.platform.sql.internal.PgConnection - JDBC url of database 'polarion_history' is: *****************************************_history
2025-07-29 18:08:06,365 [main] INFO  com.polarion.platform.sql.internal.SqlModule - Initializing database finished [ TIME 2.78 s. ]
2025-07-29 18:08:06,548 [main] INFO  com.polarion.platform.cluster.ClusterService - Initializing cluster service
2025-07-29 18:08:06,548 [main] INFO  com.polarion.platform.cluster.ClusterService - Cluster service is disabled.
2025-07-29 18:08:06,749 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-29 18:08:06,777 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool - pg polarion_history - Starting...
2025-07-29 18:08:06,822 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool - pg polarion_history - Start completed.
2025-07-29 18:08:06,885 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.136 s. ]
2025-07-29 18:08:06,885 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-29 18:08:06,896 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool - pg polarion - Starting...
2025-07-29 18:08:06,903 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool - pg polarion - Start completed.
2025-07-29 18:08:06,931 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0454 s. ]
2025-07-29 18:08:06,931 [main] INFO  com.polarion.psvn.launcher.internal.platform.PlatformService - Platform boot started
2025-07-29 18:08:06,976 [main] INFO  com.polarion.platform.repository.driver.svn.internal.security.SVNWatcher - SVN auth file watcher started with a period of 3000 milliseconds
2025-07-29 18:08:06,983 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-29 18:08:07,013 [main] INFO  com.polarion.platform.internal.security.auth.LoginFlowUserPassword - User polarion authenticated from system
2025-07-29 18:08:07,071 [main] INFO  com.polarion.platform.internal.security.auth.LoginFlowUserPassword - User polarion logged in from system
2025-07-29 18:08:07,078 [main | u:p] INFO  com.polarion.platform.internal.service.repository.PlatformRepositoryService - Repository Service Created
2025-07-29 18:08:07,080 [main | u:p] INFO  com.polarion.platform.internal.service.repository.PlatformRepositoryService - Repository Service Initialized
2025-07-29 18:08:07,087 [main | u:p] INFO  com.polarion.core.util.profiling.SimpleProfiler - Initialization
2025-07-29 18:08:07,120 [main | u:p] INFO  org.objectweb.jotm - JOTM started with a local transaction factory which is not bound.
2025-07-29 18:08:07,120 [main | u:p] INFO  org.objectweb.jotm - CAROL initialization
2025-07-29 18:08:07,133 [main | u:p] INFO  com.polarion.platform.internal.service.repository.listeners.job.PullingJob - lastFullyProcessedRevision [204]
2025-07-29 18:08:07,148 [main | u:p] INFO  com.polarion.platform.internal.service.repository.PlatformRepositoryService - END initializeService
2025-07-29 18:08:07,157 [main | u:p] INFO  com.polarion.psvn.launcher.internal.platform.PlatformService - Polarion startup estimation:  [ TIME 12 s. ]
2025-07-29 18:08:07,157 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 12 s. ]
2025-07-29 18:08:07,171 [main | u:p] INFO  com.polarion.platform.monitoring - Full monitoring results are stored in file /opt/polarion/data/workspace/monitoring/results.txt
2025-07-29 18:08:07,213 [main | u:p] INFO  com.polarion.platform.monitoring - Executing actions from stage PREBOOT
2025-07-29 18:08:07,215 [main | u:p] INFO  com.polarion.platform.monitoring - Executing action 'system.info.disk.data.free'
2025-07-29 18:08:07,218 [main | u:p] INFO  com.polarion.platform.monitoring - system.info.disk.data.free (Free data disk '/opt/polarion/data' space) = Disk Space 
1,427.01 GB
 [Tue Jul 29 18:08:07 CST 2025]
2025-07-29 18:08:07,387 [main | u:p] INFO  com.polarion.platform.monitoring - Executing action 'system.info.jvm.version'
2025-07-29 18:08:07,387 [main | u:p] INFO  com.polarion.platform.monitoring - system.info.jvm.version (JVM Version) = JVM Version 
Microsoft OpenJDK 64-Bit Server VM 11.0.27+6-LTS
 [Tue Jul 29 18:08:07 CST 2025]
2025-07-29 18:08:07,389 [main | u:p] INFO  com.polarion.platform.monitoring - Executing action 'system.info.os.version'
2025-07-29 18:08:07,389 [main | u:p] INFO  com.polarion.platform.monitoring - system.info.os.version (OS Version) = OS Version 
Mac OS X 15.5 aarch64
 [Tue Jul 29 18:08:07 CST 2025]
2025-07-29 18:08:07,392 [main | u:p] INFO  com.polarion.platform.monitoring - Finished with actions from stage PREBOOT: {OK=3}
2025-07-29 18:08:07,392 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.02 s. ]
2025-07-29 18:08:07,392 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0476 s [58% update (144x), 42% query (12x)] (221x), svn: 0.0382 s [55% getLatestRevision (2x), 38% testConnection (1x)] (4x)
2025-07-29 18:08:07,392 [main | u:p] INFO  com.polarion.psvn.launcher.internal.platform.PlatformService - calling ILowLevelPersistence.boot to start persistence
2025-07-29 18:08:07,405 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Persistence initialization started
2025-07-29 18:08:07,454 [main | u:p] INFO  com.polarion.subterra.base.internal.location.LocationCacheContext - Registered invalidationListener: com.polarion.platform.repository.internal.config.RepositoryConfigService$1@5c6cf123
2025-07-29 18:08:07,483 [main | u:p] INFO  com.polarion.platform.persistence.internal.CustomFieldsService - Custom fields control field is not set for prototype: BaselineCollection
2025-07-29 18:08:07,483 [main | u:p] INFO  com.polarion.platform.persistence.internal.CustomFieldsService - Custom fields control field is not set for prototype: TestRun
2025-07-29 18:08:07,483 [main | u:p] INFO  com.polarion.platform.persistence.internal.CustomFieldsService - Custom fields control field is not set for prototype: Plan
2025-07-29 18:08:07,495 [main | u:p] INFO  com.polarion.platform.repository.internal.context.RepositoryContextService - Context recognition started
2025-07-29 18:08:07,495 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:08:07,495 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-29 18:08:07,520 [main | u:p] INFO  com.polarion.platform.repository.internal.context.RepositoryContextService - Context recognition finished [ TIME 0.0249 s. ]
2025-07-29 18:08:07,520 [main | u:p] INFO  com.polarion.platform.repository.internal.context.RepositoryContextService - Context tree: 
ROOT_CTX_NAME (ContextNature[Root], ContextId[context [global]])
+-default (ContextNature[Repository], ContextId[cluster default, context [global]])
  +-WBS (ContextNature[Project], ContextId[cluster default, context WBS])
  +-Demo Projects (ContextNature[ProjectGroup], ContextId[cluster default, context --Demo Projects])
  | +-drivepilot (ContextNature[Project], ContextId[cluster default, context drivepilot])
  | +-elibrary (ContextNature[Project], ContextId[cluster default, context elibrary])
  +-library (ContextNature[Project], ContextId[cluster default, context library])
  +-WBSdev (ContextNature[Project], ContextId[cluster default, context WBSdev])
  +-hesai (ContextNature[Project], ContextId[cluster default, context hesai])
2025-07-29 18:08:07,520 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.13 s. ]
2025-07-29 18:08:07,520 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0284 s [63% getDir2 content (2x), 30% info (3x)] (6x)
2025-07-29 18:08:07,520 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:08:07,520 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-29 18:08:07,520 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Startup workers for phase 3: 6
2025-07-29 18:08:07,528 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (5/9)
2025-07-29 18:08:07,528 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context WBSdev] (5/9) ...
2025-07-29 18:08:07,528 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (6/9)
2025-07-29 18:08:07,528 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context WBS] (6/9) ...
2025-07-29 18:08:07,528 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (1/9)
2025-07-29 18:08:07,528 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[context [global]] (1/9) ...
2025-07-29 18:08:07,528 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (2/9)
2025-07-29 18:08:07,528 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context [global]] (2/9) ...
2025-07-29 18:08:07,528 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (3/9)
2025-07-29 18:08:07,528 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context library] (3/9) ...
2025-07-29 18:08:07,528 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (4/9)
2025-07-29 18:08:07,528 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context --Demo Projects] (4/9) ...
2025-07-29 18:08:07,538 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[context [global]] (1/9) TOOK  [ TIME 0.0094 s. ]
2025-07-29 18:08:07,538 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-07-29 18:08:07,538 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context hesai] (7/9) ...
2025-07-29 18:08:07,647 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/Demo Projects/.polarion'
2025-07-29 18:08:07,656 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/Demo Projects/modules'
2025-07-29 18:08:07,666 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/Demo Projects/documents'
2025-07-29 18:08:07,673 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/Demo Projects/_wiki'
2025-07-29 18:08:07,675 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context --Demo Projects contains 0 primary objects (work items+comments).
2025-07-29 18:08:07,675 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context --Demo Projects] (4/9) TOOK  [ TIME 0.147 s. ]
2025-07-29 18:08:07,676 [LowLevelDataService-contextInitializer-4 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-29 18:08:07,676 [LowLevelDataService-contextInitializer-4 | u:p | context: drivepilot] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context drivepilot] (8/9) ...
2025-07-29 18:08:07,704 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/library/documents'
2025-07-29 18:08:07,716 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/WBS/documents'
2025-07-29 18:08:07,784 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context library contains 287 primary objects (work items+comments).
2025-07-29 18:08:07,784 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context library] (3/9) TOOK  [ TIME 0.256 s. ]
2025-07-29 18:08:07,784 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-29 18:08:07,784 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context elibrary] (9/9) ...
2025-07-29 18:08:07,789 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context WBS contains 344 primary objects (work items+comments).
2025-07-29 18:08:07,789 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context WBS] (6/9) TOOK  [ TIME 0.261 s. ]
2025-07-29 18:08:07,816 [LowLevelDataService-contextInitializer-4 | u:p | context: drivepilot] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/Demo Projects/drivepilot/documents'
2025-07-29 18:08:07,863 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/Demo Projects/elibrary/documents'
2025-07-29 18:08:07,872 [LowLevelDataService-contextInitializer-4 | u:p | context: drivepilot] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context drivepilot contains 461 primary objects (work items+comments).
2025-07-29 18:08:07,872 [LowLevelDataService-contextInitializer-4 | u:p | context: drivepilot] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context drivepilot] (8/9) TOOK  [ TIME 0.196 s. ]
2025-07-29 18:08:07,884 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context elibrary contains 334 primary objects (work items+comments).
2025-07-29 18:08:07,884 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context elibrary] (9/9) TOOK  [ TIME 0.0999 s. ]
2025-07-29 18:08:07,894 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/hesai/documents'
2025-07-29 18:08:07,961 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context hesai contains 1148 primary objects (work items+comments).
2025-07-29 18:08:07,961 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context hesai] (7/9) TOOK  [ TIME 0.422 s. ]
2025-07-29 18:08:08,037 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context null contains 2214 primary objects (work items+comments).
2025-07-29 18:08:08,037 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context [global]] (2/9) TOOK  [ TIME 0.509 s. ]
2025-07-29 18:08:08,070 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/WBSdev/documents'
2025-07-29 18:08:08,190 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context WBSdev contains 3321 primary objects (work items+comments).
2025-07-29 18:08:08,190 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context WBSdev] (5/9) TOOK  [ TIME 0.662 s. ]
2025-07-29 18:08:08,191 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.668 s, CPU [user: 0.185 s, system: 0.257 s], Allocated memory: 52.8 MB, transactions: 0, ObjectMaps: 0.109 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-29 18:08:08,191 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.668 s, CPU [user: 0.218 s, system: 0.315 s], Allocated memory: 68.4 MB, transactions: 0, ObjectMaps: 0.12 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-29 18:08:08,191 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.668 s, CPU [user: 0.114 s, system: 0.19 s], Allocated memory: 24.1 MB, transactions: 0, ObjectMaps: 0.0606 s [100% getAllPrimaryObjects (1x)] (10x)
2025-07-29 18:08:08,191 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.668 s, CPU [user: 0.0692 s, system: 0.0882 s], Allocated memory: 9.4 MB, transactions: 0, svn: 0.0872 s [34% log2 (5x), 21% info (5x), 21% log (1x), 10% getLatestRevision (2x)] (18x), ObjectMaps: 0.0688 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-29 18:08:08,191 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.668 s, CPU [user: 0.0806 s, system: 0.122 s], Allocated memory: 12.4 MB, transactions: 0, ObjectMaps: 0.0912 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.0723 s [80% log2 (10x)] (13x)
2025-07-29 18:08:08,191 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.668 s, CPU [user: 0.0779 s, system: 0.102 s], Allocated memory: 10.7 MB, transactions: 0, svn: 0.0981 s [85% log2 (10x)] (13x), ObjectMaps: 0.0475 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-29 18:08:08,192 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.67 s. ]
2025-07-29 18:08:08,192 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.497 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.317 s [66% log2 (36x), 12% getLatestRevision (9x), 9% testConnection (6x)] (61x)
2025-07-29 18:08:08,203 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.RevisionsPersistenceModule - Processing new revisions [START].
2025-07-29 18:08:08,203 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:08:08,203 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-29 18:08:08,307 [main | u:p | u:p] ERROR com.polarion.platform.repository.internal.config.RepositoryConfigService$ConfigProblemCatcher - Failed to work with configuration from location /WBS/.polarion/repositories/repositories.xml:
[/WBS/.polarion/repositories/repositories.xml]: 
---- Debugging information ----
cause-exception     : com.thoughtworks.xstream.mapper.CannotResolveClassException
cause-message       : AutoBranchGitLab
class               : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
required-type       : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
converter-type      : com.thoughtworks.xstream.converters.collections.ArrayConverter
version             : 1.4.17
-------------------------------
com.polarion.platform.repository.config.RepositoryConfigurationException: [/WBS/.polarion/repositories/repositories.xml]: 
---- Debugging information ----
cause-exception     : com.thoughtworks.xstream.mapper.CannotResolveClassException
cause-message       : AutoBranchGitLab
class               : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
required-type       : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
converter-type      : com.thoughtworks.xstream.converters.collections.ArrayConverter
version             : 1.4.17
-------------------------------
	at com.polarion.platform.repository.spi.config.AbstractDataHandler.readLocation(AbstractDataHandler.java:87) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.spi.config.AbstractDataHandler.readLocations(AbstractDataHandler.java:61) ~[platform-repository.jar:?]
	at $IDataHandler_19855a74aa1.readLocations($IDataHandler_19855a74aa1.java) ~[?:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.readLocations(RepositoryConfigService.java:291) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService$3.runImpl(RepositoryConfigService.java:328) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService$SVNAccessAction$1.runWEx(RepositoryConfigService.java:113) ~[platform-repository.jar:?]
	at com.polarion.core.util.RunnableWEx.runWRet(RunnableWEx.java:61) ~[util.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService$SVNAccessAction.run(RepositoryConfigService.java:123) ~[platform-repository.jar:?]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:?]
	at javax.security.auth.Subject.doAs(Subject.java:361) ~[?:?]
	at com.polarion.platform.internal.security.SubjectNDC.doAs(SubjectNDC.java:58) ~[platform.jar:?]
	at com.polarion.platform.internal.security.SecurityService.doAsUser(SecurityService.java:422) ~[platform.jar:?]
	at com.polarion.platform.internal.security.SecurityService.doAsSystemUser(SecurityService.java:412) ~[platform.jar:?]
	at $ISecurityService_19855a748bc.doAsSystemUser($ISecurityService_19855a748bc.java) ~[?:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.readConfiguration(RepositoryConfigService.java:324) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.getConfigurationImpl(RepositoryConfigService.java:239) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.getReadConfigurationImpl(RepositoryConfigService.java:199) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.getReadConfiguration(RepositoryConfigService.java:177) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.addConfigurationListener(RepositoryConfigService.java:263) ~[platform-repository.jar:?]
	at $IRepositoryConfigService_19855a748ca.addConfigurationListener($IRepositoryConfigService_19855a748ca.java) ~[?:?]
	at com.polarion.platform.repository.external.internal.ExternalRepositoryProviderRegistry.initialize(ExternalRepositoryProviderRegistry.java:146) ~[platform-repository.jar:?]
	at $IExternalRepositoryProviderRegistry_19855a74986.initialize($IExternalRepositoryProviderRegistry_19855a74986.java) ~[?:?]
	at $IExternalRepositoryProviderRegistry_19855a74985.initialize($IExternalRepositoryProviderRegistry_19855a74985.java) ~[?:?]
	at com.polarion.platform.persistence.internal.pe.RevisionsPersistenceModule.initModule(RevisionsPersistenceModule.java:353) ~[platform-persistence.jar:?]
	at $IObjectPersistenceModule_19855a74a75.initModule($IObjectPersistenceModule_19855a74a75.java) ~[?:?]
	at com.polarion.subterra.persistence.internal.PersistenceEngine.initModule(PersistenceEngine.java:251) ~[subterra-uniform-persistence.jar:?]
	at $IPersistenceEngine_19855a74a5d.initModule($IPersistenceEngine_19855a74a5d.java) ~[?:?]
	at com.polarion.platform.persistence.internal.pe.LowLevelDataService.boot(LowLevelDataService.java:352) ~[platform-persistence.jar:?]
	at $ILowLevelPersistence_19855a74982.boot($ILowLevelPersistence_19855a74982.java) ~[?:?]
	at $ILowLevelPersistence_19855a74981.boot($ILowLevelPersistence_19855a74981.java) ~[?:?]
	at com.polarion.psvn.launcher.internal.platform.PlatformService.lambda$0(PlatformService.java:294) ~[launcher.jar:?]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:?]
	at javax.security.auth.Subject.doAs(Subject.java:423) [?:?]
	at com.polarion.platform.internal.security.SubjectNDC.doAs(SubjectNDC.java:69) [platform.jar:?]
	at com.polarion.platform.internal.security.SecurityService.doAsUser(SecurityService.java:417) [platform.jar:?]
	at com.polarion.platform.internal.security.SecurityService.doAsSystemUser(SecurityService.java:407) [platform.jar:?]
	at $ISecurityService_19855a748bd.doAsSystemUser($ISecurityService_19855a748bd.java) [?:?]
	at $ISecurityService_19855a748bc.doAsSystemUser($ISecurityService_19855a748bc.java) [?:?]
	at com.polarion.psvn.launcher.internal.platform.PlatformService.bootPlatform(PlatformService.java:286) [launcher.jar:?]
	at com.polarion.psvn.launcher.internal.platform.PlatformService.start(PlatformService.java:92) [launcher.jar:?]
	at com.polarion.psvn.launcher.PolarionSVNApplication.runImpl(PolarionSVNApplication.java:139) [launcher.jar:?]
	at com.polarion.psvn.launcher.PolarionSVNApplication.run(PolarionSVNApplication.java:94) [launcher.jar:?]
	at com.polarion.core.boot.launchers.BasicAppLauncher.launch(BasicAppLauncher.java:53) [boot.jar:?]
	at com.polarion.core.boot.impl.AppLaunchersManager.start(AppLaunchersManager.java:184) [boot.jar:?]
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:196) [org.eclipse.equinox.app_1.3.500.v20171221-2204.jar:?]
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:134) [org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:104) [org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:388) [org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:243) [org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:566) ~[?:?]
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:656) [org.eclipse.equinox.launcher_1.5.0.v20180512-1130.jar:?]
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:592) [org.eclipse.equinox.launcher_1.5.0.v20180512-1130.jar:?]
	at org.eclipse.equinox.launcher.Main.run(Main.java:1498) [org.eclipse.equinox.launcher_1.5.0.v20180512-1130.jar:?]
	at org.eclipse.equinox.launcher.Main.main(Main.java:1471) [org.eclipse.equinox.launcher_1.5.0.v20180512-1130.jar:?]
Caused by: com.thoughtworks.xstream.converters.ConversionException: 
---- Debugging information ----
cause-exception     : com.thoughtworks.xstream.mapper.CannotResolveClassException
cause-message       : AutoBranchGitLab
class               : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
required-type       : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
converter-type      : com.thoughtworks.xstream.converters.collections.ArrayConverter
version             : 1.4.17
-------------------------------
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convert(TreeUnmarshaller.java:77) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convertAnother(TreeUnmarshaller.java:66) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convertAnother(TreeUnmarshaller.java:50) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.start(TreeUnmarshaller.java:134) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.AbstractTreeMarshallingStrategy.unmarshal(AbstractTreeMarshallingStrategy.java:32) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.unmarshal(XStream.java:1431) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.unmarshal(XStream.java:1411) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.fromXML(XStream.java:1305) ~[xstream-1.4.17.jar:1.4.17]
	at com.polarion.platform.repository.external.internal.RepositoriesDataHandler.processData(RepositoriesDataHandler.java:119) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.spi.config.AbstractDataHandler.readLocation(AbstractDataHandler.java:83) ~[platform-repository.jar:?]
	... 56 more
Caused by: com.thoughtworks.xstream.mapper.CannotResolveClassException: AutoBranchGitLab
	at com.thoughtworks.xstream.mapper.DefaultMapper.realClass(DefaultMapper.java:81) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.DynamicProxyMapper.realClass(DynamicProxyMapper.java:55) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.PackageAliasingMapper.realClass(PackageAliasingMapper.java:88) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.ClassAliasingMapper.realClass(ClassAliasingMapper.java:79) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.ArrayMapper.realClass(ArrayMapper.java:74) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.SecurityMapper.realClass(SecurityMapper.java:71) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.CachingMapper.realClass(CachingMapper.java:47) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.util.HierarchicalStreams.readClassType(HierarchicalStreams.java:29) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.converters.collections.AbstractCollectionConverter.readBareItem(AbstractCollectionConverter.java:131) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.converters.collections.AbstractCollectionConverter.readItem(AbstractCollectionConverter.java:117) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.converters.collections.AbstractCollectionConverter.readCompleteItem(AbstractCollectionConverter.java:147) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.converters.collections.ArrayConverter.unmarshal(ArrayConverter.java:54) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convert(TreeUnmarshaller.java:72) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convertAnother(TreeUnmarshaller.java:66) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convertAnother(TreeUnmarshaller.java:50) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.start(TreeUnmarshaller.java:134) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.AbstractTreeMarshallingStrategy.unmarshal(AbstractTreeMarshallingStrategy.java:32) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.unmarshal(XStream.java:1431) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.unmarshal(XStream.java:1411) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.fromXML(XStream.java:1305) ~[xstream-1.4.17.jar:1.4.17]
	at com.polarion.platform.repository.external.internal.RepositoriesDataHandler.processData(RepositoriesDataHandler.java:119) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.spi.config.AbstractDataHandler.readLocation(AbstractDataHandler.java:83) ~[platform-repository.jar:?]
	... 56 more
2025-07-29 18:08:08,409 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-29 18:08:08,420 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.RevisionsPersistenceModule - Processing new revisions from repository default in context ContextId[context [global]] finished
2025-07-29 18:08:08,421 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.RevisionsPersistenceModule - Processing new revisions [FINISHED].
2025-07-29 18:08:08,421 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.23 s. ]
2025-07-29 18:08:08,421 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.195 s [100% getReadConfiguration (48x)] (48x), svn: 0.0712 s [84% info (18x)] (38x)
2025-07-29 18:08:08,452 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:08:08,453 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-29 18:08:08,453 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Inspecting repository for build artifacts-related changes
2025-07-29 18:08:08,453 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[context [global]]
2025-07-29 18:08:08,453 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[context [global]]
2025-07-29 18:08:08,454 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[context [global]] has been successfully processed
2025-07-29 18:08:08,456 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[context [global]] finished [ TIME 0.0029 s. ]
2025-07-29 18:08:08,456 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-29 18:08:08,456 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context [global]]
2025-07-29 18:08:08,456 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context [global]]
2025-07-29 18:08:08,478 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context [global]] has been successfully processed
2025-07-29 18:08:08,506 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context [global]] finished [ TIME 0.0505 s. ]
2025-07-29 18:08:08,506 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-29 18:08:08,506 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context WBS]
2025-07-29 18:08:08,506 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context WBS]
2025-07-29 18:08:08,528 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context WBS] has been successfully processed
2025-07-29 18:08:08,554 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context WBS] finished [ TIME 0.0481 s. ]
2025-07-29 18:08:08,554 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-07-29 18:08:08,555 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context --Demo Projects]
2025-07-29 18:08:08,555 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context --Demo Projects]
2025-07-29 18:08:08,567 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context --Demo Projects] has been successfully processed
2025-07-29 18:08:08,581 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context --Demo Projects] finished [ TIME 0.026 s. ]
2025-07-29 18:08:08,581 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (4/9)
2025-07-29 18:08:08,581 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context library]
2025-07-29 18:08:08,581 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context library]
2025-07-29 18:08:08,590 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context library] has been successfully processed
2025-07-29 18:08:08,607 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context library] finished [ TIME 0.0258 s. ]
2025-07-29 18:08:08,607 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (5/9)
2025-07-29 18:08:08,607 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context WBSdev]
2025-07-29 18:08:08,607 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context WBSdev]
2025-07-29 18:08:08,624 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context WBSdev] has been successfully processed
2025-07-29 18:08:08,642 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context WBSdev] finished [ TIME 0.0357 s. ]
2025-07-29 18:08:08,642 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (6/9)
2025-07-29 18:08:08,643 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context hesai]
2025-07-29 18:08:08,643 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context hesai]
2025-07-29 18:08:08,652 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context hesai] has been successfully processed
2025-07-29 18:08:08,667 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context hesai] finished [ TIME 0.0241 s. ]
2025-07-29 18:08:08,667 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-07-29 18:08:08,667 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context drivepilot]
2025-07-29 18:08:08,667 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context drivepilot]
2025-07-29 18:08:08,687 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context drivepilot] has been successfully processed
2025-07-29 18:08:08,711 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context drivepilot] finished [ TIME 0.044 s. ]
2025-07-29 18:08:08,711 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-29 18:08:08,711 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context elibrary]
2025-07-29 18:08:08,711 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context elibrary]
2025-07-29 18:08:08,722 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context elibrary] has been successfully processed
2025-07-29 18:08:08,738 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context elibrary] finished [ TIME 0.0273 s. ]
2025-07-29 18:08:08,738 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-29 18:08:08,738 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... repository inspection finished [ TIME 0.286 s. ]
2025-07-29 18:08:08,738 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.32 s. ]
2025-07-29 18:08:08,739 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.245 s [77% info (94x), 16% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.185 s [100% getReadConfiguration (54x)] (54x)
2025-07-29 18:08:08,739 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:08:08,739 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-29 18:08:08,739 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Inspecting BIR for new or removed builds
2025-07-29 18:08:08,752 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... 0 build(s) were removed (including calculations from previous run)
2025-07-29 18:08:08,752 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... 0 build(s) were added or modified
2025-07-29 18:08:08,752 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... BIR inspection finished [ TIME 0.0133 s. ]
2025-07-29 18:08:08,752 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-07-29 18:08:08,752 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:08:08,752 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-29 18:08:08,752 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.PersistenceEngineListener - Flushing startup index events, starting iterations.
2025-07-29 18:08:08,752 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.PersistenceEngineListener - Iteration 1 - processing 5 events
2025-07-29 18:08:08,757 [main | u:p] INFO  com.polarion.alm.tracker.internal.planning.PlanFieldsProvider - livePlanXMLLocation: Location[path /default/.reports/xml/live-plan.xml]
2025-07-29 18:08:08,777 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.PersistenceEngineListener -  - reindexing 1 existing objects and 0 deleted objects.
2025-07-29 18:08:08,861 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-29 18:08:08,865 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-29 18:08:08,866 [main | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Revision
2025-07-29 18:08:08,866 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: WikiPage
2025-07-29 18:08:08,866 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: User
2025-07-29 18:08:08,866 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: RichPageAttachment
2025-07-29 18:08:08,906 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: WikiPage
2025-07-29 18:08:08,906 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: User
2025-07-29 18:08:08,906 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Category
2025-07-29 18:08:08,910 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: RichPageAttachment
2025-07-29 18:08:08,910 [PolarionDocIdCreator-6] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Attachment
2025-07-29 18:08:08,911 [PolarionDocIdCreator-6] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Attachment
2025-07-29 18:08:08,911 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Category
2025-07-29 18:08:08,913 [PolarionDocIdCreator-6] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: WorkItem
2025-07-29 18:08:08,916 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: UserGroup
2025-07-29 18:08:08,917 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: UserGroup
2025-07-29 18:08:08,917 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: BaselineCollection
2025-07-29 18:08:08,920 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: TestRun
2025-07-29 18:08:08,932 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: TestRun
2025-07-29 18:08:08,932 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Build
2025-07-29 18:08:08,933 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: ModuleComment
2025-07-29 18:08:08,934 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Build
2025-07-29 18:08:08,936 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: ModuleComment
2025-07-29 18:08:08,936 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: BuildArtifact
2025-07-29 18:08:08,937 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Comment
2025-07-29 18:08:08,939 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: BaselineCollection
2025-07-29 18:08:08,939 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: BuildArtifact
2025-07-29 18:08:08,940 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: WikiPageAttachment
2025-07-29 18:08:08,940 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Comment
2025-07-29 18:08:08,940 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: DocumentWorkflowSignature
2025-07-29 18:08:08,941 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: WikiPageAttachment
2025-07-29 18:08:08,941 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: TimePoint
2025-07-29 18:08:08,941 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: DocumentWorkflowSignature
2025-07-29 18:08:08,941 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Baseline
2025-07-29 18:08:08,943 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: TimePoint
2025-07-29 18:08:08,949 [PolarionDocIdCreator-6] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: WorkItem
2025-07-29 18:08:08,954 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Baseline
2025-07-29 18:08:08,954 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Plan
2025-07-29 18:08:08,958 [PolarionDocIdCreator-6] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: WorkRecord
2025-07-29 18:08:08,959 [PolarionDocIdCreator-6] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: WorkRecord
2025-07-29 18:08:08,978 [PolarionDocIdCreator-6] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: WorkItem-OutlineNumbers
2025-07-29 18:08:08,981 [PolarionDocIdCreator-5] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.127 s, CPU [user: 0.0329 s, system: 0.0096 s], Allocated memory: 11.2 MB, GC: 0.018 s [100% G1 Young Generation (1x)] (1x)
2025-07-29 18:08:08,982 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Project
2025-07-29 18:08:08,984 [PolarionDocIdCreator-6] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: WorkItem-OutlineNumbers
2025-07-29 18:08:08,985 [PolarionDocIdCreator-6] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: ModuleAttachment
2025-07-29 18:08:08,989 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Project
2025-07-29 18:08:08,989 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: TestRunComment
2025-07-29 18:08:08,991 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: TestRunComment
2025-07-29 18:08:08,991 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Revision
2025-07-29 18:08:08,994 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Plan
2025-07-29 18:08:08,994 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: TestRunAttachment
2025-07-29 18:08:08,995 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: RichPage
2025-07-29 18:08:08,995 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: TestRunAttachment
2025-07-29 18:08:08,998 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Module
2025-07-29 18:08:09,000 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Revision
2025-07-29 18:08:09,000 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}20
2025-07-29 18:08:09,001 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}9
2025-07-29 18:08:09,001 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}19
2025-07-29 18:08:09,001 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}10
2025-07-29 18:08:09,001 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}15
2025-07-29 18:08:09,001 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}16
2025-07-29 18:08:09,001 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}17
2025-07-29 18:08:09,001 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}18
2025-07-29 18:08:09,001 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}7
2025-07-29 18:08:09,001 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}8
2025-07-29 18:08:09,001 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}26
2025-07-29 18:08:09,001 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}32
2025-07-29 18:08:09,001 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}23
2025-07-29 18:08:09,001 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}21
2025-07-29 18:08:09,001 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}22
2025-07-29 18:08:09,001 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}24
2025-07-29 18:08:09,001 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}25
2025-07-29 18:08:09,001 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}31
2025-07-29 18:08:09,001 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}35
2025-07-29 18:08:09,001 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}37
2025-07-29 18:08:09,001 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}36
2025-07-29 18:08:09,001 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}39
2025-07-29 18:08:09,001 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}40
2025-07-29 18:08:09,001 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}41
2025-07-29 18:08:09,001 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}42
2025-07-29 18:08:09,001 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}43
2025-07-29 18:08:09,001 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}46
2025-07-29 18:08:09,001 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}47
2025-07-29 18:08:09,001 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}59
2025-07-29 18:08:09,001 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}51
2025-07-29 18:08:09,001 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}52
2025-07-29 18:08:09,001 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}53
2025-07-29 18:08:09,001 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}55
2025-07-29 18:08:09,001 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}60
2025-07-29 18:08:09,001 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}61
2025-07-29 18:08:09,002 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}65
2025-07-29 18:08:09,002 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}66
2025-07-29 18:08:09,002 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}77
2025-07-29 18:08:09,002 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}67
2025-07-29 18:08:09,002 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}69
2025-07-29 18:08:09,002 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}79
2025-07-29 18:08:09,002 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}80
2025-07-29 18:08:09,002 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}81
2025-07-29 18:08:09,002 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}82
2025-07-29 18:08:09,002 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}83
2025-07-29 18:08:09,002 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}84
2025-07-29 18:08:09,002 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}85
2025-07-29 18:08:09,002 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}86
2025-07-29 18:08:09,002 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}87
2025-07-29 18:08:09,002 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}88
2025-07-29 18:08:09,002 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}89
2025-07-29 18:08:09,002 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}91
2025-07-29 18:08:09,002 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}92
2025-07-29 18:08:09,002 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}93
2025-07-29 18:08:09,002 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/WBS:local/${Revision}09c2010030e517ae250d033127310dd72f63a0ef
2025-07-29 18:08:09,002 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/WBS:local/${Revision}7789a94e058df81e542433b71b0f51142728d99e
2025-07-29 18:08:09,002 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}99
2025-07-29 18:08:09,002 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}103
2025-07-29 18:08:09,002 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}100
2025-07-29 18:08:09,002 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}101
2025-07-29 18:08:09,002 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}96
2025-07-29 18:08:09,002 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}97
2025-07-29 18:08:09,002 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}108
2025-07-29 18:08:09,002 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}105
2025-07-29 18:08:09,002 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}107
2025-07-29 18:08:09,002 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}114
2025-07-29 18:08:09,002 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}115
2025-07-29 18:08:09,002 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}116
2025-07-29 18:08:09,002 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}117
2025-07-29 18:08:09,002 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}118
2025-07-29 18:08:09,002 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}110
2025-07-29 18:08:09,002 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}119
2025-07-29 18:08:09,002 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}120
2025-07-29 18:08:09,002 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}121
2025-07-29 18:08:09,002 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}122
2025-07-29 18:08:09,002 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}123
2025-07-29 18:08:09,002 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}124
2025-07-29 18:08:09,002 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}125
2025-07-29 18:08:09,002 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}126
2025-07-29 18:08:09,002 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}127
2025-07-29 18:08:09,002 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}130
2025-07-29 18:08:09,002 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}136
2025-07-29 18:08:09,002 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}128
2025-07-29 18:08:09,002 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}129
2025-07-29 18:08:09,002 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}131
2025-07-29 18:08:09,002 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}132
2025-07-29 18:08:09,002 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}133
2025-07-29 18:08:09,002 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}134
2025-07-29 18:08:09,002 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}135
2025-07-29 18:08:09,002 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}142
2025-07-29 18:08:09,002 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}137
2025-07-29 18:08:09,002 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}138
2025-07-29 18:08:09,002 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}140
2025-07-29 18:08:09,003 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}141
2025-07-29 18:08:09,003 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}143
2025-07-29 18:08:09,003 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}144
2025-07-29 18:08:09,003 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}145
2025-07-29 18:08:09,003 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}147
2025-07-29 18:08:09,003 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}148
2025-07-29 18:08:09,003 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}149
2025-07-29 18:08:09,003 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}151
2025-07-29 18:08:09,003 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}152
2025-07-29 18:08:09,003 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}154
2025-07-29 18:08:09,003 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}156
2025-07-29 18:08:09,003 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}158
2025-07-29 18:08:09,003 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}159
2025-07-29 18:08:09,003 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}171
2025-07-29 18:08:09,003 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}166
2025-07-29 18:08:09,003 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}167
2025-07-29 18:08:09,003 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}174
2025-07-29 18:08:09,003 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}176
2025-07-29 18:08:09,003 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}169
2025-07-29 18:08:09,003 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}160
2025-07-29 18:08:09,003 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}162
2025-07-29 18:08:09,003 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}164
2025-07-29 18:08:09,003 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}182
2025-07-29 18:08:09,003 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}183
2025-07-29 18:08:09,003 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}185
2025-07-29 18:08:09,003 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}186
2025-07-29 18:08:09,003 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}187
2025-07-29 18:08:09,003 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}188
2025-07-29 18:08:09,003 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}177
2025-07-29 18:08:09,003 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}178
2025-07-29 18:08:09,003 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}179
2025-07-29 18:08:09,003 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}196
2025-07-29 18:08:09,003 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}200
2025-07-29 18:08:09,003 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}189
2025-07-29 18:08:09,003 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}197
2025-07-29 18:08:09,003 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}198
2025-07-29 18:08:09,003 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}201
2025-07-29 18:08:09,003 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}202
2025-07-29 18:08:09,003 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}203
2025-07-29 18:08:09,003 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}204
2025-07-29 18:08:09,003 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: ProjectGroup
2025-07-29 18:08:09,004 [PolarionDocIdCreator-6] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: ModuleAttachment
2025-07-29 18:08:09,009 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: ProjectGroup
2025-07-29 18:08:09,010 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Module
2025-07-29 18:08:09,017 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: RichPage
2025-07-29 18:08:09,021 [PolarionDocIdCreator-1] INFO  com.polarion.subterra.index.impl.lucene.baseline.PolarionDocIdCreator - Bloom filter loading for 28 indices took  [ TIME 0.169 s. ]
2025-07-29 18:08:09,022 [main | u:p] INFO  com.polarion.platform.persistence.internal.calcfields.DelegatingCalculatedFieldsListener - Calculated fields mode: async
2025-07-29 18:08:09,024 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.PersistenceEngineListener - Flushing took  [ TIME 0.272 s. ]
2025-07-29 18:08:09,024 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.27 s. ]
2025-07-29 18:08:09,025 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.272 s [100% doFinishStartup (1x)] (1x), commit: 0.0815 s [100% Revision (1x)] (1x), Lucene: 0.0401 s [100% refresh (1x)] (1x), DB: 0.0202 s [39% execute (1x), 30% update (3x), 22% query (1x)] (8x), derivedLinkedRevisionsContributor: 0.0153 s [100% objectsToInv (1x)] (1x)
2025-07-29 18:08:09,025 [main | u:p] INFO  com.polarion.platform.internal.service.repository.ListenerManager - Starting the pulling job for repository: default
2025-07-29 18:08:09,025 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Persistence initialization finished [ TIME 1.62 s. ]
2025-07-29 18:08:09,025 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:08:09,025 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-29 18:08:09,026 [main | u:p] INFO  com.polarion.platform.persistence.internal.calcfields.CalculatedFieldsStorage - Checking integrity of calculated fields storage /opt/polarion/data/workspace/polarion-data/calculated-fields
2025-07-29 18:08:09,033 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-29 18:08:09,033 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:08:09,033 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-29 18:08:09,054 [main | u:p] INFO  com.polarion.platform.jobs.internal.service.scheduler.JobSchedulerService - Updating local scheduler state: start
2025-07-29 18:08:09,061 [main | u:p | u:p] INFO  org.quartz.simpl.SimpleThreadPool - Job execution threads will use class loader of thread: main | u:p | u:p
2025-07-29 18:08:09,065 [main | u:p | u:p] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
2025-07-29 18:08:09,065 [main | u:p | u:p] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'DefaultQuartzScheduler' initialized from default resource file in Quartz package: 'quartz.properties'
2025-07-29 18:08:09,065 [main | u:p | u:p] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 1.4.2
2025-07-29 18:08:09,065 [main | u:p | u:p] INFO  org.quartz.core.QuartzScheduler - Scheduler DefaultQuartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-29 18:08:09,066 [main | u:p | u:p] INFO  org.quartz.core.QuartzScheduler - Scheduler DefaultQuartzScheduler_$_NON_CLUSTERED paused.
2025-07-29 18:08:09,066 [main | u:p | u:p] INFO  org.quartz.core.QuartzScheduler - Scheduler DefaultQuartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-29 18:08:09,067 [main | u:p | u:p] INFO  org.quartz.simpl.SimpleThreadPool - Job execution threads will use class loader of thread: main | u:p | u:p
2025-07-29 18:08:09,067 [main | u:p | u:p] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
2025-07-29 18:08:09,067 [main | u:p | u:p] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'DefaultQuartzScheduler' initialized from default resource file in Quartz package: 'quartz.properties'
2025-07-29 18:08:09,067 [main | u:p | u:p] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 1.4.2
2025-07-29 18:08:09,067 [main | u:p | u:p] INFO  com.polarion.platform.jobs.internal.service.scheduler.JobSchedulerService - 15 scheduled job(s) configured
2025-07-29 18:08:09,072 [main | u:p | u:p] INFO  org.quartz.core.QuartzScheduler - Scheduler DefaultQuartzScheduler_$_NON_CLUSTERED started.
2025-07-29 18:08:09,241 [main] INFO  com.polarion.psvn.launcher.internal.platform.PlatformService - Platform boot finished
2025-07-29 18:08:09,241 [main] INFO  com.polarion.psvn.launcher.internal.platform.PlatformService - Platform started
2025-07-29 18:08:09,256 [main] INFO  com.polarion.portal.tomcat.TomcatPlugin - Tomcat home directory was set to /opt/polarion/data/workspace/.metadata/.plugins/com.polarion.portal.tomcat
2025-07-29 18:08:09,264 [main] INFO  com.polarion.psvn.launcher.internal.tomcat.TomcatService - Starting Tomcat...
2025-07-29 18:08:09,351 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: webui, contextRoot: webapp/webui, plugin: com.polarion.alm.ui, priority: -10]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:08:09,352 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion, contextRoot: webapp/authapp, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:08:09,352 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/.well-known, contextRoot: webapp/well-known, plugin: com.polarion.platform, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:08:09,352 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/ProjectPlanGantt, contextRoot: webapp, plugin: com.polarion.alm.ProjectPlanGantt_new, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:08:09,352 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/activate, contextRoot: webapp/activation, plugin: com.polarion.psvn.launcher, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:08:09,352 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/announcements, contextRoot: webapp/announcements, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:08:09,352 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/bir, contextRoot: webapp/bir, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:08:09,352 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/codemirror-modes, contextRoot: webapp/codemirror-modes, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:08:09,352 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/doorsconnector, contextRoot: webapp, plugin: com.polarion.synchronizer, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:08:09,352 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/export, contextRoot: webapp/export, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:08:09,352 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/fileupload, contextRoot: webapp/fileupload, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:08:09,352 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/gwt, contextRoot: war, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:08:09,352 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/icons, contextRoot: webapp/icons, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:08:09,353 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/internal-login, contextRoot: webapp/internal-login, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:08:09,353 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/module-attachment, contextRoot: webapp/attachments, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:08:09,353 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/modulehome, contextRoot: webapp/module-home, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:08:09,353 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/mxgraph, contextRoot: draw.io/war, plugin: com.polarion.alm.ui.diagrams.mxgraph, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:08:09,353 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/oauth-feishu, contextRoot: webapp, plugin: com.fasnote.alm.auth.feishu, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:08:09,353 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/page-attachment, contextRoot: webapp/attachments, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:08:09,353 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/perf-testing, contextRoot: webapp/perf-testing, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:08:09,353 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/plugin-manage, contextRoot: webapp, plugin: com.fasnote.alm.plugin.manage, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:08:09,353 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/print, contextRoot: webapp/print, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:08:09,353 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/register, contextRoot: webapp/register, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:08:09,353 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/rest, contextRoot: webapp, plugin: com.siemens.polarion.rest, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:08:09,353 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/ria, contextRoot: webapp/ria, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:08:09,353 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/richpagehome, contextRoot: webapp/richpage-home, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:08:09,353 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/rt, contextRoot: src/main/webapp, plugin: com.siemens.polarion.rt, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:08:09,354 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/rt-connect, contextRoot: ws, plugin: com.siemens.polarion.rt.communication.polarion, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:08:09,354 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/scripting, contextRoot: webapp/scripting, plugin: com.polarion.scripting.servlet, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:08:09,354 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/sdk, contextRoot: webapp/sdk, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:08:09,354 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/svnwebclient, contextRoot: webapp, plugin: org.polarion.svnwebclient, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:08:09,354 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/swagger, contextRoot: webapp/swagger, plugin: com.siemens.polarion.rest, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:08:09,354 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/synchronizer, contextRoot: webapp, plugin: com.polarion.synchronizer.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:08:09,354 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/template-download, contextRoot: webapp/project-template, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:08:09,354 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/testrun-attachment, contextRoot: webapp/attachments, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:08:09,354 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/tour, contextRoot: webapp/tour, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:08:09,354 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/wi-attachment, contextRoot: webapp/attachments, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:08:09,354 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/wi-attachment-auth, contextRoot: webapp/wi-attachment-auth, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:08:09,354 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/widget-resource, contextRoot: webapp/widget-resource, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:08:09,354 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/wiki, contextRoot: src/main/webapp, plugin: com.polarion.alm.wiki, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:08:09,354 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/workreport, contextRoot: webapp/workreport, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:08:09,354 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/ws, contextRoot: ws, plugin: com.polarion.alm.ws, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:08:09,355 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/xunitimport, contextRoot: webapp/xunitimport, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:08:09,355 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/oslc, contextRoot: webapp, plugin: com.polarion.alm.oslc, priority: 1]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:08:09,408 [main] INFO  org.apache.coyote.ajp.AjpNioProtocol - Initializing ProtocolHandler ["ajp-nio-127.0.0.1-8889"]
2025-07-29 18:08:09,421 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-29 18:08:09,421 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.53]
2025-07-29 18:08:09,442 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@69eb668b] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:08:09,442 [Catalina-utility-4] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@2b697117] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:08:09,442 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@2a5ae8d4] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:08:09,442 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@1d55df3a] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:08:09,442 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@3abad829] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:08:09,442 [Catalina-utility-2] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@565f233a] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:08:09,465 [Catalina-utility-6] INFO  org.apache.catalina.startup.ContextConfig - No global web.xml found
2025-07-29 18:08:09,475 [Catalina-utility-6] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:08:09,475 [Catalina-utility-1] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:08:09,475 [Catalina-utility-4] WARN  org.apache.catalina.startup.ContextConfig - Security role name [admin] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:08:09,475 [Catalina-utility-4] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:08:09,484 [Catalina-utility-2] FATAL org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/polarion/oauth-feishu] - For security constraints with URL pattern [/userinfo] only the HTTP methods [POST GET] are covered. All other methods are uncovered.
2025-07-29 18:08:09,500 [Catalina-utility-2] INFO  com.fasnote.alm.plugin.manage.Activator - 启动许可证管理插件...
2025-07-29 18:08:09,500 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.Activator - 初始化许可证配置...
2025-07-29 18:08:09,501 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.Activator - 从环境变量加载配置完成
2025-07-29 18:08:09,501 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.Activator - 许可证配置初始化完成
2025-07-29 18:08:09,501 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.Activator - 初始化核心组件...
2025-07-29 18:08:09,504 [Catalina-utility-2] INFO  com.fasnote.alm.plugin.manage.security.SecurityValidator - 初始化安全验证器
2025-07-29 18:08:09,504 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@16a1057e] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:08:09,504 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@3a360264] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:08:09,505 [Catalina-utility-4] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@37033745] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:08:09,505 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.security.SecurityValidator - 成功获取机器码
2025-07-29 18:08:09,505 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@7f449d0c] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:08:09,508 [Catalina-utility-4] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:08:09,510 [Catalina-utility-4] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@63c9df77] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:08:09,510 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.security.SecurityValidator - 未能从任何安全位置加载公钥
2025-07-29 18:08:09,510 [Catalina-utility-2] WARN  com.fasnote.alm.plugin.manage.security.SecurityValidator - 未找到外部公钥配置，使用内置公钥
2025-07-29 18:08:09,511 [Catalina-utility-2] INFO  com.fasnote.alm.license.crypto.RSAKeyManager - [RSAKeyManager] 使用硬编码公钥，版本: v1
2025-07-29 18:08:09,512 [Catalina-utility-2] INFO  com.fasnote.alm.license.crypto.RSAKeyManager - [RSAKeyManager] 成功加载硬编码公钥
2025-07-29 18:08:09,513 [Catalina-utility-2] INFO  com.fasnote.alm.license.crypto.RSALicenseEncryption - [RSALicenseEncryption] RSA+AES混合加密系统初始化完成，密钥版本: v1
2025-07-29 18:08:09,514 [Catalina-utility-2] INFO  com.fasnote.alm.plugin.manage.security.SecurityValidator - 安全验证器初始化完成，机器码: 8AG9****1962
2025-07-29 18:08:09,514 [Catalina-utility-5] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:08:09,515 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.Activator - 核心组件初始化完成
2025-07-29 18:08:09,515 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.Activator - 初始化服务...
2025-07-29 18:08:09,515 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@6e7c539d] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:08:09,515 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.Activator - 服务初始化完成
2025-07-29 18:08:09,515 [Catalina-utility-2] INFO  com.fasnote.alm.plugin.manage.Activator - 开始初始化OSGi许可证框架...
2025-07-29 18:08:09,515 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@7c8d4a94] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:08:09,516 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@57514114] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:08:09,522 [Catalina-utility-6] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:08:09,523 [Catalina-utility-1] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:08:09,524 [Catalina-utility-4] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@5f9f94ec] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:08:09,530 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@7a5073f8] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:08:09,530 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@304c70b1] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:08:09,532 [Catalina-utility-4] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@6bcb493e] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:08:09,535 [Catalina-utility-3] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:08:09,535 [Catalina-utility-1] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:08:09,535 [Catalina-utility-6] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:08:09,540 [Catalina-utility-1] INFO  org.apache.jasper.servlet.TldScanner - At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-07-29 18:08:09,541 [Catalina-utility-4] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:08:09,544 [Catalina-utility-3] INFO  org.apache.tomcat.dbcp.dbcp2.BasicDataSourceFactory - Name = XWikiDS Ignoring unknown property: value of "DB Connection" for "description" property
2025-07-29 18:08:09,556 [Catalina-utility-4] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@357afae1] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:08:09,559 [Catalina-utility-4] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:08:09,566 [Catalina-utility-4] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@4735e7d9] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:08:09,573 [Catalina-utility-4] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:08:09,575 [Catalina-utility-6] INFO  org.apache.jasper.servlet.TldScanner - At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-07-29 18:08:09,577 [Catalina-utility-4] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@416f3cd1] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:08:09,586 [Catalina-utility-4] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:08:09,591 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@77d252d6] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:08:09,594 [Catalina-utility-4] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@316a1241] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:08:09,598 [Catalina-utility-4] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:08:09,607 [Catalina-utility-4] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@3e632c01] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:08:09,623 [Catalina-utility-4] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@45ae53aa] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:08:09,644 [Catalina-utility-5] INFO  org.polarion.svncommons.commentscache.CommentsCache - Initializing comments cache. Id: http://localhost/repo, repository: http://localhost/repo/, url: http://localhost/repo/, cache directory: /opt/polarion/data/workspace/polarion-data/log-messages-cache, cache page size: 100
2025-07-29 18:08:09,647 [Catalina-utility-4] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@7e3a9bf] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:08:09,656 [Catalina-utility-4] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@78fa82c] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:08:09,668 [Catalina-utility-6] INFO  com.polarion.portal.velocity.VelocityPathManager - VelocityTemplatesPath=/opt/polarion/polarion/plugins/com.polarion.alm.ui_3.22.1/webapp/authapp/, /opt/polarion/polarion/plugins/com.polarion.alm.ui_3.22.1/webapp/webui/, /opt/polarion/polarion/plugins/com.polarion.alm.wiki_3.22.1/src/main/webapp/, ., /opt/polarion/polarion/plugins/com.polarion.alm.ui_3.22.1/webapp/webui/
2025-07-29 18:08:09,671 [Catalina-utility-4] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@58048676] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:08:09,672 [Catalina-utility-3] INFO  org.apache.jasper.servlet.TldScanner - At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-07-29 18:08:09,708 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@373aa5d6] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:08:09,714 [Catalina-utility-5] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:08:09,721 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@5cef3456] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:08:09,724 [Catalina-utility-5] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:08:09,730 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@2e7075d4] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:08:09,742 [Catalina-utility-5] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:08:09,746 [Catalina-utility-5] INFO  org.apache.jasper.servlet.TldScanner - At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-07-29 18:08:09,753 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@2b2220c] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:08:09,772 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@1c543742] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:08:09,779 [Catalina-utility-5] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:08:09,784 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@467ba23] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:08:09,794 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@30393db8] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:08:09,797 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@68a395f1] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:08:09,798 [Catalina-utility-5] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:08:09,806 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@1e786c08] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:08:09,806 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@e1affe8] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:08:09,816 [Catalina-utility-3] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:08:09,827 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@2958c9d7] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:08:09,829 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@7eb6889c] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:08:09,833 [Catalina-utility-5] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:08:09,833 [Catalina-utility-3] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:08:09,861 [Catalina-utility-5] INFO  org.apache.jasper.servlet.TldScanner - At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-07-29 18:08:09,875 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@575e2c6f] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:08:09,879 [Catalina-utility-5] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:08:09,887 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@4fcbf387] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:08:09,895 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@b74eabb] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:08:09,902 [Catalina-utility-5] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:08:09,907 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@79eaabdc] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:08:10,073 [Catalina-utility-4] INFO  com.siemens.polarion.rt.config.RtAppConfig - RT server config is created
2025-07-29 18:08:10,131 [Catalina-utility-4] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Started.
2025-07-29 18:08:10,903 [Catalina-utility-4] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Initialization of RT parsers...
2025-07-29 18:08:10,905 [Catalina-utility-4] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.java (contributed by 'com.siemens.polarion.rt[87]')
2025-07-29 18:08:10,906 [Catalina-utility-4] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.xml (contributed by 'com.siemens.polarion.rt[87]')
2025-07-29 18:08:10,908 [Catalina-utility-4] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.c (contributed by 'com.siemens.polarion.rt.parsers.c[93]')
2025-07-29 18:08:10,914 [Catalina-utility-4] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Initialization of RT collectors...
2025-07-29 18:08:10,915 [Catalina-utility-4] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Found collector for subversion (contributed by 'com.siemens.polarion.rt[87]')
2025-07-29 18:08:10,916 [Catalina-utility-4] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Found collector for git (contributed by 'com.siemens.polarion.rt.collectors.git[89]')
2025-07-29 18:08:11,762 [Catalina-utility-2] INFO  com.fasnote.alm.injection.osgi.InjectionActivator - 启动ALM依赖注入框架
2025-07-29 18:08:12,748 [Catalina-utility-2] INFO  com.fasnote.alm.injection.impl.DependencyInjector - 创建OSGi感知的纯粹依赖注入器
2025-07-29 18:08:20,965 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.osgi.InjectionActivator - 开始扫描模块
2025-07-29 18:08:24,973 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.osgi.InjectionActivator - 扫描模块包: com.fasnote.alm
2025-07-29 18:08:28,122 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 扫描模块包: com.fasnote.alm
2025-07-29 18:26:13,319 [PullingJob-default] INFO  TXLOGGER - Summary for 'PullingJob': Total: 0.124 s, CPU [user: 0.0023 s, system: 0.00189 s], Allocated memory: 132.0 kB, transactions: 0, PullingJob: 0.122 s [100% collectChanges (1x)] (1x), svn: 0.122 s [100% getLatestRevision (1x)] (1x), GC: 0.112 s [100% G1 Young Generation (1x)] (1x)
2025-07-29 18:31:16,922 [PullingJob-default] INFO  TXLOGGER - Summary for 'PullingJob': Total: 0.359 s, CPU [user: 0.00281 s, system: 0.0172 s], Allocated memory: 129.7 kB, transactions: 0, PullingJob: 0.121 s [100% collectChanges (1x)] (1x), svn: 0.12 s [100% getLatestRevision (1x)] (1x)
2025-07-29 18:33:40,492 [Catalina-utility-2] INFO  com.fasnote.alm.injection.osgi.InjectionActivator - 模块扫描完成，已安装模块数量: 0
2025-07-29 18:33:40,495 [Catalina-utility-2] INFO  com.fasnote.alm.injection.osgi.InjectionActivator - ALM依赖注入框架启动成功
2025-07-29 18:33:40,504 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.Activator - OSGi许可证框架初始化器创建完成
2025-07-29 18:33:40,505 [Catalina-utility-2] INFO  com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 初始化OSGi感知的许可证框架
2025-07-29 18:33:40,510 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.eclipse.osgi
2025-07-29 18:33:40,510 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.eclipse.osgi
2025-07-29 18:33:40,510 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.eclipse.equinox.simpleconfigurator
2025-07-29 18:33:40,510 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.eclipse.equinox.simpleconfigurator
2025-07-29 18:33:40,510 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: antlr
2025-07-29 18:33:40,510 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: antlr
2025-07-29 18:33:40,510 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: antlr4
2025-07-29 18:33:40,510 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: antlr4
2025-07-29 18:33:40,510 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: antlr4-runtime
2025-07-29 18:33:40,510 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: antlr4-runtime
2025-07-29 18:33:40,510 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: bcprov
2025-07-29 18:33:40,510 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: bcprov
2025-07-29 18:33:40,510 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.auth0.java-jwt
2025-07-29 18:33:40,510 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.auth0.java-jwt
2025-07-29 18:33:40,510 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.fasnote.alm.injection
2025-07-29 18:33:40,510 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.fasnote.alm.injection
2025-07-29 18:33:40,510 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.fasterxml.classmate
2025-07-29 18:33:40,511 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.fasterxml.classmate
2025-07-29 18:33:40,511 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.fasterxml.jackson
2025-07-29 18:33:40,511 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.fasterxml.jackson
2025-07-29 18:33:40,511 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.fasterxml.jackson.dataformat.jackson-dataformat-yaml
2025-07-29 18:33:40,511 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.fasterxml.jackson.dataformat.jackson-dataformat-yaml
2025-07-29 18:33:40,511 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.fasterxml.jackson.jaxrs
2025-07-29 18:33:40,511 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.fasterxml.jackson.jaxrs
2025-07-29 18:33:40,511 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.fasterxml.woodstox
2025-07-29 18:33:40,511 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.fasterxml.woodstox
2025-07-29 18:33:40,511 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.google.gson
2025-07-29 18:33:40,511 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.google.gson
2025-07-29 18:33:40,511 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.google.guava
2025-07-29 18:33:40,511 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.google.guava
2025-07-29 18:33:40,511 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.google.guava.failureaccess
2025-07-29 18:33:40,511 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.google.guava.failureaccess
2025-07-29 18:33:40,511 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.ibm.icu.icu4j
2025-07-29 18:33:40,511 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.ibm.icu.icu4j
2025-07-29 18:33:40,511 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.icl.saxon
2025-07-29 18:33:40,511 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.icl.saxon
2025-07-29 18:33:40,511 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.jayway.jsonpath.json-path
2025-07-29 18:33:40,511 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.jayway.jsonpath.json-path
2025-07-29 18:33:40,511 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.jcraft.jsch
2025-07-29 18:33:40,511 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.jcraft.jsch
2025-07-29 18:33:40,511 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.networknt.json-schema-validator
2025-07-29 18:33:40,511 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.networknt.json-schema-validator
2025-07-29 18:33:40,511 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.nimbusds.content-type
2025-07-29 18:33:40,511 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.nimbusds.content-type
2025-07-29 18:33:40,511 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.nimbusds.nimbus-jose-jwt
2025-07-29 18:33:40,511 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.nimbusds.nimbus-jose-jwt
2025-07-29 18:33:40,511 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.opensymphony.quartz
2025-07-29 18:33:40,511 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.opensymphony.quartz
2025-07-29 18:33:40,511 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.alm.ProjectPlanGantt_new
2025-07-29 18:33:40,511 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.alm.ProjectPlanGantt_new
2025-07-29 18:33:40,511 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.alm.builder
2025-07-29 18:33:40,511 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.alm.builder
2025-07-29 18:33:40,511 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.alm.checker
2025-07-29 18:33:40,511 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.alm.checker
2025-07-29 18:33:40,511 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.alm.impex
2025-07-29 18:33:40,511 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.alm.impex
2025-07-29 18:33:40,511 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.alm.install
2025-07-29 18:33:40,511 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.alm.install
2025-07-29 18:33:40,511 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.alm.oslc
2025-07-29 18:33:40,511 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.alm.oslc
2025-07-29 18:33:40,511 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.alm.projects
2025-07-29 18:33:40,511 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.alm.projects
2025-07-29 18:33:40,511 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.alm.tracker
2025-07-29 18:33:40,511 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.alm.tracker
2025-07-29 18:33:40,511 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.alm.ui
2025-07-29 18:33:40,511 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.alm.ui
2025-07-29 18:33:40,511 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.alm.ui.diagrams.mxgraph
2025-07-29 18:33:40,511 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.alm.ui.diagrams.mxgraph
2025-07-29 18:33:40,512 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.alm.wiki
2025-07-29 18:33:40,512 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.alm.wiki
2025-07-29 18:33:40,512 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.cluster
2025-07-29 18:33:40,512 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.cluster
2025-07-29 18:33:40,512 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.core.boot
2025-07-29 18:33:40,512 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.core.boot
2025-07-29 18:33:40,512 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.core.util
2025-07-29 18:33:40,512 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.core.util
2025-07-29 18:33:40,512 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.fop
2025-07-29 18:33:40,512 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.fop
2025-07-29 18:33:40,512 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.platform
2025-07-29 18:33:40,512 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.platform
2025-07-29 18:33:40,512 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.platform.guice
2025-07-29 18:33:40,512 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.platform.guice
2025-07-29 18:33:40,512 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.platform.hivemind
2025-07-29 18:33:40,512 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.platform.hivemind
2025-07-29 18:33:40,512 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.platform.jobs
2025-07-29 18:33:40,512 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.platform.jobs
2025-07-29 18:33:40,512 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.platform.monitoring
2025-07-29 18:33:40,512 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.platform.monitoring
2025-07-29 18:33:40,512 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.platform.persistence
2025-07-29 18:33:40,512 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.platform.persistence
2025-07-29 18:33:40,512 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.platform.repository
2025-07-29 18:33:40,512 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.platform.repository
2025-07-29 18:33:40,512 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.platform.repository.driver.svn
2025-07-29 18:33:40,512 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.platform.repository.driver.svn
2025-07-29 18:33:40,512 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.platform.repository.external
2025-07-29 18:33:40,512 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.platform.repository.external
2025-07-29 18:33:40,512 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.platform.repository.external.git
2025-07-29 18:33:40,512 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.platform.repository.external.git
2025-07-29 18:33:40,512 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.platform.repository.external.svn
2025-07-29 18:33:40,512 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.platform.repository.external.svn
2025-07-29 18:33:40,512 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.platform.sql
2025-07-29 18:33:40,512 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.platform.sql
2025-07-29 18:33:40,512 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.portal.tomcat
2025-07-29 18:33:40,512 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.portal.tomcat
2025-07-29 18:33:40,512 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.psvn.launcher
2025-07-29 18:33:40,512 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.psvn.launcher
2025-07-29 18:33:40,512 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.psvn.translations.en
2025-07-29 18:33:40,512 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.psvn.translations.en
2025-07-29 18:33:40,512 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.purevariants
2025-07-29 18:33:40,512 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.purevariants
2025-07-29 18:33:40,512 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.qcentre
2025-07-29 18:33:40,512 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.qcentre
2025-07-29 18:33:40,512 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.scripting.servlet
2025-07-29 18:33:40,512 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.scripting.servlet
2025-07-29 18:33:40,512 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.subterra.base
2025-07-29 18:33:40,512 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.subterra.base
2025-07-29 18:33:40,512 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.subterra.index
2025-07-29 18:33:40,512 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.subterra.index
2025-07-29 18:33:40,513 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.subterra.persistence
2025-07-29 18:33:40,513 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.subterra.persistence
2025-07-29 18:33:40,513 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.subterra.persistence.document
2025-07-29 18:33:40,513 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.subterra.persistence.document
2025-07-29 18:33:40,513 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.synchronizer
2025-07-29 18:33:40,513 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.synchronizer
2025-07-29 18:33:40,513 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.synchronizer.proxy.feishu
2025-07-29 18:33:40,513 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.synchronizer.proxy.feishu
2025-07-29 18:33:40,513 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.synchronizer.proxy.hpalm
2025-07-29 18:33:40,513 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.synchronizer.proxy.hpalm
2025-07-29 18:33:40,513 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.synchronizer.proxy.jira
2025-07-29 18:33:40,513 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.synchronizer.proxy.jira
2025-07-29 18:33:40,513 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.synchronizer.proxy.polarion
2025-07-29 18:33:40,513 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.synchronizer.proxy.polarion
2025-07-29 18:33:40,513 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.synchronizer.proxy.reqif
2025-07-29 18:33:40,513 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.synchronizer.proxy.reqif
2025-07-29 18:33:40,513 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.synchronizer.ui
2025-07-29 18:33:40,513 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.synchronizer.ui
2025-07-29 18:33:40,513 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.usdp.persistence
2025-07-29 18:33:40,513 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.usdp.persistence
2025-07-29 18:33:40,513 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.xray.doc.user
2025-07-29 18:33:40,513 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.xray.doc.user
2025-07-29 18:33:40,513 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.siemens.des.logger.api
2025-07-29 18:33:40,513 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.siemens.des.logger.api
2025-07-29 18:33:40,513 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.siemens.plm.bitools.analytics
2025-07-29 18:33:40,513 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.siemens.plm.bitools.analytics
2025-07-29 18:33:40,513 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.siemens.polarion.ct.collectors.git
2025-07-29 18:33:40,513 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.siemens.polarion.ct.collectors.git
2025-07-29 18:33:40,513 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.siemens.polarion.eclipse.configurator
2025-07-29 18:33:40,513 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.siemens.polarion.eclipse.configurator
2025-07-29 18:33:40,513 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.siemens.polarion.integration.ci
2025-07-29 18:33:40,513 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.siemens.polarion.integration.ci
2025-07-29 18:33:40,513 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.siemens.polarion.previewer
2025-07-29 18:33:40,513 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.siemens.polarion.previewer
2025-07-29 18:33:40,513 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.siemens.polarion.previewer.external
2025-07-29 18:33:40,513 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.siemens.polarion.previewer.external
2025-07-29 18:33:40,513 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.siemens.polarion.rest
2025-07-29 18:33:40,513 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.siemens.polarion.rest
2025-07-29 18:33:40,513 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.siemens.polarion.rt
2025-07-29 18:33:40,513 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.siemens.polarion.rt
2025-07-29 18:33:40,513 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.siemens.polarion.rt.api
2025-07-29 18:33:40,513 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.siemens.polarion.rt.api
2025-07-29 18:33:40,513 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.siemens.polarion.rt.collectors.git
2025-07-29 18:33:40,513 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.siemens.polarion.rt.collectors.git
2025-07-29 18:33:40,513 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.siemens.polarion.rt.communication.common
2025-07-29 18:33:40,513 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.siemens.polarion.rt.communication.common
2025-07-29 18:33:40,513 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.siemens.polarion.rt.communication.polarion
2025-07-29 18:33:40,513 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.siemens.polarion.rt.communication.polarion
2025-07-29 18:33:40,513 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.siemens.polarion.rt.communication.rt
2025-07-29 18:33:40,513 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.siemens.polarion.rt.communication.rt
2025-07-29 18:33:40,513 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.siemens.polarion.rt.parsers.c
2025-07-29 18:33:40,513 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.siemens.polarion.rt.parsers.c
2025-07-29 18:33:40,513 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.siemens.polarion.rt.ui
2025-07-29 18:33:40,513 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.siemens.polarion.rt.ui
2025-07-29 18:33:40,513 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.siemens.polarion.synchronizer.proxy.tfs
2025-07-29 18:33:40,513 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.siemens.polarion.synchronizer.proxy.tfs
2025-07-29 18:33:40,513 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.sun.activation.javax.activation
2025-07-29 18:33:40,513 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.sun.activation.javax.activation
2025-07-29 18:33:40,513 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.sun.istack.commons-runtime
2025-07-29 18:33:40,513 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.sun.istack.commons-runtime
2025-07-29 18:33:40,513 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.sun.jna.platform
2025-07-29 18:33:40,514 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.sun.jna.platform
2025-07-29 18:33:40,514 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.sun.xml.bind.jaxb-impl
2025-07-29 18:33:40,514 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.sun.xml.bind.jaxb-impl
2025-07-29 18:33:40,514 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.trilead.ssh2
2025-07-29 18:33:40,514 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.trilead.ssh2
2025-07-29 18:33:40,514 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.zaxxer.hikariCP
2025-07-29 18:33:40,514 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.zaxxer.hikariCP
2025-07-29 18:33:40,514 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: des-sdk-core
2025-07-29 18:33:40,514 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: des-sdk-core
2025-07-29 18:33:40,514 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: des-sdk-dss
2025-07-29 18:33:40,514 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: des-sdk-dss
2025-07-29 18:33:40,514 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: io.github.resilience4j.circuitbreaker
2025-07-29 18:33:40,514 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: io.github.resilience4j.circuitbreaker
2025-07-29 18:33:40,514 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: io.github.resilience4j.core
2025-07-29 18:33:40,514 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: io.github.resilience4j.core
2025-07-29 18:33:40,514 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: io.github.resilience4j.retry
2025-07-29 18:33:40,514 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: io.github.resilience4j.retry
2025-07-29 18:33:40,514 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: io.swagger
2025-07-29 18:33:40,514 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: io.swagger
2025-07-29 18:33:40,514 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: io.vavr
2025-07-29 18:33:40,514 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: io.vavr
2025-07-29 18:33:40,514 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: jakaroma
2025-07-29 18:33:40,514 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: jakaroma
2025-07-29 18:33:40,514 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: jakarta.validation.validation-api
2025-07-29 18:33:40,514 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: jakarta.validation.validation-api
2025-07-29 18:33:40,514 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: javassist
2025-07-29 18:33:40,514 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: javassist
2025-07-29 18:33:40,514 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: javax.annotation-api
2025-07-29 18:33:40,514 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: javax.annotation-api
2025-07-29 18:33:40,514 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: javax.cache
2025-07-29 18:33:40,514 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: javax.cache
2025-07-29 18:33:40,514 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: javax.el
2025-07-29 18:33:40,514 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: javax.el
2025-07-29 18:33:40,514 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: javax.inject
2025-07-29 18:33:40,514 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: javax.inject
2025-07-29 18:33:40,514 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: javax.servlet
2025-07-29 18:33:40,514 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: javax.servlet
2025-07-29 18:33:40,514 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: javax.servlet.jsp
2025-07-29 18:33:40,514 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: javax.servlet.jsp
2025-07-29 18:33:40,514 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: javax.transaction
2025-07-29 18:33:40,514 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: javax.transaction
2025-07-29 18:33:40,514 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: jaxb-api
2025-07-29 18:33:40,514 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: jaxb-api
2025-07-29 18:33:40,514 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: jcip-annotations
2025-07-29 18:33:40,514 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: jcip-annotations
2025-07-29 18:33:40,514 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: jcl.over.slf4j
2025-07-29 18:33:40,514 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: jcl.over.slf4j
2025-07-29 18:33:40,514 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: jul.to.slf4j
2025-07-29 18:33:40,514 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: jul.to.slf4j
2025-07-29 18:33:40,514 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: kuromoji-core
2025-07-29 18:33:40,514 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: kuromoji-core
2025-07-29 18:33:40,514 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: kuromoji-ipadic
2025-07-29 18:33:40,514 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: kuromoji-ipadic
2025-07-29 18:33:40,514 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: lang-tag
2025-07-29 18:33:40,514 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: lang-tag
2025-07-29 18:33:40,514 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: net.htmlparser.jericho
2025-07-29 18:33:40,514 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: net.htmlparser.jericho
2025-07-29 18:33:40,514 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: net.java.dev.jna
2025-07-29 18:33:40,514 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: net.java.dev.jna
2025-07-29 18:33:40,514 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: net.minidev.accessors-smart
2025-07-29 18:33:40,514 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: net.minidev.accessors-smart
2025-07-29 18:33:40,514 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: net.minidev.asm
2025-07-29 18:33:40,514 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: net.minidev.asm
2025-07-29 18:33:40,514 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: net.minidev.json-smart
2025-07-29 18:33:40,515 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: net.minidev.json-smart
2025-07-29 18:33:40,515 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: net.n3.nanoxml
2025-07-29 18:33:40,515 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: net.n3.nanoxml
2025-07-29 18:33:40,515 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: net.sourceforge.cssparser
2025-07-29 18:33:40,515 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: net.sourceforge.cssparser
2025-07-29 18:33:40,515 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: nu.xom
2025-07-29 18:33:40,515 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: nu.xom
2025-07-29 18:33:40,515 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: oauth2-oidc-sdk
2025-07-29 18:33:40,515 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: oauth2-oidc-sdk
2025-07-29 18:33:40,515 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.ant
2025-07-29 18:33:40,515 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.ant
2025-07-29 18:33:40,515 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.avro
2025-07-29 18:33:40,515 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.avro
2025-07-29 18:33:40,515 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.axis
2025-07-29 18:33:40,515 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.axis
2025-07-29 18:33:40,515 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.batik
2025-07-29 18:33:40,515 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.batik
2025-07-29 18:33:40,515 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.commons.codec
2025-07-29 18:33:40,515 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.commons.codec
2025-07-29 18:33:40,515 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.commons.collections
2025-07-29 18:33:40,515 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.commons.collections
2025-07-29 18:33:40,515 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.commons.commons-beanutils
2025-07-29 18:33:40,515 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.commons.commons-beanutils
2025-07-29 18:33:40,515 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.commons.commons-collections4
2025-07-29 18:33:40,515 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.commons.commons-collections4
2025-07-29 18:33:40,515 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.commons.commons-compress
2025-07-29 18:33:40,515 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.commons.commons-compress
2025-07-29 18:33:40,515 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.commons.commons-fileupload
2025-07-29 18:33:40,515 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.commons.commons-fileupload
2025-07-29 18:33:40,515 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.commons.digester
2025-07-29 18:33:40,515 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.commons.digester
2025-07-29 18:33:40,515 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.commons.exec
2025-07-29 18:33:40,515 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.commons.exec
2025-07-29 18:33:40,515 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.commons.io
2025-07-29 18:33:40,515 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.commons.io
2025-07-29 18:33:40,515 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.commons.lang
2025-07-29 18:33:40,515 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.commons.lang
2025-07-29 18:33:40,515 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.commons.lang3
2025-07-29 18:33:40,515 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.commons.lang3
2025-07-29 18:33:40,515 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.commons.logging
2025-07-29 18:33:40,515 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.commons.logging
2025-07-29 18:33:40,515 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.curator
2025-07-29 18:33:40,515 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.curator
2025-07-29 18:33:40,515 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.fop
2025-07-29 18:33:40,515 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.fop
2025-07-29 18:33:40,515 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.hivemind
2025-07-29 18:33:40,515 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.hivemind
2025-07-29 18:33:40,515 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.httpcomponents.httpclient
2025-07-29 18:33:40,515 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.httpcomponents.httpclient
2025-07-29 18:33:40,515 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.httpcomponents.httpcore
2025-07-29 18:33:40,515 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.httpcomponents.httpcore
2025-07-29 18:33:40,515 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.jasper.glassfish
2025-07-29 18:33:40,515 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.jasper.glassfish
2025-07-29 18:33:40,515 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.kafka.clients
2025-07-29 18:33:40,515 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.kafka.clients
2025-07-29 18:33:40,516 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.kafka.streams
2025-07-29 18:33:40,516 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.kafka.streams
2025-07-29 18:33:40,516 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.logging.log4j.1.2-api
2025-07-29 18:33:40,516 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.logging.log4j.1.2-api
2025-07-29 18:33:40,516 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.logging.log4j.api
2025-07-29 18:33:40,516 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.logging.log4j.api
2025-07-29 18:33:40,516 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.logging.log4j.apiconf
2025-07-29 18:33:40,516 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.logging.log4j.apiconf
2025-07-29 18:33:40,516 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.logging.log4j.core
2025-07-29 18:33:40,516 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.logging.log4j.core
2025-07-29 18:33:40,516 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.logging.log4j.slf4j-impl
2025-07-29 18:33:40,516 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.logging.log4j.slf4j-impl
2025-07-29 18:33:40,516 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.lucene.analyzers-common
2025-07-29 18:33:40,516 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.lucene.analyzers-common
2025-07-29 18:33:40,516 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.lucene.analyzers-common
2025-07-29 18:33:40,516 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.lucene.analyzers-common
2025-07-29 18:33:40,516 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.lucene.analyzers-smartcn
2025-07-29 18:33:40,516 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.lucene.analyzers-smartcn
2025-07-29 18:33:40,516 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.lucene.core
2025-07-29 18:33:40,516 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.lucene.core
2025-07-29 18:33:40,516 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.lucene.core
2025-07-29 18:33:40,516 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.lucene.core
2025-07-29 18:33:40,516 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.lucene.grouping
2025-07-29 18:33:40,516 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.lucene.grouping
2025-07-29 18:33:40,516 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.lucene.queryparser
2025-07-29 18:33:40,516 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.lucene.queryparser
2025-07-29 18:33:40,516 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.oro
2025-07-29 18:33:40,516 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.oro
2025-07-29 18:33:40,516 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.pdfbox.fontbox
2025-07-29 18:33:40,516 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.pdfbox.fontbox
2025-07-29 18:33:40,516 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.poi
2025-07-29 18:33:40,516 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.poi
2025-07-29 18:33:40,516 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.tika
2025-07-29 18:33:40,516 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.tika
2025-07-29 18:33:40,516 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.xalan
2025-07-29 18:33:40,516 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.xalan
2025-07-29 18:33:40,516 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.xercesImpl
2025-07-29 18:33:40,516 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.xercesImpl
2025-07-29 18:33:40,516 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.xml.serializer
2025-07-29 18:33:40,516 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.xml.serializer
2025-07-29 18:33:40,516 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.xmlgraphics.commons
2025-07-29 18:33:40,516 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.xmlgraphics.commons
2025-07-29 18:33:40,516 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.zookeeper
2025-07-29 18:33:40,516 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.zookeeper
2025-07-29 18:33:40,516 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.codehaus.jettison
2025-07-29 18:33:40,516 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.codehaus.jettison
2025-07-29 18:33:40,516 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.dom4j
2025-07-29 18:33:40,516 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.dom4j
2025-07-29 18:33:40,516 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.eclipse.core.contenttype
2025-07-29 18:33:40,516 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.eclipse.core.contenttype
2025-07-29 18:33:40,516 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.eclipse.core.jobs
2025-07-29 18:33:40,516 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.eclipse.core.jobs
2025-07-29 18:33:40,516 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.eclipse.core.runtime
2025-07-29 18:33:40,516 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.eclipse.core.runtime
2025-07-29 18:33:40,516 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.eclipse.equinox.app
2025-07-29 18:33:40,516 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.eclipse.equinox.app
2025-07-29 18:33:40,516 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.eclipse.equinox.common
2025-07-29 18:33:40,516 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.eclipse.equinox.common
2025-07-29 18:33:40,516 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.eclipse.equinox.http.registry
2025-07-29 18:33:40,516 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.eclipse.equinox.http.registry
2025-07-29 18:33:40,516 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.eclipse.equinox.http.servlet
2025-07-29 18:33:40,516 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.eclipse.equinox.http.servlet
2025-07-29 18:33:40,516 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.eclipse.equinox.launcher
2025-07-29 18:33:40,516 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.eclipse.equinox.launcher
2025-07-29 18:33:40,516 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.eclipse.equinox.preferences
2025-07-29 18:33:40,516 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.eclipse.equinox.preferences
2025-07-29 18:33:40,516 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.eclipse.equinox.registry
2025-07-29 18:33:40,516 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.eclipse.equinox.registry
2025-07-29 18:33:40,516 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.eclipse.jgit
2025-07-29 18:33:40,517 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.eclipse.jgit
2025-07-29 18:33:40,517 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.eclipse.osgi.services
2025-07-29 18:33:40,517 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.eclipse.osgi.services
2025-07-29 18:33:40,517 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.eclipse.osgi.util
2025-07-29 18:33:40,517 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.eclipse.osgi.util
2025-07-29 18:33:40,517 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.ehcache
2025-07-29 18:33:40,517 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.ehcache
2025-07-29 18:33:40,517 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.gitlab.java-gitlab-api
2025-07-29 18:33:40,517 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.gitlab.java-gitlab-api
2025-07-29 18:33:40,517 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.glassfish.jersey
2025-07-29 18:33:40,517 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.glassfish.jersey
2025-07-29 18:33:40,517 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.hibernate.annotations
2025-07-29 18:33:40,517 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.hibernate.annotations
2025-07-29 18:33:40,517 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.hibernate.core
2025-07-29 18:33:40,517 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.hibernate.core
2025-07-29 18:33:40,517 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.hibernate.entitymanager
2025-07-29 18:33:40,517 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.hibernate.entitymanager
2025-07-29 18:33:40,517 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.hibernate.hikaricp
2025-07-29 18:33:40,517 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.hibernate.hikaricp
2025-07-29 18:33:40,517 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.hibernate.jpa.2.1.api
2025-07-29 18:33:40,517 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.hibernate.jpa.2.1.api
2025-07-29 18:33:40,517 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.jboss.logging
2025-07-29 18:33:40,517 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.jboss.logging
2025-07-29 18:33:40,517 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.jvnet.mimepull
2025-07-29 18:33:40,517 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.jvnet.mimepull
2025-07-29 18:33:40,517 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.objectweb.asm
2025-07-29 18:33:40,517 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.objectweb.asm
2025-07-29 18:33:40,517 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.objectweb.jotm
2025-07-29 18:33:40,517 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.objectweb.jotm
2025-07-29 18:33:40,517 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.opensaml
2025-07-29 18:33:40,517 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.opensaml
2025-07-29 18:33:40,517 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.polarion.svncommons
2025-07-29 18:33:40,517 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.polarion.svncommons
2025-07-29 18:33:40,517 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.polarion.svnwebclient
2025-07-29 18:33:40,517 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.polarion.svnwebclient
2025-07-29 18:33:40,517 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.postgesql
2025-07-29 18:33:40,517 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.postgesql
2025-07-29 18:33:40,517 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.rocksdb.rocksdbjni
2025-07-29 18:33:40,517 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.rocksdb.rocksdbjni
2025-07-29 18:33:40,517 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.springframework.data.core
2025-07-29 18:33:40,517 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.springframework.data.core
2025-07-29 18:33:40,517 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.springframework.data.jpa
2025-07-29 18:33:40,517 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.springframework.data.jpa
2025-07-29 18:33:40,517 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.springframework.spring-aop
2025-07-29 18:33:40,517 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.springframework.spring-aop
2025-07-29 18:33:40,517 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.springframework.spring-beans
2025-07-29 18:33:40,517 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.springframework.spring-beans
2025-07-29 18:33:40,517 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.springframework.spring-context
2025-07-29 18:33:40,517 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.springframework.spring-context
2025-07-29 18:33:40,517 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.springframework.spring-core
2025-07-29 18:33:40,517 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.springframework.spring-core
2025-07-29 18:33:40,517 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.springframework.spring-expression
2025-07-29 18:33:40,517 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.springframework.spring-expression
2025-07-29 18:33:40,517 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.springframework.spring-jdbc
2025-07-29 18:33:40,517 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.springframework.spring-jdbc
2025-07-29 18:33:40,517 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.springframework.spring-orm
2025-07-29 18:33:40,517 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.springframework.spring-orm
2025-07-29 18:33:40,517 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.springframework.spring-test
2025-07-29 18:33:40,517 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.springframework.spring-test
2025-07-29 18:33:40,517 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.springframework.spring-tx
2025-07-29 18:33:40,517 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.springframework.spring-tx
2025-07-29 18:33:40,517 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.springframework.spring-web
2025-07-29 18:33:40,517 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.springframework.spring-web
2025-07-29 18:33:40,517 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.springframework.spring-webmvc
2025-07-29 18:33:40,517 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.springframework.spring-webmvc
2025-07-29 18:33:40,517 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.tmatesoft.sqljet
2025-07-29 18:33:40,517 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.tmatesoft.sqljet
2025-07-29 18:33:40,517 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.tmatesoft.svnkit
2025-07-29 18:33:40,517 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.tmatesoft.svnkit
2025-07-29 18:33:40,517 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: saaj-api
2025-07-29 18:33:40,517 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: saaj-api
2025-07-29 18:33:40,518 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: sdk-lifecycle-collab
2025-07-29 18:33:40,518 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: sdk-lifecycle-collab
2025-07-29 18:33:40,518 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: sdk-lifecycle-docmgmt
2025-07-29 18:33:40,518 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: sdk-lifecycle-docmgmt
2025-07-29 18:33:40,518 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: siemens.des.clientsecurity
2025-07-29 18:33:40,518 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: siemens.des.clientsecurity
2025-07-29 18:33:40,518 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: slf4j.api
2025-07-29 18:33:40,518 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: slf4j.api
2025-07-29 18:33:40,518 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: xml-apis
2025-07-29 18:33:40,518 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: xml-apis
2025-07-29 18:33:40,518 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: xml.apis.ext
2025-07-29 18:33:40,518 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: xml.apis.ext
2025-07-29 18:33:40,518 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: xstream
2025-07-29 18:33:40,518 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: xstream
2025-07-29 18:33:40,518 [Catalina-utility-2] INFO  com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 已注册所有活跃Bundle到DI框架，总数: 249
2025-07-29 18:33:40,518 [Catalina-utility-2] INFO  com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 开始安装许可证模块...
2025-07-29 18:33:40,526 [Catalina-utility-2] FATAL org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/polarion/oauth-feishu] - Servlet [FeishuUserInfoServlet] in web application [/polarion/oauth-feishu] threw load() exception
java.lang.ClassNotFoundException: com.fasnote.alm.auth.feishu.FeishuUserInfoServlet
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1407) ~[catalina.jar:9.0.53]
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1215) ~[catalina.jar:9.0.53]
	at org.apache.catalina.core.DefaultInstanceManager.loadClass(DefaultInstanceManager.java:538) ~[catalina.jar:9.0.53]
	at org.apache.catalina.core.DefaultInstanceManager.loadClassMaybePrivileged(DefaultInstanceManager.java:519) ~[catalina.jar:9.0.53]
	at org.apache.catalina.core.DefaultInstanceManager.newInstance(DefaultInstanceManager.java:149) ~[catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1049) ~[catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:989) ~[catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardContext.loadOnStartup(StandardContext.java:4957) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5264) [catalina.jar:9.0.53]
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1396) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1386) [catalina.jar:9.0.53]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628) [?:?]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-util.jar:9.0.53]
	at java.lang.Thread.run(Thread.java:829) [?:?]
2025-07-29 18:33:40,552 [main] INFO  org.apache.coyote.ajp.AjpNioProtocol - Starting ProtocolHandler ["ajp-nio-127.0.0.1-8889"]
2025-07-29 18:33:40,562 [main] INFO  com.polarion.psvn.launcher.internal.tomcat.TomcatService - Tomcat is listening on port 8889 using AJP/1.3 protocol with 600000 timeout in millis
2025-07-29 18:33:40,563 [main] INFO  com.polarion.psvn.launcher.internal.help.HelpService - Starting Help Service...
2025-07-29 18:33:40,572 [main] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@27180b96] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:33:40,580 [main] INFO  com.polarion.psvn.launcher.internal.help.HelpService - Help Service started
2025-07-29 18:33:40,679 [ajp-nio-127.0.0.1-8889-exec-1 | cID:55bebdf6-c0a8d700-1acc4c5a-87fccb9b] DEBUG com.fasnote.alm.auth.feishu.feishuAuthenticatorManagerInterceptorFactory - Creating SingletonProxy for service com.fasnote.alm.auth.feishu.feishuAuthenticatorManagerInterceptorFactory
2025-07-29 18:33:40,681 [ajp-nio-127.0.0.1-8889-exec-1 | cID:55bebdf6-c0a8d700-1acc4c5a-87fccb9b] DEBUG com.fasnote.alm.auth.feishu.feishuAuthenticatorManagerInterceptorFactory - Constructing core service implementation for service com.fasnote.alm.auth.feishu.feishuAuthenticatorManagerInterceptorFactory
2025-07-29 18:33:40,684 [ajp-nio-127.0.0.1-8889-exec-1 | cID:55bebdf6-c0a8d700-1acc4c5a-87fccb9b] FATAL org.apache.catalina.core.ContainerBase.[Tomcat].[localhost] - Exception Processing /polarion/svnwebclient/fileDownload.jsp
org.apache.hivemind.ApplicationRuntimeException: Unable to construct service com.polarion.platform.authenticatorProviderManager: Unable to construct service com.fasnote.alm.auth.feishu.feishuAuthenticatorManagerInterceptorFactory: Error building service com.fasnote.alm.auth.feishu.feishuAuthenticatorManagerInterceptorFactory: Unable to convert type 'com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory' to a Java class, either as is, or in package com.fasnote.alm.auth.feishu.
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructNewServiceImplementation(AbstractServiceModelImpl.java:163) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructServiceImplementation(AbstractServiceModelImpl.java:141) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.SingletonServiceModel.getActualServiceImplementation(SingletonServiceModel.java:69) ~[hivemind-1.1.1.jar:?]
	at $IAuthenticatorManager_19855a74bbc._service($IAuthenticatorManager_19855a74bbc.java) ~[?:?]
	at $IAuthenticatorManager_19855a74bbc.getAuthenticators($IAuthenticatorManager_19855a74bbc.java) ~[?:?]
	at $IAuthenticatorManager_19855a74bbb.getAuthenticators($IAuthenticatorManager_19855a74bbb.java) ~[?:?]
	at com.polarion.platform.security.auth.impl.ContributedPolarionAuthenticator.isExpectedCallback(ContributedPolarionAuthenticator.java:33) ~[platform.jar:?]
	at com.polarion.platform.security.auth.PolarionAuthenticator.authenticateImpl(PolarionAuthenticator.java:127) ~[platform.jar:?]
	at com.polarion.platform.security.auth.PolarionAuthenticator.doAuthenticate(PolarionAuthenticator.java:105) ~[platform.jar:?]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:624) ~[catalina.jar:9.0.53]
	at com.polarion.platform.security.auth.PolarionAuthenticator.invokeInternal(PolarionAuthenticator.java:248) ~[platform.jar:?]
	at com.polarion.platform.security.auth.PolarionAuthenticator.invoke(PolarionAuthenticator.java:242) ~[platform.jar:?]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) [catalina.jar:9.0.53]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.SingleSignOn.invoke(SingleSignOn.java:312) [catalina.jar:9.0.53]
	at com.polarion.platform.session.PolarionLocalSingleSignOn.invoke(PolarionLocalSingleSignOn.java:164) [platform.jar:?]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) [catalina.jar:9.0.53]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357) [catalina.jar:9.0.53]
	at org.apache.coyote.ajp.AjpProcessor.service(AjpProcessor.java:433) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1726) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-util.jar:9.0.53]
	at java.lang.Thread.run(Thread.java:829) [?:?]
Caused by: org.apache.hivemind.ApplicationRuntimeException: Unable to construct service com.fasnote.alm.auth.feishu.feishuAuthenticatorManagerInterceptorFactory: Error building service com.fasnote.alm.auth.feishu.feishuAuthenticatorManagerInterceptorFactory: Unable to convert type 'com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory' to a Java class, either as is, or in package com.fasnote.alm.auth.feishu.
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructNewServiceImplementation(AbstractServiceModelImpl.java:163) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructServiceImplementation(AbstractServiceModelImpl.java:141) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.SingletonServiceModel.getActualServiceImplementation(SingletonServiceModel.java:69) ~[hivemind-1.1.1.jar:?]
	at $ServiceInterceptorFactory_19855a74bce._service($ServiceInterceptorFactory_19855a74bce.java) ~[?:?]
	at $ServiceInterceptorFactory_19855a74bce.createInterceptor($ServiceInterceptorFactory_19855a74bce.java) ~[?:?]
	at $ServiceInterceptorFactory_19855a74bcc.createInterceptor($ServiceInterceptorFactory_19855a74bcc.java) ~[?:?]
	at org.apache.hivemind.impl.ServiceInterceptorContributionImpl.createInterceptor(ServiceInterceptorContributionImpl.java:95) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.InterceptorStackImpl.process(InterceptorStackImpl.java:116) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.addInterceptors(AbstractServiceModelImpl.java:85) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructNewServiceImplementation(AbstractServiceModelImpl.java:159) ~[polarion-hivemind.jar:?]
	... 26 more
Caused by: org.apache.hivemind.ApplicationRuntimeException: Error building service com.fasnote.alm.auth.feishu.feishuAuthenticatorManagerInterceptorFactory: Unable to convert type 'com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory' to a Java class, either as is, or in package com.fasnote.alm.auth.feishu.
	at org.apache.hivemind.impl.InvokeFactoryServiceConstructor.constructCoreServiceImplementation(InvokeFactoryServiceConstructor.java:66) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructCoreServiceImplementation(AbstractServiceModelImpl.java:108) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructNewServiceImplementation(AbstractServiceModelImpl.java:157) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructServiceImplementation(AbstractServiceModelImpl.java:141) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.SingletonServiceModel.getActualServiceImplementation(SingletonServiceModel.java:69) ~[hivemind-1.1.1.jar:?]
	at $ServiceInterceptorFactory_19855a74bce._service($ServiceInterceptorFactory_19855a74bce.java) ~[?:?]
	at $ServiceInterceptorFactory_19855a74bce.createInterceptor($ServiceInterceptorFactory_19855a74bce.java) ~[?:?]
	at $ServiceInterceptorFactory_19855a74bcc.createInterceptor($ServiceInterceptorFactory_19855a74bcc.java) ~[?:?]
	at org.apache.hivemind.impl.ServiceInterceptorContributionImpl.createInterceptor(ServiceInterceptorContributionImpl.java:95) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.InterceptorStackImpl.process(InterceptorStackImpl.java:116) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.addInterceptors(AbstractServiceModelImpl.java:85) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructNewServiceImplementation(AbstractServiceModelImpl.java:159) ~[polarion-hivemind.jar:?]
	... 26 more
Caused by: org.apache.hivemind.ApplicationRuntimeException: Error building service com.fasnote.alm.auth.feishu.feishuAuthenticatorManagerInterceptorFactory: Unable to convert type 'com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory' to a Java class, either as is, or in package com.fasnote.alm.auth.feishu.
	at org.apache.hivemind.service.impl.BuilderFactoryLogic.createService(BuilderFactoryLogic.java:87) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.service.impl.BuilderFactory.createCoreServiceImplementation(BuilderFactory.java:42) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.InvokeFactoryServiceConstructor.constructCoreServiceImplementation(InvokeFactoryServiceConstructor.java:62) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructCoreServiceImplementation(AbstractServiceModelImpl.java:108) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructNewServiceImplementation(AbstractServiceModelImpl.java:157) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructServiceImplementation(AbstractServiceModelImpl.java:141) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.SingletonServiceModel.getActualServiceImplementation(SingletonServiceModel.java:69) ~[hivemind-1.1.1.jar:?]
	at $ServiceInterceptorFactory_19855a74bce._service($ServiceInterceptorFactory_19855a74bce.java) ~[?:?]
	at $ServiceInterceptorFactory_19855a74bce.createInterceptor($ServiceInterceptorFactory_19855a74bce.java) ~[?:?]
	at $ServiceInterceptorFactory_19855a74bcc.createInterceptor($ServiceInterceptorFactory_19855a74bcc.java) ~[?:?]
	at org.apache.hivemind.impl.ServiceInterceptorContributionImpl.createInterceptor(ServiceInterceptorContributionImpl.java:95) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.InterceptorStackImpl.process(InterceptorStackImpl.java:116) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.addInterceptors(AbstractServiceModelImpl.java:85) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructNewServiceImplementation(AbstractServiceModelImpl.java:159) ~[polarion-hivemind.jar:?]
	... 26 more
Caused by: org.apache.hivemind.ApplicationRuntimeException: Unable to convert type 'com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory' to a Java class, either as is, or in package com.fasnote.alm.auth.feishu.
	at org.apache.hivemind.impl.ModuleImpl.findTypeInClassResolver(ModuleImpl.java:219) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.ModuleImpl.resolveType(ModuleImpl.java:203) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.service.impl.BuilderFactoryLogic.instantiateCoreServiceInstance(BuilderFactoryLogic.java:100) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.service.impl.BuilderFactoryLogic.createService(BuilderFactoryLogic.java:75) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.service.impl.BuilderFactory.createCoreServiceImplementation(BuilderFactory.java:42) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.InvokeFactoryServiceConstructor.constructCoreServiceImplementation(InvokeFactoryServiceConstructor.java:62) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructCoreServiceImplementation(AbstractServiceModelImpl.java:108) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructNewServiceImplementation(AbstractServiceModelImpl.java:157) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructServiceImplementation(AbstractServiceModelImpl.java:141) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.SingletonServiceModel.getActualServiceImplementation(SingletonServiceModel.java:69) ~[hivemind-1.1.1.jar:?]
	at $ServiceInterceptorFactory_19855a74bce._service($ServiceInterceptorFactory_19855a74bce.java) ~[?:?]
	at $ServiceInterceptorFactory_19855a74bce.createInterceptor($ServiceInterceptorFactory_19855a74bce.java) ~[?:?]
	at $ServiceInterceptorFactory_19855a74bcc.createInterceptor($ServiceInterceptorFactory_19855a74bcc.java) ~[?:?]
	at org.apache.hivemind.impl.ServiceInterceptorContributionImpl.createInterceptor(ServiceInterceptorContributionImpl.java:95) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.InterceptorStackImpl.process(InterceptorStackImpl.java:116) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.addInterceptors(AbstractServiceModelImpl.java:85) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructNewServiceImplementation(AbstractServiceModelImpl.java:159) ~[polarion-hivemind.jar:?]
	... 26 more
2025-07-29 18:33:40,779 [ajp-nio-127.0.0.1-8889-exec-1 | cID:55bebdf6-c0a8d700-1acc4c5a-87fccb9b] INFO  TXLOGGER - Summary for 'servlet /polarion/svnwebclient/fileDownload.jsp?url=.polarion%2Fsynchronizer%2Fconfiguration.xml&attachment=true': Total: 0.18 s, CPU [user: 0.0541 s, system: 0.0194 s], Allocated memory: 7.7 MB, transactions: 0, PolarionAuthenticator: 0.0675 s [100% authenticate (1x)] (1x)
2025-07-29 18:33:40,815 [main | u:p] INFO  com.xpn.xwiki.XWiki - xwiki.cfg taken from /WEB-INF/xwiki.cfg because the XWikiConfig variable is not set in the context
2025-07-29 18:33:41,172 [main | u:p | u:p] INFO  TXLOGGER - Tx 66156fafef43f_0_66156fafef43f_0_: finished. Total: 0.119 s, CPU [user: 0.0649 s, system: 0.014 s], Allocated memory: 7.6 MB, GlobalHandler: 0.0134 s [97% applyTxChanges (1x)] (4x)
2025-07-29 18:33:41,411 [Thread-33] INFO  class com.polarion.alm.server.util.ChartExporterStartup - Chart renderer says: Server started on 127.0.0.1:34567
2025-07-29 18:33:41,515 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtCommunicationStartup - Rt configuration local mediator is started
2025-07-29 18:33:41,680 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55bec219-c0a8d700-1acc4c5a-639a9096] INFO  com.siemens.polarion.rt.dataprovider.controller.RtDataProviderController - RT server is notified to update all configurations.
2025-07-29 18:33:41,705 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtNotifier - RT server was successfully notified of configuration change.
2025-07-29 18:33:41,706 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtCommunicationStartup - Rt notification service is started
2025-07-29 18:33:41,729 [Thread-37] INFO  com.polarion.platform.jobs.info - Job "Attachment Indexer" has id 55bec250-c0a8d700-1acc4c5a-2a4c6810
2025-07-29 18:33:41,730 [main | u:p] INFO  com.polarion.platform.monitoring - Next slow periodic actions will be executed on Wed Jul 30 01:00:41 CST 2025
2025-07-29 18:33:41,730 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.alm.tracker.internal.HttpsConfiguratorStartup successfully initialized
2025-07-29 18:33:41,730 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.alm.server.util.ChartExporterStartup successfully initialized
2025-07-29 18:33:41,731 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.alm.wiki.WikiPlugin successfully initialized
2025-07-29 18:33:41,731 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.alm.qcentre.internal.QCentreStartup successfully initialized
2025-07-29 18:33:41,731 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.siemens.polarion.rt.communication.connection.RtCommunicationStartup successfully initialized
2025-07-29 18:33:41,731 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.platform.internal.startup.NotificationServerStartup successfully initialized
2025-07-29 18:33:41,731 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.subterra.index.impl.IndexingJobsStartup successfully initialized
2025-07-29 18:33:41,731 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.alm.ui.server.ServerStartup successfully initialized
2025-07-29 18:33:41,732 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.alm.server.util.FormulaServerStartup successfully initialized
2025-07-29 18:33:41,732 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.platform.monitoring.internal.MonitoringServiceStart successfully initialized
2025-07-29 18:33:41,732 [Thread-37] INFO  com.polarion.platform.jobs.info - State of job "Attachment Indexer" changed to UNSCHEDULED
2025-07-29 18:33:41,733 [main] INFO  com.polarion.platform.monitoring - Executing actions from stage POSTBOOT
2025-07-29 18:33:41,733 [main] INFO  com.polarion.platform.monitoring - Executing action 'polarion.info.cache.statistics'
2025-07-29 18:33:41,734 [Thread-37] INFO  com.polarion.platform.jobs.info - Working directory of root job "Attachment Indexer" is /opt/polarion/data/workspace/polarion-data/jobs/20250729-1833
2025-07-29 18:33:41,735 [Thread-37] INFO  com.polarion.platform.jobs.info - Job "Attachment Indexer" runs as user "polarion"
2025-07-29 18:33:41,737 [Thread-37 | u:p] INFO  com.polarion.platform.jobs.info - State of job "Attachment Indexer" changed to ACTIVATING
2025-07-29 18:33:41,741 [Thread-37 | u:p] INFO  com.polarion.platform.jobs.info - State of job "Attachment Indexer" changed to WAITING
2025-07-29 18:33:41,749 [main] INFO  com.polarion.platform.monitoring - polarion.info.cache.statistics (Statistics of caches) = Statistics of caches 
                         CACHE       HITS     MISSES  HIT_RATIO       GETS       PUTS    REMOVAL   EVICTION 
     attachments-content-cache          0          0          0%          0          0          0          0 
                baseline-cache          0          0          0%          0          0          0          0 
                            bq          0          0          0%          0          0          0          0 
                dao-attachment          0          0          0%          0          0          0          0 
                   dao-comment          0          0          0%          0          0          0          0 
                    dao-global          0          4          0%          4          0          0          0 
                    dao-module          0          0          0%          0          0          0          0 
          dao-moduleattachment          0          0          0%          0          0          0          0 
             dao-modulecomment          0          0          0%          0          0          0          0 
                      dao-plan          0          0          0%          0          0          0          0 
                   dao-project          0          0          0%          0          0          0          0 
              dao-projectgroup          0          6          0%          6          3          0          0 
                  dao-richpage          0          0          0%          0          0          0          0 
        dao-richpageattachment          0          0          0%          0          0          0          0 
                   dao-testrun          0          0          0%          0          0          0          0 
         dao-testrunattachment          0          0          0%          0          0          0          0 
                      dao-user          0          0          0%          0          0          0          0 
                  dao-wikipage          0          0          0%          0          0          0          0 
        dao-wikipageattachment          0          0          0%          0          0          0          0 
                  dao-workitem          0          0          0%          0          0          0          0 
      derived-linked-revisions          0          0          0%          0          0          0          0 
                  formulas-svg          0          0          0%          0          0          0          0 
                github-commits          0          0          0%          0          0          0          0 
            historical-queries          0          0          0%          0          0          0          0 
      historical-queries-sizes          0          0          0%          0          0          0          0 
        oslc-linked-item-cache          0          0          0%          0          0          0          0 
               plan-statistics          0          0          0%          0          0          0          0 
                   rmd-default          3          5         38%          8          6          0          0 
                   ss-combined          0          0          0%          0          0          0          0 
                    ss-context          0          0          0%          0          0          0          0 
                     wiki-page          2          8         20%         10          9          1          0 
              wiki-page-exists          0          0          0%          0          9          1          0 

 [Tue Jul 29 18:33:41 CST 2025]
2025-07-29 18:33:41,754 [Thread-36] INFO  com.polarion.core.util.process.JavaRunner - Executing /Users/<USER>/Library/Java/JavaVirtualMachines/ms-11.0.27/Contents/Home/bin/java
  -- args [-jar, /opt/polarion/polarion/plugins/com.polarion.platform_3.22.1/services/notification-service/NotificationService.jar, --server.port=40608, --jwksUrl=http://localhost/polarion/.well-known/jwks.json]
  -- env null
  -- dir /var/folders/z_/shw6wc7d7ps_fjvv781t4gt80000gn/T/polarionSpringServerSubprocess13446181187911058271.tmp
2025-07-29 18:33:41,756 [Thread-37] INFO  com.polarion.platform.jobs.info - Job "DB History Creator" has id 55bec278-c0a8d700-1acc4c5a-80a7ef34
2025-07-29 18:33:41,756 [Worker-0: Attachment Indexer | u:p] INFO  com.polarion.platform.jobs.info - State of job "Attachment Indexer" changed to RUNNING
2025-07-29 18:33:41,769 [Thread-37] INFO  com.polarion.platform.jobs.info - State of job "DB History Creator" changed to UNSCHEDULED
2025-07-29 18:33:41,775 [Thread-37] INFO  com.polarion.platform.jobs.info - Working directory of root job "DB History Creator" is /opt/polarion/data/workspace/polarion-data/jobs/20250729-1833_0
2025-07-29 18:33:41,779 [main] INFO  com.polarion.platform.monitoring - Finished with actions from stage POSTBOOT: {OK=1}
2025-07-29 18:33:41,779 [Thread-37] INFO  com.polarion.platform.jobs.info - Job "DB History Creator" runs as user "polarion"
2025-07-29 18:33:41,780 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 1532.74 s. ]
2025-07-29 18:33:41,780 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 17, svn: 5.03 s [93% getLatestRevision (511x)] (685x), PullingJob: 4.76 s [100% collectChanges (510x)] (510x)
2025-07-29 18:33:41,780 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 18:33:41,780 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 1540 s. ]
2025-07-29 18:33:41,780 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 18:33:41,780 [Thread-37 | u:p] INFO  com.polarion.platform.jobs.info - State of job "DB History Creator" changed to ACTIVATING
2025-07-29 18:33:41,784 [Thread-37 | u:p] INFO  com.polarion.platform.jobs.info - State of job "DB History Creator" changed to WAITING
2025-07-29 18:33:41,784 [Worker-0: Attachment Indexer | u:p | job: polarion.jobs.attachment.indexer] INFO  TXLOGGER - Summary after AttachmentIndexer: transactions: 0, notification worker: 0.00069 s [100% BuildActivityCreator (1x)] (1x), EHCache: 0.000434 s [100% GET (2x)] (2x)
2025-07-29 18:33:41,787 [PreLoadDataService] INFO  com.polarion.psvn.launcher.internal.data.PreLoadDataService - Preloading data ...
2025-07-29 18:33:41,788 [Worker-0: Attachment Indexer | u:p] INFO  com.polarion.platform.jobs.info - State of job "Attachment Indexer" changed to FINISHED
2025-07-29 18:33:41,792 [Worker-1: DB History Creator | u:p] INFO  com.polarion.platform.jobs.info - State of job "DB History Creator" changed to RUNNING
2025-07-29 18:33:41,795 [Worker-0: Attachment Indexer | u:p] INFO  com.polarion.platform.jobs.info - Status of job "Attachment Indexer" is OK
2025-07-29 18:33:42,037 [PreLoadDataService | u:p] DEBUG com.fasnote.alm.queryexpander.queryExpanderInterceptorFactory - Creating SingletonProxy for service com.fasnote.alm.queryexpander.queryExpanderInterceptorFactory
2025-07-29 18:33:42,038 [PreLoadDataService | u:p] DEBUG com.fasnote.alm.queryexpander.queryExpanderInterceptorFactory - Constructing core service implementation for service com.fasnote.alm.queryexpander.queryExpanderInterceptorFactory
2025-07-29 18:33:42,072 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: TimePoint
2025-07-29 18:33:42,074 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: TimePoint
2025-07-29 18:33:42,082 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Category
2025-07-29 18:33:42,097 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Category
2025-07-29 18:33:42,226 [Thread-43] INFO  class com.polarion.alm.server.util.FormulaServerStartup - Formula renderer says: Server started on 127.0.0.1:34568
2025-07-29 18:33:42,381 [Thread-40] INFO  NotificationService - 
2025-07-29 18:33:42,381 [Thread-40] INFO  NotificationService -   .   ____          _            __ _ _
2025-07-29 18:33:42,381 [Thread-40] INFO  NotificationService -  /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
2025-07-29 18:33:42,381 [Thread-40] INFO  NotificationService - ( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
2025-07-29 18:33:42,381 [Thread-40] INFO  NotificationService -  \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
2025-07-29 18:33:42,381 [Thread-40] INFO  NotificationService -   '  |____| .__|_| |_|_| |_\__, | / / / /
2025-07-29 18:33:42,381 [Thread-40] INFO  NotificationService -  =========|_|==============|___/=/_/_/_/
2025-07-29 18:33:42,382 [Thread-40] INFO  NotificationService -  :: Spring Boot ::                (v2.6.6)
2025-07-29 18:33:42,382 [Thread-40] INFO  NotificationService - 
2025-07-29 18:33:42,467 [Thread-40] INFO  NotificationService - [main] INFO  c.s.polarion.service.notification.Application - Starting Application using Java 11.0.27 on zhangwentiandeMac-mini-2.local with PID 54742 (/opt/polarion/polarion/plugins/com.polarion.platform_3.22.1/services/notification-service/NotificationService.jar started by zhangwentian in /private/var/folders/z_/shw6wc7d7ps_fjvv781t4gt80000gn/T/polarionSpringServerSubprocess13446181187911058271.tmp)
2025-07-29 18:33:42,468 [Thread-40] INFO  NotificationService - [main] INFO  c.s.polarion.service.notification.Application - No active profile set, falling back to 1 default profile: "default"
2025-07-29 18:33:42,574 [Activities-Bulk-Publisher] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Activities
2025-07-29 18:33:42,669 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  com.polarion.subterra.index.impl.ObjectIndex - Preloading of baselines: 22, for prototypes: WorkItem; with days range: 180d, took  [ TIME 0.737 s. ] 
2025-07-29 18:33:42,670 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.754 s, CPU [user: 0.00514 s, system: 0.00237 s], Allocated memory: 531.2 kB, transactions: 1
2025-07-29 18:33:42,671 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 27, resolve: 0.108 s [92% User (2x)] (4x), persistence listener: 0.0379 s [90% indexRefreshPersistenceListener (1x)] (7x), notification worker: 0.0319 s [76% RevisionActivityCreator (2x), 12% WorkItemActivityCreator (1x)] (5x), Incremental Baseline: 0.0265 s [100% WorkItem (21x)] (21x), ObjectMaps: 0.0188 s [100% getPrimaryObjectLocation (2x)] (2x), Lucene: 0.0122 s [59% add (1x), 41% refresh (1x)] (2x)
2025-07-29 18:33:42,671 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.757 s, CPU [user: 0.158 s, system: 0.0334 s], Allocated memory: 18.5 MB, transactions: 23, svn: 0.665 s [99% getDatedRevision (181x)] (183x), Lucene: 0.0559 s [81% buildBaselineSnapshots (1x)] (23x)
2025-07-29 18:33:42,672 [Worker-1: DB History Creator | u:p] INFO  com.polarion.platform.jobs.info - State of job "DB History Creator" changed to FINISHED
2025-07-29 18:33:42,673 [Worker-1: DB History Creator | u:p] INFO  com.polarion.platform.jobs.info - Status of job "DB History Creator" is OK
2025-07-29 18:33:42,987 [Thread-40] INFO  NotificationService - [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 40608 (http)
2025-07-29 18:33:42,993 [Thread-40] INFO  NotificationService - [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-40608"]
2025-07-29 18:33:42,993 [Thread-40] INFO  NotificationService - [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-29 18:33:42,993 [Thread-40] INFO  NotificationService - [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-07-29 18:33:43,013 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.ObjectIndex - Preloading of baselines: 22, for prototypes: WorkItem; with days range: 180d, took  [ TIME 0.344 s. ] 
2025-07-29 18:33:43,028 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66156fb0a8045_0_66156fb0a8045_0_: finished. Total: 1.24 s, CPU [user: 0.352 s, system: 0.103 s], Allocated memory: 55.1 MB, svn: 0.668 s [50% getDatedRevision (181x), 32% getDir2 content (25x)] (307x), resolve: 0.411 s [100% Category (96x)] (96x), ObjectMaps: 0.143 s [41% getPrimaryObjectProperty (96x), 36% getPrimaryObjectLocation (96x), 23% getLastPromoted (96x)] (388x)
2025-07-29 18:33:43,031 [Thread-40] INFO  NotificationService - [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-29 18:33:43,031 [Thread-40] INFO  NotificationService - [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 523 ms
2025-07-29 18:33:43,033 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: ProjectGroup
2025-07-29 18:33:43,037 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: ProjectGroup
2025-07-29 18:33:43,052 [Thread-40] INFO  NotificationService - [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing AtmosphereFramework
2025-07-29 18:33:43,062 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Project
2025-07-29 18:33:43,066 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Project
2025-07-29 18:33:43,113 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: User
2025-07-29 18:33:43,116 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: User
2025-07-29 18:33:43,216 [Thread-40] INFO  NotificationService - [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-40608"]
2025-07-29 18:33:43,221 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66156fb1f2048_0_66156fb1f2048_0_: finished. Total: 0.109 s, CPU [user: 0.0504 s, system: 0.0122 s], Allocated memory: 8.4 MB, RepositoryConfigService: 0.0455 s [58% getReadUserConfiguration (10x), 42% getReadConfiguration (162x)] (172x), svn: 0.0394 s [57% info (19x), 36% getFile content (16x)] (37x), resolve: 0.0346 s [100% User (9x)] (9x), ObjectMaps: 0.0185 s [42% getPrimaryObjectLocation (9x), 41% getPrimaryObjectProperty (9x)] (37x)
2025-07-29 18:33:43,250 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Atmosphere is using org.atmosphere.cpr.DefaultAnnotationProcessor for processing annotation
2025-07-29 18:33:43,250 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.DefaultAnnotationProcessor - AnnotationProcessor class org.atmosphere.cpr.DefaultAnnotationProcessor$BytecodeBasedAnnotationProcessor being used
2025-07-29 18:33:43,268 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AnnotationHandler - Found Annotation in class com.siemens.polarion.service.notification.NotificationService being scanned: interface org.atmosphere.config.service.ManagedService
2025-07-29 18:33:43,271 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.annotation.AnnotationUtil - Adding class org.atmosphere.interceptor.AtmosphereResourceLifecycleInterceptor
2025-07-29 18:33:43,274 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.annotation.AnnotationUtil - Adding class org.atmosphere.client.TrackMessageSizeInterceptor
2025-07-29 18:33:43,275 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.annotation.AnnotationUtil - Adding class org.atmosphere.interceptor.SuspendTrackerInterceptor
2025-07-29 18:33:43,278 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.annotation.AnnotationUtil - Adding class org.atmosphere.config.managed.ManagedServiceInterceptor
2025-07-29 18:33:43,292 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.annotation.AnnotationUtil - Adding class com.siemens.polarion.service.notification.JwtVerificationInterceptor
2025-07-29 18:33:43,301 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.util.ForkJoinPool - Using ForkJoinPool  java.util.concurrent.ForkJoinPool. Set the org.atmosphere.cpr.broadcaster.maxAsyncWriteThreads to -1 to fully use its power.
2025-07-29 18:33:43,356 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66156fb20d449_0_66156fb20d449_0_: finished. Total: 0.119 s, CPU [user: 0.0307 s, system: 0.00518 s], Allocated memory: 3.8 MB, RepositoryConfigService: 0.0483 s [98% getReadConfiguration (54x)] (77x), svn: 0.0196 s [98% getFile content (12x)] (13x)
2025-07-29 18:33:43,431 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereHandler org.atmosphere.config.managed.ManagedAtmosphereHandler mapped to context-path /notification and Broadcaster Class org.atmosphere.cpr.DefaultBroadcaster
2025-07-29 18:33:43,431 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor [Atmosphere LifeCycle,  Track Message Size Interceptor using |, UUID Tracking Interceptor, @ManagedService Interceptor, @Service Event Listeners, com.siemens.polarion.service.notification.JwtVerificationInterceptor] mapped to AtmosphereHandler org.atmosphere.config.managed.ManagedAtmosphereHandler
2025-07-29 18:33:43,450 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Auto detecting WebSocketHandler in /WEB-INF/classes/
2025-07-29 18:33:43,458 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed WebSocketProtocol org.atmosphere.websocket.protocol.SimpleHttpProtocol 
2025-07-29 18:33:43,484 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.container.JSR356AsyncSupport - JSR 356 Mapping path /notification
2025-07-29 18:33:43,527 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installing Default AtmosphereInterceptors
2025-07-29 18:33:43,527 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.CorsInterceptor : CORS Interceptor Support
2025-07-29 18:33:43,527 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.CacheHeadersInterceptor : Default Response's Headers Interceptor
2025-07-29 18:33:43,531 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.PaddingAtmosphereInterceptor : Browser Padding Interceptor Support
2025-07-29 18:33:43,531 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.AndroidAtmosphereInterceptor : Android Interceptor Support
2025-07-29 18:33:43,532 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.HeartbeatInterceptor : Heartbeat Interceptor Support
2025-07-29 18:33:43,533 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.SSEAtmosphereInterceptor : SSE Interceptor Support
2025-07-29 18:33:43,533 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.JSONPAtmosphereInterceptor : JSONP Interceptor Support
2025-07-29 18:33:43,534 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.JavaScriptProtocol : Atmosphere JavaScript Protocol
2025-07-29 18:33:43,534 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.WebSocketMessageSuspendInterceptor : org.atmosphere.interceptor.WebSocketMessageSuspendInterceptor
2025-07-29 18:33:43,536 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.OnDisconnectInterceptor : Browser disconnection detection
2025-07-29 18:33:43,537 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.IdleResourceInterceptor : org.atmosphere.interceptor.IdleResourceInterceptor
2025-07-29 18:33:43,538 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Set org.atmosphere.cpr.AtmosphereInterceptor.disableDefaults to disable them.
2025-07-29 18:33:43,538 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor CORS Interceptor Support with priority FIRST_BEFORE_DEFAULT 
2025-07-29 18:33:43,540 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor Default Response's Headers Interceptor with priority AFTER_DEFAULT 
2025-07-29 18:33:43,540 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor Browser Padding Interceptor Support with priority AFTER_DEFAULT 
2025-07-29 18:33:43,540 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor Android Interceptor Support with priority AFTER_DEFAULT 
2025-07-29 18:33:43,541 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.interceptor.HeartbeatInterceptor - HeartbeatInterceptor configured with padding value 'X', client frequency 30 seconds and server frequency 120 seconds
2025-07-29 18:33:43,541 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor Heartbeat Interceptor Support with priority AFTER_DEFAULT 
2025-07-29 18:33:43,541 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor SSE Interceptor Support with priority AFTER_DEFAULT 
2025-07-29 18:33:43,541 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor JSONP Interceptor Support with priority AFTER_DEFAULT 
2025-07-29 18:33:43,541 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor Atmosphere JavaScript Protocol with priority AFTER_DEFAULT 
2025-07-29 18:33:43,541 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor org.atmosphere.interceptor.WebSocketMessageSuspendInterceptor with priority AFTER_DEFAULT 
2025-07-29 18:33:43,541 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor Browser disconnection detection with priority AFTER_DEFAULT 
2025-07-29 18:33:43,541 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor org.atmosphere.interceptor.IdleResourceInterceptor with priority BEFORE_DEFAULT 
2025-07-29 18:33:43,556 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Using EndpointMapper class org.atmosphere.util.DefaultEndpointMapper
2025-07-29 18:33:43,556 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Using BroadcasterCache: org.atmosphere.cache.UUIDBroadcasterCache
2025-07-29 18:33:43,556 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Default Broadcaster Class: org.atmosphere.cpr.DefaultBroadcaster
2025-07-29 18:33:43,556 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Broadcaster Shared List Resources: false
2025-07-29 18:33:43,556 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Broadcaster Polling Wait Time 100
2025-07-29 18:33:43,556 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Shared ExecutorService supported: true
2025-07-29 18:33:43,556 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Messaging ExecutorService Pool Size unavailable - Not instance of ThreadPoolExecutor
2025-07-29 18:33:43,556 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Async I/O Thread Pool Size: 200
2025-07-29 18:33:43,556 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Using BroadcasterFactory: org.atmosphere.cpr.DefaultBroadcasterFactory
2025-07-29 18:33:43,556 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Using AtmosphereResurceFactory: org.atmosphere.cpr.DefaultAtmosphereResourceFactory
2025-07-29 18:33:43,557 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Using WebSocketProcessor: org.atmosphere.websocket.DefaultWebSocketProcessor
2025-07-29 18:33:43,563 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Invoke AtmosphereInterceptor on WebSocket message true
2025-07-29 18:33:43,563 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - HttpSession supported: false
2025-07-29 18:33:43,563 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Atmosphere is using Spring Web ObjectFactory for dependency injection and object creation
2025-07-29 18:33:43,563 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Atmosphere is using async support: org.atmosphere.container.JSR356AsyncSupport running under container: Apache Tomcat/9.0.60 using javax.servlet/3.0 and jsr356/WebSocket API
2025-07-29 18:33:43,563 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Atmosphere Framework 2.6.4 started.
2025-07-29 18:33:43,563 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 
2025-07-29 18:33:43,563 [Thread-40] INFO  NotificationService - 
2025-07-29 18:33:43,563 [Thread-40] INFO  NotificationService - 	For Atmosphere Framework Commercial Support, visit 
2025-07-29 18:33:43,563 [Thread-40] INFO  NotificationService - 	http://www.async-io.org/ or send an <NAME_EMAIL>
2025-07-29 18:33:43,563 [Thread-40] INFO  NotificationService - 
2025-07-29 18:33:43,568 [Thread-40] INFO  NotificationService - [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 40608 (http) with context path ''
2025-07-29 18:33:43,581 [Thread-40] INFO  NotificationService - [main] INFO  c.s.polarion.service.notification.Application - Started Application in 1.437 seconds (JVM running for 1.792)
2025-07-29 18:33:43,752 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testtype) created
2025-07-29 18:33:43,759 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (subtype) created
2025-07-29 18:33:43,766 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66156fb22f44a_0_66156fb22f44a_0_: finished. Total: 0.407 s, CPU [user: 0.0698 s, system: 0.0163 s], Allocated memory: 19.9 MB, svn: 0.355 s [83% getDir2 content (17x)] (62x), RepositoryConfigService: 0.0928 s [98% getReadConfiguration (170x)] (192x)
2025-07-29 18:33:44,099 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (YesNo) created
2025-07-29 18:33:44,107 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (software_VerificationMethod) created
2025-07-29 18:33:44,111 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (checklist) created
2025-07-29 18:33:44,115 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (commonreqproperty) created
2025-07-29 18:33:44,120 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (objectoriented) created
2025-07-29 18:33:44,121 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (submodule) created
2025-07-29 18:33:44,122 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (yesno) created
2025-07-29 18:33:44,123 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (cICategory) created
2025-07-29 18:33:44,123 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (wpFormat) created
2025-07-29 18:33:44,125 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (trigger) created
2025-07-29 18:33:44,133 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (ASILLevel) created
2025-07-29 18:33:44,134 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (CSRelated) created
2025-07-29 18:33:44,142 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (project_Module) created
2025-07-29 18:33:44,154 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (规格对象类型) created
2025-07-29 18:33:44,157 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (jenkins_job) created
2025-07-29 18:33:44,157 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (truefalse) created
2025-07-29 18:33:44,158 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (takeOnGroups) created
2025-07-29 18:33:44,166 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testcasetype) created
2025-07-29 18:33:44,170 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (processGroup) created
2025-07-29 18:33:44,173 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (changeManagement) created
2025-07-29 18:33:44,180 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (seriousness) created
2025-07-29 18:33:44,184 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (softReqClass) created
2025-07-29 18:33:44,189 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (SWDetailDesign) created
2025-07-29 18:33:44,191 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (PhaseChecklists) created
2025-07-29 18:33:44,194 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (PassNotpass) created
2025-07-29 18:33:44,198 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (baseLineType) created
2025-07-29 18:33:44,200 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (boolYesOrNo) created
2025-07-29 18:33:44,202 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testlevel) created
2025-07-29 18:33:44,206 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (source) created
2025-07-29 18:33:44,211 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (objectType) created
2025-07-29 18:33:44,215 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (atppblversion) created
2025-07-29 18:33:44,218 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (aSIL) created
2025-07-29 18:33:44,223 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (EE) created
2025-07-29 18:33:44,226 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (issueType) created
2025-07-29 18:33:44,232 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (SYS_reqClassification) created
2025-07-29 18:33:44,236 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (oem_2Status) created
2025-07-29 18:33:44,240 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (class) created
2025-07-29 18:33:44,243 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (promotionState) created
2025-07-29 18:33:44,247 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (git_project) created
2025-07-29 18:33:44,254 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (storageType) created
2025-07-29 18:33:44,257 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (issueproperty) created
2025-07-29 18:33:44,262 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (commonissueclass) created
2025-07-29 18:33:44,265 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (AgreeDisagree) created
2025-07-29 18:33:44,268 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (SA_Category) created
2025-07-29 18:33:44,272 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (relevance) created
2025-07-29 18:33:44,278 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (implementationPhase) created
2025-07-29 18:33:44,281 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (supplier_2Status) created
2025-07-29 18:33:44,290 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Testtype) created
2025-07-29 18:33:44,294 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (conf_baselineTime) created
2025-07-29 18:33:44,302 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (levelneed) created
2025-07-29 18:33:44,304 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (finalresult) created
2025-07-29 18:33:44,307 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testability) created
2025-07-29 18:33:44,313 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (solution) created
2025-07-29 18:33:44,316 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Responsible) created
2025-07-29 18:33:44,318 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (verificationstatus) created
2025-07-29 18:33:44,342 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (hsiassigngroup) created
2025-07-29 18:33:44,346 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (reqCategory) created
2025-07-29 18:33:44,349 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (baseLineName) created
2025-07-29 18:33:44,352 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (taskType) created
2025-07-29 18:33:44,366 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (changeReason) created
2025-07-29 18:33:44,368 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (objectmodule) created
2025-07-29 18:33:44,373 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (TestCaseOutputMethod) created
2025-07-29 18:33:44,378 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (SoftwareFeature) created
2025-07-29 18:33:44,386 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (ResponsibleGroup) created
2025-07-29 18:33:44,390 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (hsifunctionmodule) created
2025-07-29 18:33:44,394 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (FwReqSource) created
2025-07-29 18:33:44,398 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (occurPhase) created
2025-07-29 18:33:44,400 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (compiletask) created
2025-07-29 18:33:44,404 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (REQBVerificationMethod) created
2025-07-29 18:33:44,407 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (functionmodule) created
2025-07-29 18:33:44,411 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (variant) created
2025-07-29 18:33:44,412 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Fusatype) created
2025-07-29 18:33:44,415 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (hardwareversion) created
2025-07-29 18:33:44,420 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (appversion) created
2025-07-29 18:33:44,422 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (casefirstmodule) created
2025-07-29 18:33:44,426 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (auditType) created
2025-07-29 18:33:44,432 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Samplestage) created
2025-07-29 18:33:44,436 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (casesecondmodule) created
2025-07-29 18:33:44,438 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (issue_source) created
2025-07-29 18:33:44,442 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (ifNeedRegressionTesting) created
2025-07-29 18:33:44,446 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (atpsfsversion) created
2025-07-29 18:33:44,449 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (CustomerAllocation) created
2025-07-29 18:33:44,453 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (issuesubclass) created
2025-07-29 18:33:44,456 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (QANC_importance) created
2025-07-29 18:33:44,460 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (reviewMethod) created
2025-07-29 18:33:44,476 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (QANC_findType) created
2025-07-29 18:33:44,482 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (editType) created
2025-07-29 18:33:44,490 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testingobjects) created
2025-07-29 18:33:44,494 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testcaselevel) created
2025-07-29 18:33:44,499 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (supplierproblem) created
2025-07-29 18:33:44,502 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (reqattribute) created
2025-07-29 18:33:44,505 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (fsigroup) created
2025-07-29 18:33:44,509 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (project_reqsource) created
2025-07-29 18:33:44,514 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (preset) created
2025-07-29 18:33:44,518 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Mechverificationmethod) created
2025-07-29 18:33:44,521 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (CPMToTPM) created
2025-07-29 18:33:44,525 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (REQBType) created
2025-07-29 18:33:44,531 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testcasesign) created
2025-07-29 18:33:44,536 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (verificationphase) created
2025-07-29 18:33:44,547 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (processArea) created
2025-07-29 18:33:44,550 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (artifactType) created
2025-07-29 18:33:44,558 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Classification) created
2025-07-29 18:33:44,562 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (verificationmethod) created
2025-07-29 18:33:44,565 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (changeType) created
2025-07-29 18:33:44,567 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (hardwareAndSoftwareSubType) created
2025-07-29 18:33:44,570 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (SWIntegrationVerificationMethod) created
2025-07-29 18:33:44,573 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (category) created
2025-07-29 18:33:44,587 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (REQBCategory) created
2025-07-29 18:33:44,597 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (softreqclass) created
2025-07-29 18:33:44,600 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (TestMethod) created
2025-07-29 18:33:44,603 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (reType) created
2025-07-29 18:33:44,606 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (VerificationCriteria) created
2025-07-29 18:33:44,610 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (baseLinechecklist) created
2025-07-29 18:33:44,613 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Category) created
2025-07-29 18:33:44,618 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (SWUnitTestDerivingMethods) created
2025-07-29 18:33:44,620 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (firmware_Category) created
2025-07-29 18:33:44,622 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testMethod) created
2025-07-29 18:33:44,632 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (QAPorcessAreas) created
2025-07-29 18:33:44,635 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (findSource) created
2025-07-29 18:33:44,640 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66156fb29584b_0_66156fb29584b_0_: finished. Total: 0.874 s, CPU [user: 0.358 s, system: 0.0337 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.66 s [98% getReadConfiguration (8682x)] (9021x), svn: 0.46 s [65% getFile content (412x), 35% getDir2 content (21x)] (434x), GC: 0.065 s [100% G1 Young Generation (4x)] (4x)
2025-07-29 18:33:44,717 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (tshirt-sizes) created
2025-07-29 18:33:44,719 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (reqtype) created
2025-07-29 18:33:44,864 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Feasibility) created
2025-07-29 18:33:44,866 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (locaMod) created
2025-07-29 18:33:44,867 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (unitTestCaseType) created
2025-07-29 18:33:44,868 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (ControlLevel) created
2025-07-29 18:33:44,869 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (NCitemSev) created
2025-07-29 18:33:44,870 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (signType) created
2025-07-29 18:33:44,873 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (WBSCategory) created
2025-07-29 18:33:44,875 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (swTestCaseEnv) created
2025-07-29 18:33:44,876 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (verifiability) created
2025-07-29 18:33:44,878 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (@ProjectUser) created
2025-07-29 18:33:44,881 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (standardReq) created
2025-07-29 18:33:44,882 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (statusa) created
2025-07-29 18:33:44,884 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (@WorkItems[type:configurationitemversion]) created
2025-07-29 18:33:44,884 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (CIRevisionStatus) created
2025-07-29 18:33:44,887 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (dogTimeout) created
2025-07-29 18:33:44,888 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (REQCategory) created
2025-07-29 18:33:44,891 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (proStage) created
2025-07-29 18:33:44,897 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (BaselineType) created
2025-07-29 18:33:44,899 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (custConfStat) created
2025-07-29 18:33:44,900 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (sofReqVer) created
2025-07-29 18:33:44,903 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Source) created
2025-07-29 18:33:44,904 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (scCategory) created
2025-07-29 18:33:44,905 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (softTestCaseType) created
2025-07-29 18:33:44,906 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (solAdv) created
2025-07-29 18:33:44,908 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (unitTestCaseMet) created
2025-07-29 18:33:44,909 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (sysTestCaseMeth) created
2025-07-29 18:33:44,910 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (softTestCaseMe) created
2025-07-29 18:33:44,911 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (softTestCaseEnv) created
2025-07-29 18:33:44,915 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (auditTarget) created
2025-07-29 18:33:44,916 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (@ReviewForm) created
2025-07-29 18:33:44,917 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (swTestCaseType) created
2025-07-29 18:33:44,918 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (@Collection) created
2025-07-29 18:33:44,919 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (submissionStage) created
2025-07-29 18:33:44,920 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (sysTestCaseMet) created
2025-07-29 18:33:44,921 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (demandType) created
2025-07-29 18:33:44,922 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (swTestCaseMet) created
2025-07-29 18:33:44,923 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (taskUrgen) created
2025-07-29 18:33:44,925 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (solveMethod) created
2025-07-29 18:33:44,926 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (audMethod) created
2025-07-29 18:33:44,927 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (desStat) created
2025-07-29 18:33:44,929 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (scType) created
2025-07-29 18:33:44,930 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (sysTestCaseType) created
2025-07-29 18:33:44,932 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (releaseType) created
2025-07-29 18:33:44,934 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (unitTestCaseEnv) created
2025-07-29 18:33:44,935 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (targetStage) created
2025-07-29 18:33:44,936 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (ClassificationType) created
2025-07-29 18:33:44,937 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testItem) created
2025-07-29 18:33:44,939 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (InfoSecurity) created
2025-07-29 18:33:44,940 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Verification) created
2025-07-29 18:33:44,941 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (triggerMod) created
2025-07-29 18:33:44,942 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (verMethod) created
2025-07-29 18:33:44,944 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (diagramCategory) created
2025-07-29 18:33:44,945 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (assSubsystem) created
2025-07-29 18:33:44,947 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (OccurrenceProbability) created
2025-07-29 18:33:44,949 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (developmentMethod) created
2025-07-29 18:33:44,951 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (portType) created
2025-07-29 18:33:44,953 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (checkType) created
2025-07-29 18:33:44,955 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (demandStatus) created
2025-07-29 18:33:44,956 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (important) created
2025-07-29 18:33:44,958 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (triggerMec) created
2025-07-29 18:33:44,959 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (sysTestCaseTy) created
2025-07-29 18:33:44,960 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (recentPre) created
2025-07-29 18:33:44,962 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (TestCaseDesignMethod) created
2025-07-29 18:33:44,963 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testCasePri) created
2025-07-29 18:33:44,966 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (relObj) created
2025-07-29 18:33:44,967 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (proSer) created
2025-07-29 18:33:44,969 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (TestProblemType) created
2025-07-29 18:33:44,970 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (chipName) created
2025-07-29 18:33:44,971 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (auditTiming) created
2025-07-29 18:33:44,974 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66156fb38b04e_0_66156fb38b04e_0_: finished. Total: 0.226 s, CPU [user: 0.0939 s, system: 0.00469 s], Allocated memory: 384.1 MB, RepositoryConfigService: 0.143 s [97% getReadConfiguration (2787x)] (3025x), svn: 0.141 s [51% getFile content (185x), 49% getDir2 content (20x)] (206x)
2025-07-29 18:33:44,974 [PreLoadDataService] INFO  com.polarion.psvn.launcher.internal.data.PreLoadDataService - Preloading data FINISHED took  [ TIME 3.19 s. ]
2025-07-29 18:33:44,974 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 3.19 s, CPU [user: 1.01 s, system: 0.186 s], Allocated memory: 1.6 GB, transactions: 10, svn: 1.81 s [46% getDir2 content (114x), 33% getFile content (809x), 18% getDatedRevision (181x)] (1144x), RepositoryConfigService: 1.03 s [95% getReadConfiguration (12019x)] (12691x), resolve: 0.489 s [84% Category (96x)] (117x), ObjectMaps: 0.175 s [43% getPrimaryObjectProperty (110x), 36% getPrimaryObjectLocation (116x), 22% getLastPromoted (110x)] (452x)
2025-07-29 18:33:44,974 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 55, svn: 2.48 s [40% getDatedRevision (362x), 33% getDir2 content (114x), 24% getFile content (809x)] (1328x), RepositoryConfigService: 1.03 s [95% getReadConfiguration (12019x)] (12691x), resolve: 0.489 s [84% Category (96x)] (118x), ObjectMaps: 0.175 s [43% getPrimaryObjectProperty (110x), 36% getPrimaryObjectLocation (116x), 22% getLastPromoted (110x)] (452x)
2025-07-29 18:33:47,097 [ajp-nio-127.0.0.1-8889-exec-4 | cID:55bed756-c0a8d700-1acc4c5a-b849dda3] DEBUG com.fasnote.alm.auth.feishu.feishuAuthenticatorManagerInterceptorFactory - Constructing core service implementation for service com.fasnote.alm.auth.feishu.feishuAuthenticatorManagerInterceptorFactory
2025-07-29 18:33:47,099 [ajp-nio-127.0.0.1-8889-exec-4 | cID:55bed756-c0a8d700-1acc4c5a-b849dda3] FATAL org.apache.catalina.core.ContainerBase.[Tomcat].[localhost] - Exception Processing /polarion/svnwebclient/
org.apache.hivemind.ApplicationRuntimeException: Unable to construct service com.polarion.platform.authenticatorProviderManager: Unable to construct service com.fasnote.alm.auth.feishu.feishuAuthenticatorManagerInterceptorFactory: Error building service com.fasnote.alm.auth.feishu.feishuAuthenticatorManagerInterceptorFactory: Unable to convert type 'com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory' to a Java class, either as is, or in package com.fasnote.alm.auth.feishu.
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructNewServiceImplementation(AbstractServiceModelImpl.java:163) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructServiceImplementation(AbstractServiceModelImpl.java:141) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.SingletonServiceModel.getActualServiceImplementation(SingletonServiceModel.java:69) ~[hivemind-1.1.1.jar:?]
	at $IAuthenticatorManager_19855a74bbc._service($IAuthenticatorManager_19855a74bbc.java) ~[?:?]
	at $IAuthenticatorManager_19855a74bbc.getAuthenticators($IAuthenticatorManager_19855a74bbc.java) ~[?:?]
	at $IAuthenticatorManager_19855a74bbb.getAuthenticators($IAuthenticatorManager_19855a74bbb.java) ~[?:?]
	at com.polarion.platform.security.auth.impl.ContributedPolarionAuthenticator.isExpectedCallback(ContributedPolarionAuthenticator.java:33) ~[platform.jar:?]
	at com.polarion.platform.security.auth.PolarionAuthenticator.authenticateImpl(PolarionAuthenticator.java:127) ~[platform.jar:?]
	at com.polarion.platform.security.auth.PolarionAuthenticator.doAuthenticate(PolarionAuthenticator.java:105) ~[platform.jar:?]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:624) ~[catalina.jar:9.0.53]
	at com.polarion.platform.security.auth.PolarionAuthenticator.invokeInternal(PolarionAuthenticator.java:248) ~[platform.jar:?]
	at com.polarion.platform.security.auth.PolarionAuthenticator.invoke(PolarionAuthenticator.java:242) ~[platform.jar:?]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) [catalina.jar:9.0.53]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.SingleSignOn.invoke(SingleSignOn.java:312) [catalina.jar:9.0.53]
	at com.polarion.platform.session.PolarionLocalSingleSignOn.invoke(PolarionLocalSingleSignOn.java:164) [platform.jar:?]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) [catalina.jar:9.0.53]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357) [catalina.jar:9.0.53]
	at org.apache.coyote.ajp.AjpProcessor.service(AjpProcessor.java:433) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1726) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-util.jar:9.0.53]
	at java.lang.Thread.run(Thread.java:829) [?:?]
Caused by: org.apache.hivemind.ApplicationRuntimeException: Unable to construct service com.fasnote.alm.auth.feishu.feishuAuthenticatorManagerInterceptorFactory: Error building service com.fasnote.alm.auth.feishu.feishuAuthenticatorManagerInterceptorFactory: Unable to convert type 'com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory' to a Java class, either as is, or in package com.fasnote.alm.auth.feishu.
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructNewServiceImplementation(AbstractServiceModelImpl.java:163) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructServiceImplementation(AbstractServiceModelImpl.java:141) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.SingletonServiceModel.getActualServiceImplementation(SingletonServiceModel.java:69) ~[hivemind-1.1.1.jar:?]
	at $ServiceInterceptorFactory_19855a74bce._service($ServiceInterceptorFactory_19855a74bce.java) ~[?:?]
	at $ServiceInterceptorFactory_19855a74bce.createInterceptor($ServiceInterceptorFactory_19855a74bce.java) ~[?:?]
	at $ServiceInterceptorFactory_19855a74bcc.createInterceptor($ServiceInterceptorFactory_19855a74bcc.java) ~[?:?]
	at org.apache.hivemind.impl.ServiceInterceptorContributionImpl.createInterceptor(ServiceInterceptorContributionImpl.java:95) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.InterceptorStackImpl.process(InterceptorStackImpl.java:116) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.addInterceptors(AbstractServiceModelImpl.java:85) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructNewServiceImplementation(AbstractServiceModelImpl.java:159) ~[polarion-hivemind.jar:?]
	... 26 more
Caused by: org.apache.hivemind.ApplicationRuntimeException: Error building service com.fasnote.alm.auth.feishu.feishuAuthenticatorManagerInterceptorFactory: Unable to convert type 'com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory' to a Java class, either as is, or in package com.fasnote.alm.auth.feishu.
	at org.apache.hivemind.impl.InvokeFactoryServiceConstructor.constructCoreServiceImplementation(InvokeFactoryServiceConstructor.java:66) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructCoreServiceImplementation(AbstractServiceModelImpl.java:108) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructNewServiceImplementation(AbstractServiceModelImpl.java:157) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructServiceImplementation(AbstractServiceModelImpl.java:141) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.SingletonServiceModel.getActualServiceImplementation(SingletonServiceModel.java:69) ~[hivemind-1.1.1.jar:?]
	at $ServiceInterceptorFactory_19855a74bce._service($ServiceInterceptorFactory_19855a74bce.java) ~[?:?]
	at $ServiceInterceptorFactory_19855a74bce.createInterceptor($ServiceInterceptorFactory_19855a74bce.java) ~[?:?]
	at $ServiceInterceptorFactory_19855a74bcc.createInterceptor($ServiceInterceptorFactory_19855a74bcc.java) ~[?:?]
	at org.apache.hivemind.impl.ServiceInterceptorContributionImpl.createInterceptor(ServiceInterceptorContributionImpl.java:95) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.InterceptorStackImpl.process(InterceptorStackImpl.java:116) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.addInterceptors(AbstractServiceModelImpl.java:85) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructNewServiceImplementation(AbstractServiceModelImpl.java:159) ~[polarion-hivemind.jar:?]
	... 26 more
Caused by: org.apache.hivemind.ApplicationRuntimeException: Error building service com.fasnote.alm.auth.feishu.feishuAuthenticatorManagerInterceptorFactory: Unable to convert type 'com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory' to a Java class, either as is, or in package com.fasnote.alm.auth.feishu.
	at org.apache.hivemind.service.impl.BuilderFactoryLogic.createService(BuilderFactoryLogic.java:87) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.service.impl.BuilderFactory.createCoreServiceImplementation(BuilderFactory.java:42) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.InvokeFactoryServiceConstructor.constructCoreServiceImplementation(InvokeFactoryServiceConstructor.java:62) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructCoreServiceImplementation(AbstractServiceModelImpl.java:108) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructNewServiceImplementation(AbstractServiceModelImpl.java:157) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructServiceImplementation(AbstractServiceModelImpl.java:141) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.SingletonServiceModel.getActualServiceImplementation(SingletonServiceModel.java:69) ~[hivemind-1.1.1.jar:?]
	at $ServiceInterceptorFactory_19855a74bce._service($ServiceInterceptorFactory_19855a74bce.java) ~[?:?]
	at $ServiceInterceptorFactory_19855a74bce.createInterceptor($ServiceInterceptorFactory_19855a74bce.java) ~[?:?]
	at $ServiceInterceptorFactory_19855a74bcc.createInterceptor($ServiceInterceptorFactory_19855a74bcc.java) ~[?:?]
	at org.apache.hivemind.impl.ServiceInterceptorContributionImpl.createInterceptor(ServiceInterceptorContributionImpl.java:95) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.InterceptorStackImpl.process(InterceptorStackImpl.java:116) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.addInterceptors(AbstractServiceModelImpl.java:85) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructNewServiceImplementation(AbstractServiceModelImpl.java:159) ~[polarion-hivemind.jar:?]
	... 26 more
Caused by: org.apache.hivemind.ApplicationRuntimeException: Unable to convert type 'com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory' to a Java class, either as is, or in package com.fasnote.alm.auth.feishu.
	at org.apache.hivemind.impl.ModuleImpl.findTypeInClassResolver(ModuleImpl.java:219) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.ModuleImpl.resolveType(ModuleImpl.java:203) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.service.impl.BuilderFactoryLogic.instantiateCoreServiceInstance(BuilderFactoryLogic.java:100) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.service.impl.BuilderFactoryLogic.createService(BuilderFactoryLogic.java:75) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.service.impl.BuilderFactory.createCoreServiceImplementation(BuilderFactory.java:42) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.InvokeFactoryServiceConstructor.constructCoreServiceImplementation(InvokeFactoryServiceConstructor.java:62) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructCoreServiceImplementation(AbstractServiceModelImpl.java:108) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructNewServiceImplementation(AbstractServiceModelImpl.java:157) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructServiceImplementation(AbstractServiceModelImpl.java:141) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.SingletonServiceModel.getActualServiceImplementation(SingletonServiceModel.java:69) ~[hivemind-1.1.1.jar:?]
	at $ServiceInterceptorFactory_19855a74bce._service($ServiceInterceptorFactory_19855a74bce.java) ~[?:?]
	at $ServiceInterceptorFactory_19855a74bce.createInterceptor($ServiceInterceptorFactory_19855a74bce.java) ~[?:?]
	at $ServiceInterceptorFactory_19855a74bcc.createInterceptor($ServiceInterceptorFactory_19855a74bcc.java) ~[?:?]
	at org.apache.hivemind.impl.ServiceInterceptorContributionImpl.createInterceptor(ServiceInterceptorContributionImpl.java:95) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.InterceptorStackImpl.process(InterceptorStackImpl.java:116) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.addInterceptors(AbstractServiceModelImpl.java:85) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructNewServiceImplementation(AbstractServiceModelImpl.java:159) ~[polarion-hivemind.jar:?]
	... 26 more
2025-07-29 18:33:49,721 [ajp-nio-127.0.0.1-8889-exec-6 | cID:55bee197-c0a8d700-1acc4c5a-b5da5f3b] DEBUG com.fasnote.alm.auth.feishu.feishuAuthenticatorManagerInterceptorFactory - Constructing core service implementation for service com.fasnote.alm.auth.feishu.feishuAuthenticatorManagerInterceptorFactory
2025-07-29 18:33:49,722 [ajp-nio-127.0.0.1-8889-exec-6 | cID:55bee197-c0a8d700-1acc4c5a-b5da5f3b] FATAL org.apache.catalina.core.ContainerBase.[Tomcat].[localhost] - Exception Processing /polarion/svnwebclient/
org.apache.hivemind.ApplicationRuntimeException: Unable to construct service com.polarion.platform.authenticatorProviderManager: Unable to construct service com.fasnote.alm.auth.feishu.feishuAuthenticatorManagerInterceptorFactory: Error building service com.fasnote.alm.auth.feishu.feishuAuthenticatorManagerInterceptorFactory: Unable to convert type 'com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory' to a Java class, either as is, or in package com.fasnote.alm.auth.feishu.
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructNewServiceImplementation(AbstractServiceModelImpl.java:163) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructServiceImplementation(AbstractServiceModelImpl.java:141) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.SingletonServiceModel.getActualServiceImplementation(SingletonServiceModel.java:69) ~[hivemind-1.1.1.jar:?]
	at $IAuthenticatorManager_19855a74bbc._service($IAuthenticatorManager_19855a74bbc.java) ~[?:?]
	at $IAuthenticatorManager_19855a74bbc.getAuthenticators($IAuthenticatorManager_19855a74bbc.java) ~[?:?]
	at $IAuthenticatorManager_19855a74bbb.getAuthenticators($IAuthenticatorManager_19855a74bbb.java) ~[?:?]
	at com.polarion.platform.security.auth.impl.ContributedPolarionAuthenticator.isExpectedCallback(ContributedPolarionAuthenticator.java:33) ~[platform.jar:?]
	at com.polarion.platform.security.auth.PolarionAuthenticator.authenticateImpl(PolarionAuthenticator.java:127) ~[platform.jar:?]
	at com.polarion.platform.security.auth.PolarionAuthenticator.doAuthenticate(PolarionAuthenticator.java:105) ~[platform.jar:?]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:624) ~[catalina.jar:9.0.53]
	at com.polarion.platform.security.auth.PolarionAuthenticator.invokeInternal(PolarionAuthenticator.java:248) ~[platform.jar:?]
	at com.polarion.platform.security.auth.PolarionAuthenticator.invoke(PolarionAuthenticator.java:242) ~[platform.jar:?]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) [catalina.jar:9.0.53]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.SingleSignOn.invoke(SingleSignOn.java:312) [catalina.jar:9.0.53]
	at com.polarion.platform.session.PolarionLocalSingleSignOn.invoke(PolarionLocalSingleSignOn.java:164) [platform.jar:?]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) [catalina.jar:9.0.53]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357) [catalina.jar:9.0.53]
	at org.apache.coyote.ajp.AjpProcessor.service(AjpProcessor.java:433) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1726) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-util.jar:9.0.53]
	at java.lang.Thread.run(Thread.java:829) [?:?]
Caused by: org.apache.hivemind.ApplicationRuntimeException: Unable to construct service com.fasnote.alm.auth.feishu.feishuAuthenticatorManagerInterceptorFactory: Error building service com.fasnote.alm.auth.feishu.feishuAuthenticatorManagerInterceptorFactory: Unable to convert type 'com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory' to a Java class, either as is, or in package com.fasnote.alm.auth.feishu.
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructNewServiceImplementation(AbstractServiceModelImpl.java:163) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructServiceImplementation(AbstractServiceModelImpl.java:141) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.SingletonServiceModel.getActualServiceImplementation(SingletonServiceModel.java:69) ~[hivemind-1.1.1.jar:?]
	at $ServiceInterceptorFactory_19855a74bce._service($ServiceInterceptorFactory_19855a74bce.java) ~[?:?]
	at $ServiceInterceptorFactory_19855a74bce.createInterceptor($ServiceInterceptorFactory_19855a74bce.java) ~[?:?]
	at $ServiceInterceptorFactory_19855a74bcc.createInterceptor($ServiceInterceptorFactory_19855a74bcc.java) ~[?:?]
	at org.apache.hivemind.impl.ServiceInterceptorContributionImpl.createInterceptor(ServiceInterceptorContributionImpl.java:95) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.InterceptorStackImpl.process(InterceptorStackImpl.java:116) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.addInterceptors(AbstractServiceModelImpl.java:85) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructNewServiceImplementation(AbstractServiceModelImpl.java:159) ~[polarion-hivemind.jar:?]
	... 26 more
Caused by: org.apache.hivemind.ApplicationRuntimeException: Error building service com.fasnote.alm.auth.feishu.feishuAuthenticatorManagerInterceptorFactory: Unable to convert type 'com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory' to a Java class, either as is, or in package com.fasnote.alm.auth.feishu.
	at org.apache.hivemind.impl.InvokeFactoryServiceConstructor.constructCoreServiceImplementation(InvokeFactoryServiceConstructor.java:66) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructCoreServiceImplementation(AbstractServiceModelImpl.java:108) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructNewServiceImplementation(AbstractServiceModelImpl.java:157) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructServiceImplementation(AbstractServiceModelImpl.java:141) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.SingletonServiceModel.getActualServiceImplementation(SingletonServiceModel.java:69) ~[hivemind-1.1.1.jar:?]
	at $ServiceInterceptorFactory_19855a74bce._service($ServiceInterceptorFactory_19855a74bce.java) ~[?:?]
	at $ServiceInterceptorFactory_19855a74bce.createInterceptor($ServiceInterceptorFactory_19855a74bce.java) ~[?:?]
	at $ServiceInterceptorFactory_19855a74bcc.createInterceptor($ServiceInterceptorFactory_19855a74bcc.java) ~[?:?]
	at org.apache.hivemind.impl.ServiceInterceptorContributionImpl.createInterceptor(ServiceInterceptorContributionImpl.java:95) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.InterceptorStackImpl.process(InterceptorStackImpl.java:116) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.addInterceptors(AbstractServiceModelImpl.java:85) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructNewServiceImplementation(AbstractServiceModelImpl.java:159) ~[polarion-hivemind.jar:?]
	... 26 more
Caused by: org.apache.hivemind.ApplicationRuntimeException: Error building service com.fasnote.alm.auth.feishu.feishuAuthenticatorManagerInterceptorFactory: Unable to convert type 'com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory' to a Java class, either as is, or in package com.fasnote.alm.auth.feishu.
	at org.apache.hivemind.service.impl.BuilderFactoryLogic.createService(BuilderFactoryLogic.java:87) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.service.impl.BuilderFactory.createCoreServiceImplementation(BuilderFactory.java:42) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.InvokeFactoryServiceConstructor.constructCoreServiceImplementation(InvokeFactoryServiceConstructor.java:62) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructCoreServiceImplementation(AbstractServiceModelImpl.java:108) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructNewServiceImplementation(AbstractServiceModelImpl.java:157) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructServiceImplementation(AbstractServiceModelImpl.java:141) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.SingletonServiceModel.getActualServiceImplementation(SingletonServiceModel.java:69) ~[hivemind-1.1.1.jar:?]
	at $ServiceInterceptorFactory_19855a74bce._service($ServiceInterceptorFactory_19855a74bce.java) ~[?:?]
	at $ServiceInterceptorFactory_19855a74bce.createInterceptor($ServiceInterceptorFactory_19855a74bce.java) ~[?:?]
	at $ServiceInterceptorFactory_19855a74bcc.createInterceptor($ServiceInterceptorFactory_19855a74bcc.java) ~[?:?]
	at org.apache.hivemind.impl.ServiceInterceptorContributionImpl.createInterceptor(ServiceInterceptorContributionImpl.java:95) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.InterceptorStackImpl.process(InterceptorStackImpl.java:116) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.addInterceptors(AbstractServiceModelImpl.java:85) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructNewServiceImplementation(AbstractServiceModelImpl.java:159) ~[polarion-hivemind.jar:?]
	... 26 more
Caused by: org.apache.hivemind.ApplicationRuntimeException: Unable to convert type 'com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory' to a Java class, either as is, or in package com.fasnote.alm.auth.feishu.
	at org.apache.hivemind.impl.ModuleImpl.findTypeInClassResolver(ModuleImpl.java:219) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.ModuleImpl.resolveType(ModuleImpl.java:203) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.service.impl.BuilderFactoryLogic.instantiateCoreServiceInstance(BuilderFactoryLogic.java:100) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.service.impl.BuilderFactoryLogic.createService(BuilderFactoryLogic.java:75) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.service.impl.BuilderFactory.createCoreServiceImplementation(BuilderFactory.java:42) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.InvokeFactoryServiceConstructor.constructCoreServiceImplementation(InvokeFactoryServiceConstructor.java:62) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructCoreServiceImplementation(AbstractServiceModelImpl.java:108) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructNewServiceImplementation(AbstractServiceModelImpl.java:157) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructServiceImplementation(AbstractServiceModelImpl.java:141) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.SingletonServiceModel.getActualServiceImplementation(SingletonServiceModel.java:69) ~[hivemind-1.1.1.jar:?]
	at $ServiceInterceptorFactory_19855a74bce._service($ServiceInterceptorFactory_19855a74bce.java) ~[?:?]
	at $ServiceInterceptorFactory_19855a74bce.createInterceptor($ServiceInterceptorFactory_19855a74bce.java) ~[?:?]
	at $ServiceInterceptorFactory_19855a74bcc.createInterceptor($ServiceInterceptorFactory_19855a74bcc.java) ~[?:?]
	at org.apache.hivemind.impl.ServiceInterceptorContributionImpl.createInterceptor(ServiceInterceptorContributionImpl.java:95) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.InterceptorStackImpl.process(InterceptorStackImpl.java:116) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.addInterceptors(AbstractServiceModelImpl.java:85) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructNewServiceImplementation(AbstractServiceModelImpl.java:159) ~[polarion-hivemind.jar:?]
	... 26 more
2025-07-29 18:33:51,782 [Thread-36] INFO  NotificationService - Notification service was started successfully.
