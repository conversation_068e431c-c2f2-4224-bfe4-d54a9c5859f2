2025-07-28 11:43:08,663 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0392 s [66% update (144x), 34% query (12x)] (221x), svn: 0.0118 s [62% getLatestRevision (2x), 30% testConnection (1x)] (4x)
2025-07-28 11:43:08,766 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0311 s [59% getDir2 content (2x), 32% info (3x)] (6x)
2025-07-28 11:43:09,454 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.684 s, CPU [user: 0.0426 s, system: 0.0814 s], Allocated memory: 6.8 MB, transactions: 0, ObjectMaps: 0.0551 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0413 s [81% log2 (5x)] (7x)
2025-07-28 11:43:09,454 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.684 s, CPU [user: 0.179 s, system: 0.273 s], Allocated memory: 53.1 MB, transactions: 0, ObjectMaps: 0.103 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-28 11:43:09,454 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.684 s, CPU [user: 0.222 s, system: 0.334 s], Allocated memory: 70.5 MB, transactions: 0, ObjectMaps: 0.123 s [99% getAllPrimaryObjects (1x)] (14x)
2025-07-28 11:43:09,454 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.684 s, CPU [user: 0.0998 s, system: 0.211 s], Allocated memory: 23.8 MB, transactions: 0, ObjectMaps: 0.0725 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-28 11:43:09,454 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.684 s, CPU [user: 0.0932 s, system: 0.162 s], Allocated memory: 16.7 MB, transactions: 0, svn: 0.116 s [43% log2 (10x), 25% log (1x), 14% info (5x)] (24x), ObjectMaps: 0.0767 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-28 11:43:09,454 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.684 s, CPU [user: 0.0583 s, system: 0.0869 s], Allocated memory: 8.5 MB, transactions: 0, svn: 0.074 s [81% log2 (10x)] (13x), ObjectMaps: 0.0502 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-28 11:43:09,455 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.481 s [100% getAllPrimaryObjects (8x)] (63x), svn: 0.292 s [62% log2 (36x), 12% getLatestRevision (9x), 10% log (1x)] (61x)
2025-07-28 11:43:09,588 [main | u:p] INFO  TXLOGGER - Tx 6613c822a8c01_0_6613c822a8c01_0_: finished. Total: 0.111 s, CPU [user: 0.0927 s, system: 0.00426 s], Allocated memory: 21.8 MB
2025-07-28 11:43:09,713 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.224 s [100% getReadConfiguration (48x)] (48x), svn: 0.0757 s [83% info (18x)] (38x)
2025-07-28 11:43:10,001 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.216 s [75% info (94x), 18% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.162 s [100% getReadConfiguration (54x)] (54x)
2025-07-28 11:43:10,235 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.22 s [100% doFinishStartup (1x)] (1x), commit: 0.0644 s [100% Revision (1x)] (1x), Lucene: 0.0268 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.0134 s [100% objectsToInv (1x)] (1x), DB: 0.0114 s [36% query (1x), 36% update (3x), 19% execute (1x)] (8x)
2025-07-28 11:44:05,139 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.512 s [59% info (158x), 34% getLatestRevision (19x)] (187x), PullingJob: 0.172 s [100% collectChanges (18x)] (18x)
2025-07-28 11:44:05,884 [DBHistoryCreator-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.643 s, CPU [user: 0.0046 s, system: 0.00287 s], Allocated memory: 483.0 kB, transactions: 1
2025-07-28 11:44:05,884 [DBHistoryCreator-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.643 s, CPU [user: 0.00326 s, system: 0.00224 s], Allocated memory: 220.0 kB, transactions: 1
2025-07-28 11:44:05,884 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.643 s, CPU [user: 0.00343 s, system: 0.0023 s], Allocated memory: 207.2 kB, transactions: 1
2025-07-28 11:44:05,885 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 27, Lucene: 0.0436 s [100% refresh (3x)] (3x), resolve: 0.029 s [84% User (1x)] (3x), Incremental Baseline: 0.023 s [100% WorkItem (19x)] (19x), notification worker: 0.0229 s [54% RevisionActivityCreator (2x), 15% WorkItemActivityCreator (1x), 13% TestRunActivityCreator (1x)] (6x), persistence listener: 0.0226 s [71% indexRefreshPersistenceListener (1x), 12% WorkItemActivityCreator (1x)] (7x), ObjectMaps: 0.00788 s [100% getPrimaryObjectLocation (1x)] (1x), DB: 0.00766 s [74% update (3x), 26% commit (3x)] (6x)
2025-07-28 11:44:05,885 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.744 s, CPU [user: 0.131 s, system: 0.0336 s], Allocated memory: 17.9 MB, transactions: 21, svn: 0.558 s [98% getDatedRevision (181x)] (183x)
2025-07-28 11:44:06,211 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6613c85907040_0_6613c85907040_0_: finished. Total: 1.06 s, CPU [user: 0.334 s, system: 0.103 s], Allocated memory: 53.2 MB, svn: 0.637 s [48% getDatedRevision (181x), 36% getDir2 content (25x)] (307x), resolve: 0.356 s [100% Category (96x)] (96x), ObjectMaps: 0.128 s [42% getPrimaryObjectProperty (96x), 35% getPrimaryObjectLocation (96x), 23% getLastPromoted (96x)] (388x)
2025-07-28 11:44:06,396 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6613c85a22848_0_6613c85a22848_0_: finished. Total: 0.113 s, CPU [user: 0.0498 s, system: 0.011 s], Allocated memory: 8.3 MB, RepositoryConfigService: 0.0495 s [54% getReadConfiguration (162x), 46% getReadUserConfiguration (10x)] (172x), svn: 0.0488 s [53% info (19x), 41% getFile content (16x)] (37x), resolve: 0.0386 s [100% User (9x)] (9x), ObjectMaps: 0.0195 s [42% getPrimaryObjectLocation (9x), 42% getPrimaryObjectProperty (9x)] (37x)
2025-07-28 11:44:06,629 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6613c85a4704a_0_6613c85a4704a_0_: finished. Total: 0.201 s, CPU [user: 0.0582 s, system: 0.0101 s], Allocated memory: 19.9 MB, RepositoryConfigService: 0.117 s [99% getReadConfiguration (170x)] (192x), svn: 0.109 s [61% getDir2 content (17x), 39% getFile content (44x)] (62x), GC: 0.041 s [100% G1 Young Generation (1x)] (1x)
2025-07-28 11:44:07,415 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6613c85a7944b_0_6613c85a7944b_0_: finished. Total: 0.785 s, CPU [user: 0.348 s, system: 0.0362 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.608 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.403 s [69% getFile content (412x), 31% getDir2 content (21x)] (434x)
2025-07-28 11:44:07,773 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6613c85b5c44e_0_6613c85b5c44e_0_: finished. Total: 0.236 s, CPU [user: 0.0981 s, system: 0.006 s], Allocated memory: 384.1 MB, RepositoryConfigService: 0.15 s [96% getReadConfiguration (2787x)] (3025x), svn: 0.148 s [51% getFile content (185x), 49% getDir2 content (20x)] (206x)
2025-07-28 11:44:07,773 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 2.63 s, CPU [user: 0.965 s, system: 0.18 s], Allocated memory: 1.6 GB, transactions: 10, svn: 1.49 s [40% getDir2 content (114x), 36% getFile content (809x), 20% getDatedRevision (181x)] (1144x), RepositoryConfigService: 0.983 s [95% getReadConfiguration (12019x)] (12691x), resolve: 0.431 s [82% Category (96x)] (117x), ObjectMaps: 0.158 s [43% getPrimaryObjectProperty (110x), 35% getPrimaryObjectLocation (116x), 21% getLastPromoted (110x)] (452x)
2025-07-28 11:44:07,773 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 51, svn: 2.06 s [41% getDatedRevision (362x), 29% getDir2 content (114x), 26% getFile content (809x)] (1328x), RepositoryConfigService: 0.983 s [95% getReadConfiguration (12019x)] (12691x), resolve: 0.431 s [82% Category (96x)] (118x), ObjectMaps: 0.158 s [43% getPrimaryObjectProperty (110x), 35% getPrimaryObjectLocation (116x), 21% getLastPromoted (110x)] (452x)
