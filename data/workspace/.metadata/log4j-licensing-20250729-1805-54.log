2025-07-29 18:05:54,356 [main] INFO  PolarionLicensing - Searching for valid license file in /opt/polarion/polarion/license
2025-07-29 18:05:54,357 [main] INFO  PolarionLicensing - Trying to load license file polarion.lic
2025-07-29 18:05:54,359 [main] INFO  PolarionLicensing - The license file contains the following fields:
2025-07-29 18:05:54,359 [main] INFO  PolarionLicensing - *** License fields ***
2025-07-29 18:05:54,359 [main] INFO  PolarionLicensing - VariantsNamedUsers = 3
2025-07-29 18:05:54,359 [main] INFO  PolarionLicensing - almNamedUsers = 3
2025-07-29 18:05:54,359 [main] INFO  PolarionLicensing - dateCreated = 23.07.2025
2025-07-29 18:05:54,359 [main] INFO  PolarionLicensing - expirationDate = 21.08.2025
2025-07-29 18:05:54,359 [main] INFO  PolarionLicensing - hardwareKey = 8AG9-261C-1962
2025-07-29 18:05:54,359 [main] INFO  PolarionLicensing - licenseFormat = 2022
2025-07-29 18:05:54,359 [main] INFO  PolarionLicensing - licenseType = EVAL
2025-07-29 18:05:54,359 [main] INFO  PolarionLicensing - multiInstanceRunningInstances = 3
2025-07-29 18:05:54,359 [main] INFO  PolarionLicensing - userCompany = Polarion Eval
2025-07-29 18:05:54,359 [main] INFO  PolarionLicensing - *** License fields END ***
2025-07-29 18:05:54,375 [main] INFO  PolarionLicensing - Removing allocations by null
2025-07-29 18:05:54,376 [main] INFO  PolarionLicensing - STATS:concurrentVariantsUser,current:0,peak:0,limit:0
2025-07-29 18:05:54,376 [main] INFO  PolarionLicensing - 0 namedReviewerUser assignments (out of 0) loaded: []
2025-07-29 18:05:54,376 [main] INFO  PolarionLicensing - 0 concurrentReviewerUser assignments (out of 0) loaded: []
2025-07-29 18:05:54,376 [main] INFO  PolarionLicensing - STATS:concurrentReviewerUser,current:0,peak:0,limit:0
2025-07-29 18:05:54,376 [main] INFO  PolarionLicensing - 0 namedXBaseUser assignments (out of 0) loaded: []
2025-07-29 18:05:54,376 [main] INFO  PolarionLicensing - 0 concurrentXBaseUser assignments (out of 0) loaded: []
2025-07-29 18:05:54,376 [main] INFO  PolarionLicensing - STATS:concurrentXBaseUser,current:0,peak:0,limit:0
2025-07-29 18:05:54,376 [main] INFO  PolarionLicensing - 0 namedXProUser assignments (out of 0) loaded: []
2025-07-29 18:05:54,376 [main] INFO  PolarionLicensing - 0 concurrentXProUser assignments (out of 0) loaded: []
2025-07-29 18:05:54,376 [main] INFO  PolarionLicensing - STATS:concurrentXProUser,current:0,peak:0,limit:0
2025-07-29 18:05:54,376 [main] INFO  PolarionLicensing - 0 namedXEnterpriseUser assignments (out of 0) loaded: []
2025-07-29 18:05:54,376 [main] INFO  PolarionLicensing - 0 concurrentXEnterpriseUser assignments (out of 0) loaded: []
2025-07-29 18:05:54,376 [main] INFO  PolarionLicensing - STATS:concurrentXEnterpriseUser,current:0,peak:0,limit:0
2025-07-29 18:05:54,376 [main] INFO  PolarionLicensing - 0 namedProUser assignments (out of 0) loaded: []
2025-07-29 18:05:54,376 [main] INFO  PolarionLicensing - 0 concurrentProUser assignments (out of 0) loaded: []
2025-07-29 18:05:54,376 [main] INFO  PolarionLicensing - STATS:concurrentProUser,current:0,peak:0,limit:0
2025-07-29 18:05:54,376 [main] INFO  PolarionLicensing - 0 namedRequirementsUser assignments (out of 0) loaded: []
2025-07-29 18:05:54,376 [main] INFO  PolarionLicensing - 0 concurrentRequirementsUser assignments (out of 0) loaded: []
2025-07-29 18:05:54,377 [main] INFO  PolarionLicensing - STATS:concurrentRequirementsUser,current:0,peak:0,limit:0
2025-07-29 18:05:54,377 [main] INFO  PolarionLicensing - 0 namedQAUser assignments (out of 0) loaded: []
2025-07-29 18:05:54,377 [main] INFO  PolarionLicensing - 0 concurrentQAUser assignments (out of 0) loaded: []
2025-07-29 18:05:54,377 [main] INFO  PolarionLicensing - STATS:concurrentQAUser,current:0,peak:0,limit:0
2025-07-29 18:05:54,377 [main] INFO  PolarionLicensing - 3 namedALMUser assignments (out of 3) loaded: [admin, ou_d6f3139d36fb2978b33a8f870096b9e3, mTest]
2025-07-29 18:05:54,377 [main] INFO  PolarionLicensing - 0 concurrentALMUser assignments (out of 0) loaded: []
2025-07-29 18:05:54,377 [main] INFO  PolarionLicensing - STATS:concurrentALMUser,current:0,peak:0,limit:0
2025-07-29 18:05:54,377 [main] INFO  PolarionLicensing - 
*******************************************************************
 Polarion successfully activated
*******************************************************************
2025-07-29 18:06:08,651 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55a58730-c0a8d700-5cecbd49-9a5a80c3] INFO  PolarionLicensing - User 'admin' logged in with named ALM
