2025-07-29 18:07:29,121 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Using logging context STANDALONE
2025-07-29 18:07:29,123 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Launchers manager started...
2025-07-29 18:07:29,123 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Using home directory /opt/polarion/polarion
2025-07-29 18:07:29,123 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Using root directory /opt/polarion
2025-07-29 18:07:29,123 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Using workspace directory /opt/polarion/data/workspace
2025-07-29 18:07:29,123 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Using config directory /Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion
2025-07-29 18:07:29,123 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Loading external properties ...
2025-07-29 18:07:29,123 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Using external property file /opt/polarion/etc/polarion.properties
2025-07-29 18:07:29,124 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Loading internal properties ...
2025-07-29 18:07:29,126 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Host: zhangwentiandeMac-mini-2.local (*************)
2025-07-29 18:07:29,128 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Product: com.polarion.alm
2025-07-29 18:07:29,128 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Version: 3.22.1
2025-07-29 18:07:29,128 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Build: 20220419-1528-22_R1-be3adceb
2025-07-29 18:07:29,129 [main] WARN  com.polarion.core.boot.impl.AppLaunchersManager - missing subfolder 'eclipse' under extension directory: /opt/polarion/polarion/extensions/fasnote
2025-07-29 18:07:29,129 [main] WARN  com.polarion.core.boot.impl.AppLaunchersManager - missing subfolder 'eclipse' under extension directory: /opt/polarion/polarion/extensions/2404
2025-07-29 18:07:29,129 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Extensions: [exts]
2025-07-29 18:07:29,133 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Workspace location: /opt/polarion/data/workspace
2025-07-29 18:07:29,133 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Workspace lock acquired
2025-07-29 18:07:29,134 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Found applications: [polarion.server, polarion.coordinator, polarion.rt]
2025-07-29 18:07:29,134 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Starting application: polarion.server
2025-07-29 18:07:29,139 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Application extension successfully read
2025-07-29 18:07:29,143 [main] INFO  com.polarion.platform.internal.SystemStatistics - Initializing monitoring, isThreadCpuTimeSupported: true, isThreadContentionMonitoringSupported: true, isThreadAllocatedMemorySupported: true
2025-07-29 18:07:29,144 [main] INFO  com.polarion.platform.internal.SystemStatistics - State before enabling: isThreadCpuTimeEnabled: true, isThreadContentionMonitoringEnabled: false, isThreadAllocatedMemoryEnabled: true
2025-07-29 18:07:29,144 [main] INFO  com.polarion.platform.internal.SystemStatistics - State after enabling: isThreadCpuTimeEnabled: true, isThreadContentionMonitoringEnabled: false, isThreadAllocatedMemoryEnabled: true
2025-07-29 18:07:29,147 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 18:07:29,147 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-29 18:07:29,147 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 18:07:29,147 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-29 18:07:29,147 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-29 18:07:29,147 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:07:29,147 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-29 18:07:29,148 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - **** Java system properties listing: 
2025-07-29 18:07:29,170 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - adminPasswd = admin
2025-07-29 18:07:29,171 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - adminUser = admin
2025-07-29 18:07:29,171 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - announcer.smtp.auth = false
2025-07-29 18:07:29,171 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - announcer.smtp.host = 
2025-07-29 18:07:29,171 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - announcer.smtp.password = **PASSWORD**HIDDEN**
2025-07-29 18:07:29,171 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - announcer.smtp.port = 25
2025-07-29 18:07:29,171 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - announcer.smtp.user = 
2025-07-29 18:07:29,171 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - awt.toolkit = sun.lwawt.macosx.LWCToolkit
2025-07-29 18:07:29,171 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - base.url = http://localhost
2025-07-29 18:07:29,171 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - bfh.jobs.workdir = /opt/polarion/data/workspace/polarion-data/jobs
2025-07-29 18:07:29,171 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - BIRDir = /opt/polarion/data/BIR
2025-07-29 18:07:29,171 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - calculated.fields.mode = async
2025-07-29 18:07:29,171 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.activation.activationHelpLink = https://polarion.plm.automation.siemens.com/getlicense
2025-07-29 18:07:29,171 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.activation.server = https://license.polarion.com/licenseGenerator/generator/generate
2025-07-29 18:07:29,171 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.alm.ui.gravatar.enabled = false
2025-07-29 18:07:29,171 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.alm.ui.gravatar.url = http://www.gravatar.com/avatar/$emailHash$?d=identicon&s=50
2025-07-29 18:07:29,171 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.application = polarion.server
2025-07-29 18:07:29,171 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.config = /Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion
2025-07-29 18:07:29,171 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.data = /opt/polarion/data
2025-07-29 18:07:29,172 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.eclipse = /opt/polarion/polarion
2025-07-29 18:07:29,172 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.home = /opt/polarion/polarion
2025-07-29 18:07:29,172 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.licenseDir = /opt/polarion/polarion/license
2025-07-29 18:07:29,172 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.platform.internalPG = polarion:polarion@localhost:5434
2025-07-29 18:07:29,172 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.platform.monitoring.notifications.disabled = true
2025-07-29 18:07:29,172 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.platform.monitoring.notifications.receivers = 
2025-07-29 18:07:29,172 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.platform.monitoring.notifications.sender = 
2025-07-29 18:07:29,172 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.platform.monitoring.notifications.subject.prefix = 
2025-07-29 18:07:29,172 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.platform.persistence.notifications.disabled = true
2025-07-29 18:07:29,172 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.propertyFile = /opt/polarion/etc/polarion.properties
2025-07-29 18:07:29,172 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.root = /opt/polarion
2025-07-29 18:07:29,172 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.workspace = /opt/polarion/data/workspace
2025-07-29 18:07:29,172 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.collaborationNotifications.enabled = true
2025-07-29 18:07:29,172 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.document.listStyle = 1ai
2025-07-29 18:07:29,172 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.loggingContext = STANDALONE
2025-07-29 18:07:29,172 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.preview.thumbnailsDataDir = /opt/polarion/data/workspace/previews-data/thumbnails
2025-07-29 18:07:29,172 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.rest.cors.allowedOrigins = *
2025-07-29 18:07:29,172 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.rest.enabled = true
2025-07-29 18:07:29,172 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.rest.swaggerUi.enabled = true
2025-07-29 18:07:29,172 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.xcelerator.accEndpointUrl = https://acc.collab.sws.siemens.com
2025-07-29 18:07:29,172 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.xcelerator.baseDomain = sws.siemens.com
2025-07-29 18:07:29,172 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.xcelerator.shareEndpointUrl = https://share.sws.siemens.com
2025-07-29 18:07:29,172 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - content.types.user.table = /opt/polarion/polarion/plugins/com.polarion.core.boot_3.22.1/content-types.properties
2025-07-29 18:07:29,172 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - controlHostname = localhost
2025-07-29 18:07:29,172 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - controlPort = 8887
2025-07-29 18:07:29,172 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - createproject.default.location = Sandbox/
2025-07-29 18:07:29,172 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - createproject.default.useUserId = true
2025-07-29 18:07:29,172 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - createproject.limitedAccessMessage = You may create a project in the Sandbox project group (only). Please fill in the required properties below. For example:<br/><table><tr><td>Location:</td><td>Sandbox/MyFirstProject</td></tr><tr><td>ID:</td><td>MyFirstProject</td></tr></table><br/>Or use the suggested defaults.
2025-07-29 18:07:29,172 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - debug = false
2025-07-29 18:07:29,173 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - debug.license.validation = true
2025-07-29 18:07:29,173 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - debug.machine.code.generation = true
2025-07-29 18:07:29,173 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - debug.security.validation = true
2025-07-29 18:07:29,173 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - default.project.template.ALM = alm_vmodel
2025-07-29 18:07:29,173 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - default.project.template.Pro = alm_vmodel
2025-07-29 18:07:29,173 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - default.project.template.QA = qa_vmodel
2025-07-29 18:07:29,173 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - default.project.template.Requirements = req_vmodel
2025-07-29 18:07:29,173 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - default.project.template.XBase = alm_vmodel
2025-07-29 18:07:29,173 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - default.project.template.XEnterprise = alm_vmodel
2025-07-29 18:07:29,173 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - default.project.template.XPro = alm_vmodel
2025-07-29 18:07:29,173 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - derby.system.home = /opt/polarion/data/logs/derby
2025-07-29 18:07:29,173 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - eclipse.application = com.polarion.core.boot.app
2025-07-29 18:07:29,173 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - eclipse.commands = -application
com.polarion.core.boot.app
-data
/opt/polarion/data/workspace
-configuration
file:///Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/
-dev
file:///Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/dev.properties
-os
linux
-ws
linux
-arch
arm64
-appId
polarion.server

2025-07-29 18:07:29,173 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - eclipse.home.location = file:/opt/polarion/polarion/plugins/
2025-07-29 18:07:29,173 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - eclipse.p2.data.area = @config.dir/.p2
2025-07-29 18:07:29,173 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - eclipse.pde.launch = true
2025-07-29 18:07:29,173 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - eclipse.startTime = *************
2025-07-29 18:07:29,173 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - eclipse.stateSaveDelayInterval = 30000
2025-07-29 18:07:29,173 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - enableCreateAccountForm = false
2025-07-29 18:07:29,173 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - equinox.init.uuid = true
2025-07-29 18:07:29,173 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - error.report.email = 
2025-07-29 18:07:29,173 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - file.encoding = UTF-8
2025-07-29 18:07:29,173 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - file.separator = /
2025-07-29 18:07:29,173 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - ftp.nonProxyHosts = 127.0.0.1|***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|**********/16|*.**********/16|localhost|*.localhost|local|*.local|crashlytics.com|*.crashlytics.com|<local>|*.<local>
2025-07-29 18:07:29,173 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - gopherProxySet = false
2025-07-29 18:07:29,173 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - gosh.args = --nointeractive
2025-07-29 18:07:29,173 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - htpasswd.path = htpasswd
2025-07-29 18:07:29,173 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - http.nonProxyHosts = 127.0.0.1|***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|**********/16|*.**********/16|localhost|*.localhost|local|*.local|crashlytics.com|*.crashlytics.com|<local>|*.<local>
2025-07-29 18:07:29,173 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - index.activities = /opt/polarion/data/workspace/polarion-data/index
2025-07-29 18:07:29,173 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.awt.graphicsenv = sun.awt.CGraphicsEnvironment
2025-07-29 18:07:29,173 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.awt.headless = true
2025-07-29 18:07:29,173 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.awt.printerjob = sun.lwawt.macosx.CPrinterJob
2025-07-29 18:07:29,173 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.class.path = /opt/polarion/polarion/plugins/org.eclipse.equinox.launcher_1.5.0.v20180512-1130.jar:
2025-07-29 18:07:29,173 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.class.version = 55.0
2025-07-29 18:07:29,173 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.home = /Users/<USER>/Library/Java/JavaVirtualMachines/ms-11.0.27/Contents/Home
2025-07-29 18:07:29,174 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.io.tmpdir = /var/folders/z_/shw6wc7d7ps_fjvv781t4gt80000gn/T/
2025-07-29 18:07:29,174 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.library.path = /Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:.
2025-07-29 18:07:29,174 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.runtime.name = OpenJDK Runtime Environment
2025-07-29 18:07:29,174 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.runtime.version = 11.0.27+6-LTS
2025-07-29 18:07:29,174 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.security.policy = /opt/polarion/polarion/policy
2025-07-29 18:07:29,174 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.specification.maintenance.version = 3
2025-07-29 18:07:29,174 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.specification.name = Java Platform API Specification
2025-07-29 18:07:29,174 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.specification.vendor = Oracle Corporation
2025-07-29 18:07:29,174 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.specification.version = 11
2025-07-29 18:07:29,174 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vendor = Microsoft
2025-07-29 18:07:29,174 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vendor.url = https://www.microsoft.com
2025-07-29 18:07:29,174 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vendor.url.bug = https://github.com/microsoft/openjdk/issues
2025-07-29 18:07:29,174 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vendor.version = Microsoft-11367290
2025-07-29 18:07:29,174 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.version = 11.0.27
2025-07-29 18:07:29,174 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.version.date = 2025-04-15
2025-07-29 18:07:29,174 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.compressedOopsMode = Zero based
2025-07-29 18:07:29,174 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.info = mixed mode
2025-07-29 18:07:29,174 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.name = OpenJDK 64-Bit Server VM
2025-07-29 18:07:29,174 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.specification.name = Java Virtual Machine Specification
2025-07-29 18:07:29,174 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.specification.vendor = Oracle Corporation
2025-07-29 18:07:29,174 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.specification.version = 11
2025-07-29 18:07:29,174 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.vendor = Microsoft
2025-07-29 18:07:29,174 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.version = 11.0.27+6-LTS
2025-07-29 18:07:29,174 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - javasvn.timeout = 10000
2025-07-29 18:07:29,174 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - jdk.debug = release
2025-07-29 18:07:29,174 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - ldap.bind.password = **PASSWORD**HIDDEN**
2025-07-29 18:07:29,174 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.audit.enabled = true
2025-07-29 18:07:29,174 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.auto.scan.enabled = true
2025-07-29 18:07:29,174 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.cache.size = 100
2025-07-29 18:07:29,174 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.cache.ttl = 1800
2025-07-29 18:07:29,174 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.check.interval = 0
2025-07-29 18:07:29,174 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.allow.expired = true
2025-07-29 18:07:29,174 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.allow.local.files = true
2025-07-29 18:07:29,174 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.default.features = all
2025-07-29 18:07:29,174 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.default.max.users = 10
2025-07-29 18:07:29,174 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.default.plugin.id = com.fasnote.alm.plugin.manage
2025-07-29 18:07:29,174 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.mode = true
2025-07-29 18:07:29,174 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.show.machine.code = true
2025-07-29 18:07:29,174 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.skip.machine.binding = true
2025-07-29 18:07:29,174 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.skip.network.validation = true
2025-07-29 18:07:29,175 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.directory = dev-licenses
2025-07-29 18:07:29,175 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.encryption.enabled = false
2025-07-29 18:07:29,175 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.hot.reload.enabled = true
2025-07-29 18:07:29,175 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.log.level = DEBUG
2025-07-29 18:07:29,175 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.machine.binding.enabled = false
2025-07-29 18:07:29,175 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.max.plugins = 1000
2025-07-29 18:07:29,175 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.scan.interval = 60
2025-07-29 18:07:29,175 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.signature.validation.enabled = false
2025-07-29 18:07:29,175 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.validation.timeout = 1000
2025-07-29 18:07:29,175 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - licenseForNewUserAccount = 
2025-07-29 18:07:29,175 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - line.separator = 

2025-07-29 18:07:29,175 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - log4j2.contextSelector = org.apache.logging.log4j.core.selector.BasicContextSelector
2025-07-29 18:07:29,175 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - log4j2.loggerContextFactory = org.apache.logging.log4j.core.impl.Log4jContextFactory
2025-07-29 18:07:29,175 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - logDir = /opt/polarion/data/workspace/.metadata/
2025-07-29 18:07:29,175 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - login = polarion
2025-07-29 18:07:29,175 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - mavenConfigDir = /opt/polarion/polarion/../maven
2025-07-29 18:07:29,175 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - minimalPasswordLength = **PASSWORD**HIDDEN**
2025-07-29 18:07:29,175 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.eclipse.equinox.simpleconfigurator.configUrl = file:/Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/org.eclipse.equinox.simpleconfigurator/bundles.info
2025-07-29 18:07:29,175 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.eclipse.lyo.oslc4j.strictDatatypes = false
2025-07-29 18:07:29,175 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.executionenvironment = OSGi/Minimum-1.0, OSGi/Minimum-1.1, OSGi/Minimum-1.2, JavaSE/compact1-1.8, JavaSE/compact2-1.8, JavaSE/compact3-1.8, JRE-1.1, J2SE-1.2, J2SE-1.3, J2SE-1.4, J2SE-1.5, JavaSE-1.6, JavaSE-1.7, JavaSE-1.8, JavaSE-9, JavaSE-10, JavaSE-11
2025-07-29 18:07:29,175 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.language = zh
2025-07-29 18:07:29,175 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.os.name = MacOSX
2025-07-29 18:07:29,175 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.os.version = 15.5.0
2025-07-29 18:07:29,175 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.processor = aarch64
2025-07-29 18:07:29,175 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.storage = /Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion
2025-07-29 18:07:29,175 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.system.capabilities = osgi.ee; osgi.ee="OSGi/Minimum"; version:List<Version>="1.0, 1.1, 1.2", osgi.ee; osgi.ee="JRE"; version:List<Version>="1.0, 1.1", osgi.ee; osgi.ee="JavaSE"; version:List<Version>="1.0, 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 1.7, 1.8, 9.0, 10.0, 11.0",osgi.ee; osgi.ee="JavaSE/compact1"; version:List<Version>="1.8, 9.0, 10.0, 11.0",osgi.ee; osgi.ee="JavaSE/compact2"; version:List<Version>="1.8, 9.0, 10.0, 11.0",osgi.ee; osgi.ee="JavaSE/compact3"; version:List<Version>="1.8, 9.0, 10.0, 11.0"
2025-07-29 18:07:29,175 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.system.packages = com.sun.jarsigner, com.sun.java.accessibility.util, com.sun.javadoc, com.sun.jdi, com.sun.jdi.connect, com.sun.jdi.connect.spi, com.sun.jdi.event, com.sun.jdi.request, com.sun.jndi.ldap.spi, com.sun.management, com.sun.net.httpserver, com.sun.net.httpserver.spi, com.sun.nio.file, com.sun.nio.sctp, com.sun.security.auth, com.sun.security.auth.callback, com.sun.security.auth.login, com.sun.security.auth.module, com.sun.security.jgss, com.sun.source.doctree, com.sun.source.tree, com.sun.source.util, com.sun.tools.attach, com.sun.tools.attach.spi, com.sun.tools.javac, com.sun.tools.javadoc, com.sun.tools.jconsole, java.applet, java.awt, java.awt.color, java.awt.datatransfer, java.awt.desktop, java.awt.dnd, java.awt.event, java.awt.font, java.awt.geom, java.awt.im, java.awt.im.spi, java.awt.image, java.awt.image.renderable, java.awt.print, java.beans, java.beans.beancontext, java.io, java.lang, java.lang.annotation, java.lang.instrument, java.lang.invoke, java.lang.management, java.lang.module, java.lang.ref, java.lang.reflect, java.math, java.net, java.net.http, java.net.spi, java.nio, java.nio.channels, java.nio.channels.spi, java.nio.charset, java.nio.charset.spi, java.nio.file, java.nio.file.attribute, java.nio.file.spi, java.rmi, java.rmi.activation, java.rmi.dgc, java.rmi.registry, java.rmi.server, java.security, java.security.acl, java.security.cert, java.security.interfaces, java.security.spec, java.sql, java.text, java.text.spi, java.time, java.time.chrono, java.time.format, java.time.temporal, java.time.zone, java.util, java.util.concurrent, java.util.concurrent.atomic, java.util.concurrent.locks, java.util.function, java.util.jar, java.util.logging, java.util.prefs, java.util.regex, java.util.spi, java.util.stream, java.util.zip, javax.accessibility, javax.annotation.processing, javax.crypto, javax.crypto.interfaces, javax.crypto.spec, javax.imageio, javax.imageio.event, javax.imageio.metadata, javax.imageio.plugins.bmp, javax.imageio.plugins.jpeg, javax.imageio.plugins.tiff, javax.imageio.spi, javax.imageio.stream, javax.lang.model, javax.lang.model.element, javax.lang.model.type, javax.lang.model.util, javax.management, javax.management.loading, javax.management.modelmbean, javax.management.monitor, javax.management.openmbean, javax.management.relation, javax.management.remote, javax.management.remote.rmi, javax.management.timer, javax.naming, javax.naming.directory, javax.naming.event, javax.naming.ldap, javax.naming.spi, javax.net, javax.net.ssl, javax.print, javax.print.attribute, javax.print.attribute.standard, javax.print.event, javax.rmi.ssl, javax.script, javax.security.auth, javax.security.auth.callback, javax.security.auth.kerberos, javax.security.auth.login, javax.security.auth.spi, javax.security.auth.x500, javax.security.cert, javax.security.sasl, javax.smartcardio, javax.sound.midi, javax.sound.midi.spi, javax.sound.sampled, javax.sound.sampled.spi, javax.sql, javax.sql.rowset, javax.sql.rowset.serial, javax.sql.rowset.spi, javax.swing, javax.swing.border, javax.swing.colorchooser, javax.swing.event, javax.swing.filechooser, javax.swing.plaf, javax.swing.plaf.basic, javax.swing.plaf.metal, javax.swing.plaf.multi, javax.swing.plaf.nimbus, javax.swing.plaf.synth, javax.swing.table, javax.swing.text, javax.swing.text.html, javax.swing.text.html.parser, javax.swing.text.rtf, javax.swing.tree, javax.swing.undo, javax.tools, javax.transaction.xa, javax.xml, javax.xml.catalog, javax.xml.crypto, javax.xml.crypto.dom, javax.xml.crypto.dsig, javax.xml.crypto.dsig.dom, javax.xml.crypto.dsig.keyinfo, javax.xml.crypto.dsig.spec, javax.xml.datatype, javax.xml.namespace, javax.xml.parsers, javax.xml.stream, javax.xml.stream.events, javax.xml.stream.util, javax.xml.transform, javax.xml.transform.dom, javax.xml.transform.sax, javax.xml.transform.stax, javax.xml.transform.stream, javax.xml.validation, javax.xml.xpath, jdk.dynalink, jdk.dynalink.beans, jdk.dynalink.linker, jdk.dynalink.linker.support, jdk.dynalink.support, jdk.javadoc.doclet, jdk.jfr, jdk.jfr.consumer, jdk.jshell, jdk.jshell.execution, jdk.jshell.spi, jdk.jshell.tool, jdk.management.jfr, jdk.nashorn.api.scripting, jdk.nashorn.api.tree, jdk.net, jdk.nio, jdk.security.jarsigner, jdk.swing.interop, netscape.javascript, org.ietf.jgss, org.w3c.dom, org.w3c.dom.bootstrap, org.w3c.dom.css, org.w3c.dom.events, org.w3c.dom.html, org.w3c.dom.ls, org.w3c.dom.ranges, org.w3c.dom.stylesheets, org.w3c.dom.traversal, org.w3c.dom.views, org.w3c.dom.xpath, org.xml.sax, org.xml.sax.ext, org.xml.sax.helpers, sun.misc, sun.reflect
2025-07-29 18:07:29,176 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.uuid = bb180c08-2e3e-4276-8a39-4fbec0948269
2025-07-29 18:07:29,176 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.vendor = Eclipse
2025-07-29 18:07:29,176 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.version = 1.9.0
2025-07-29 18:07:29,176 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.supports.framework.extension = true
2025-07-29 18:07:29,176 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.supports.framework.fragment = true
2025-07-29 18:07:29,176 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.supports.framework.requirebundle = true
2025-07-29 18:07:29,176 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.xsocket.connection.client.readbuffer.usedirect = true
2025-07-29 18:07:29,176 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.xsocket.connection.server.readbuffer.usedirect = true
2025-07-29 18:07:29,176 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - os.arch = aarch64
2025-07-29 18:07:29,176 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - os.name = Mac OS X
2025-07-29 18:07:29,176 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - os.version = 15.5
2025-07-29 18:07:29,176 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.arch = arm64
2025-07-29 18:07:29,176 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.bundles = reference:file:/opt/polarion/polarion/plugins/org.eclipse.equinox.simpleconfigurator_1.3.0.v20180502-1828.jar@1:start
2025-07-29 18:07:29,176 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.bundles.defaultStartLevel = 4
2025-07-29 18:07:29,176 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.checkConfiguration = true
2025-07-29 18:07:29,176 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.compatibility.bootdelegation = true
2025-07-29 18:07:29,176 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.compatibility.bootdelegation.default = true
2025-07-29 18:07:29,176 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.configuration.area = file:/Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/
2025-07-29 18:07:29,176 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.configuration.cascaded = false
2025-07-29 18:07:29,176 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.dev = file:///Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/dev.properties
2025-07-29 18:07:29,176 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.framework = file:/opt/polarion/polarion/plugins/org.eclipse.osgi_3.13.0.v20180409-1500.jar
2025-07-29 18:07:29,176 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.framework.shape = jar
2025-07-29 18:07:29,176 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.framework.useSystemProperties = true
2025-07-29 18:07:29,176 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.install.area = file:/opt/polarion/polarion/plugins/
2025-07-29 18:07:29,176 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.instance.area = file:/opt/polarion/data/workspace/
2025-07-29 18:07:29,176 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.logfile = /opt/polarion/data/workspace/.metadata/.log
2025-07-29 18:07:29,176 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.nl = zh_CN_#Hans
2025-07-29 18:07:29,176 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.os = linux
2025-07-29 18:07:29,176 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.syspath = /opt/polarion/polarion/plugins
2025-07-29 18:07:29,176 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.tracefile = /opt/polarion/data/workspace/.metadata/trace.log
2025-07-29 18:07:29,176 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.ws = linux
2025-07-29 18:07:29,176 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - password = **PASSWORD**HIDDEN**
2025-07-29 18:07:29,176 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - path.separator = :
2025-07-29 18:07:29,176 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - pdfbox.fontcache = /opt/polarion/data/workspace/polarion-data
2025-07-29 18:07:29,176 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - pdfexport.config = /opt/polarion/polarion/configuration/pdfexport.xml
2025-07-29 18:07:29,176 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - polarion.build.default.deploy.repository.id = polarion-shared
2025-07-29 18:07:29,176 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - polarion.build.default.deploy.repository.url = file:///opt/polarion/data/shared-maven-repo
2025-07-29 18:07:29,176 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - polarion.build.maven.location.maven2 = /opt/polarion/polarion/../maven/distribution
2025-07-29 18:07:29,176 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - polarion.global.doc.cache.size = 100
2025-07-29 18:07:29,176 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - polarion.global.doc.cache.with.history = false
2025-07-29 18:07:29,176 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - polarion.tx.doc.cache.size = 100
2025-07-29 18:07:29,176 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - productLink.com.polarion.alm = https://polarion.plm.automation.siemens.com/products/polarion-alm
2025-07-29 18:07:29,176 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - productLink.com.polarion.qa = https://polarion.plm.automation.siemens.com/products/polarion-qa
2025-07-29 18:07:29,176 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - productLink.com.polarion.requirements = https://polarion.plm.automation.siemens.com/products/polarion-requirements
2025-07-29 18:07:29,176 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - repo = http://localhost/repo
2025-07-29 18:07:29,176 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - rolesForNewUserAccount = user
2025-07-29 18:07:29,176 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - RRDir = /opt/polarion/data/RR
2025-07-29 18:07:29,177 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - SDKDir = /opt/polarion/polarion/SDK
2025-07-29 18:07:29,177 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - secure.approvals = false
2025-07-29 18:07:29,177 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - shutdownCatchPhrase = shutdown
2025-07-29 18:07:29,177 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - simple.profiler.enabled = false
2025-07-29 18:07:29,177 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - skip.data.preloading = false
2025-07-29 18:07:29,177 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - socksNonProxyHosts = 127.0.0.1|***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|**********/16|*.**********/16|localhost|*.localhost|local|*.local|crashlytics.com|*.crashlytics.com|<local>|*.<local>
2025-07-29 18:07:29,177 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - stderr.encoding = UTF-8
2025-07-29 18:07:29,177 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - stdout.encoding = UTF-8
2025-07-29 18:07:29,177 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - storeUrl.com.polarion.requirements = https://polarion.plm.automation.siemens.com/products/licensing?product=REQUIREMENTS
2025-07-29 18:07:29,177 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.arch.data.model = 64
2025-07-29 18:07:29,177 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.boot.library.path = /Users/<USER>/Library/Java/JavaVirtualMachines/ms-11.0.27/Contents/Home/lib
2025-07-29 18:07:29,177 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.cpu.endian = little
2025-07-29 18:07:29,177 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.cpu.isalist = 
2025-07-29 18:07:29,177 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.io.unicode.encoding = UnicodeBig
2025-07-29 18:07:29,177 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.java.command = org.eclipse.equinox.launcher.Main -application com.polarion.core.boot.app -data /opt/polarion/data/workspace -configuration file:///Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/ -dev file:///Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/dev.properties -os linux -ws linux -arch arm64 -appId polarion.server
2025-07-29 18:07:29,177 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.java.launcher = SUN_STANDARD
2025-07-29 18:07:29,177 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.jnu.encoding = UTF-8
2025-07-29 18:07:29,177 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.management.compiler = HotSpot 64-Bit Tiered Compilers
2025-07-29 18:07:29,177 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.os.patch.level = unknown
2025-07-29 18:07:29,177 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - support.contact = https://polarion.plm.automation.siemens.com/techsupport/resources
2025-07-29 18:07:29,177 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - support.license.email = <EMAIL>
2025-07-29 18:07:29,178 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - support.sales.email = <EMAIL>
2025-07-29 18:07:29,178 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - svn.access.file = /opt/polarion/data/svn/access
2025-07-29 18:07:29,178 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - svn.passwd.file = /opt/polarion/data/svn/passwd
2025-07-29 18:07:29,178 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - svnkit.http.encoding = UTF-8
2025-07-29 18:07:29,178 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - svnkit.library.gnome-keyring.enabled = false
2025-07-29 18:07:29,178 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - TomcatService.ajp13-port = 8889
2025-07-29 18:07:29,178 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - TomcatService.request.safeListedHosts = 0.0.0.0
2025-07-29 18:07:29,178 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - user.country = CN
2025-07-29 18:07:29,178 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - user.dir = /Applications/Eclipse JEE.app/Contents/MacOS
2025-07-29 18:07:29,178 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - user.home = /Users/<USER>
2025-07-29 18:07:29,178 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - user.language = zh
2025-07-29 18:07:29,178 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - user.name = zhangwentian
2025-07-29 18:07:29,178 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - user.script = Hans
2025-07-29 18:07:29,178 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - user.timezone = Asia/Shanghai
2025-07-29 18:07:29,178 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - userAccountVault = /opt/polarion/data/workspace/user-account-vault
2025-07-29 18:07:29,178 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - workDir = /opt/polarion/data/workspace/polarion-data
2025-07-29 18:07:29,178 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - **** END of Java system properties
2025-07-29 18:07:29,180 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - XML parsers factory: com.sun.org.apache.xerces.internal.jaxp.DocumentBuilderFactoryImpl
2025-07-29 18:07:29,180 [main] INFO  com.polarion.psvn.launcher.internal.platform.PlatformService - Starting Platform...
2025-07-29 18:07:29,189 [main] INFO  PolarionLicensing - Searching for valid license file in /opt/polarion/polarion/license
2025-07-29 18:07:29,191 [main] INFO  PolarionLicensing - Trying to load license file polarion.lic
2025-07-29 18:07:29,192 [main] INFO  PolarionLicensing - The license file contains the following fields:
2025-07-29 18:07:29,192 [main] INFO  PolarionLicensing - *** License fields ***
2025-07-29 18:07:29,192 [main] INFO  PolarionLicensing - VariantsNamedUsers = 3
2025-07-29 18:07:29,192 [main] INFO  PolarionLicensing - almNamedUsers = 3
2025-07-29 18:07:29,192 [main] INFO  PolarionLicensing - dateCreated = 23.07.2025
2025-07-29 18:07:29,192 [main] INFO  PolarionLicensing - expirationDate = 21.08.2025
2025-07-29 18:07:29,192 [main] INFO  PolarionLicensing - hardwareKey = 8AG9-261C-1962
2025-07-29 18:07:29,192 [main] INFO  PolarionLicensing - licenseFormat = 2022
2025-07-29 18:07:29,192 [main] INFO  PolarionLicensing - licenseType = EVAL
2025-07-29 18:07:29,192 [main] INFO  PolarionLicensing - multiInstanceRunningInstances = 3
2025-07-29 18:07:29,192 [main] INFO  PolarionLicensing - userCompany = Polarion Eval
2025-07-29 18:07:29,192 [main] INFO  PolarionLicensing - *** License fields END ***
2025-07-29 18:07:29,206 [main] INFO  PolarionLicensing - Removing allocations by null
2025-07-29 18:07:29,207 [main] INFO  PolarionLicensing - STATS:concurrentVariantsUser,current:0,peak:0,limit:0
2025-07-29 18:07:29,207 [main] INFO  PolarionLicensing - 0 namedReviewerUser assignments (out of 0) loaded: []
2025-07-29 18:07:29,207 [main] INFO  PolarionLicensing - 0 concurrentReviewerUser assignments (out of 0) loaded: []
2025-07-29 18:07:29,207 [main] INFO  PolarionLicensing - STATS:concurrentReviewerUser,current:0,peak:0,limit:0
2025-07-29 18:07:29,207 [main] INFO  PolarionLicensing - 0 namedXBaseUser assignments (out of 0) loaded: []
2025-07-29 18:07:29,207 [main] INFO  PolarionLicensing - 0 concurrentXBaseUser assignments (out of 0) loaded: []
2025-07-29 18:07:29,207 [main] INFO  PolarionLicensing - STATS:concurrentXBaseUser,current:0,peak:0,limit:0
2025-07-29 18:07:29,208 [main] INFO  PolarionLicensing - 0 namedXProUser assignments (out of 0) loaded: []
2025-07-29 18:07:29,208 [main] INFO  PolarionLicensing - 0 concurrentXProUser assignments (out of 0) loaded: []
2025-07-29 18:07:29,208 [main] INFO  PolarionLicensing - STATS:concurrentXProUser,current:0,peak:0,limit:0
2025-07-29 18:07:29,208 [main] INFO  PolarionLicensing - 0 namedXEnterpriseUser assignments (out of 0) loaded: []
2025-07-29 18:07:29,208 [main] INFO  PolarionLicensing - 0 concurrentXEnterpriseUser assignments (out of 0) loaded: []
2025-07-29 18:07:29,208 [main] INFO  PolarionLicensing - STATS:concurrentXEnterpriseUser,current:0,peak:0,limit:0
2025-07-29 18:07:29,208 [main] INFO  PolarionLicensing - 0 namedProUser assignments (out of 0) loaded: []
2025-07-29 18:07:29,208 [main] INFO  PolarionLicensing - 0 concurrentProUser assignments (out of 0) loaded: []
2025-07-29 18:07:29,208 [main] INFO  PolarionLicensing - STATS:concurrentProUser,current:0,peak:0,limit:0
2025-07-29 18:07:29,208 [main] INFO  PolarionLicensing - 0 namedRequirementsUser assignments (out of 0) loaded: []
2025-07-29 18:07:29,208 [main] INFO  PolarionLicensing - 0 concurrentRequirementsUser assignments (out of 0) loaded: []
2025-07-29 18:07:29,208 [main] INFO  PolarionLicensing - STATS:concurrentRequirementsUser,current:0,peak:0,limit:0
2025-07-29 18:07:29,208 [main] INFO  PolarionLicensing - 0 namedQAUser assignments (out of 0) loaded: []
2025-07-29 18:07:29,208 [main] INFO  PolarionLicensing - 0 concurrentQAUser assignments (out of 0) loaded: []
2025-07-29 18:07:29,208 [main] INFO  PolarionLicensing - STATS:concurrentQAUser,current:0,peak:0,limit:0
2025-07-29 18:07:29,208 [main] INFO  PolarionLicensing - 3 namedALMUser assignments (out of 3) loaded: [admin, ou_d6f3139d36fb2978b33a8f870096b9e3, mTest]
2025-07-29 18:07:29,209 [main] INFO  PolarionLicensing - 0 concurrentALMUser assignments (out of 0) loaded: []
2025-07-29 18:07:29,209 [main] INFO  PolarionLicensing - STATS:concurrentALMUser,current:0,peak:0,limit:0
2025-07-29 18:07:29,209 [main] INFO  PolarionLicensing - 
*******************************************************************
 Polarion successfully activated
*******************************************************************
2025-07-29 18:07:29,276 [main] INFO  com.polarion.platform.internal.i18n.LanguageContributor - Localization file /META-INF/messages_en.properties read successfully (7789 messages)
2025-07-29 18:07:29,309 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Processing bundles:
2025-07-29 18:07:29,309 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [0] - org.eclipse.osgi
2025-07-29 18:07:29,309 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [1] - org.eclipse.equinox.simpleconfigurator
2025-07-29 18:07:29,309 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [2] - antlr
2025-07-29 18:07:29,309 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [3] - antlr4
2025-07-29 18:07:29,309 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [4] - antlr4-runtime
2025-07-29 18:07:29,309 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [5] - bcprov
2025-07-29 18:07:29,310 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [6] - com.auth0.java-jwt
2025-07-29 18:07:29,310 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [7] - com.fasnote.alm.auth.feishu
2025-07-29 18:07:29,311 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.fasnote.alm.auth.feishu to HiveMind
2025-07-29 18:07:29,311 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [8] - com.fasnote.alm.injection
2025-07-29 18:07:29,311 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [9] - com.fasnote.alm.plugin.manage
2025-07-29 18:07:29,312 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [10] - com.fasnote.alm.queryexpander
2025-07-29 18:07:29,312 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.fasnote.alm.queryexpander to HiveMind
2025-07-29 18:07:29,312 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [11] - com.fasnote.alm.test
2025-07-29 18:07:29,314 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [12] - com.fasterxml.classmate
2025-07-29 18:07:29,315 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [13] - com.fasterxml.jackson
2025-07-29 18:07:29,315 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [14] - com.fasterxml.jackson.dataformat.jackson-dataformat-yaml
2025-07-29 18:07:29,315 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [15] - com.fasterxml.jackson.jaxrs
2025-07-29 18:07:29,315 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [16] - com.fasterxml.woodstox
2025-07-29 18:07:29,316 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [17] - com.google.gson
2025-07-29 18:07:29,316 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [18] - com.google.guava
2025-07-29 18:07:29,316 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [19] - com.google.guava.failureaccess
2025-07-29 18:07:29,316 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [20] - com.ibm.icu.icu4j
2025-07-29 18:07:29,317 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [21] - com.icl.saxon
2025-07-29 18:07:29,317 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [22] - com.jayway.jsonpath.json-path
2025-07-29 18:07:29,317 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [23] - com.jcraft.jsch
2025-07-29 18:07:29,317 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [24] - com.networknt.json-schema-validator
2025-07-29 18:07:29,317 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [25] - com.nimbusds.content-type
2025-07-29 18:07:29,317 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [26] - com.nimbusds.nimbus-jose-jwt
2025-07-29 18:07:29,317 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [27] - com.opensymphony.quartz
2025-07-29 18:07:29,318 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [28] - com.polarion.alm.ProjectPlanGantt_new
2025-07-29 18:07:29,319 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.ProjectPlanGantt_new to HiveMind
2025-07-29 18:07:29,319 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [29] - com.polarion.alm.builder
2025-07-29 18:07:29,319 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.builder to HiveMind
2025-07-29 18:07:29,319 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [30] - com.polarion.alm.checker
2025-07-29 18:07:29,319 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.checker to HiveMind
2025-07-29 18:07:29,319 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [31] - com.polarion.alm.extension.vcontext
2025-07-29 18:07:29,320 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.extension.vcontext to HiveMind
2025-07-29 18:07:29,320 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [32] - com.polarion.alm.impex
2025-07-29 18:07:29,321 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.impex to HiveMind
2025-07-29 18:07:29,321 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [33] - com.polarion.alm.install
2025-07-29 18:07:29,321 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [34] - com.polarion.alm.oslc
2025-07-29 18:07:29,323 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.oslc to HiveMind
2025-07-29 18:07:29,323 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [35] - com.polarion.alm.projects
2025-07-29 18:07:29,323 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.projects to HiveMind
2025-07-29 18:07:29,323 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [36] - com.polarion.alm.qcentre
2025-07-29 18:07:29,323 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.qcentre to HiveMind
2025-07-29 18:07:29,323 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [37] - com.polarion.alm.tracker
2025-07-29 18:07:29,324 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.tracker to HiveMind
2025-07-29 18:07:29,324 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [38] - com.polarion.alm.ui
2025-07-29 18:07:29,329 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.ui to HiveMind
2025-07-29 18:07:29,329 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [39] - com.polarion.alm.ui.diagrams.mxgraph
2025-07-29 18:07:29,329 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [40] - com.polarion.alm.wiki
2025-07-29 18:07:29,331 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.wiki to HiveMind
2025-07-29 18:07:29,331 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [41] - com.polarion.alm.ws
2025-07-29 18:07:29,331 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [42] - com.polarion.alm.ws.client
2025-07-29 18:07:29,333 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [43] - com.polarion.cluster
2025-07-29 18:07:29,334 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.cluster to HiveMind
2025-07-29 18:07:29,334 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [44] - com.polarion.core.boot
2025-07-29 18:07:29,334 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [45] - com.polarion.core.util
2025-07-29 18:07:29,334 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [46] - com.polarion.fop
2025-07-29 18:07:29,334 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [47] - com.polarion.platform
2025-07-29 18:07:29,335 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform to HiveMind
2025-07-29 18:07:29,335 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [48] - com.polarion.platform.guice
2025-07-29 18:07:29,335 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [49] - com.polarion.platform.hivemind
2025-07-29 18:07:29,335 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.hivemind to HiveMind
2025-07-29 18:07:29,335 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [50] - com.polarion.platform.jobs
2025-07-29 18:07:29,336 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.jobs to HiveMind
2025-07-29 18:07:29,336 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [51] - com.polarion.platform.monitoring
2025-07-29 18:07:29,336 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.monitoring to HiveMind
2025-07-29 18:07:29,336 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [52] - com.polarion.platform.persistence
2025-07-29 18:07:29,337 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.persistence to HiveMind
2025-07-29 18:07:29,337 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [53] - com.polarion.platform.repository
2025-07-29 18:07:29,337 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.repository to HiveMind
2025-07-29 18:07:29,337 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [54] - com.polarion.platform.repository.driver.svn
2025-07-29 18:07:29,338 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.repository.driver.svn to HiveMind
2025-07-29 18:07:29,338 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [55] - com.polarion.platform.repository.external
2025-07-29 18:07:29,338 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.repository.external to HiveMind
2025-07-29 18:07:29,338 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [56] - com.polarion.platform.repository.external.git
2025-07-29 18:07:29,338 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.repository.external.git to HiveMind
2025-07-29 18:07:29,338 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [57] - com.polarion.platform.repository.external.svn
2025-07-29 18:07:29,338 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.repository.external.svn to HiveMind
2025-07-29 18:07:29,338 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [58] - com.polarion.platform.sql
2025-07-29 18:07:29,339 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [59] - com.polarion.portal.tomcat
2025-07-29 18:07:29,344 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [60] - com.polarion.psvn.launcher
2025-07-29 18:07:29,344 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.psvn.launcher to HiveMind
2025-07-29 18:07:29,344 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [61] - com.polarion.psvn.translations.en
2025-07-29 18:07:29,344 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [62] - com.polarion.purevariants
2025-07-29 18:07:29,347 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.purevariants to HiveMind
2025-07-29 18:07:29,347 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [63] - com.polarion.qcentre
2025-07-29 18:07:29,349 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [64] - com.polarion.scripting
2025-07-29 18:07:29,356 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.scripting to HiveMind
2025-07-29 18:07:29,356 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [65] - com.polarion.scripting.servlet
2025-07-29 18:07:29,356 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [66] - com.polarion.subterra.base
2025-07-29 18:07:29,356 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [67] - com.polarion.subterra.index
2025-07-29 18:07:29,357 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.subterra.index to HiveMind
2025-07-29 18:07:29,357 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [68] - com.polarion.subterra.persistence
2025-07-29 18:07:29,358 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.subterra.persistence to HiveMind
2025-07-29 18:07:29,358 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [69] - com.polarion.subterra.persistence.document
2025-07-29 18:07:29,358 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.subterra.persistence.document to HiveMind
2025-07-29 18:07:29,359 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [70] - com.polarion.synchronizer
2025-07-29 18:07:29,359 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.synchronizer to HiveMind
2025-07-29 18:07:29,359 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [71] - com.polarion.synchronizer.proxy.feishu
2025-07-29 18:07:29,359 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [72] - com.polarion.synchronizer.proxy.hpalm
2025-07-29 18:07:29,362 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [73] - com.polarion.synchronizer.proxy.jira
2025-07-29 18:07:29,363 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [74] - com.polarion.synchronizer.proxy.polarion
2025-07-29 18:07:29,364 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [75] - com.polarion.synchronizer.proxy.reqif
2025-07-29 18:07:29,373 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [76] - com.polarion.synchronizer.ui
2025-07-29 18:07:29,373 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [77] - com.polarion.usdp.persistence
2025-07-29 18:07:29,374 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.usdp.persistence to HiveMind
2025-07-29 18:07:29,374 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [78] - com.polarion.xray.doc.user
2025-07-29 18:07:29,374 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [79] - com.siemens.des.logger.api
2025-07-29 18:07:29,374 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [80] - com.siemens.plm.bitools.analytics
2025-07-29 18:07:29,374 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [81] - com.siemens.polarion.ct.collectors.git
2025-07-29 18:07:29,375 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.siemens.polarion.ct.collectors.git to HiveMind
2025-07-29 18:07:29,375 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [82] - com.siemens.polarion.eclipse.configurator
2025-07-29 18:07:29,376 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [83] - com.siemens.polarion.integration.ci
2025-07-29 18:07:29,377 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.siemens.polarion.integration.ci to HiveMind
2025-07-29 18:07:29,377 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [84] - com.siemens.polarion.previewer
2025-07-29 18:07:29,377 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.siemens.polarion.previewer to HiveMind
2025-07-29 18:07:29,377 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [85] - com.siemens.polarion.previewer.external
2025-07-29 18:07:29,377 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.siemens.polarion.previewer.external to HiveMind
2025-07-29 18:07:29,377 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [86] - com.siemens.polarion.rest
2025-07-29 18:07:29,378 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [87] - com.siemens.polarion.rt
2025-07-29 18:07:29,378 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [88] - com.siemens.polarion.rt.api
2025-07-29 18:07:29,379 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [89] - com.siemens.polarion.rt.collectors.git
2025-07-29 18:07:29,379 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [90] - com.siemens.polarion.rt.communication.common
2025-07-29 18:07:29,380 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [91] - com.siemens.polarion.rt.communication.polarion
2025-07-29 18:07:29,381 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.siemens.polarion.rt.communication.polarion to HiveMind
2025-07-29 18:07:29,381 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [92] - com.siemens.polarion.rt.communication.rt
2025-07-29 18:07:29,382 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [93] - com.siemens.polarion.rt.parsers.c
2025-07-29 18:07:29,382 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [94] - com.siemens.polarion.rt.ui
2025-07-29 18:07:29,382 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [95] - com.siemens.polarion.synchronizer.proxy.tfs
2025-07-29 18:07:29,383 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [96] - com.sun.activation.javax.activation
2025-07-29 18:07:29,383 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [97] - com.sun.istack.commons-runtime
2025-07-29 18:07:29,383 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [98] - com.sun.jna
2025-07-29 18:07:29,384 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [99] - com.sun.jna.platform
2025-07-29 18:07:29,384 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [100] - com.sun.xml.bind.jaxb-impl
2025-07-29 18:07:29,384 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [101] - com.trilead.ssh2
2025-07-29 18:07:29,384 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [102] - com.zaxxer.hikariCP
2025-07-29 18:07:29,384 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [103] - des-sdk-core
2025-07-29 18:07:29,384 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [104] - des-sdk-dss
2025-07-29 18:07:29,384 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [105] - io.github.resilience4j.circuitbreaker
2025-07-29 18:07:29,384 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [106] - io.github.resilience4j.core
2025-07-29 18:07:29,384 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [107] - io.github.resilience4j.retry
2025-07-29 18:07:29,384 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [108] - io.swagger
2025-07-29 18:07:29,385 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [109] - io.vavr
2025-07-29 18:07:29,385 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [110] - jakaroma
2025-07-29 18:07:29,385 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [111] - jakarta.validation.validation-api
2025-07-29 18:07:29,385 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [112] - javassist
2025-07-29 18:07:29,385 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [113] - javax.annotation-api
2025-07-29 18:07:29,385 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [114] - javax.cache
2025-07-29 18:07:29,385 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [115] - javax.el
2025-07-29 18:07:29,385 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [116] - javax.inject
2025-07-29 18:07:29,386 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [117] - javax.servlet
2025-07-29 18:07:29,386 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [118] - javax.servlet.jsp
2025-07-29 18:07:29,386 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [119] - javax.transaction
2025-07-29 18:07:29,386 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [120] - jaxb-api
2025-07-29 18:07:29,387 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [121] - jcip-annotations
2025-07-29 18:07:29,387 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [122] - jcl.over.slf4j
2025-07-29 18:07:29,387 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [123] - jul.to.slf4j
2025-07-29 18:07:29,387 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [124] - kuromoji-core
2025-07-29 18:07:29,387 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [125] - kuromoji-ipadic
2025-07-29 18:07:29,387 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [126] - lang-tag
2025-07-29 18:07:29,387 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [127] - net.htmlparser.jericho
2025-07-29 18:07:29,388 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [128] - net.java.dev.jna
2025-07-29 18:07:29,388 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [129] - net.minidev.accessors-smart
2025-07-29 18:07:29,388 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [130] - net.minidev.asm
2025-07-29 18:07:29,388 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [131] - net.minidev.json-smart
2025-07-29 18:07:29,388 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [132] - net.n3.nanoxml
2025-07-29 18:07:29,388 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [133] - net.sourceforge.cssparser
2025-07-29 18:07:29,388 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [134] - nu.xom
2025-07-29 18:07:29,389 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [135] - oauth2-oidc-sdk
2025-07-29 18:07:29,389 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [136] - org.apache.ant
2025-07-29 18:07:29,396 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [137] - org.apache.avro
2025-07-29 18:07:29,398 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [138] - org.apache.axis
2025-07-29 18:07:29,402 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [139] - org.apache.batik
2025-07-29 18:07:29,402 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [140] - org.apache.commons.codec
2025-07-29 18:07:29,402 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [141] - org.apache.commons.collections
2025-07-29 18:07:29,402 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [142] - org.apache.commons.commons-beanutils
2025-07-29 18:07:29,402 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [143] - org.apache.commons.commons-collections4
2025-07-29 18:07:29,403 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [144] - org.apache.commons.commons-compress
2025-07-29 18:07:29,403 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [145] - org.apache.commons.commons-fileupload
2025-07-29 18:07:29,403 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [146] - org.apache.commons.digester
2025-07-29 18:07:29,403 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [147] - org.apache.commons.exec
2025-07-29 18:07:29,403 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [148] - org.apache.commons.io
2025-07-29 18:07:29,403 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [149] - org.apache.commons.lang
2025-07-29 18:07:29,403 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [150] - org.apache.commons.lang3
2025-07-29 18:07:29,403 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [151] - org.apache.commons.logging
2025-07-29 18:07:29,403 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [152] - org.apache.curator
2025-07-29 18:07:29,409 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [153] - org.apache.fop
2025-07-29 18:07:29,409 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [154] - org.apache.hivemind
2025-07-29 18:07:29,410 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle org.apache.hivemind to HiveMind
2025-07-29 18:07:29,410 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [155] - org.apache.httpcomponents.httpclient
2025-07-29 18:07:29,410 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [156] - org.apache.httpcomponents.httpcore
2025-07-29 18:07:29,410 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [157] - org.apache.jasper.glassfish
2025-07-29 18:07:29,410 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [158] - org.apache.kafka.clients
2025-07-29 18:07:29,411 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [159] - org.apache.kafka.streams
2025-07-29 18:07:29,411 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [160] - org.apache.logging.log4j.1.2-api
2025-07-29 18:07:29,411 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [161] - org.apache.logging.log4j.api
2025-07-29 18:07:29,412 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [162] - org.apache.logging.log4j.apiconf
2025-07-29 18:07:29,412 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [163] - org.apache.logging.log4j.core
2025-07-29 18:07:29,413 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [164] - org.apache.logging.log4j.slf4j-impl
2025-07-29 18:07:29,413 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [165] - org.apache.lucene.analyzers-common
2025-07-29 18:07:29,413 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [166] - org.apache.lucene.analyzers-common
2025-07-29 18:07:29,415 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [167] - org.apache.lucene.analyzers-smartcn
2025-07-29 18:07:29,416 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [168] - org.apache.lucene.core
2025-07-29 18:07:29,416 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [169] - org.apache.lucene.core
2025-07-29 18:07:29,418 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [170] - org.apache.lucene.grouping
2025-07-29 18:07:29,418 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [171] - org.apache.lucene.queryparser
2025-07-29 18:07:29,418 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [172] - org.apache.oro
2025-07-29 18:07:29,419 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [173] - org.apache.pdfbox.fontbox
2025-07-29 18:07:29,419 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [174] - org.apache.poi
2025-07-29 18:07:29,428 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [175] - org.apache.tika
2025-07-29 18:07:29,856 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [176] - org.apache.xalan
2025-07-29 18:07:29,857 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [177] - org.apache.xercesImpl
2025-07-29 18:07:29,857 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [178] - org.apache.xml.serializer
2025-07-29 18:07:29,857 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [179] - org.apache.xmlgraphics.commons
2025-07-29 18:07:29,857 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [180] - org.apache.zookeeper
2025-07-29 18:07:29,857 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [181] - org.codehaus.groovy
2025-07-29 18:07:29,858 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [182] - org.codehaus.jettison
2025-07-29 18:07:29,858 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [183] - org.dom4j
2025-07-29 18:07:29,858 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [184] - org.eclipse.core.contenttype
2025-07-29 18:07:29,858 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [185] - org.eclipse.core.expressions
2025-07-29 18:07:29,858 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [186] - org.eclipse.core.filesystem
2025-07-29 18:07:29,858 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [187] - org.eclipse.core.jobs
2025-07-29 18:07:29,858 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [188] - org.eclipse.core.net
2025-07-29 18:07:29,858 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [189] - org.eclipse.core.resources
2025-07-29 18:07:29,858 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [190] - org.eclipse.core.runtime
2025-07-29 18:07:29,858 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [191] - org.eclipse.equinox.app
2025-07-29 18:07:29,859 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [192] - org.eclipse.equinox.common
2025-07-29 18:07:29,859 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [193] - org.eclipse.equinox.event
2025-07-29 18:07:29,859 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [194] - org.eclipse.equinox.http.registry
2025-07-29 18:07:29,859 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [195] - org.eclipse.equinox.http.servlet
2025-07-29 18:07:29,859 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [196] - org.eclipse.equinox.jsp.jasper
2025-07-29 18:07:29,859 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [197] - org.eclipse.equinox.jsp.jasper.registry
2025-07-29 18:07:29,859 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [198] - org.eclipse.equinox.launcher
2025-07-29 18:07:29,859 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [199] - org.eclipse.equinox.preferences
2025-07-29 18:07:29,859 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [200] - org.eclipse.equinox.registry
2025-07-29 18:07:29,859 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [201] - org.eclipse.equinox.security
2025-07-29 18:07:29,860 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [202] - org.eclipse.help
2025-07-29 18:07:29,860 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [203] - org.eclipse.help.base
2025-07-29 18:07:29,860 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [204] - org.eclipse.help.webapp
2025-07-29 18:07:29,860 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [205] - org.eclipse.jgit
2025-07-29 18:07:29,860 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [206] - org.eclipse.osgi.services
2025-07-29 18:07:29,860 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [207] - org.eclipse.osgi.util
2025-07-29 18:07:29,860 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [208] - org.ehcache
2025-07-29 18:07:29,862 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [209] - org.gitlab.java-gitlab-api
2025-07-29 18:07:29,862 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [210] - org.glassfish.jersey
2025-07-29 18:07:29,862 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [211] - org.hibernate.annotations
2025-07-29 18:07:29,863 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [212] - org.hibernate.core
2025-07-29 18:07:29,863 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [213] - org.hibernate.entitymanager
2025-07-29 18:07:29,863 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [214] - org.hibernate.hikaricp
2025-07-29 18:07:29,863 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [215] - org.hibernate.jpa.2.1.api
2025-07-29 18:07:29,863 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [216] - org.jboss.logging
2025-07-29 18:07:29,863 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [217] - org.jvnet.mimepull
2025-07-29 18:07:29,863 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [218] - org.objectweb.asm
2025-07-29 18:07:29,863 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [219] - org.objectweb.jotm
2025-07-29 18:07:29,864 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [220] - org.opensaml
2025-07-29 18:07:29,876 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [221] - org.polarion.svncommons
2025-07-29 18:07:29,876 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [222] - org.polarion.svnwebclient
2025-07-29 18:07:29,877 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [223] - org.postgesql
2025-07-29 18:07:29,877 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [224] - org.projectlombok.lombok
2025-07-29 18:07:29,888 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [225] - org.rocksdb.rocksdbjni
2025-07-29 18:07:29,888 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [226] - org.springframework.data.core
2025-07-29 18:07:29,888 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [227] - org.springframework.data.jpa
2025-07-29 18:07:29,888 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [228] - org.springframework.spring-aop
2025-07-29 18:07:29,888 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [229] - org.springframework.spring-beans
2025-07-29 18:07:29,888 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [230] - org.springframework.spring-context
2025-07-29 18:07:29,889 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [231] - org.springframework.spring-core
2025-07-29 18:07:29,889 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [232] - org.springframework.spring-expression
2025-07-29 18:07:29,889 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [233] - org.springframework.spring-jdbc
2025-07-29 18:07:29,889 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [234] - org.springframework.spring-orm
2025-07-29 18:07:29,889 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [235] - org.springframework.spring-test
2025-07-29 18:07:29,889 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [236] - org.springframework.spring-tx
2025-07-29 18:07:29,889 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [237] - org.springframework.spring-web
2025-07-29 18:07:29,890 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [238] - org.springframework.spring-webmvc
2025-07-29 18:07:29,890 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [239] - org.tmatesoft.sqljet
2025-07-29 18:07:29,890 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [240] - org.tmatesoft.svnkit
2025-07-29 18:07:29,890 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [241] - saaj-api
2025-07-29 18:07:29,890 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [242] - sdk-lifecycle-collab
2025-07-29 18:07:29,890 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [243] - sdk-lifecycle-docmgmt
2025-07-29 18:07:29,890 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [244] - siemens.des.clientsecurity
2025-07-29 18:07:29,890 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [245] - slf4j.api
2025-07-29 18:07:29,891 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [246] - xml-apis
2025-07-29 18:07:29,891 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [247] - xml.apis.ext
2025-07-29 18:07:29,891 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [248] - xstream
2025-07-29 18:07:30,135 [main] INFO  com.polarion.core.util.remote.server.SocketRemoteControlServer - Remote control server socket is ready to listen on localhost/127.0.0.1:8887
2025-07-29 18:07:30,136 [xServer:8887] INFO  org.xsocket.connection.Server - server listening on localhost:8887 (xSocket 2.5.3)
2025-07-29 18:07:30,319 [main] INFO  com.polarion.platform.sql.internal.SqlModule - Initializing database...
2025-07-29 18:07:30,372 [main] INFO  com.polarion.platform.sql.internal.PgServerInfo - PG server listening on localhost:5435
2025-07-29 18:07:32,507 [main] INFO  com.polarion.platform.internal.cache.CacheConfigurator - EHCache uses internal configuration
2025-07-29 18:07:32,783 [main] WARN  org.ehcache.impl.internal.executor.PooledExecutionService - No default pool configured, services requiring thread pools must be configured explicitly using named thread pools
2025-07-29 18:07:32,858 [main] INFO  org.ehcache.sizeof.filters.AnnotationSizeOfFilter - Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-07-29 18:07:32,862 [main] INFO  org.ehcache.sizeof.impl.JvmInformation - Detected JVM data model settings of: 64-Bit OpenJDK JVM with Compressed OOPs
2025-07-29 18:07:32,873 [main] INFO  org.ehcache.sizeof.impl.AgentLoader - Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-07-29 18:07:32,971 [main] INFO  com.polarion.platform.internal.cache.CachingProviderHandler - All the caches have been destroyed because of not clean shutdown. You can ignore this message if Polarion started in reindex mode.
2025-07-29 18:07:33,005 [main] INFO  com.polarion.platform.sql.internal.PgConnection - JDBC url of database 'polarion' is: *****************************************
2025-07-29 18:07:33,006 [main] INFO  com.polarion.platform.sql.internal.PgConnection - JDBC url of database 'polarion' is: *****************************************
2025-07-29 18:07:33,007 [main] INFO  com.polarion.platform.sql.internal.PgConnection - JDBC url of database 'polarion_history' is: *************************************************
2025-07-29 18:07:33,057 [main] INFO  com.polarion.platform.sql.internal.SqlModule - Initializing database finished [ TIME 2.74 s. ]
2025-07-29 18:07:33,226 [main] INFO  com.polarion.platform.cluster.ClusterService - Initializing cluster service
2025-07-29 18:07:33,226 [main] INFO  com.polarion.platform.cluster.ClusterService - Cluster service is disabled.
2025-07-29 18:07:33,451 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-29 18:07:33,472 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool - pg polarion_history - Starting...
2025-07-29 18:07:33,525 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool - pg polarion_history - Start completed.
2025-07-29 18:07:33,583 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.131 s. ]
2025-07-29 18:07:33,583 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-29 18:07:33,592 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool - pg polarion - Starting...
2025-07-29 18:07:33,596 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool - pg polarion - Start completed.
2025-07-29 18:07:33,620 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0369 s. ]
2025-07-29 18:07:33,620 [main] INFO  com.polarion.psvn.launcher.internal.platform.PlatformService - Platform boot started
2025-07-29 18:07:33,664 [main] INFO  com.polarion.platform.repository.driver.svn.internal.security.SVNWatcher - SVN auth file watcher started with a period of 3000 milliseconds
2025-07-29 18:07:33,670 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-29 18:07:33,695 [main] INFO  com.polarion.platform.internal.security.auth.LoginFlowUserPassword - User polarion authenticated from system
2025-07-29 18:07:33,740 [main] INFO  com.polarion.platform.internal.security.auth.LoginFlowUserPassword - User polarion logged in from system
2025-07-29 18:07:33,745 [main | u:p] INFO  com.polarion.platform.internal.service.repository.PlatformRepositoryService - Repository Service Created
2025-07-29 18:07:33,746 [main | u:p] INFO  com.polarion.platform.internal.service.repository.PlatformRepositoryService - Repository Service Initialized
2025-07-29 18:07:33,751 [main | u:p] INFO  com.polarion.core.util.profiling.SimpleProfiler - Initialization
2025-07-29 18:07:33,762 [main | u:p] INFO  org.objectweb.jotm - JOTM started with a local transaction factory which is not bound.
2025-07-29 18:07:33,762 [main | u:p] INFO  org.objectweb.jotm - CAROL initialization
2025-07-29 18:07:33,768 [main | u:p] INFO  com.polarion.platform.internal.service.repository.listeners.job.PullingJob - lastFullyProcessedRevision [204]
2025-07-29 18:07:33,774 [main | u:p] INFO  com.polarion.platform.internal.service.repository.PlatformRepositoryService - END initializeService
2025-07-29 18:07:33,778 [main | u:p] INFO  com.polarion.psvn.launcher.internal.platform.PlatformService - Polarion startup estimation:  [ TIME 12 s. ]
2025-07-29 18:07:33,778 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 12 s. ]
2025-07-29 18:07:33,784 [main | u:p] INFO  com.polarion.platform.monitoring - Full monitoring results are stored in file /opt/polarion/data/workspace/monitoring/results.txt
2025-07-29 18:07:33,810 [main | u:p] INFO  com.polarion.platform.monitoring - Executing actions from stage PREBOOT
2025-07-29 18:07:33,811 [main | u:p] INFO  com.polarion.platform.monitoring - Executing action 'system.info.disk.data.free'
2025-07-29 18:07:33,812 [main | u:p] INFO  com.polarion.platform.monitoring - system.info.disk.data.free (Free data disk '/opt/polarion/data' space) = Disk Space 
1,427.01 GB
 [Tue Jul 29 18:07:33 CST 2025]
2025-07-29 18:07:33,969 [main | u:p] INFO  com.polarion.platform.monitoring - Executing action 'system.info.jvm.version'
2025-07-29 18:07:33,969 [main | u:p] INFO  com.polarion.platform.monitoring - system.info.jvm.version (JVM Version) = JVM Version 
Microsoft OpenJDK 64-Bit Server VM 11.0.27+6-LTS
 [Tue Jul 29 18:07:33 CST 2025]
2025-07-29 18:07:33,972 [main | u:p] INFO  com.polarion.platform.monitoring - Executing action 'system.info.os.version'
2025-07-29 18:07:33,972 [main | u:p] INFO  com.polarion.platform.monitoring - system.info.os.version (OS Version) = OS Version 
Mac OS X 15.5 aarch64
 [Tue Jul 29 18:07:33 CST 2025]
2025-07-29 18:07:33,974 [main | u:p] INFO  com.polarion.platform.monitoring - Finished with actions from stage PREBOOT: {OK=3}
2025-07-29 18:07:33,975 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.83 s. ]
2025-07-29 18:07:33,975 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0392 s [64% update (144x), 36% query (12x)] (221x), svn: 0.0127 s [59% getLatestRevision (2x), 33% testConnection (1x)] (4x)
2025-07-29 18:07:33,975 [main | u:p] INFO  com.polarion.psvn.launcher.internal.platform.PlatformService - calling ILowLevelPersistence.boot to start persistence
2025-07-29 18:07:33,984 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Persistence initialization started
2025-07-29 18:07:34,018 [main | u:p] INFO  com.polarion.subterra.base.internal.location.LocationCacheContext - Registered invalidationListener: com.polarion.platform.repository.internal.config.RepositoryConfigService$1@36a5aa20
2025-07-29 18:07:34,043 [main | u:p] INFO  com.polarion.platform.persistence.internal.CustomFieldsService - Custom fields control field is not set for prototype: BaselineCollection
2025-07-29 18:07:34,043 [main | u:p] INFO  com.polarion.platform.persistence.internal.CustomFieldsService - Custom fields control field is not set for prototype: TestRun
2025-07-29 18:07:34,043 [main | u:p] INFO  com.polarion.platform.persistence.internal.CustomFieldsService - Custom fields control field is not set for prototype: Plan
2025-07-29 18:07:34,056 [main | u:p] INFO  com.polarion.platform.repository.internal.context.RepositoryContextService - Context recognition started
2025-07-29 18:07:34,056 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:07:34,056 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-29 18:07:34,076 [main | u:p] INFO  com.polarion.platform.repository.internal.context.RepositoryContextService - Context recognition finished [ TIME 0.0197 s. ]
2025-07-29 18:07:34,076 [main | u:p] INFO  com.polarion.platform.repository.internal.context.RepositoryContextService - Context tree: 
ROOT_CTX_NAME (ContextNature[Root], ContextId[context [global]])
+-default (ContextNature[Repository], ContextId[cluster default, context [global]])
  +-WBS (ContextNature[Project], ContextId[cluster default, context WBS])
  +-Demo Projects (ContextNature[ProjectGroup], ContextId[cluster default, context --Demo Projects])
  | +-drivepilot (ContextNature[Project], ContextId[cluster default, context drivepilot])
  | +-elibrary (ContextNature[Project], ContextId[cluster default, context elibrary])
  +-library (ContextNature[Project], ContextId[cluster default, context library])
  +-WBSdev (ContextNature[Project], ContextId[cluster default, context WBSdev])
  +-hesai (ContextNature[Project], ContextId[cluster default, context hesai])
2025-07-29 18:07:34,076 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.1 s. ]
2025-07-29 18:07:34,076 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0252 s [54% getDir2 content (2x), 34% info (3x)] (6x)
2025-07-29 18:07:34,076 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:07:34,076 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-29 18:07:34,076 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Startup workers for phase 3: 6
2025-07-29 18:07:34,081 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (2/9)
2025-07-29 18:07:34,081 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (3/9)
2025-07-29 18:07:34,081 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (6/9)
2025-07-29 18:07:34,081 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context library] (6/9) ...
2025-07-29 18:07:34,081 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context WBSdev] (3/9) ...
2025-07-29 18:07:34,081 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context [global]] (2/9) ...
2025-07-29 18:07:34,081 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (5/9)
2025-07-29 18:07:34,081 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context --Demo Projects] (5/9) ...
2025-07-29 18:07:34,081 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (4/9)
2025-07-29 18:07:34,081 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (1/9)
2025-07-29 18:07:34,081 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[context [global]] (4/9) ...
2025-07-29 18:07:34,081 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context WBS] (1/9) ...
2025-07-29 18:07:34,086 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[context [global]] (4/9) TOOK  [ TIME 0.00509 s. ]
2025-07-29 18:07:34,087 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-07-29 18:07:34,087 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context hesai] (7/9) ...
2025-07-29 18:07:34,193 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/Demo Projects/.polarion'
2025-07-29 18:07:34,207 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/Demo Projects/modules'
2025-07-29 18:07:34,224 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/Demo Projects/documents'
2025-07-29 18:07:34,235 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/Demo Projects/_wiki'
2025-07-29 18:07:34,238 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context --Demo Projects contains 0 primary objects (work items+comments).
2025-07-29 18:07:34,239 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context --Demo Projects] (5/9) TOOK  [ TIME 0.158 s. ]
2025-07-29 18:07:34,239 [LowLevelDataService-contextInitializer-4 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-29 18:07:34,239 [LowLevelDataService-contextInitializer-4 | u:p | context: drivepilot] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context drivepilot] (8/9) ...
2025-07-29 18:07:34,267 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/WBS/documents'
2025-07-29 18:07:34,275 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/library/documents'
2025-07-29 18:07:34,327 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context library contains 287 primary objects (work items+comments).
2025-07-29 18:07:34,327 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context library] (6/9) TOOK  [ TIME 0.246 s. ]
2025-07-29 18:07:34,328 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-29 18:07:34,328 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context elibrary] (9/9) ...
2025-07-29 18:07:34,344 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context WBS contains 344 primary objects (work items+comments).
2025-07-29 18:07:34,344 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context WBS] (1/9) TOOK  [ TIME 0.263 s. ]
2025-07-29 18:07:34,396 [LowLevelDataService-contextInitializer-4 | u:p | context: drivepilot] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/Demo Projects/drivepilot/documents'
2025-07-29 18:07:34,432 [LowLevelDataService-contextInitializer-4 | u:p | context: drivepilot] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context drivepilot contains 461 primary objects (work items+comments).
2025-07-29 18:07:34,433 [LowLevelDataService-contextInitializer-4 | u:p | context: drivepilot] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context drivepilot] (8/9) TOOK  [ TIME 0.194 s. ]
2025-07-29 18:07:34,480 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/Demo Projects/elibrary/documents'
2025-07-29 18:07:34,503 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/hesai/documents'
2025-07-29 18:07:34,507 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context elibrary contains 334 primary objects (work items+comments).
2025-07-29 18:07:34,508 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context elibrary] (9/9) TOOK  [ TIME 0.18 s. ]
2025-07-29 18:07:34,580 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context hesai contains 1148 primary objects (work items+comments).
2025-07-29 18:07:34,581 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context hesai] (7/9) TOOK  [ TIME 0.493 s. ]
2025-07-29 18:07:34,634 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context null contains 2214 primary objects (work items+comments).
2025-07-29 18:07:34,634 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context [global]] (2/9) TOOK  [ TIME 0.553 s. ]
2025-07-29 18:07:34,674 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/WBSdev/documents'
2025-07-29 18:07:34,797 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context WBSdev contains 3321 primary objects (work items+comments).
2025-07-29 18:07:34,797 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context WBSdev] (3/9) TOOK  [ TIME 0.716 s. ]
2025-07-29 18:07:34,798 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.718 s, CPU [user: 0.22 s, system: 0.317 s], Allocated memory: 68.4 MB, transactions: 0, ObjectMaps: 0.123 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-29 18:07:34,798 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.72 s, CPU [user: 0.183 s, system: 0.26 s], Allocated memory: 53.1 MB, transactions: 0, ObjectMaps: 0.113 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-29 18:07:34,799 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.719 s, CPU [user: 0.0712 s, system: 0.0978 s], Allocated memory: 11.0 MB, transactions: 0, svn: 0.108 s [84% log2 (10x)] (13x)
2025-07-29 18:07:34,799 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.72 s, CPU [user: 0.111 s, system: 0.193 s], Allocated memory: 24.1 MB, transactions: 0, svn: 0.0834 s [71% log2 (5x), 20% getLatestRevision (1x)] (7x), ObjectMaps: 0.069 s [100% getAllPrimaryObjects (1x)] (10x)
2025-07-29 18:07:34,798 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.719 s, CPU [user: 0.0784 s, system: 0.132 s], Allocated memory: 12.1 MB, transactions: 0, svn: 0.137 s [85% log2 (10x)] (13x), ObjectMaps: 0.0718 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-29 18:07:34,799 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.719 s, CPU [user: 0.0612 s, system: 0.0851 s], Allocated memory: 9.2 MB, transactions: 0, svn: 0.101 s [45% log2 (5x), 19% info (5x), 16% log (1x), 13% getLatestRevision (2x)] (18x), ObjectMaps: 0.0694 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-29 18:07:34,799 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.72 s. ]
2025-07-29 18:07:34,799 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.472 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.468 s [71% log2 (36x), 14% getLatestRevision (9x)] (61x)
2025-07-29 18:07:34,810 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.RevisionsPersistenceModule - Processing new revisions [START].
2025-07-29 18:07:34,810 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:07:34,810 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-29 18:07:34,918 [main | u:p | u:p] ERROR com.polarion.platform.repository.internal.config.RepositoryConfigService$ConfigProblemCatcher - Failed to work with configuration from location /WBS/.polarion/repositories/repositories.xml:
[/WBS/.polarion/repositories/repositories.xml]: 
---- Debugging information ----
cause-exception     : com.thoughtworks.xstream.mapper.CannotResolveClassException
cause-message       : AutoBranchGitLab
class               : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
required-type       : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
converter-type      : com.thoughtworks.xstream.converters.collections.ArrayConverter
version             : 1.4.17
-------------------------------
com.polarion.platform.repository.config.RepositoryConfigurationException: [/WBS/.polarion/repositories/repositories.xml]: 
---- Debugging information ----
cause-exception     : com.thoughtworks.xstream.mapper.CannotResolveClassException
cause-message       : AutoBranchGitLab
class               : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
required-type       : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
converter-type      : com.thoughtworks.xstream.converters.collections.ArrayConverter
version             : 1.4.17
-------------------------------
	at com.polarion.platform.repository.spi.config.AbstractDataHandler.readLocation(AbstractDataHandler.java:87) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.spi.config.AbstractDataHandler.readLocations(AbstractDataHandler.java:61) ~[platform-repository.jar:?]
	at $IDataHandler_19855a6c8c6.readLocations($IDataHandler_19855a6c8c6.java) ~[?:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.readLocations(RepositoryConfigService.java:291) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService$3.runImpl(RepositoryConfigService.java:328) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService$SVNAccessAction$1.runWEx(RepositoryConfigService.java:113) ~[platform-repository.jar:?]
	at com.polarion.core.util.RunnableWEx.runWRet(RunnableWEx.java:61) ~[util.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService$SVNAccessAction.run(RepositoryConfigService.java:123) ~[platform-repository.jar:?]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:?]
	at javax.security.auth.Subject.doAs(Subject.java:361) ~[?:?]
	at com.polarion.platform.internal.security.SubjectNDC.doAs(SubjectNDC.java:58) ~[platform.jar:?]
	at com.polarion.platform.internal.security.SecurityService.doAsUser(SecurityService.java:422) ~[platform.jar:?]
	at com.polarion.platform.internal.security.SecurityService.doAsSystemUser(SecurityService.java:412) ~[platform.jar:?]
	at $ISecurityService_19855a6c6e1.doAsSystemUser($ISecurityService_19855a6c6e1.java) ~[?:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.readConfiguration(RepositoryConfigService.java:324) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.getConfigurationImpl(RepositoryConfigService.java:239) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.getReadConfigurationImpl(RepositoryConfigService.java:199) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.getReadConfiguration(RepositoryConfigService.java:177) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.addConfigurationListener(RepositoryConfigService.java:263) ~[platform-repository.jar:?]
	at $IRepositoryConfigService_19855a6c6ef.addConfigurationListener($IRepositoryConfigService_19855a6c6ef.java) ~[?:?]
	at com.polarion.platform.repository.external.internal.ExternalRepositoryProviderRegistry.initialize(ExternalRepositoryProviderRegistry.java:146) ~[platform-repository.jar:?]
	at $IExternalRepositoryProviderRegistry_19855a6c7ab.initialize($IExternalRepositoryProviderRegistry_19855a6c7ab.java) ~[?:?]
	at $IExternalRepositoryProviderRegistry_19855a6c7aa.initialize($IExternalRepositoryProviderRegistry_19855a6c7aa.java) ~[?:?]
	at com.polarion.platform.persistence.internal.pe.RevisionsPersistenceModule.initModule(RevisionsPersistenceModule.java:353) ~[platform-persistence.jar:?]
	at $IObjectPersistenceModule_19855a6c89a.initModule($IObjectPersistenceModule_19855a6c89a.java) ~[?:?]
	at com.polarion.subterra.persistence.internal.PersistenceEngine.initModule(PersistenceEngine.java:251) ~[subterra-uniform-persistence.jar:?]
	at $IPersistenceEngine_19855a6c882.initModule($IPersistenceEngine_19855a6c882.java) ~[?:?]
	at com.polarion.platform.persistence.internal.pe.LowLevelDataService.boot(LowLevelDataService.java:352) ~[platform-persistence.jar:?]
	at $ILowLevelPersistence_19855a6c7a7.boot($ILowLevelPersistence_19855a6c7a7.java) ~[?:?]
	at $ILowLevelPersistence_19855a6c7a6.boot($ILowLevelPersistence_19855a6c7a6.java) ~[?:?]
	at com.polarion.psvn.launcher.internal.platform.PlatformService.lambda$0(PlatformService.java:294) ~[launcher.jar:?]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:?]
	at javax.security.auth.Subject.doAs(Subject.java:423) [?:?]
	at com.polarion.platform.internal.security.SubjectNDC.doAs(SubjectNDC.java:69) [platform.jar:?]
	at com.polarion.platform.internal.security.SecurityService.doAsUser(SecurityService.java:417) [platform.jar:?]
	at com.polarion.platform.internal.security.SecurityService.doAsSystemUser(SecurityService.java:407) [platform.jar:?]
	at $ISecurityService_19855a6c6e2.doAsSystemUser($ISecurityService_19855a6c6e2.java) [?:?]
	at $ISecurityService_19855a6c6e1.doAsSystemUser($ISecurityService_19855a6c6e1.java) [?:?]
	at com.polarion.psvn.launcher.internal.platform.PlatformService.bootPlatform(PlatformService.java:286) [launcher.jar:?]
	at com.polarion.psvn.launcher.internal.platform.PlatformService.start(PlatformService.java:92) [launcher.jar:?]
	at com.polarion.psvn.launcher.PolarionSVNApplication.runImpl(PolarionSVNApplication.java:139) [launcher.jar:?]
	at com.polarion.psvn.launcher.PolarionSVNApplication.run(PolarionSVNApplication.java:94) [launcher.jar:?]
	at com.polarion.core.boot.launchers.BasicAppLauncher.launch(BasicAppLauncher.java:53) [boot.jar:?]
	at com.polarion.core.boot.impl.AppLaunchersManager.start(AppLaunchersManager.java:184) [boot.jar:?]
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:196) [org.eclipse.equinox.app_1.3.500.v20171221-2204.jar:?]
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:134) [org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:104) [org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:388) [org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:243) [org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:566) ~[?:?]
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:656) [org.eclipse.equinox.launcher_1.5.0.v20180512-1130.jar:?]
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:592) [org.eclipse.equinox.launcher_1.5.0.v20180512-1130.jar:?]
	at org.eclipse.equinox.launcher.Main.run(Main.java:1498) [org.eclipse.equinox.launcher_1.5.0.v20180512-1130.jar:?]
	at org.eclipse.equinox.launcher.Main.main(Main.java:1471) [org.eclipse.equinox.launcher_1.5.0.v20180512-1130.jar:?]
Caused by: com.thoughtworks.xstream.converters.ConversionException: 
---- Debugging information ----
cause-exception     : com.thoughtworks.xstream.mapper.CannotResolveClassException
cause-message       : AutoBranchGitLab
class               : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
required-type       : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
converter-type      : com.thoughtworks.xstream.converters.collections.ArrayConverter
version             : 1.4.17
-------------------------------
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convert(TreeUnmarshaller.java:77) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convertAnother(TreeUnmarshaller.java:66) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convertAnother(TreeUnmarshaller.java:50) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.start(TreeUnmarshaller.java:134) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.AbstractTreeMarshallingStrategy.unmarshal(AbstractTreeMarshallingStrategy.java:32) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.unmarshal(XStream.java:1431) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.unmarshal(XStream.java:1411) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.fromXML(XStream.java:1305) ~[xstream-1.4.17.jar:1.4.17]
	at com.polarion.platform.repository.external.internal.RepositoriesDataHandler.processData(RepositoriesDataHandler.java:119) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.spi.config.AbstractDataHandler.readLocation(AbstractDataHandler.java:83) ~[platform-repository.jar:?]
	... 56 more
Caused by: com.thoughtworks.xstream.mapper.CannotResolveClassException: AutoBranchGitLab
	at com.thoughtworks.xstream.mapper.DefaultMapper.realClass(DefaultMapper.java:81) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.DynamicProxyMapper.realClass(DynamicProxyMapper.java:55) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.PackageAliasingMapper.realClass(PackageAliasingMapper.java:88) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.ClassAliasingMapper.realClass(ClassAliasingMapper.java:79) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.ArrayMapper.realClass(ArrayMapper.java:74) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.SecurityMapper.realClass(SecurityMapper.java:71) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.CachingMapper.realClass(CachingMapper.java:47) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.util.HierarchicalStreams.readClassType(HierarchicalStreams.java:29) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.converters.collections.AbstractCollectionConverter.readBareItem(AbstractCollectionConverter.java:131) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.converters.collections.AbstractCollectionConverter.readItem(AbstractCollectionConverter.java:117) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.converters.collections.AbstractCollectionConverter.readCompleteItem(AbstractCollectionConverter.java:147) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.converters.collections.ArrayConverter.unmarshal(ArrayConverter.java:54) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convert(TreeUnmarshaller.java:72) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convertAnother(TreeUnmarshaller.java:66) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convertAnother(TreeUnmarshaller.java:50) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.start(TreeUnmarshaller.java:134) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.AbstractTreeMarshallingStrategy.unmarshal(AbstractTreeMarshallingStrategy.java:32) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.unmarshal(XStream.java:1431) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.unmarshal(XStream.java:1411) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.fromXML(XStream.java:1305) ~[xstream-1.4.17.jar:1.4.17]
	at com.polarion.platform.repository.external.internal.RepositoriesDataHandler.processData(RepositoriesDataHandler.java:119) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.spi.config.AbstractDataHandler.readLocation(AbstractDataHandler.java:83) ~[platform-repository.jar:?]
	... 56 more
2025-07-29 18:07:35,032 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-29 18:07:35,043 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.RevisionsPersistenceModule - Processing new revisions from repository default in context ContextId[context [global]] finished
2025-07-29 18:07:35,044 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.RevisionsPersistenceModule - Processing new revisions [FINISHED].
2025-07-29 18:07:35,044 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.24 s. ]
2025-07-29 18:07:35,044 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.21 s [100% getReadConfiguration (48x)] (48x), svn: 0.0755 s [84% info (18x)] (38x)
2025-07-29 18:07:35,068 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:07:35,068 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-29 18:07:35,068 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Inspecting repository for build artifacts-related changes
2025-07-29 18:07:35,068 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[context [global]]
2025-07-29 18:07:35,068 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[context [global]]
2025-07-29 18:07:35,069 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[context [global]] has been successfully processed
2025-07-29 18:07:35,072 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[context [global]] finished [ TIME 0.00365 s. ]
2025-07-29 18:07:35,072 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-29 18:07:35,072 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context [global]]
2025-07-29 18:07:35,072 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context [global]]
2025-07-29 18:07:35,093 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context [global]] has been successfully processed
2025-07-29 18:07:35,126 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context [global]] finished [ TIME 0.0539 s. ]
2025-07-29 18:07:35,126 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-29 18:07:35,126 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context WBS]
2025-07-29 18:07:35,126 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context WBS]
2025-07-29 18:07:35,147 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context WBS] has been successfully processed
2025-07-29 18:07:35,177 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context WBS] finished [ TIME 0.051 s. ]
2025-07-29 18:07:35,177 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-07-29 18:07:35,177 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context --Demo Projects]
2025-07-29 18:07:35,177 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context --Demo Projects]
2025-07-29 18:07:35,190 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context --Demo Projects] has been successfully processed
2025-07-29 18:07:35,203 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context --Demo Projects] finished [ TIME 0.0261 s. ]
2025-07-29 18:07:35,203 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (4/9)
2025-07-29 18:07:35,203 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context library]
2025-07-29 18:07:35,203 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context library]
2025-07-29 18:07:35,212 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context library] has been successfully processed
2025-07-29 18:07:35,226 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context library] finished [ TIME 0.023 s. ]
2025-07-29 18:07:35,226 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (5/9)
2025-07-29 18:07:35,226 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context WBSdev]
2025-07-29 18:07:35,226 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context WBSdev]
2025-07-29 18:07:35,242 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context WBSdev] has been successfully processed
2025-07-29 18:07:35,263 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context WBSdev] finished [ TIME 0.0371 s. ]
2025-07-29 18:07:35,264 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (6/9)
2025-07-29 18:07:35,264 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context hesai]
2025-07-29 18:07:35,264 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context hesai]
2025-07-29 18:07:35,285 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context hesai] has been successfully processed
2025-07-29 18:07:35,309 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context hesai] finished [ TIME 0.0455 s. ]
2025-07-29 18:07:35,309 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-07-29 18:07:35,309 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context drivepilot]
2025-07-29 18:07:35,309 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context drivepilot]
2025-07-29 18:07:35,328 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context drivepilot] has been successfully processed
2025-07-29 18:07:35,357 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context drivepilot] finished [ TIME 0.0478 s. ]
2025-07-29 18:07:35,357 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-29 18:07:35,357 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context elibrary]
2025-07-29 18:07:35,357 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context elibrary]
2025-07-29 18:07:35,373 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context elibrary] has been successfully processed
2025-07-29 18:07:35,388 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context elibrary] finished [ TIME 0.0305 s. ]
2025-07-29 18:07:35,388 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-29 18:07:35,388 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... repository inspection finished [ TIME 0.32 s. ]
2025-07-29 18:07:35,388 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.34 s. ]
2025-07-29 18:07:35,388 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.277 s [76% info (94x), 17% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.207 s [100% getReadConfiguration (54x)] (54x)
2025-07-29 18:07:35,388 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:07:35,388 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-29 18:07:35,388 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Inspecting BIR for new or removed builds
2025-07-29 18:07:35,401 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... 0 build(s) were removed (including calculations from previous run)
2025-07-29 18:07:35,401 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... 0 build(s) were added or modified
2025-07-29 18:07:35,401 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... BIR inspection finished [ TIME 0.0132 s. ]
2025-07-29 18:07:35,401 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-07-29 18:07:35,401 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:07:35,401 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-29 18:07:35,401 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.PersistenceEngineListener - Flushing startup index events, starting iterations.
2025-07-29 18:07:35,401 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.PersistenceEngineListener - Iteration 1 - processing 5 events
2025-07-29 18:07:35,407 [main | u:p] INFO  com.polarion.alm.tracker.internal.planning.PlanFieldsProvider - livePlanXMLLocation: Location[path /default/.reports/xml/live-plan.xml]
2025-07-29 18:07:35,426 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.PersistenceEngineListener -  - reindexing 1 existing objects and 0 deleted objects.
2025-07-29 18:07:35,514 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-29 18:07:35,521 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-29 18:07:35,521 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Attachment
2025-07-29 18:07:35,521 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: User
2025-07-29 18:07:35,521 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: WikiPage
2025-07-29 18:07:35,521 [main | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Revision
2025-07-29 18:07:35,533 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Attachment
2025-07-29 18:07:35,534 [PolarionDocIdCreator-6] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: RichPageAttachment
2025-07-29 18:07:35,539 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: WorkItem
2025-07-29 18:07:35,560 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: WikiPage
2025-07-29 18:07:35,560 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: User
2025-07-29 18:07:35,560 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Category
2025-07-29 18:07:35,565 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Category
2025-07-29 18:07:35,565 [PolarionDocIdCreator-6] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: RichPageAttachment
2025-07-29 18:07:35,567 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: UserGroup
2025-07-29 18:07:35,567 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: WorkItem
2025-07-29 18:07:35,568 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: UserGroup
2025-07-29 18:07:35,568 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: BaselineCollection
2025-07-29 18:07:35,573 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: TestRun
2025-07-29 18:07:35,578 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: BaselineCollection
2025-07-29 18:07:35,578 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: TestRun
2025-07-29 18:07:35,578 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Build
2025-07-29 18:07:35,580 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Build
2025-07-29 18:07:35,580 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: ModuleComment
2025-07-29 18:07:35,581 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Comment
2025-07-29 18:07:35,582 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Comment
2025-07-29 18:07:35,582 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: DocumentWorkflowSignature
2025-07-29 18:07:35,582 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: DocumentWorkflowSignature
2025-07-29 18:07:35,583 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: ModuleComment
2025-07-29 18:07:35,583 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: BuildArtifact
2025-07-29 18:07:35,583 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: WikiPageAttachment
2025-07-29 18:07:35,583 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: TimePoint
2025-07-29 18:07:35,584 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: WikiPageAttachment
2025-07-29 18:07:35,584 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: TimePoint
2025-07-29 18:07:35,585 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Baseline
2025-07-29 18:07:35,587 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: BuildArtifact
2025-07-29 18:07:35,592 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Baseline
2025-07-29 18:07:35,592 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: TestRunAttachment
2025-07-29 18:07:35,593 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: WorkRecord
2025-07-29 18:07:35,593 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: WorkItem-OutlineNumbers
2025-07-29 18:07:35,594 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: WorkItem-OutlineNumbers
2025-07-29 18:07:35,594 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: TestRunAttachment
2025-07-29 18:07:35,595 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: ModuleAttachment
2025-07-29 18:07:35,595 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Project
2025-07-29 18:07:35,595 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: WorkRecord
2025-07-29 18:07:35,595 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: TestRunComment
2025-07-29 18:07:35,595 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Plan
2025-07-29 18:07:35,596 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: TestRunComment
2025-07-29 18:07:35,596 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Revision
2025-07-29 18:07:35,601 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Project
2025-07-29 18:07:35,602 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: RichPage
2025-07-29 18:07:35,603 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Revision
2025-07-29 18:07:35,603 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}20
2025-07-29 18:07:35,603 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}9
2025-07-29 18:07:35,603 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}19
2025-07-29 18:07:35,603 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}10
2025-07-29 18:07:35,603 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}15
2025-07-29 18:07:35,603 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}16
2025-07-29 18:07:35,603 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}17
2025-07-29 18:07:35,603 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}18
2025-07-29 18:07:35,604 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}7
2025-07-29 18:07:35,604 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}8
2025-07-29 18:07:35,604 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}26
2025-07-29 18:07:35,604 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}32
2025-07-29 18:07:35,604 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}23
2025-07-29 18:07:35,604 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}21
2025-07-29 18:07:35,604 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}22
2025-07-29 18:07:35,604 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}24
2025-07-29 18:07:35,604 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}25
2025-07-29 18:07:35,604 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}31
2025-07-29 18:07:35,604 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}35
2025-07-29 18:07:35,604 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}37
2025-07-29 18:07:35,604 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}36
2025-07-29 18:07:35,604 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}39
2025-07-29 18:07:35,604 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}40
2025-07-29 18:07:35,604 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}41
2025-07-29 18:07:35,604 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}42
2025-07-29 18:07:35,604 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}43
2025-07-29 18:07:35,604 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}46
2025-07-29 18:07:35,604 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}47
2025-07-29 18:07:35,604 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}59
2025-07-29 18:07:35,605 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}51
2025-07-29 18:07:35,605 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}52
2025-07-29 18:07:35,605 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}53
2025-07-29 18:07:35,605 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}55
2025-07-29 18:07:35,605 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}60
2025-07-29 18:07:35,605 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}61
2025-07-29 18:07:35,605 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}65
2025-07-29 18:07:35,605 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}66
2025-07-29 18:07:35,605 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}77
2025-07-29 18:07:35,605 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}67
2025-07-29 18:07:35,605 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}69
2025-07-29 18:07:35,605 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}79
2025-07-29 18:07:35,605 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}80
2025-07-29 18:07:35,605 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}81
2025-07-29 18:07:35,605 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}82
2025-07-29 18:07:35,605 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}83
2025-07-29 18:07:35,605 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}84
2025-07-29 18:07:35,605 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}85
2025-07-29 18:07:35,605 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}86
2025-07-29 18:07:35,605 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}87
2025-07-29 18:07:35,606 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}88
2025-07-29 18:07:35,606 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}89
2025-07-29 18:07:35,606 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}91
2025-07-29 18:07:35,606 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}92
2025-07-29 18:07:35,606 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}93
2025-07-29 18:07:35,606 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/WBS:local/${Revision}09c2010030e517ae250d033127310dd72f63a0ef
2025-07-29 18:07:35,606 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/WBS:local/${Revision}7789a94e058df81e542433b71b0f51142728d99e
2025-07-29 18:07:35,606 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}99
2025-07-29 18:07:35,606 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}103
2025-07-29 18:07:35,606 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}100
2025-07-29 18:07:35,606 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}101
2025-07-29 18:07:35,606 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}96
2025-07-29 18:07:35,606 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}97
2025-07-29 18:07:35,606 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}108
2025-07-29 18:07:35,606 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}105
2025-07-29 18:07:35,606 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}107
2025-07-29 18:07:35,606 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}114
2025-07-29 18:07:35,606 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}115
2025-07-29 18:07:35,606 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}116
2025-07-29 18:07:35,606 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}117
2025-07-29 18:07:35,606 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}118
2025-07-29 18:07:35,606 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}110
2025-07-29 18:07:35,606 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}119
2025-07-29 18:07:35,606 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}120
2025-07-29 18:07:35,606 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}121
2025-07-29 18:07:35,606 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}122
2025-07-29 18:07:35,606 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}123
2025-07-29 18:07:35,606 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}124
2025-07-29 18:07:35,606 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}125
2025-07-29 18:07:35,606 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}126
2025-07-29 18:07:35,606 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}127
2025-07-29 18:07:35,606 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}130
2025-07-29 18:07:35,606 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}136
2025-07-29 18:07:35,606 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}128
2025-07-29 18:07:35,606 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}129
2025-07-29 18:07:35,606 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}131
2025-07-29 18:07:35,606 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}132
2025-07-29 18:07:35,606 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}133
2025-07-29 18:07:35,606 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}134
2025-07-29 18:07:35,606 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}135
2025-07-29 18:07:35,606 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}142
2025-07-29 18:07:35,606 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}137
2025-07-29 18:07:35,606 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}138
2025-07-29 18:07:35,606 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}140
2025-07-29 18:07:35,606 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}141
2025-07-29 18:07:35,606 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}143
2025-07-29 18:07:35,606 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}144
2025-07-29 18:07:35,607 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}145
2025-07-29 18:07:35,607 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}147
2025-07-29 18:07:35,607 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}148
2025-07-29 18:07:35,607 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}149
2025-07-29 18:07:35,607 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}151
2025-07-29 18:07:35,607 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}152
2025-07-29 18:07:35,607 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}154
2025-07-29 18:07:35,607 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}156
2025-07-29 18:07:35,607 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}158
2025-07-29 18:07:35,607 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}159
2025-07-29 18:07:35,607 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}171
2025-07-29 18:07:35,607 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}166
2025-07-29 18:07:35,607 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}167
2025-07-29 18:07:35,607 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}174
2025-07-29 18:07:35,607 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}176
2025-07-29 18:07:35,607 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}169
2025-07-29 18:07:35,607 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}160
2025-07-29 18:07:35,607 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}162
2025-07-29 18:07:35,607 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}164
2025-07-29 18:07:35,607 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}182
2025-07-29 18:07:35,607 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}183
2025-07-29 18:07:35,607 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}185
2025-07-29 18:07:35,607 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}186
2025-07-29 18:07:35,607 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}187
2025-07-29 18:07:35,607 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}188
2025-07-29 18:07:35,607 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}177
2025-07-29 18:07:35,607 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}178
2025-07-29 18:07:35,607 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}179
2025-07-29 18:07:35,607 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}196
2025-07-29 18:07:35,607 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}200
2025-07-29 18:07:35,607 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}189
2025-07-29 18:07:35,607 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}197
2025-07-29 18:07:35,607 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}198
2025-07-29 18:07:35,607 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}201
2025-07-29 18:07:35,607 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: ModuleAttachment
2025-07-29 18:07:35,607 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}202
2025-07-29 18:07:35,607 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}203
2025-07-29 18:07:35,607 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}204
2025-07-29 18:07:35,609 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.106 s, CPU [user: 0.026 s, system: 0.00838 s], Allocated memory: 10.6 MB
2025-07-29 18:07:35,609 [PolarionDocIdCreator-6] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: ProjectGroup
2025-07-29 18:07:35,611 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Plan
2025-07-29 18:07:35,612 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Module
2025-07-29 18:07:35,612 [PolarionDocIdCreator-6] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: ProjectGroup
2025-07-29 18:07:35,615 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: RichPage
2025-07-29 18:07:35,615 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Module
2025-07-29 18:07:35,623 [PolarionDocIdCreator-1] INFO  com.polarion.subterra.index.impl.lucene.baseline.PolarionDocIdCreator - Bloom filter loading for 28 indices took  [ TIME 0.126 s. ]
2025-07-29 18:07:35,661 [main | u:p] INFO  com.polarion.platform.persistence.internal.calcfields.DelegatingCalculatedFieldsListener - Calculated fields mode: async
2025-07-29 18:07:35,665 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.PersistenceEngineListener - Flushing took  [ TIME 0.263 s. ]
2025-07-29 18:07:35,665 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.26 s. ]
2025-07-29 18:07:35,665 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.263 s [100% doFinishStartup (1x)] (1x), commit: 0.0731 s [100% Revision (1x)] (1x), Lucene: 0.0384 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.0146 s [100% objectsToInv (1x)] (1x)
2025-07-29 18:07:35,665 [main | u:p] INFO  com.polarion.platform.internal.service.repository.ListenerManager - Starting the pulling job for repository: default
2025-07-29 18:07:35,666 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Persistence initialization finished [ TIME 1.68 s. ]
2025-07-29 18:07:35,666 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:07:35,666 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-29 18:07:35,667 [main | u:p] INFO  com.polarion.platform.persistence.internal.calcfields.CalculatedFieldsStorage - Checking integrity of calculated fields storage /opt/polarion/data/workspace/polarion-data/calculated-fields
2025-07-29 18:07:35,676 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-29 18:07:35,676 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:07:35,676 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-29 18:07:35,698 [main | u:p] INFO  com.polarion.platform.jobs.internal.service.scheduler.JobSchedulerService - Updating local scheduler state: start
2025-07-29 18:07:35,709 [main | u:p | u:p] INFO  org.quartz.simpl.SimpleThreadPool - Job execution threads will use class loader of thread: main | u:p | u:p
2025-07-29 18:07:35,712 [main | u:p | u:p] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
2025-07-29 18:07:35,712 [main | u:p | u:p] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'DefaultQuartzScheduler' initialized from default resource file in Quartz package: 'quartz.properties'
2025-07-29 18:07:35,712 [main | u:p | u:p] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 1.4.2
2025-07-29 18:07:35,712 [main | u:p | u:p] INFO  org.quartz.core.QuartzScheduler - Scheduler DefaultQuartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-29 18:07:35,712 [main | u:p | u:p] INFO  org.quartz.core.QuartzScheduler - Scheduler DefaultQuartzScheduler_$_NON_CLUSTERED paused.
2025-07-29 18:07:35,713 [main | u:p | u:p] INFO  org.quartz.core.QuartzScheduler - Scheduler DefaultQuartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-29 18:07:35,714 [main | u:p | u:p] INFO  org.quartz.simpl.SimpleThreadPool - Job execution threads will use class loader of thread: main | u:p | u:p
2025-07-29 18:07:35,715 [main | u:p | u:p] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
2025-07-29 18:07:35,715 [main | u:p | u:p] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'DefaultQuartzScheduler' initialized from default resource file in Quartz package: 'quartz.properties'
2025-07-29 18:07:35,715 [main | u:p | u:p] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 1.4.2
2025-07-29 18:07:35,716 [main | u:p | u:p] INFO  com.polarion.platform.jobs.internal.service.scheduler.JobSchedulerService - 15 scheduled job(s) configured
2025-07-29 18:07:35,723 [main | u:p | u:p] INFO  org.quartz.core.QuartzScheduler - Scheduler DefaultQuartzScheduler_$_NON_CLUSTERED started.
2025-07-29 18:07:35,806 [main] INFO  com.polarion.psvn.launcher.internal.platform.PlatformService - Platform boot finished
2025-07-29 18:07:35,806 [main] INFO  com.polarion.psvn.launcher.internal.platform.PlatformService - Platform started
2025-07-29 18:07:35,815 [main] INFO  com.polarion.portal.tomcat.TomcatPlugin - Tomcat home directory was set to /opt/polarion/data/workspace/.metadata/.plugins/com.polarion.portal.tomcat
2025-07-29 18:07:35,822 [main] INFO  com.polarion.psvn.launcher.internal.tomcat.TomcatService - Starting Tomcat...
2025-07-29 18:07:35,901 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: webui, contextRoot: webapp/webui, plugin: com.polarion.alm.ui, priority: -10]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:07:35,902 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion, contextRoot: webapp/authapp, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:07:35,902 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/.well-known, contextRoot: webapp/well-known, plugin: com.polarion.platform, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:07:35,902 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/ProjectPlanGantt, contextRoot: webapp, plugin: com.polarion.alm.ProjectPlanGantt_new, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:07:35,902 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/activate, contextRoot: webapp/activation, plugin: com.polarion.psvn.launcher, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:07:35,902 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/announcements, contextRoot: webapp/announcements, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:07:35,902 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/bir, contextRoot: webapp/bir, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:07:35,903 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/codemirror-modes, contextRoot: webapp/codemirror-modes, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:07:35,903 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/doorsconnector, contextRoot: webapp, plugin: com.polarion.synchronizer, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:07:35,903 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/export, contextRoot: webapp/export, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:07:35,903 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/fileupload, contextRoot: webapp/fileupload, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:07:35,903 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/gwt, contextRoot: war, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:07:35,903 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/icons, contextRoot: webapp/icons, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:07:35,903 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/internal-login, contextRoot: webapp/internal-login, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:07:35,903 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/module-attachment, contextRoot: webapp/attachments, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:07:35,904 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/modulehome, contextRoot: webapp/module-home, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:07:35,904 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/mxgraph, contextRoot: draw.io/war, plugin: com.polarion.alm.ui.diagrams.mxgraph, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:07:35,904 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/oauth-feishu, contextRoot: webapp, plugin: com.fasnote.alm.auth.feishu, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:07:35,904 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/page-attachment, contextRoot: webapp/attachments, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:07:35,904 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/perf-testing, contextRoot: webapp/perf-testing, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:07:35,904 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/plugin-manage, contextRoot: webapp, plugin: com.fasnote.alm.plugin.manage, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:07:35,904 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/print, contextRoot: webapp/print, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:07:35,904 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/register, contextRoot: webapp/register, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:07:35,904 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/rest, contextRoot: webapp, plugin: com.siemens.polarion.rest, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:07:35,904 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/ria, contextRoot: webapp/ria, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:07:35,904 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/richpagehome, contextRoot: webapp/richpage-home, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:07:35,905 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/rt, contextRoot: src/main/webapp, plugin: com.siemens.polarion.rt, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:07:35,905 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/rt-connect, contextRoot: ws, plugin: com.siemens.polarion.rt.communication.polarion, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:07:35,905 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/scripting, contextRoot: webapp/scripting, plugin: com.polarion.scripting.servlet, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:07:35,905 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/sdk, contextRoot: webapp/sdk, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:07:35,905 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/svnwebclient, contextRoot: webapp, plugin: org.polarion.svnwebclient, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:07:35,905 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/swagger, contextRoot: webapp/swagger, plugin: com.siemens.polarion.rest, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:07:35,906 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/synchronizer, contextRoot: webapp, plugin: com.polarion.synchronizer.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:07:35,906 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/template-download, contextRoot: webapp/project-template, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:07:35,906 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/testrun-attachment, contextRoot: webapp/attachments, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:07:35,906 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/tour, contextRoot: webapp/tour, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:07:35,906 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/wi-attachment, contextRoot: webapp/attachments, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:07:35,906 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/wi-attachment-auth, contextRoot: webapp/wi-attachment-auth, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:07:35,906 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/widget-resource, contextRoot: webapp/widget-resource, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:07:35,906 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/wiki, contextRoot: src/main/webapp, plugin: com.polarion.alm.wiki, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:07:35,906 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/workreport, contextRoot: webapp/workreport, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:07:35,906 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/ws, contextRoot: ws, plugin: com.polarion.alm.ws, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:07:35,906 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/xunitimport, contextRoot: webapp/xunitimport, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:07:35,906 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/oslc, contextRoot: webapp, plugin: com.polarion.alm.oslc, priority: 1]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:07:35,961 [main] INFO  org.apache.coyote.ajp.AjpNioProtocol - Initializing ProtocolHandler ["ajp-nio-127.0.0.1-8889"]
2025-07-29 18:07:35,967 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-29 18:07:35,967 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.53]
2025-07-29 18:07:35,992 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@707f6ab1] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:07:35,992 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@5aa686cf] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:07:35,992 [Catalina-utility-2] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@1edd6c8c] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:07:35,992 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@225be644] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:07:35,993 [Catalina-utility-4] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@22f07a00] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:07:35,993 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@60c69660] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:07:36,007 [Catalina-utility-5] INFO  org.apache.catalina.startup.ContextConfig - No global web.xml found
2025-07-29 18:07:36,016 [Catalina-utility-1] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:07:36,016 [Catalina-utility-5] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:07:36,017 [Catalina-utility-4] WARN  org.apache.catalina.startup.ContextConfig - Security role name [admin] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:07:36,017 [Catalina-utility-4] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:07:36,026 [Catalina-utility-2] FATAL org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/polarion/oauth-feishu] - For security constraints with URL pattern [/userinfo] only the HTTP methods [POST GET] are covered. All other methods are uncovered.
2025-07-29 18:07:36,040 [Catalina-utility-2] FATAL org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/polarion/oauth-feishu] - Servlet [FeishuUserInfoServlet] in web application [/polarion/oauth-feishu] threw load() exception
java.lang.ClassNotFoundException: com.fasnote.alm.auth.feishu.FeishuUserInfoServlet
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1407) ~[catalina.jar:9.0.53]
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1215) ~[catalina.jar:9.0.53]
	at org.apache.catalina.core.DefaultInstanceManager.loadClass(DefaultInstanceManager.java:538) ~[catalina.jar:9.0.53]
	at org.apache.catalina.core.DefaultInstanceManager.loadClassMaybePrivileged(DefaultInstanceManager.java:519) ~[catalina.jar:9.0.53]
	at org.apache.catalina.core.DefaultInstanceManager.newInstance(DefaultInstanceManager.java:149) ~[catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1049) ~[catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:989) ~[catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardContext.loadOnStartup(StandardContext.java:4957) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5264) [catalina.jar:9.0.53]
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1396) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1386) [catalina.jar:9.0.53]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628) [?:?]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-util.jar:9.0.53]
	at java.lang.Thread.run(Thread.java:829) [?:?]
2025-07-29 18:07:36,045 [Catalina-utility-4] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@d1fb877] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:07:36,045 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@78863bf8] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:07:36,045 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@1c79d400] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:07:36,046 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@7220e4d5] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:07:36,046 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@51485383] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:07:36,049 [Catalina-utility-3] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:07:36,053 [Catalina-utility-2] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@1cad68ca] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:07:36,054 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@6dfbd571] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:07:36,054 [Catalina-utility-1] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:07:36,054 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@6c138e] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:07:36,055 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@63b7dc64] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:07:36,058 [Catalina-utility-3] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:07:36,058 [Catalina-utility-4] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:07:36,059 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@4c28b19f] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:07:36,061 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@4e4fe7ba] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:07:36,062 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@6098c5d8] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:07:36,062 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@7ace0b5d] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:07:36,064 [Catalina-utility-1] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:07:36,065 [Catalina-utility-5] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:07:36,066 [Catalina-utility-2] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:07:36,067 [Catalina-utility-1] INFO  org.apache.jasper.servlet.TldScanner - At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-07-29 18:07:36,067 [Catalina-utility-3] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:07:36,072 [Catalina-utility-6] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:07:36,074 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@142e0283] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:07:36,075 [Catalina-utility-2] INFO  org.apache.tomcat.dbcp.dbcp2.BasicDataSourceFactory - Name = XWikiDS Ignoring unknown property: value of "DB Connection" for "description" property
2025-07-29 18:07:36,078 [Catalina-utility-3] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:07:36,079 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@40ffbed1] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:07:36,081 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@7648167b] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:07:36,084 [Catalina-utility-6] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:07:36,091 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@4847d817] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:07:36,094 [Catalina-utility-6] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:07:36,096 [Catalina-utility-5] INFO  org.apache.jasper.servlet.TldScanner - At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-07-29 18:07:36,099 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@477cd83e] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:07:36,106 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@527afebf] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:07:36,111 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@7ec287d] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:07:36,112 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@522e238c] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:07:36,116 [Catalina-utility-3] INFO  com.polarion.portal.velocity.VelocityPathManager - VelocityTemplatesPath=/opt/polarion/polarion/plugins/com.polarion.alm.ui_3.22.1/webapp/authapp/, /opt/polarion/polarion/plugins/com.polarion.alm.ui_3.22.1/webapp/webui/, /opt/polarion/polarion/plugins/com.polarion.alm.wiki_3.22.1/src/main/webapp/, ., /opt/polarion/polarion/plugins/com.polarion.alm.ui_3.22.1/webapp/webui/
2025-07-29 18:07:36,118 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@47ca28c1] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:07:36,125 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@3a09f2f5] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:07:36,145 [Catalina-utility-5] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:07:36,149 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@9bd49ca] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:07:36,157 [Catalina-utility-5] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:07:36,157 [Catalina-utility-4] INFO  org.polarion.svncommons.commentscache.CommentsCache - Initializing comments cache. Id: http://localhost/repo, repository: http://localhost/repo/, url: http://localhost/repo/, cache directory: /opt/polarion/data/workspace/polarion-data/log-messages-cache, cache page size: 100
2025-07-29 18:07:36,161 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@38aeebb2] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:07:36,176 [Catalina-utility-5] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:07:36,180 [Catalina-utility-5] INFO  org.apache.jasper.servlet.TldScanner - At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-07-29 18:07:36,188 [Catalina-utility-2] INFO  org.apache.jasper.servlet.TldScanner - At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-07-29 18:07:36,189 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@4b7a8683] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:07:36,193 [Catalina-utility-4] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@63d3006a] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:07:36,196 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@1af03f1a] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:07:36,197 [Catalina-utility-4] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:07:36,200 [Catalina-utility-4] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@2b15284d] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:07:36,202 [Catalina-utility-4] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:07:36,202 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@229dfe7e] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:07:36,210 [Catalina-utility-4] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@4e5f391c] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:07:36,219 [Catalina-utility-4] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@7b1ca32d] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:07:36,224 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@53b2678] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:07:36,226 [Catalina-utility-4] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:07:36,228 [Catalina-utility-5] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:07:36,231 [Catalina-utility-4] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@73f4c35f] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:07:36,235 [Catalina-utility-4] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:07:36,248 [Catalina-utility-5] INFO  org.apache.jasper.servlet.TldScanner - At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-07-29 18:07:36,258 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@682c2bab] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:07:36,261 [Catalina-utility-5] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:07:36,269 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@57bb97ff] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:07:36,279 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@6b67805a] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:07:36,281 [Catalina-utility-5] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:07:36,283 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@287f0369] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:07:36,452 [Catalina-utility-6] INFO  com.siemens.polarion.rt.config.RtAppConfig - RT server config is created
2025-07-29 18:07:36,507 [Catalina-utility-6] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Started.
2025-07-29 18:07:37,360 [Catalina-utility-6] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Initialization of RT parsers...
2025-07-29 18:07:37,362 [Catalina-utility-6] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.java (contributed by 'com.siemens.polarion.rt[87]')
2025-07-29 18:07:37,363 [Catalina-utility-6] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.xml (contributed by 'com.siemens.polarion.rt[87]')
2025-07-29 18:07:37,365 [Catalina-utility-6] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.c (contributed by 'com.siemens.polarion.rt.parsers.c[93]')
2025-07-29 18:07:37,369 [Catalina-utility-6] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Initialization of RT collectors...
2025-07-29 18:07:37,371 [Catalina-utility-6] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Found collector for subversion (contributed by 'com.siemens.polarion.rt[87]')
2025-07-29 18:07:37,372 [Catalina-utility-6] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Found collector for git (contributed by 'com.siemens.polarion.rt.collectors.git[89]')
2025-07-29 18:07:37,472 [main] INFO  org.apache.coyote.ajp.AjpNioProtocol - Starting ProtocolHandler ["ajp-nio-127.0.0.1-8889"]
2025-07-29 18:07:37,477 [main] INFO  com.polarion.psvn.launcher.internal.tomcat.TomcatService - Tomcat is listening on port 8889 using AJP/1.3 protocol with 600000 timeout in millis
2025-07-29 18:07:37,477 [main] INFO  com.polarion.psvn.launcher.internal.help.HelpService - Starting Help Service...
2025-07-29 18:07:37,480 [main] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@3702c19] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:07:37,484 [main] INFO  com.polarion.psvn.launcher.internal.help.HelpService - Help Service started
2025-07-29 18:07:37,537 [main | u:p] INFO  com.xpn.xwiki.XWiki - xwiki.cfg taken from /WEB-INF/xwiki.cfg because the XWikiConfig variable is not set in the context
2025-07-29 18:07:37,978 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtCommunicationStartup - Rt configuration local mediator is started
2025-07-29 18:07:37,995 [Thread-33] INFO  class com.polarion.alm.server.util.ChartExporterStartup - Chart renderer says: Server started on 127.0.0.1:34567
2025-07-29 18:07:38,091 [ajp-nio-127.0.0.1-8889-exec-1 | cID:55a6e656-c0a8d700-7d06a187-85bb60c5] INFO  com.siemens.polarion.rt.dataprovider.controller.RtDataProviderController - RT server is notified to update all configurations.
2025-07-29 18:07:38,110 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtNotifier - RT server was successfully notified of configuration change.
2025-07-29 18:07:38,110 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtCommunicationStartup - Rt notification service is started
2025-07-29 18:07:38,125 [Thread-37] INFO  com.polarion.platform.jobs.info - Job "Attachment Indexer" has id 55a6e683-c0a8d700-7d06a187-9ed6fdcf
2025-07-29 18:07:38,126 [Thread-37] INFO  com.polarion.platform.jobs.info - State of job "Attachment Indexer" changed to UNSCHEDULED
2025-07-29 18:07:38,126 [Thread-37] INFO  com.polarion.platform.jobs.info - Working directory of root job "Attachment Indexer" is /opt/polarion/data/workspace/polarion-data/jobs/20250729-1807
2025-07-29 18:07:38,127 [Thread-37] INFO  com.polarion.platform.jobs.info - Job "Attachment Indexer" runs as user "polarion"
2025-07-29 18:07:38,128 [Thread-37 | u:p] INFO  com.polarion.platform.jobs.info - State of job "Attachment Indexer" changed to ACTIVATING
2025-07-29 18:07:38,128 [Thread-37 | u:p] INFO  com.polarion.platform.jobs.info - State of job "Attachment Indexer" changed to WAITING
2025-07-29 18:07:38,130 [main | u:p] INFO  com.polarion.platform.monitoring - Next slow periodic actions will be executed on Wed Jul 30 01:00:38 CST 2025
2025-07-29 18:07:38,130 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.alm.tracker.internal.HttpsConfiguratorStartup successfully initialized
2025-07-29 18:07:38,130 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.alm.server.util.ChartExporterStartup successfully initialized
2025-07-29 18:07:38,130 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.alm.wiki.WikiPlugin successfully initialized
2025-07-29 18:07:38,130 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.alm.qcentre.internal.QCentreStartup successfully initialized
2025-07-29 18:07:38,130 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.siemens.polarion.rt.communication.connection.RtCommunicationStartup successfully initialized
2025-07-29 18:07:38,130 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.platform.internal.startup.NotificationServerStartup successfully initialized
2025-07-29 18:07:38,130 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.subterra.index.impl.IndexingJobsStartup successfully initialized
2025-07-29 18:07:38,130 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.alm.ui.server.ServerStartup successfully initialized
2025-07-29 18:07:38,131 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.alm.server.util.FormulaServerStartup successfully initialized
2025-07-29 18:07:38,131 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.platform.monitoring.internal.MonitoringServiceStart successfully initialized
2025-07-29 18:07:38,131 [main] INFO  com.polarion.platform.monitoring - Executing actions from stage POSTBOOT
2025-07-29 18:07:38,132 [main] INFO  com.polarion.platform.monitoring - Executing action 'polarion.info.cache.statistics'
2025-07-29 18:07:38,134 [Worker-0: Attachment Indexer | u:p] INFO  com.polarion.platform.jobs.info - State of job "Attachment Indexer" changed to RUNNING
2025-07-29 18:07:38,134 [Thread-37] INFO  com.polarion.platform.jobs.info - Job "DB History Creator" has id 55a6e694-c0a8d700-7d06a187-8e53a21c
2025-07-29 18:07:38,135 [Thread-37] INFO  com.polarion.platform.jobs.info - State of job "DB History Creator" changed to UNSCHEDULED
2025-07-29 18:07:38,135 [Thread-37] INFO  com.polarion.platform.jobs.info - Working directory of root job "DB History Creator" is /opt/polarion/data/workspace/polarion-data/jobs/20250729-1807_0
2025-07-29 18:07:38,136 [Thread-37] INFO  com.polarion.platform.jobs.info - Job "DB History Creator" runs as user "polarion"
2025-07-29 18:07:38,137 [Thread-37 | u:p] INFO  com.polarion.platform.jobs.info - State of job "DB History Creator" changed to ACTIVATING
2025-07-29 18:07:38,137 [Thread-37 | u:p] INFO  com.polarion.platform.jobs.info - State of job "DB History Creator" changed to WAITING
2025-07-29 18:07:38,141 [main] INFO  com.polarion.platform.monitoring - polarion.info.cache.statistics (Statistics of caches) = Statistics of caches 
                         CACHE       HITS     MISSES  HIT_RATIO       GETS       PUTS    REMOVAL   EVICTION 
     attachments-content-cache          0          0          0%          0          0          0          0 
                baseline-cache          0          0          0%          0          0          0          0 
                            bq          0          0          0%          0          0          0          0 
                dao-attachment          0          0          0%          0          0          0          0 
                   dao-comment          0          0          0%          0          0          0          0 
                    dao-global          0          4          0%          4          0          0          0 
                    dao-module          0          0          0%          0          0          0          0 
          dao-moduleattachment          0          0          0%          0          0          0          0 
             dao-modulecomment          0          0          0%          0          0          0          0 
                      dao-plan          0          0          0%          0          0          0          0 
                   dao-project          0          0          0%          0          0          0          0 
              dao-projectgroup          0          6          0%          6          3          0          0 
                  dao-richpage          0          0          0%          0          0          0          0 
        dao-richpageattachment          0          0          0%          0          0          0          0 
                   dao-testrun          0          0          0%          0          0          0          0 
         dao-testrunattachment          0          0          0%          0          0          0          0 
                      dao-user          0          0          0%          0          0          0          0 
                  dao-wikipage          0          0          0%          0          0          0          0 
        dao-wikipageattachment          0          0          0%          0          0          0          0 
                  dao-workitem          0          0          0%          0          0          0          0 
      derived-linked-revisions          0          0          0%          0          0          0          0 
                  formulas-svg          0          0          0%          0          0          0          0 
                github-commits          0          0          0%          0          0          0          0 
            historical-queries          0          0          0%          0          0          0          0 
      historical-queries-sizes          0          0          0%          0          0          0          0 
        oslc-linked-item-cache          0          0          0%          0          0          0          0 
               plan-statistics          0          0          0%          0          0          0          0 
                   rmd-default          3          5         38%          8          6          0          0 
                   ss-combined          0          0          0%          0          0          0          0 
                    ss-context          0          0          0%          0          0          0          0 
                     wiki-page          2          8         20%         10          9          1          0 
              wiki-page-exists          0          0          0%          0          9          1          0 

 [Tue Jul 29 18:07:38 CST 2025]
2025-07-29 18:07:38,141 [Worker-1: DB History Creator | u:p] INFO  com.polarion.platform.jobs.info - State of job "DB History Creator" changed to RUNNING
2025-07-29 18:07:38,144 [main] INFO  com.polarion.platform.monitoring - Finished with actions from stage POSTBOOT: {OK=1}
2025-07-29 18:07:38,145 [Thread-36] INFO  com.polarion.core.util.process.JavaRunner - Executing /Users/<USER>/Library/Java/JavaVirtualMachines/ms-11.0.27/Contents/Home/bin/java
  -- args [-jar, /opt/polarion/polarion/plugins/com.polarion.platform_3.22.1/services/notification-service/NotificationService.jar, --server.port=40608, --jwksUrl=http://localhost/polarion/.well-known/jwks.json]
  -- env null
  -- dir /var/folders/z_/shw6wc7d7ps_fjvv781t4gt80000gn/T/polarionSpringServerSubprocess15589183122966700889.tmp
2025-07-29 18:07:38,145 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.47 s. ]
2025-07-29 18:07:38,145 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.297 s [90% info (158x)] (168x)
2025-07-29 18:07:38,145 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 18:07:38,145 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 9 s. ]
2025-07-29 18:07:38,145 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 18:07:38,148 [Worker-0: Attachment Indexer | u:p] INFO  com.polarion.platform.jobs.info - State of job "Attachment Indexer" changed to FINISHED
2025-07-29 18:07:38,149 [Worker-0: Attachment Indexer | u:p] INFO  com.polarion.platform.jobs.info - Status of job "Attachment Indexer" is OK
2025-07-29 18:07:38,149 [PreLoadDataService] INFO  com.polarion.psvn.launcher.internal.data.PreLoadDataService - Preloading data ...
2025-07-29 18:07:38,292 [Activities-Bulk-Publisher] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Activities
2025-07-29 18:07:38,400 [PreLoadDataService | u:p] DEBUG com.fasnote.alm.queryexpander.queryExpanderInterceptorFactory - Creating SingletonProxy for service com.fasnote.alm.queryexpander.queryExpanderInterceptorFactory
2025-07-29 18:07:38,402 [PreLoadDataService | u:p] DEBUG com.fasnote.alm.queryexpander.queryExpanderInterceptorFactory - Constructing core service implementation for service com.fasnote.alm.queryexpander.queryExpanderInterceptorFactory
2025-07-29 18:07:38,443 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: TimePoint
2025-07-29 18:07:38,444 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: TimePoint
2025-07-29 18:07:38,451 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Category
2025-07-29 18:07:38,470 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Category
2025-07-29 18:07:38,568 [Thread-43] INFO  class com.polarion.alm.server.util.FormulaServerStartup - Formula renderer says: Server started on 127.0.0.1:34568
2025-07-29 18:07:38,822 [Thread-40] INFO  NotificationService - 
2025-07-29 18:07:38,823 [Thread-40] INFO  NotificationService -   .   ____          _            __ _ _
2025-07-29 18:07:38,823 [Thread-40] INFO  NotificationService -  /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
2025-07-29 18:07:38,823 [Thread-40] INFO  NotificationService - ( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
2025-07-29 18:07:38,823 [Thread-40] INFO  NotificationService -  \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
2025-07-29 18:07:38,823 [Thread-40] INFO  NotificationService -   '  |____| .__|_| |_|_| |_\__, | / / / /
2025-07-29 18:07:38,823 [Thread-40] INFO  NotificationService -  =========|_|==============|___/=/_/_/_/
2025-07-29 18:07:38,825 [Thread-40] INFO  NotificationService -  :: Spring Boot ::                (v2.6.6)
2025-07-29 18:07:38,825 [Thread-40] INFO  NotificationService - 
2025-07-29 18:07:38,911 [Thread-40] INFO  NotificationService - [main] INFO  c.s.polarion.service.notification.Application - Starting Application using Java 11.0.27 on zhangwentiandeMac-mini-2.local with PID 37793 (/opt/polarion/polarion/plugins/com.polarion.platform_3.22.1/services/notification-service/NotificationService.jar started by zhangwentian in /private/var/folders/z_/shw6wc7d7ps_fjvv781t4gt80000gn/T/polarionSpringServerSubprocess15589183122966700889.tmp)
2025-07-29 18:07:38,911 [Thread-40] INFO  NotificationService - [main] INFO  c.s.polarion.service.notification.Application - No active profile set, falling back to 1 default profile: "default"
2025-07-29 18:07:39,064 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  com.polarion.subterra.index.impl.ObjectIndex - Preloading of baselines: 22, for prototypes: WorkItem; with days range: 180d, took  [ TIME 0.827 s. ] 
2025-07-29 18:07:39,065 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.842 s, CPU [user: 0.00648 s, system: 0.00119 s], Allocated memory: 531.2 kB, transactions: 1
2025-07-29 18:07:39,066 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 28, Incremental Baseline: 0.0288 s [100% WorkItem (21x)] (21x), notification worker: 0.023 s [59% RevisionActivityCreator (2x), 17% WorkItemActivityCreator (1x), 16% PlanActivityCreator (1x)] (6x), Lucene: 0.0181 s [77% add (1x), 23% refresh (1x)] (2x), resolve: 0.0168 s [61% User (1x), 39% Revision (2x)] (3x), persistence listener: 0.0139 s [76% indexRefreshPersistenceListener (1x), 15% WorkItemActivityCreator (1x)] (7x), PullingJob: 0.00678 s [100% collectChanges (1x)] (1x), svn: 0.00667 s [55% testConnection (1x), 45% getLatestRevision (1x)] (2x), Full Baseline: 0.00524 s [100% WorkItem (1x)] (1x), ObjectMaps: 0.00208 s [100% getPrimaryObjectLocation (1x)] (1x)
2025-07-29 18:07:39,067 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.925 s, CPU [user: 0.174 s, system: 0.0258 s], Allocated memory: 18.3 MB, transactions: 22, svn: 0.761 s [99% getDatedRevision (181x)] (183x), Lucene: 0.0481 s [77% buildBaselineSnapshots (1x), 23% buildBaseline (22x)] (23x)
2025-07-29 18:07:39,068 [Worker-1: DB History Creator | u:p] INFO  com.polarion.platform.jobs.info - State of job "DB History Creator" changed to FINISHED
2025-07-29 18:07:39,069 [Worker-1: DB History Creator | u:p] INFO  com.polarion.platform.jobs.info - Status of job "DB History Creator" is OK
2025-07-29 18:07:39,569 [Thread-40] INFO  NotificationService - [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 40608 (http)
2025-07-29 18:07:39,581 [Thread-40] INFO  NotificationService - [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-40608"]
2025-07-29 18:07:39,581 [Thread-40] INFO  NotificationService - [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-29 18:07:39,581 [Thread-40] INFO  NotificationService - [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-07-29 18:07:39,598 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.ObjectIndex - Preloading of baselines: 22, for prototypes: WorkItem; with days range: 180d, took  [ TIME 0.533 s. ] 
2025-07-29 18:07:39,614 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661569b9a9c40_0_661569b9a9c40_0_: finished. Total: 1.46 s, CPU [user: 0.441 s, system: 0.0916 s], Allocated memory: 54.6 MB, svn: 0.877 s [59% getDatedRevision (181x), 25% getDir2 content (25x)] (307x), resolve: 0.494 s [100% Category (96x)] (96x), ObjectMaps: 0.145 s [40% getPrimaryObjectLocation (96x), 39% getPrimaryObjectProperty (96x), 20% getLastPromoted (96x)] (388x)
2025-07-29 18:07:39,617 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: ProjectGroup
2025-07-29 18:07:39,621 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: ProjectGroup
2025-07-29 18:07:39,627 [Thread-40] INFO  NotificationService - [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-29 18:07:39,627 [Thread-40] INFO  NotificationService - [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 663 ms
2025-07-29 18:07:39,644 [Thread-40] INFO  NotificationService - [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing AtmosphereFramework
2025-07-29 18:07:39,647 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Project
2025-07-29 18:07:39,653 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Project
2025-07-29 18:07:39,718 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: User
2025-07-29 18:07:39,722 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: User
2025-07-29 18:07:39,848 [Thread-40] INFO  NotificationService - [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-40608"]
2025-07-29 18:07:39,857 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661569bb31448_0_661569bb31448_0_: finished. Total: 0.139 s, CPU [user: 0.0671 s, system: 0.0107 s], Allocated memory: 8.4 MB, RepositoryConfigService: 0.0611 s [52% getReadConfiguration (162x), 48% getReadUserConfiguration (10x)] (172x), svn: 0.0568 s [53% info (19x), 40% getFile content (16x)] (37x), resolve: 0.0438 s [100% User (9x)] (9x), ObjectMaps: 0.0231 s [44% getPrimaryObjectProperty (9x), 41% getPrimaryObjectLocation (9x)] (37x)
2025-07-29 18:07:39,881 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Atmosphere is using org.atmosphere.cpr.DefaultAnnotationProcessor for processing annotation
2025-07-29 18:07:39,881 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.DefaultAnnotationProcessor - AnnotationProcessor class org.atmosphere.cpr.DefaultAnnotationProcessor$BytecodeBasedAnnotationProcessor being used
2025-07-29 18:07:39,895 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AnnotationHandler - Found Annotation in class com.siemens.polarion.service.notification.NotificationService being scanned: interface org.atmosphere.config.service.ManagedService
2025-07-29 18:07:39,898 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.annotation.AnnotationUtil - Adding class org.atmosphere.interceptor.AtmosphereResourceLifecycleInterceptor
2025-07-29 18:07:39,899 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.annotation.AnnotationUtil - Adding class org.atmosphere.client.TrackMessageSizeInterceptor
2025-07-29 18:07:39,900 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.annotation.AnnotationUtil - Adding class org.atmosphere.interceptor.SuspendTrackerInterceptor
2025-07-29 18:07:39,900 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.annotation.AnnotationUtil - Adding class org.atmosphere.config.managed.ManagedServiceInterceptor
2025-07-29 18:07:39,907 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.annotation.AnnotationUtil - Adding class com.siemens.polarion.service.notification.JwtVerificationInterceptor
2025-07-29 18:07:39,910 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.util.ForkJoinPool - Using ForkJoinPool  java.util.concurrent.ForkJoinPool. Set the org.atmosphere.cpr.broadcaster.maxAsyncWriteThreads to -1 to fully use its power.
2025-07-29 18:07:39,913 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereHandler org.atmosphere.config.managed.ManagedAtmosphereHandler mapped to context-path /notification and Broadcaster Class org.atmosphere.cpr.DefaultBroadcaster
2025-07-29 18:07:39,913 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor [Atmosphere LifeCycle,  Track Message Size Interceptor using |, UUID Tracking Interceptor, @ManagedService Interceptor, @Service Event Listeners, com.siemens.polarion.service.notification.JwtVerificationInterceptor] mapped to AtmosphereHandler org.atmosphere.config.managed.ManagedAtmosphereHandler
2025-07-29 18:07:39,921 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Auto detecting WebSocketHandler in /WEB-INF/classes/
2025-07-29 18:07:39,922 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed WebSocketProtocol org.atmosphere.websocket.protocol.SimpleHttpProtocol 
2025-07-29 18:07:39,924 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.container.JSR356AsyncSupport - JSR 356 Mapping path /notification
2025-07-29 18:07:39,928 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installing Default AtmosphereInterceptors
2025-07-29 18:07:39,929 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.CorsInterceptor : CORS Interceptor Support
2025-07-29 18:07:39,929 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.CacheHeadersInterceptor : Default Response's Headers Interceptor
2025-07-29 18:07:39,929 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.PaddingAtmosphereInterceptor : Browser Padding Interceptor Support
2025-07-29 18:07:39,929 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.AndroidAtmosphereInterceptor : Android Interceptor Support
2025-07-29 18:07:39,930 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.HeartbeatInterceptor : Heartbeat Interceptor Support
2025-07-29 18:07:39,930 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.SSEAtmosphereInterceptor : SSE Interceptor Support
2025-07-29 18:07:39,930 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.JSONPAtmosphereInterceptor : JSONP Interceptor Support
2025-07-29 18:07:39,931 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.JavaScriptProtocol : Atmosphere JavaScript Protocol
2025-07-29 18:07:39,931 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.WebSocketMessageSuspendInterceptor : org.atmosphere.interceptor.WebSocketMessageSuspendInterceptor
2025-07-29 18:07:39,931 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.OnDisconnectInterceptor : Browser disconnection detection
2025-07-29 18:07:39,931 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.IdleResourceInterceptor : org.atmosphere.interceptor.IdleResourceInterceptor
2025-07-29 18:07:39,931 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Set org.atmosphere.cpr.AtmosphereInterceptor.disableDefaults to disable them.
2025-07-29 18:07:39,931 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor CORS Interceptor Support with priority FIRST_BEFORE_DEFAULT 
2025-07-29 18:07:39,933 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor Default Response's Headers Interceptor with priority AFTER_DEFAULT 
2025-07-29 18:07:39,933 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor Browser Padding Interceptor Support with priority AFTER_DEFAULT 
2025-07-29 18:07:39,933 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor Android Interceptor Support with priority AFTER_DEFAULT 
2025-07-29 18:07:39,933 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.interceptor.HeartbeatInterceptor - HeartbeatInterceptor configured with padding value 'X', client frequency 30 seconds and server frequency 120 seconds
2025-07-29 18:07:39,933 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor Heartbeat Interceptor Support with priority AFTER_DEFAULT 
2025-07-29 18:07:39,933 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor SSE Interceptor Support with priority AFTER_DEFAULT 
2025-07-29 18:07:39,933 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor JSONP Interceptor Support with priority AFTER_DEFAULT 
2025-07-29 18:07:39,933 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor Atmosphere JavaScript Protocol with priority AFTER_DEFAULT 
2025-07-29 18:07:39,933 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor org.atmosphere.interceptor.WebSocketMessageSuspendInterceptor with priority AFTER_DEFAULT 
2025-07-29 18:07:39,933 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor Browser disconnection detection with priority AFTER_DEFAULT 
2025-07-29 18:07:39,933 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor org.atmosphere.interceptor.IdleResourceInterceptor with priority BEFORE_DEFAULT 
2025-07-29 18:07:39,935 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Using EndpointMapper class org.atmosphere.util.DefaultEndpointMapper
2025-07-29 18:07:39,935 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Using BroadcasterCache: org.atmosphere.cache.UUIDBroadcasterCache
2025-07-29 18:07:39,935 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Default Broadcaster Class: org.atmosphere.cpr.DefaultBroadcaster
2025-07-29 18:07:39,935 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Broadcaster Shared List Resources: false
2025-07-29 18:07:39,935 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Broadcaster Polling Wait Time 100
2025-07-29 18:07:39,935 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Shared ExecutorService supported: true
2025-07-29 18:07:39,935 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Messaging ExecutorService Pool Size unavailable - Not instance of ThreadPoolExecutor
2025-07-29 18:07:39,935 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Async I/O Thread Pool Size: 200
2025-07-29 18:07:39,935 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Using BroadcasterFactory: org.atmosphere.cpr.DefaultBroadcasterFactory
2025-07-29 18:07:39,935 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Using AtmosphereResurceFactory: org.atmosphere.cpr.DefaultAtmosphereResourceFactory
2025-07-29 18:07:39,935 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Using WebSocketProcessor: org.atmosphere.websocket.DefaultWebSocketProcessor
2025-07-29 18:07:39,940 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Invoke AtmosphereInterceptor on WebSocket message true
2025-07-29 18:07:39,941 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - HttpSession supported: false
2025-07-29 18:07:39,941 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Atmosphere is using Spring Web ObjectFactory for dependency injection and object creation
2025-07-29 18:07:39,941 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Atmosphere is using async support: org.atmosphere.container.JSR356AsyncSupport running under container: Apache Tomcat/9.0.60 using javax.servlet/3.0 and jsr356/WebSocket API
2025-07-29 18:07:39,941 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Atmosphere Framework 2.6.4 started.
2025-07-29 18:07:39,941 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 
2025-07-29 18:07:39,941 [Thread-40] INFO  NotificationService - 
2025-07-29 18:07:39,941 [Thread-40] INFO  NotificationService - 	For Atmosphere Framework Commercial Support, visit 
2025-07-29 18:07:39,941 [Thread-40] INFO  NotificationService - 	http://www.async-io.org/ or send an <NAME_EMAIL>
2025-07-29 18:07:39,941 [Thread-40] INFO  NotificationService - 
2025-07-29 18:07:39,941 [Thread-40] INFO  NotificationService - [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 40608 (http) with context path ''
2025-07-29 18:07:39,947 [Thread-40] INFO  NotificationService - [main] INFO  c.s.polarion.service.notification.Application - Started Application in 1.454 seconds (JVM running for 1.789)
2025-07-29 18:07:40,064 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testtype) created
2025-07-29 18:07:40,069 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (subtype) created
2025-07-29 18:07:40,075 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661569bb6004a_0_661569bb6004a_0_: finished. Total: 0.17 s, CPU [user: 0.0487 s, system: 0.00459 s], Allocated memory: 19.9 MB, svn: 0.139 s [82% getDir2 content (17x)] (62x), RepositoryConfigService: 0.0417 s [98% getReadConfiguration (170x)] (192x)
2025-07-29 18:07:40,268 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (YesNo) created
2025-07-29 18:07:40,274 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (software_VerificationMethod) created
2025-07-29 18:07:40,279 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (checklist) created
2025-07-29 18:07:40,283 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (commonreqproperty) created
2025-07-29 18:07:40,287 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (objectoriented) created
2025-07-29 18:07:40,289 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (submodule) created
2025-07-29 18:07:40,291 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (yesno) created
2025-07-29 18:07:40,291 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (cICategory) created
2025-07-29 18:07:40,292 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (wpFormat) created
2025-07-29 18:07:40,294 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (trigger) created
2025-07-29 18:07:40,298 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (ASILLevel) created
2025-07-29 18:07:40,299 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (CSRelated) created
2025-07-29 18:07:40,303 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (project_Module) created
2025-07-29 18:07:40,314 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (规格对象类型) created
2025-07-29 18:07:40,316 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (jenkins_job) created
2025-07-29 18:07:40,316 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (truefalse) created
2025-07-29 18:07:40,316 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (takeOnGroups) created
2025-07-29 18:07:40,323 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testcasetype) created
2025-07-29 18:07:40,327 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (processGroup) created
2025-07-29 18:07:40,330 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (changeManagement) created
2025-07-29 18:07:40,336 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (seriousness) created
2025-07-29 18:07:40,340 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (softReqClass) created
2025-07-29 18:07:40,345 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (SWDetailDesign) created
2025-07-29 18:07:40,347 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (PhaseChecklists) created
2025-07-29 18:07:40,349 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (PassNotpass) created
2025-07-29 18:07:40,352 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (baseLineType) created
2025-07-29 18:07:40,355 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (boolYesOrNo) created
2025-07-29 18:07:40,358 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testlevel) created
2025-07-29 18:07:40,361 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (source) created
2025-07-29 18:07:40,364 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (objectType) created
2025-07-29 18:07:40,367 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (atppblversion) created
2025-07-29 18:07:40,369 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (aSIL) created
2025-07-29 18:07:40,373 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (EE) created
2025-07-29 18:07:40,377 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (issueType) created
2025-07-29 18:07:40,380 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (SYS_reqClassification) created
2025-07-29 18:07:40,383 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (oem_2Status) created
2025-07-29 18:07:40,386 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (class) created
2025-07-29 18:07:40,388 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (promotionState) created
2025-07-29 18:07:40,390 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (git_project) created
2025-07-29 18:07:40,396 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (storageType) created
2025-07-29 18:07:40,398 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (issueproperty) created
2025-07-29 18:07:40,423 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (commonissueclass) created
2025-07-29 18:07:40,427 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (AgreeDisagree) created
2025-07-29 18:07:40,429 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (SA_Category) created
2025-07-29 18:07:40,432 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (relevance) created
2025-07-29 18:07:40,435 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (implementationPhase) created
2025-07-29 18:07:40,437 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (supplier_2Status) created
2025-07-29 18:07:40,443 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Testtype) created
2025-07-29 18:07:40,446 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (conf_baselineTime) created
2025-07-29 18:07:40,452 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (levelneed) created
2025-07-29 18:07:40,453 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (finalresult) created
2025-07-29 18:07:40,456 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testability) created
2025-07-29 18:07:40,459 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (solution) created
2025-07-29 18:07:40,462 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Responsible) created
2025-07-29 18:07:40,464 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (verificationstatus) created
2025-07-29 18:07:40,471 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (hsiassigngroup) created
2025-07-29 18:07:40,473 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (reqCategory) created
2025-07-29 18:07:40,476 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (baseLineName) created
2025-07-29 18:07:40,478 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (taskType) created
2025-07-29 18:07:40,485 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (changeReason) created
2025-07-29 18:07:40,487 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (objectmodule) created
2025-07-29 18:07:40,492 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (TestCaseOutputMethod) created
2025-07-29 18:07:40,495 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (SoftwareFeature) created
2025-07-29 18:07:40,502 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (ResponsibleGroup) created
2025-07-29 18:07:40,505 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (hsifunctionmodule) created
2025-07-29 18:07:40,510 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (FwReqSource) created
2025-07-29 18:07:40,512 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (occurPhase) created
2025-07-29 18:07:40,514 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (compiletask) created
2025-07-29 18:07:40,516 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (REQBVerificationMethod) created
2025-07-29 18:07:40,518 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (functionmodule) created
2025-07-29 18:07:40,521 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (variant) created
2025-07-29 18:07:40,523 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Fusatype) created
2025-07-29 18:07:40,526 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (hardwareversion) created
2025-07-29 18:07:40,535 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (appversion) created
2025-07-29 18:07:40,536 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (casefirstmodule) created
2025-07-29 18:07:40,539 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (auditType) created
2025-07-29 18:07:40,543 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Samplestage) created
2025-07-29 18:07:40,546 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (casesecondmodule) created
2025-07-29 18:07:40,548 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (issue_source) created
2025-07-29 18:07:40,550 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (ifNeedRegressionTesting) created
2025-07-29 18:07:40,552 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (atpsfsversion) created
2025-07-29 18:07:40,555 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (CustomerAllocation) created
2025-07-29 18:07:40,558 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (issuesubclass) created
2025-07-29 18:07:40,561 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (QANC_importance) created
2025-07-29 18:07:40,563 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (reviewMethod) created
2025-07-29 18:07:40,566 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (QANC_findType) created
2025-07-29 18:07:40,568 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (editType) created
2025-07-29 18:07:40,572 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testingobjects) created
2025-07-29 18:07:40,575 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testcaselevel) created
2025-07-29 18:07:40,580 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (supplierproblem) created
2025-07-29 18:07:40,582 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (reqattribute) created
2025-07-29 18:07:40,585 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (fsigroup) created
2025-07-29 18:07:40,587 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (project_reqsource) created
2025-07-29 18:07:40,590 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (preset) created
2025-07-29 18:07:40,594 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Mechverificationmethod) created
2025-07-29 18:07:40,596 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (CPMToTPM) created
2025-07-29 18:07:40,598 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (REQBType) created
2025-07-29 18:07:40,601 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testcasesign) created
2025-07-29 18:07:40,604 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (verificationphase) created
2025-07-29 18:07:40,611 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (processArea) created
2025-07-29 18:07:40,614 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (artifactType) created
2025-07-29 18:07:40,620 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Classification) created
2025-07-29 18:07:40,631 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (verificationmethod) created
2025-07-29 18:07:40,634 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (changeType) created
2025-07-29 18:07:40,637 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (hardwareAndSoftwareSubType) created
2025-07-29 18:07:40,640 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (SWIntegrationVerificationMethod) created
2025-07-29 18:07:40,646 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (category) created
2025-07-29 18:07:40,667 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (REQBCategory) created
2025-07-29 18:07:40,677 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (softreqclass) created
2025-07-29 18:07:40,680 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (TestMethod) created
2025-07-29 18:07:40,683 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (reType) created
2025-07-29 18:07:40,688 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (VerificationCriteria) created
2025-07-29 18:07:40,694 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (baseLinechecklist) created
2025-07-29 18:07:40,697 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Category) created
2025-07-29 18:07:40,702 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (SWUnitTestDerivingMethods) created
2025-07-29 18:07:40,707 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (firmware_Category) created
2025-07-29 18:07:40,712 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testMethod) created
2025-07-29 18:07:40,729 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (QAPorcessAreas) created
2025-07-29 18:07:40,732 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (findSource) created
2025-07-29 18:07:40,738 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661569bb8ac4b_0_661569bb8ac4b_0_: finished. Total: 0.663 s, CPU [user: 0.336 s, system: 0.0131 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.512 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.326 s [68% getFile content (412x), 32% getDir2 content (21x)] (434x)
2025-07-29 18:07:40,829 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (tshirt-sizes) created
2025-07-29 18:07:40,834 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (reqtype) created
2025-07-29 18:07:41,062 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Feasibility) created
2025-07-29 18:07:41,064 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (locaMod) created
2025-07-29 18:07:41,065 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (unitTestCaseType) created
2025-07-29 18:07:41,066 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (ControlLevel) created
2025-07-29 18:07:41,067 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (NCitemSev) created
2025-07-29 18:07:41,068 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (signType) created
2025-07-29 18:07:41,071 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (WBSCategory) created
2025-07-29 18:07:41,073 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (swTestCaseEnv) created
2025-07-29 18:07:41,074 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (verifiability) created
2025-07-29 18:07:41,076 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (@ProjectUser) created
2025-07-29 18:07:41,079 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (standardReq) created
2025-07-29 18:07:41,080 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (statusa) created
2025-07-29 18:07:41,081 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (@WorkItems[type:configurationitemversion]) created
2025-07-29 18:07:41,082 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (CIRevisionStatus) created
2025-07-29 18:07:41,084 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (dogTimeout) created
2025-07-29 18:07:41,085 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (REQCategory) created
2025-07-29 18:07:41,088 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (proStage) created
2025-07-29 18:07:41,090 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (BaselineType) created
2025-07-29 18:07:41,093 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (custConfStat) created
2025-07-29 18:07:41,094 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (sofReqVer) created
2025-07-29 18:07:41,096 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Source) created
2025-07-29 18:07:41,099 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (scCategory) created
2025-07-29 18:07:41,100 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (softTestCaseType) created
2025-07-29 18:07:41,101 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (solAdv) created
2025-07-29 18:07:41,103 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (unitTestCaseMet) created
2025-07-29 18:07:41,104 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (sysTestCaseMeth) created
2025-07-29 18:07:41,106 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (softTestCaseMe) created
2025-07-29 18:07:41,107 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (softTestCaseEnv) created
2025-07-29 18:07:41,112 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (auditTarget) created
2025-07-29 18:07:41,114 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (@ReviewForm) created
2025-07-29 18:07:41,115 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (swTestCaseType) created
2025-07-29 18:07:41,117 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (@Collection) created
2025-07-29 18:07:41,118 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (submissionStage) created
2025-07-29 18:07:41,120 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (sysTestCaseMet) created
2025-07-29 18:07:41,121 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (demandType) created
2025-07-29 18:07:41,126 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (swTestCaseMet) created
2025-07-29 18:07:41,128 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (taskUrgen) created
2025-07-29 18:07:41,129 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (solveMethod) created
2025-07-29 18:07:41,130 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (audMethod) created
2025-07-29 18:07:41,132 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (desStat) created
2025-07-29 18:07:41,136 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (scType) created
2025-07-29 18:07:41,138 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (sysTestCaseType) created
2025-07-29 18:07:41,140 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (releaseType) created
2025-07-29 18:07:41,143 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (unitTestCaseEnv) created
2025-07-29 18:07:41,145 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (targetStage) created
2025-07-29 18:07:41,147 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (ClassificationType) created
2025-07-29 18:07:41,149 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testItem) created
2025-07-29 18:07:41,152 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (InfoSecurity) created
2025-07-29 18:07:41,154 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Verification) created
2025-07-29 18:07:41,158 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (triggerMod) created
2025-07-29 18:07:41,160 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (verMethod) created
2025-07-29 18:07:41,162 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (diagramCategory) created
2025-07-29 18:07:41,163 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (assSubsystem) created
2025-07-29 18:07:41,165 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (OccurrenceProbability) created
2025-07-29 18:07:41,167 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (developmentMethod) created
2025-07-29 18:07:41,169 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (portType) created
2025-07-29 18:07:41,171 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (checkType) created
2025-07-29 18:07:41,172 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (demandStatus) created
2025-07-29 18:07:41,173 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (important) created
2025-07-29 18:07:41,174 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (triggerMec) created
2025-07-29 18:07:41,176 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (sysTestCaseTy) created
2025-07-29 18:07:41,182 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (recentPre) created
2025-07-29 18:07:41,184 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (TestCaseDesignMethod) created
2025-07-29 18:07:41,187 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testCasePri) created
2025-07-29 18:07:41,193 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (relObj) created
2025-07-29 18:07:41,194 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (proSer) created
2025-07-29 18:07:41,196 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (TestProblemType) created
2025-07-29 18:07:41,198 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (chipName) created
2025-07-29 18:07:41,199 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (auditTiming) created
2025-07-29 18:07:41,201 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661569bc5c44e_0_661569bc5c44e_0_: finished. Total: 0.287 s, CPU [user: 0.118 s, system: 0.00745 s], Allocated memory: 387.2 MB, RepositoryConfigService: 0.185 s [97% getReadConfiguration (2787x)] (3025x), svn: 0.179 s [52% getFile content (185x), 47% getDir2 content (20x)] (206x)
2025-07-29 18:07:41,201 [PreLoadDataService] INFO  com.polarion.psvn.launcher.internal.data.PreLoadDataService - Preloading data FINISHED took  [ TIME 3.05 s. ]
2025-07-29 18:07:41,201 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 3.05 s, CPU [user: 1.13 s, system: 0.143 s], Allocated memory: 1.6 GB, transactions: 10, svn: 1.79 s [37% getDir2 content (114x), 31% getFile content (809x), 29% getDatedRevision (181x)] (1144x), RepositoryConfigService: 0.881 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.598 s [83% Category (96x)] (117x), ObjectMaps: 0.194 s [43% getPrimaryObjectProperty (110x), 38% getPrimaryObjectLocation (116x)] (452x)
2025-07-29 18:07:41,201 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 54, svn: 2.55 s [50% getDatedRevision (362x), 26% getDir2 content (114x), 22% getFile content (809x)] (1327x), RepositoryConfigService: 0.881 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.598 s [83% Category (96x)] (118x), ObjectMaps: 0.194 s [43% getPrimaryObjectProperty (110x), 38% getPrimaryObjectLocation (116x)] (452x)
