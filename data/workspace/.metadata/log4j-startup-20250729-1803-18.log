2025-07-29 18:03:18,747 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 18:03:18,748 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-29 18:03:18,748 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 18:03:18,748 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-29 18:03:18,748 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-29 18:03:18,748 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:03:18,748 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-29 18:03:25,092 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-29 18:03:25,234 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.142 s. ]
2025-07-29 18:03:25,234 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-29 18:03:25,295 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0606 s. ]
2025-07-29 18:03:25,347 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-29 18:03:25,475 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 12 s. ]
2025-07-29 18:03:25,704 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 6.96 s. ]
2025-07-29 18:03:25,805 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:03:25,805 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-29 18:03:25,832 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.13 s. ]
2025-07-29 18:03:25,832 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:03:25,832 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-29 18:03:25,837 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (2/9)
2025-07-29 18:03:25,837 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (4/9)
2025-07-29 18:03:25,837 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (3/9)
2025-07-29 18:03:25,837 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (1/9)
2025-07-29 18:03:25,837 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (5/9)
2025-07-29 18:03:25,837 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (6/9)
2025-07-29 18:03:25,844 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-07-29 18:03:25,998 [LowLevelDataService-contextInitializer-4 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-29 18:03:26,086 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-29 18:03:26,533 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.7 s. ]
2025-07-29 18:03:26,545 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:03:26,545 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-29 18:03:26,835 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-29 18:03:26,850 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.32 s. ]
2025-07-29 18:03:26,876 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:03:26,876 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-29 18:03:26,879 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-29 18:03:26,928 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-29 18:03:26,975 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-07-29 18:03:27,002 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (4/9)
2025-07-29 18:03:27,018 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (5/9)
2025-07-29 18:03:27,040 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (6/9)
2025-07-29 18:03:27,064 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-07-29 18:03:27,091 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-29 18:03:27,119 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-29 18:03:27,119 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.27 s. ]
2025-07-29 18:03:27,119 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:03:27,119 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-29 18:03:27,132 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-07-29 18:03:27,132 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:03:27,132 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-29 18:03:27,237 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-29 18:03:27,240 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-29 18:03:27,360 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.23 s. ]
2025-07-29 18:03:27,361 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:03:27,361 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-29 18:03:27,369 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-29 18:03:27,369 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:03:27,369 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-29 18:03:34,772 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 7.4 s. ]
2025-07-29 18:03:34,772 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 18:03:34,772 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 16 s. ]
2025-07-29 18:03:34,772 [main] INFO  com.polarion.platform.startup - ****************************************************************
