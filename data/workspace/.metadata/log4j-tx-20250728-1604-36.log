2025-07-28 16:04:41,756 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0399 s [53% update (144x), 47% query (12x)] (221x), svn: 0.0234 s [58% testConnection (1x), 21% getLatestRevision (2x), 21% checkPath (1x)] (4x)
2025-07-28 16:04:41,860 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0291 s [60% getDir2 content (2x), 33% info (3x)] (6x)
2025-07-28 16:04:42,600 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.737 s, CPU [user: 0.0774 s, system: 0.088 s], Allocated memory: 8.5 MB, transactions: 0, svn: 0.112 s [81% log2 (10x)] (13x), ObjectMaps: 0.0498 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-28 16:04:42,600 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.737 s, CPU [user: 0.211 s, system: 0.255 s], Allocated memory: 53.1 MB, transactions: 0, ObjectMaps: 0.11 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-28 16:04:42,600 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.737 s, CPU [user: 0.13 s, system: 0.197 s], Allocated memory: 24.0 MB, transactions: 0, ObjectMaps: 0.0865 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-28 16:04:42,600 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.737 s, CPU [user: 0.102 s, system: 0.143 s], Allocated memory: 14.3 MB, transactions: 0, ObjectMaps: 0.102 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.0782 s [83% log2 (10x)] (13x)
2025-07-28 16:04:42,600 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.737 s, CPU [user: 0.269 s, system: 0.31 s], Allocated memory: 72.3 MB, transactions: 0, ObjectMaps: 0.127 s [99% getAllPrimaryObjects (1x)] (12x), svn: 0.072 s [29% info (5x), 26% log2 (5x), 19% log (1x), 14% getLatestRevision (2x)] (18x)
2025-07-28 16:04:42,600 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.737 s, CPU [user: 0.0572 s, system: 0.0637 s], Allocated memory: 6.9 MB, transactions: 0, svn: 0.0512 s [63% log2 (5x), 20% testConnection (1x)] (7x), ObjectMaps: 0.0454 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-28 16:04:42,601 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.521 s [100% getAllPrimaryObjects (8x)] (61x), svn: 0.355 s [65% log2 (36x), 14% getLatestRevision (9x), 10% testConnection (6x)] (61x)
2025-07-28 16:04:42,852 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.218 s [100% getReadConfiguration (48x)] (48x), svn: 0.0876 s [85% info (18x)] (38x)
2025-07-28 16:04:43,171 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.252 s [78% info (94x), 15% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.196 s [100% getReadConfiguration (54x)] (54x)
2025-07-28 16:04:43,380 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.194 s [100% doFinishStartup (1x)] (1x), commit: 0.0398 s [100% Revision (1x)] (1x), Lucene: 0.0299 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.0129 s [100% objectsToInv (1x)] (1x), DB: 0.0118 s [45% update (3x), 35% query (1x)] (8x)
2025-07-28 16:06:02,187 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.589 s [53% info (158x), 41% getLatestRevision (27x)] (195x), PullingJob: 0.233 s [100% collectChanges (26x)] (26x), RepositoryConfigService: 0.0357 s [100% getReadConfiguration (2x)] (2x)
2025-07-28 16:06:03,066 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.727 s, CPU [user: 0.00636 s, system: 0.00355 s], Allocated memory: 569.9 kB, transactions: 1
2025-07-28 16:06:03,066 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 27, persistence listener: 0.0682 s [73% WorkItemActivityCreator (1x), 24% indexRefreshPersistenceListener (1x)] (7x), notification worker: 0.0399 s [53% RevisionActivityCreator (2x), 15% PlanActivityCreator (1x), 11% BuildActivityCreator (1x), 11% TestRunActivityCreator (1x)] (6x), resolve: 0.0213 s [59% User (1x), 41% Revision (2x)] (3x), Incremental Baseline: 0.0205 s [100% WorkItem (20x)] (20x), Full Baseline: 0.00643 s [100% WorkItem (1x)] (1x), Lucene: 0.00622 s [100% refresh (1x)] (1x)
2025-07-28 16:06:03,067 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.881 s, CPU [user: 0.151 s, system: 0.0311 s], Allocated memory: 18.0 MB, transactions: 21, svn: 0.642 s [98% getDatedRevision (181x)] (183x), GC: 0.047 s [100% G1 Young Generation (1x)] (1x), Lucene: 0.0452 s [79% buildBaselineSnapshots (1x), 21% buildBaseline (21x)] (22x)
2025-07-28 16:06:03,357 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6614044dc2445_0_6614044dc2445_0_: finished. Total: 1.11 s, CPU [user: 0.349 s, system: 0.0899 s], Allocated memory: 52.8 MB, svn: 0.629 s [43% getDatedRevision (181x), 36% getDir2 content (25x), 19% getFile content (98x)] (307x), resolve: 0.393 s [100% Category (96x)] (96x), ObjectMaps: 0.135 s [44% getPrimaryObjectProperty (96x), 35% getPrimaryObjectLocation (96x), 21% getLastPromoted (96x)] (388x)
2025-07-28 16:06:03,558 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6614044eec848_0_6614044eec848_0_: finished. Total: 0.115 s, CPU [user: 0.0539 s, system: 0.011 s], Allocated memory: 8.3 MB, RepositoryConfigService: 0.0445 s [56% getReadUserConfiguration (10x), 44% getReadConfiguration (162x)] (172x), svn: 0.042 s [57% info (19x), 35% getFile content (16x)] (37x), resolve: 0.0378 s [100% User (9x)] (9x), ObjectMaps: 0.0219 s [43% getPrimaryObjectProperty (9x), 42% getPrimaryObjectLocation (9x)] (37x)
2025-07-28 16:06:03,779 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6614044f1644a_0_6614044f1644a_0_: finished. Total: 0.17 s, CPU [user: 0.0616 s, system: 0.0074 s], Allocated memory: 19.8 MB, svn: 0.127 s [64% getDir2 content (17x), 36% getFile content (44x)] (62x), RepositoryConfigService: 0.0719 s [99% getReadConfiguration (170x)] (192x)
2025-07-28 16:06:04,427 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6614044f40c4b_0_6614044f40c4b_0_: finished. Total: 0.648 s, CPU [user: 0.314 s, system: 0.0221 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.481 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.33 s [64% getFile content (412x), 36% getDir2 content (21x)] (434x)
2025-07-28 16:06:04,764 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6614044ffc04e_0_6614044ffc04e_0_: finished. Total: 0.236 s, CPU [user: 0.0961 s, system: 0.0059 s], Allocated memory: 382.9 MB, RepositoryConfigService: 0.153 s [96% getReadConfiguration (2787x)] (3025x), svn: 0.146 s [53% getFile content (185x), 47% getDir2 content (20x)] (206x)
2025-07-28 16:06:04,764 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 2.52 s, CPU [user: 0.962 s, system: 0.15 s], Allocated memory: 1.6 GB, transactions: 10, svn: 1.42 s [41% getDir2 content (114x), 36% getFile content (809x), 19% getDatedRevision (181x)] (1144x), RepositoryConfigService: 0.816 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.475 s [83% Category (96x)] (117x), ObjectMaps: 0.171 s [46% getPrimaryObjectProperty (110x), 35% getPrimaryObjectLocation (116x)] (452x)
2025-07-28 16:06:04,764 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 52, svn: 2.06 s [44% getDatedRevision (362x), 28% getDir2 content (114x), 25% getFile content (809x)] (1328x), RepositoryConfigService: 0.816 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.476 s [83% Category (96x)] (118x), ObjectMaps: 0.171 s [46% getPrimaryObjectProperty (110x), 35% getPrimaryObjectLocation (116x)] (452x)
