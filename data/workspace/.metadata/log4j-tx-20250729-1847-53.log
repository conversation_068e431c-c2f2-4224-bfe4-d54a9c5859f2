2025-07-29 18:47:58,848 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0666 s [71% update (144x), 29% query (12x)] (221x), svn: 0.0101 s [52% getLatestRevision (2x), 38% testConnection (1x)] (4x)
2025-07-29 18:47:58,975 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0317 s [58% getDir2 content (2x), 32% info (3x)] (6x)
2025-07-29 18:47:59,842 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.862 s, CPU [user: 0.0577 s, system: 0.0876 s], Allocated memory: 6.8 MB, transactions: 0, ObjectMaps: 0.0823 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-29 18:47:59,842 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.861 s, CPU [user: 0.264 s, system: 0.327 s], Allocated memory: 68.4 MB, transactions: 0, ObjectMaps: 0.128 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-29 18:47:59,842 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.862 s, CPU [user: 0.231 s, system: 0.271 s], Allocated memory: 53.1 MB, transactions: 0, ObjectMaps: 0.158 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-29 18:47:59,842 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.862 s, CPU [user: 0.0939 s, system: 0.129 s], Allocated memory: 12.1 MB, transactions: 0, svn: 0.12 s [73% log2 (10x), 22% getLatestRevision (2x)] (13x), ObjectMaps: 0.0963 s [97% getAllPrimaryObjects (2x)] (14x)
2025-07-29 18:47:59,842 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.861 s, CPU [user: 0.0827 s, system: 0.0975 s], Allocated memory: 11.0 MB, transactions: 0, svn: 0.12 s [78% log2 (10x), 16% getLatestRevision (2x)] (13x), ObjectMaps: 0.0567 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-29 18:47:59,842 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.862 s, CPU [user: 0.166 s, system: 0.202 s], Allocated memory: 26.5 MB, transactions: 0, svn: 0.121 s [43% log2 (5x), 22% info (5x), 14% log (1x), 11% getLatestRevision (2x)] (18x), ObjectMaps: 0.116 s [100% getAllPrimaryObjects (1x)] (10x)
2025-07-29 18:47:59,842 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.638 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.45 s [65% log2 (36x), 17% getLatestRevision (9x)] (61x)
2025-07-29 18:48:00,074 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.195 s [100% getReadConfiguration (48x)] (48x), svn: 0.0757 s [85% info (18x)] (38x)
2025-07-29 18:48:00,490 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.31 s [75% info (94x), 17% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.241 s [100% getReadConfiguration (54x)] (54x)
2025-07-29 18:48:00,709 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.11 s, CPU [user: 0.0255 s, system: 0.0084 s], Allocated memory: 10.7 MB
2025-07-29 18:48:00,762 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.254 s [100% doFinishStartup (1x)] (1x), commit: 0.066 s [100% Revision (1x)] (1x), Lucene: 0.0386 s [100% refresh (1x)] (1x), DB: 0.0255 s [67% update (3x), 18% query (1x)] (8x), derivedLinkedRevisionsContributor: 0.0168 s [100% objectsToInv (1x)] (1x)
2025-07-29 18:48:03,481 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.432 s [92% info (158x)] (168x)
2025-07-29 18:48:04,318 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.746 s, CPU [user: 0.00614 s, system: 0.00156 s], Allocated memory: 531.4 kB, transactions: 1
2025-07-29 18:48:04,323 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 27, notification worker: 0.0401 s [47% RevisionActivityCreator (2x), 24% WorkItemActivityCreator (1x), 23% TestRunActivityCreator (1x)] (6x), Incremental Baseline: 0.0281 s [100% WorkItem (21x)] (21x), resolve: 0.0186 s [51% User (1x), 49% Revision (2x)] (3x), persistence listener: 0.0153 s [75% indexRefreshPersistenceListener (1x), 15% WorkItemActivityCreator (1x)] (7x), Lucene: 0.0118 s [55% add (1x), 45% refresh (1x)] (2x), PullingJob: 0.00815 s [100% collectChanges (1x)] (1x), svn: 0.00799 s [60% getLatestRevision (1x), 40% testConnection (1x)] (2x), ObjectMaps: 0.00285 s [100% getPrimaryObjectLocation (1x)] (1x)
2025-07-29 18:48:04,323 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.846 s, CPU [user: 0.167 s, system: 0.0248 s], Allocated memory: 18.5 MB, transactions: 23, svn: 0.662 s [99% getDatedRevision (181x)] (183x), Lucene: 0.0518 s [74% buildBaselineSnapshots (1x), 26% buildBaseline (22x)] (23x)
2025-07-29 18:48:04,781 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661572fa2ac40_0_661572fa2ac40_0_: finished. Total: 1.28 s, CPU [user: 0.405 s, system: 0.0855 s], Allocated memory: 54.6 MB, svn: 0.758 s [56% getDatedRevision (181x), 28% getDir2 content (25x)] (307x), resolve: 0.37 s [100% Category (96x)] (96x), ObjectMaps: 0.115 s [44% getPrimaryObjectProperty (96x), 32% getPrimaryObjectLocation (96x), 24% getLastPromoted (96x)] (388x)
2025-07-29 18:48:04,992 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661572fb81448_0_661572fb81448_0_: finished. Total: 0.123 s, CPU [user: 0.0601 s, system: 0.00998 s], Allocated memory: 8.3 MB, RepositoryConfigService: 0.0519 s [54% getReadUserConfiguration (10x), 46% getReadConfiguration (162x)] (172x), svn: 0.0496 s [57% info (19x), 37% getFile content (16x)] (37x), resolve: 0.0386 s [100% User (9x)] (9x), ObjectMaps: 0.0189 s [49% getPrimaryObjectProperty (9x), 33% getPrimaryObjectLocation (9x)] (37x)
2025-07-29 18:48:05,246 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661572fba904a_0_661572fba904a_0_: finished. Total: 0.218 s, CPU [user: 0.0812 s, system: 0.00893 s], Allocated memory: 19.9 MB, svn: 0.155 s [59% getDir2 content (17x), 41% getFile content (44x)] (62x), RepositoryConfigService: 0.106 s [99% getReadConfiguration (170x)] (192x)
2025-07-29 18:48:06,336 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661572fbdf84b_0_661572fbdf84b_0_: finished. Total: 1.09 s, CPU [user: 0.434 s, system: 0.0328 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.734 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.631 s [54% getFile content (412x), 46% getDir2 content (21x)] (434x)
2025-07-29 18:48:06,721 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661572fd0c84e_0_661572fd0c84e_0_: finished. Total: 0.271 s, CPU [user: 0.115 s, system: 0.00717 s], Allocated memory: 384.1 MB, RepositoryConfigService: 0.178 s [96% getReadConfiguration (2787x)] (3025x), svn: 0.161 s [54% getFile content (185x), 46% getDir2 content (20x)] (206x)
2025-07-29 18:48:06,722 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 3.23 s, CPU [user: 1.18 s, system: 0.157 s], Allocated memory: 1.6 GB, transactions: 10, svn: 1.9 s [40% getDir2 content (114x), 34% getFile content (809x), 23% getDatedRevision (181x)] (1144x), RepositoryConfigService: 1.13 s [95% getReadConfiguration (12019x)] (12691x), resolve: 0.46 s [81% Category (96x)] (117x)
2025-07-29 18:48:06,722 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 55, svn: 2.56 s [42% getDatedRevision (362x), 30% getDir2 content (114x), 25% getFile content (809x)] (1327x), RepositoryConfigService: 1.13 s [95% getReadConfiguration (12019x)] (12691x), resolve: 0.46 s [81% Category (96x)] (118x), ObjectMaps: 0.154 s [46% getPrimaryObjectProperty (110x), 31% getPrimaryObjectLocation (116x), 22% getLastPromoted (110x)] (452x)
2025-07-29 18:48:17,337 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55cc1cd1-c0a8d700-5f022340-4fb9b82f] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/': Total: 0.484 s, CPU [user: 0.221 s, system: 0.0643 s], Allocated memory: 42.3 MB, transactions: 2, PolarionAuthenticator: 0.352 s [100% authenticate (1x)] (1x), GC: 0.068 s [100% G1 Young Generation (1x)] (1x)
2025-07-29 18:48:17,661 [ajp-nio-127.0.0.1-8889-exec-4 | cID:55cc1f89-c0a8d700-5f022340-73129973] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/global/canModifyConfiguration': Total: 0.115 s, CPU [user: 0.0583 s, system: 0.0116 s], Allocated memory: 9.5 MB, transactions: 0
2025-07-29 18:48:17,684 [ajp-nio-127.0.0.1-8889-exec-8 | cID:55cc1fa7-c0a8d700-5f022340-d9929649] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxy_contributions?_=1753786097516': Total: 0.109 s, CPU [user: 0.027 s, system: 0.00408 s], Allocated memory: 1.9 MB, transactions: 0
2025-07-29 18:48:17,734 [ajp-nio-127.0.0.1-8889-exec-6 | cID:55cc1fa6-c0a8d700-5f022340-f0bd0625] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1753786097517': Total: 0.159 s, CPU [user: 0.0474 s, system: 0.0115 s], Allocated memory: 8.1 MB, transactions: 1, RepositoryConfigService: 0.0844 s [100% getReadConfiguration (1x)] (1x), svn: 0.0148 s [58% testConnection (1x), 42% getFile content (1x)] (3x)
2025-07-29 18:48:17,741 [ajp-nio-127.0.0.1-8889-exec-7 | cID:55cc1fa6-c0a8d700-5f022340-493dde33] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections?_=1753786097518': Total: 0.166 s, CPU [user: 0.0185 s, system: 0.0029 s], Allocated memory: 2.6 MB, transactions: 1, RepositoryConfigService: 0.0935 s [100% getReadConfiguration (1x)] (1x)
