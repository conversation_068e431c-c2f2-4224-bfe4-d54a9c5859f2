2025-07-28 11:36:57,320 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0384 s [63% update (144x), 37% query (12x)] (221x), svn: 0.0114 s [64% getLatestRevision (2x), 28% testConnection (1x)] (4x)
2025-07-28 11:36:57,425 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0286 s [64% getDir2 content (2x), 28% info (3x)] (6x)
2025-07-28 11:36:58,133 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.705 s, CPU [user: 0.171 s, system: 0.31 s], Allocated memory: 52.8 MB, transactions: 0, ObjectMaps: 0.127 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-28 11:36:58,133 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.705 s, CPU [user: 0.101 s, system: 0.247 s], Allocated memory: 23.7 MB, transactions: 0, ObjectMaps: 0.0891 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-28 11:36:58,133 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.705 s, CPU [user: 0.0408 s, system: 0.115 s], Allocated memory: 6.8 MB, transactions: 0, ObjectMaps: 0.0603 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0369 s [83% log2 (5x)] (7x)
2025-07-28 11:36:58,134 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.705 s, CPU [user: 0.0758 s, system: 0.182 s], Allocated memory: 14.3 MB, transactions: 0, ObjectMaps: 0.0812 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.0468 s [78% log2 (10x), 14% getLatestRevision (2x)] (13x)
2025-07-28 11:36:58,133 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.705 s, CPU [user: 0.0539 s, system: 0.105 s], Allocated memory: 8.6 MB, transactions: 0, svn: 0.0616 s [79% log2 (10x), 14% getLatestRevision (2x)] (13x), ObjectMaps: 0.0592 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-28 11:36:58,134 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.705 s, CPU [user: 0.231 s, system: 0.365 s], Allocated memory: 73.1 MB, transactions: 0, ObjectMaps: 0.119 s [99% getAllPrimaryObjects (1x)] (14x), svn: 0.0822 s [40% log (1x), 23% log2 (5x), 17% info (5x), 11% getLatestRevision (2x)] (18x)
2025-07-28 11:36:58,134 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.536 s [100% getAllPrimaryObjects (8x)] (63x), svn: 0.257 s [60% log2 (36x), 13% log (1x), 12% getLatestRevision (9x)] (61x)
2025-07-28 11:36:58,338 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.175 s [100% getReadConfiguration (48x)] (48x), svn: 0.0636 s [87% info (18x)] (38x)
2025-07-28 11:36:58,561 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.165 s [74% info (94x), 17% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.132 s [100% getReadConfiguration (54x)] (54x)
2025-07-28 11:36:58,770 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.112 s, CPU [user: 0.0404 s, system: 0.0103 s], Allocated memory: 11.2 MB
2025-07-28 11:36:58,792 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.218 s [100% doFinishStartup (1x)] (1x), commit: 0.0721 s [100% Revision (1x)] (1x), Lucene: 0.0269 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.012 s [100% objectsToInv (1x)] (1x)
2025-07-28 11:37:01,036 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.295 s [88% info (158x)] (168x)
2025-07-28 11:37:01,041 [Worker-0: Attachment Indexer | u:p | job: polarion.jobs.attachment.indexer] INFO  TXLOGGER - Summary after AttachmentIndexer: transactions: 3, persistence listener: 0.00844 s [74% indexRefreshPersistenceListener (1x), 9% WorkItemActivityCreator (1x)] (7x), notification worker: 0.00449 s [39% WorkItemActivityCreator (1x), 28% TestRunActivityCreator (1x), 24% BuildActivityCreator (1x)] (4x)
2025-07-28 11:37:01,664 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.576 s, CPU [user: 0.00472 s, system: 0.00108 s], Allocated memory: 552.9 kB, transactions: 1
2025-07-28 11:37:01,665 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 22, Incremental Baseline: 0.0175 s [100% WorkItem (19x)] (19x), resolve: 0.0107 s [79% User (1x), 21% Revision (2x)] (3x), notification worker: 0.00543 s [100% RevisionActivityCreator (2x)] (2x), Lucene: 0.00419 s [100% refresh (1x)] (1x), ObjectMaps: 0.00218 s [100% getPrimaryObjectLocation (1x)] (1x)
2025-07-28 11:37:01,666 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.634 s, CPU [user: 0.13 s, system: 0.0231 s], Allocated memory: 17.9 MB, transactions: 21, svn: 0.525 s [99% getDatedRevision (181x)] (183x)
2025-07-28 11:37:01,995 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6613c6badc445_0_6613c6badc445_0_: finished. Total: 0.954 s, CPU [user: 0.319 s, system: 0.0728 s], Allocated memory: 53.2 MB, svn: 0.57 s [54% getDatedRevision (181x), 27% getDir2 content (25x)] (307x), resolve: 0.309 s [100% Category (96x)] (96x), ObjectMaps: 0.101 s [42% getPrimaryObjectProperty (96x), 33% getPrimaryObjectLocation (96x), 25% getLastPromoted (96x)] (388x)
2025-07-28 11:37:02,407 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6613c6bbfe84a_0_6613c6bbfe84a_0_: finished. Total: 0.205 s, CPU [user: 0.0656 s, system: 0.00874 s], Allocated memory: 19.9 MB, svn: 0.16 s [75% getDir2 content (17x), 24% getFile content (44x)] (62x), RepositoryConfigService: 0.0663 s [99% getReadConfiguration (170x)] (192x), GC: 0.017 s [100% G1 Young Generation (1x)] (1x)
2025-07-28 11:37:02,982 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6613c6bc31c4b_0_6613c6bc31c4b_0_: finished. Total: 0.575 s, CPU [user: 0.299 s, system: 0.0216 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.416 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.283 s [59% getFile content (412x), 41% getDir2 content (21x)] (434x)
2025-07-28 11:37:03,286 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6613c6bcd744e_0_6613c6bcd744e_0_: finished. Total: 0.217 s, CPU [user: 0.0919 s, system: 0.00543 s], Allocated memory: 384.1 MB, RepositoryConfigService: 0.138 s [97% getReadConfiguration (2787x)] (3025x), svn: 0.137 s [51% getFile content (185x), 49% getDir2 content (20x)] (206x)
2025-07-28 11:37:03,287 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 2.25 s, CPU [user: 0.9 s, system: 0.127 s], Allocated memory: 1.6 GB, transactions: 10, svn: 1.32 s [41% getDir2 content (114x), 32% getFile content (809x), 23% getDatedRevision (181x)] (1144x), RepositoryConfigService: 0.719 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.369 s [84% Category (96x)] (117x), ObjectMaps: 0.124 s [45% getPrimaryObjectProperty (110x), 32% getPrimaryObjectLocation (116x), 23% getLastPromoted (110x)] (452x)
2025-07-28 11:37:03,287 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 51, svn: 1.85 s [45% getDatedRevision (362x), 29% getDir2 content (114x), 23% getFile content (809x)] (1329x), RepositoryConfigService: 0.719 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.369 s [84% Category (96x)] (118x), ObjectMaps: 0.124 s [45% getPrimaryObjectProperty (110x), 32% getPrimaryObjectLocation (116x), 23% getLastPromoted (110x)] (452x)
