2025-07-29 18:47:53,710 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 18:47:53,710 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-29 18:47:53,710 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 18:47:53,710 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-29 18:47:53,710 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-29 18:47:53,711 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:47:53,711 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-29 18:47:58,078 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-29 18:47:58,281 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.203 s. ]
2025-07-29 18:47:58,282 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-29 18:47:58,350 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0686 s. ]
2025-07-29 18:47:58,492 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-29 18:47:58,604 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 113 s. ]
2025-07-29 18:47:58,847 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.14 s. ]
2025-07-29 18:47:58,951 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:47:58,951 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-29 18:47:58,975 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.13 s. ]
2025-07-29 18:47:58,976 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:47:58,976 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-29 18:47:58,982 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (2/9)
2025-07-29 18:47:58,982 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (3/9)
2025-07-29 18:47:58,982 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (1/9)
2025-07-29 18:47:58,982 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (6/9)
2025-07-29 18:47:58,982 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (4/9)
2025-07-29 18:47:58,982 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (5/9)
2025-07-29 18:47:58,990 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-07-29 18:47:59,156 [LowLevelDataService-contextInitializer-4 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-29 18:47:59,248 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-29 18:47:59,842 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.87 s. ]
2025-07-29 18:47:59,856 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:47:59,856 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-29 18:48:00,062 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-29 18:48:00,074 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.23 s. ]
2025-07-29 18:48:00,098 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:48:00,098 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-29 18:48:00,102 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-29 18:48:00,145 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-29 18:48:00,204 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-07-29 18:48:00,237 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (4/9)
2025-07-29 18:48:00,266 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (5/9)
2025-07-29 18:48:00,355 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (6/9)
2025-07-29 18:48:00,392 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-07-29 18:48:00,443 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-29 18:48:00,490 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-29 18:48:00,490 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.42 s. ]
2025-07-29 18:48:00,490 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:48:00,490 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-29 18:48:00,507 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-07-29 18:48:00,507 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:48:00,507 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-29 18:48:00,606 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-29 18:48:00,608 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-29 18:48:00,761 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.25 s. ]
2025-07-29 18:48:00,762 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:48:00,762 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-29 18:48:00,771 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-29 18:48:00,771 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:48:00,771 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-29 18:48:03,481 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.71 s. ]
2025-07-29 18:48:03,481 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 18:48:03,481 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 9.77 s. ]
2025-07-29 18:48:03,481 [main] INFO  com.polarion.platform.startup - ****************************************************************
