2025-07-29 18:56:44,870 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.054 s [53% query (12x), 47% update (144x)] (221x), svn: 0.0121 s [59% getLatestRevision (2x), 32% testConnection (1x)] (4x)
2025-07-29 18:56:44,978 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0348 s [62% getDir2 content (2x), 30% info (3x)] (6x)
2025-07-29 18:56:45,647 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.667 s, CPU [user: 0.106 s, system: 0.206 s], Allocated memory: 24.1 MB, transactions: 0, ObjectMaps: 0.0675 s [100% getAllPrimaryObjects (1x)] (10x)
2025-07-29 18:56:45,647 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.667 s, CPU [user: 0.182 s, system: 0.273 s], Allocated memory: 53.1 MB, transactions: 0, ObjectMaps: 0.112 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-29 18:56:45,648 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.666 s, CPU [user: 0.071 s, system: 0.108 s], Allocated memory: 10.7 MB, transactions: 0, svn: 0.0786 s [82% log2 (10x)] (13x), ObjectMaps: 0.0464 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-29 18:56:45,648 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.667 s, CPU [user: 0.0453 s, system: 0.0924 s], Allocated memory: 7.1 MB, transactions: 0, ObjectMaps: 0.0589 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0353 s [77% log2 (5x), 13% testConnection (1x)] (7x)
2025-07-29 18:56:45,648 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.666 s, CPU [user: 0.0769 s, system: 0.13 s], Allocated memory: 12.1 MB, transactions: 0, ObjectMaps: 0.0697 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.0619 s [71% log2 (10x), 21% getLatestRevision (2x)] (13x)
2025-07-29 18:56:45,648 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.666 s, CPU [user: 0.235 s, system: 0.332 s], Allocated memory: 70.8 MB, transactions: 0, ObjectMaps: 0.125 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0759 s [26% log2 (5x), 25% log (1x), 24% info (5x), 13% getLatestRevision (2x)] (18x)
2025-07-29 18:56:45,648 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.48 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.297 s [62% log2 (36x), 15% getLatestRevision (9x), 10% testConnection (6x)] (61x)
2025-07-29 18:56:45,872 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.187 s [100% getReadConfiguration (48x)] (48x), svn: 0.0689 s [88% info (18x)] (38x)
2025-07-29 18:56:46,114 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.182 s [76% info (94x), 16% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.145 s [100% getReadConfiguration (54x)] (54x)
2025-07-29 18:56:46,344 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.217 s [100% doFinishStartup (1x)] (1x), commit: 0.0583 s [100% Revision (1x)] (1x), Lucene: 0.0278 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.0133 s [100% objectsToInv (1x)] (1x)
2025-07-29 18:57:16,493 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.403 s [72% info (158x), 20% getLatestRevision (11x)] (179x), PullingJob: 0.0796 s [100% collectChanges (10x)] (10x)
2025-07-29 18:57:17,377 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.805 s, CPU [user: 0.00584 s, system: 0.00185 s], Allocated memory: 531.3 kB, transactions: 1
2025-07-29 18:57:17,378 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 28, Incremental Baseline: 0.0358 s [100% WorkItem (21x)] (21x), persistence listener: 0.0167 s [76% indexRefreshPersistenceListener (1x), 18% WorkItemActivityCreator (1x)] (7x), resolve: 0.0162 s [86% User (1x)] (3x), Lucene: 0.0117 s [55% add (1x), 45% refresh (1x)] (2x), notification worker: 0.00971 s [63% RevisionActivityCreator (2x), 17% TestRunActivityCreator (1x), 14% WorkItemActivityCreator (1x)] (6x), Full Baseline: 0.00458 s [100% WorkItem (1x)] (1x), ObjectMaps: 0.00328 s [100% getPrimaryObjectLocation (1x)] (1x)
2025-07-29 18:57:17,380 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.89 s, CPU [user: 0.162 s, system: 0.0358 s], Allocated memory: 18.3 MB, transactions: 22, svn: 0.711 s [99% getDatedRevision (181x)] (183x), Lucene: 0.0507 s [79% buildBaselineSnapshots (1x), 21% buildBaseline (22x)] (23x)
2025-07-29 18:57:17,805 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6615751634840_0_6615751634840_0_: finished. Total: 1.31 s, CPU [user: 0.409 s, system: 0.113 s], Allocated memory: 54.6 MB, svn: 0.753 s [53% getDatedRevision (181x), 30% getDir2 content (25x)] (307x), resolve: 0.503 s [100% Category (96x)] (96x), ObjectMaps: 0.177 s [43% getPrimaryObjectProperty (96x), 37% getPrimaryObjectLocation (96x), 21% getLastPromoted (96x)] (388x)
2025-07-29 18:57:18,042 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6615751797c48_0_6615751797c48_0_: finished. Total: 0.123 s, CPU [user: 0.0544 s, system: 0.0125 s], Allocated memory: 8.4 MB, RepositoryConfigService: 0.0535 s [55% getReadConfiguration (162x), 45% getReadUserConfiguration (10x)] (172x), svn: 0.0465 s [54% info (19x), 40% getFile content (16x)] (37x), resolve: 0.0378 s [100% User (9x)] (9x), ObjectMaps: 0.0207 s [42% getPrimaryObjectProperty (9x), 40% getPrimaryObjectLocation (9x)] (37x)
2025-07-29 18:57:18,252 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66157517c044a_0_66157517c044a_0_: finished. Total: 0.17 s, CPU [user: 0.0505 s, system: 0.0109 s], Allocated memory: 19.9 MB, svn: 0.137 s [85% getDir2 content (17x)] (62x), RepositoryConfigService: 0.0387 s [98% getReadConfiguration (170x)] (192x)
2025-07-29 18:57:18,989 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66157517eb04b_0_66157517eb04b_0_: finished. Total: 0.737 s, CPU [user: 0.334 s, system: 0.0231 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.547 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.367 s [71% getFile content (412x), 29% getDir2 content (21x)] (434x), GC: 0.058 s [100% G1 Young Generation (4x)] (4x)
2025-07-29 18:57:19,320 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66157518bd04e_0_66157518bd04e_0_: finished. Total: 0.228 s, CPU [user: 0.0962 s, system: 0.00491 s], Allocated memory: 387.2 MB, RepositoryConfigService: 0.145 s [97% getReadConfiguration (2787x)] (3025x), svn: 0.139 s [51% getFile content (185x), 49% getDir2 content (20x)] (206x)
2025-07-29 18:57:19,320 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 2.82 s, CPU [user: 1.03 s, system: 0.181 s], Allocated memory: 1.6 GB, transactions: 10, svn: 1.59 s [38% getDir2 content (114x), 33% getFile content (809x), 25% getDatedRevision (181x)] (1144x), RepositoryConfigService: 0.854 s [95% getReadConfiguration (12019x)] (12691x), resolve: 0.597 s [84% Category (96x)] (117x), ObjectMaps: 0.218 s [46% getPrimaryObjectProperty (110x), 35% getPrimaryObjectLocation (116x)] (452x)
2025-07-29 18:57:19,320 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 54, svn: 2.3 s [48% getDatedRevision (362x), 26% getDir2 content (114x), 23% getFile content (809x)] (1327x), RepositoryConfigService: 0.854 s [95% getReadConfiguration (12019x)] (12691x), resolve: 0.598 s [84% Category (96x)] (118x), ObjectMaps: 0.218 s [46% getPrimaryObjectProperty (110x), 35% getPrimaryObjectLocation (116x)] (452x)
2025-07-29 18:59:58,192 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55d6ce7d-0a465820-13f065ac-9e512607] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/': Total: 0.498 s, CPU [user: 0.198 s, system: 0.0889 s], Allocated memory: 42.3 MB, transactions: 2, PolarionAuthenticator: 0.447 s [100% authenticate (1x)] (1x), GC: 0.052 s [100% G1 Young Generation (1x)] (1x), RepositoryConfigService: 0.0274 s [100% getReadConfiguration (1x)] (1x)
2025-07-29 18:59:58,531 [ajp-nio-127.0.0.1-8889-exec-4 | cID:55d6d125-0a465820-13f065ac-f8907fd5] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/global/canModifyConfiguration': Total: 0.157 s, CPU [user: 0.0795 s, system: 0.0197 s], Allocated memory: 9.7 MB, transactions: 0
2025-07-29 18:59:58,531 [ajp-nio-127.0.0.1-8889-exec-7 | cID:55d6d151-0a465820-13f065ac-8bfdcd26] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/canModifyConfiguration': Total: 0.113 s, CPU [user: 0.0186 s, system: 0.0048 s], Allocated memory: 1.4 MB, transactions: 0, permissions: 0.0113 s [100% readInstance (1x)] (1x)
2025-07-29 18:59:58,560 [ajp-nio-127.0.0.1-8889-exec-5 | cID:55d6d150-0a465820-13f065ac-5effc0e9] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxy_contributions?_=1753786798322': Total: 0.143 s, CPU [user: 0.0383 s, system: 0.00758 s], Allocated memory: 1.9 MB, transactions: 0
2025-07-29 18:59:58,602 [ajp-nio-127.0.0.1-8889-exec-8 | cID:55d6d150-0a465820-13f065ac-8c05290f] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections?_=1753786798324': Total: 0.184 s, CPU [user: 0.055 s, system: 0.0159 s], Allocated memory: 7.6 MB, transactions: 1, RepositoryConfigService: 0.0874 s [100% getReadConfiguration (1x)] (1x), svn: 0.0124 s [60% testConnection (1x), 40% getFile content (1x)] (3x), permissions: 0.0113 s [100% readInstance (1x)] (1x)
2025-07-29 18:59:58,614 [ajp-nio-127.0.0.1-8889-exec-6 | cID:55d6d151-0a465820-13f065ac-344d44e3] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1753786798323': Total: 0.196 s, CPU [user: 0.0282 s, system: 0.00516 s], Allocated memory: 3.4 MB, transactions: 1, RepositoryConfigService: 0.101 s [100% getReadConfiguration (1x)] (1x), permissions: 0.0114 s [100% readInstance (1x)] (1x)
