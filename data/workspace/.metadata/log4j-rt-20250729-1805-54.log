2025-07-29 18:06:02,504 [Catalina-utility-5] INFO  com.siemens.polarion.rt.config.RtAppConfig - RT server config is created
2025-07-29 18:06:03,075 [Catalina-utility-5] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Initialization of RT collectors...
2025-07-29 18:06:03,076 [Catalina-utility-5] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Found collector for subversion (contributed by 'com.siemens.polarion.rt[87]')
2025-07-29 18:06:03,076 [Catalina-utility-5] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Found collector for git (contributed by 'com.siemens.polarion.rt.collectors.git[89]')
2025-07-29 18:06:03,078 [Catalina-utility-5] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Initialization of RT parsers...
2025-07-29 18:06:03,080 [Catalina-utility-5] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.java (contributed by 'com.siemens.polarion.rt[87]')
2025-07-29 18:06:03,081 [Catalina-utility-5] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.xml (contributed by 'com.siemens.polarion.rt[87]')
2025-07-29 18:06:03,082 [Catalina-utility-5] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.c (contributed by 'com.siemens.polarion.rt.parsers.c[93]')
2025-07-29 18:06:04,314 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtCommunicationStartup - Rt configuration local mediator is started
2025-07-29 18:06:04,433 [ajp-nio-127.0.0.1-8889-exec-1 | cID:55a57879-c0a8d700-5cecbd49-8fbcffd6] INFO  com.siemens.polarion.rt.dataprovider.controller.RtDataProviderController - RT server is notified to update all configurations.
2025-07-29 18:06:04,454 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtNotifier - RT server was successfully notified of configuration change.
2025-07-29 18:06:04,455 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtCommunicationStartup - Rt notification service is started
