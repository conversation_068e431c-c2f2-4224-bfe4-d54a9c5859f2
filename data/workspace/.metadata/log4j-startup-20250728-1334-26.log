2025-07-28 13:34:26,703 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-28 13:34:26,703 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-28 13:34:26,703 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-28 13:34:26,703 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-28 13:34:26,704 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-28 13:34:26,704 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-28 13:34:26,704 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-28 13:34:30,845 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-28 13:34:30,982 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.136 s. ]
2025-07-28 13:34:30,982 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-28 13:34:31,031 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0487 s. ]
2025-07-28 13:34:31,140 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-28 13:34:31,265 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 25 s. ]
2025-07-28 13:34:31,449 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.75 s. ]
2025-07-28 13:34:31,534 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-28 13:34:31,534 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-28 13:34:31,563 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.11 s. ]
2025-07-28 13:34:31,564 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-28 13:34:31,564 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-28 13:34:31,569 [LowLevelDataService-contextInitializer-6 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (1/9)
2025-07-28 13:34:31,570 [LowLevelDataService-contextInitializer-5 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (4/9)
2025-07-28 13:34:31,570 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (3/9)
2025-07-28 13:34:31,570 [LowLevelDataService-contextInitializer-3 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (6/9)
2025-07-28 13:34:31,570 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (2/9)
2025-07-28 13:34:31,570 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (5/9)
2025-07-28 13:34:31,578 [LowLevelDataService-contextInitializer-1 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (7/9)
2025-07-28 13:34:31,708 [LowLevelDataService-contextInitializer-6 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-07-28 13:34:31,841 [LowLevelDataService-contextInitializer-1 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-07-28 13:34:32,332 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.77 s. ]
2025-07-28 13:34:32,343 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-28 13:34:32,343 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-28 13:34:32,561 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-28 13:34:32,574 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.24 s. ]
2025-07-28 13:34:32,602 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-28 13:34:32,602 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-28 13:34:32,606 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-28 13:34:32,660 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-28 13:34:32,711 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (3/9)
2025-07-28 13:34:32,747 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-07-28 13:34:32,776 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (5/9)
2025-07-28 13:34:32,790 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (6/9)
2025-07-28 13:34:32,801 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (7/9)
2025-07-28 13:34:32,826 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-07-28 13:34:32,847 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-07-28 13:34:32,847 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.27 s. ]
2025-07-28 13:34:32,848 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-28 13:34:32,848 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-28 13:34:32,876 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.03 s. ]
2025-07-28 13:34:32,877 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-28 13:34:32,877 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-28 13:34:32,980 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-28 13:34:32,982 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-28 13:34:33,093 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.22 s. ]
2025-07-28 13:34:33,094 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-28 13:34:33,094 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-28 13:34:33,101 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-28 13:34:33,101 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-28 13:34:33,101 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
