2025-07-28 15:58:28,066 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0567 s [63% update (144x), 37% query (12x)] (221x), svn: 0.0104 s [46% getLatestRevision (2x), 41% testConnection (1x)] (4x)
2025-07-28 15:58:28,208 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0334 s [60% getDir2 content (2x), 33% info (3x)] (6x)
2025-07-28 15:58:28,993 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.783 s, CPU [user: 0.214 s, system: 0.28 s], Allocated memory: 53.1 MB, transactions: 0, ObjectMaps: 0.114 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-28 15:58:28,994 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.782 s, CPU [user: 0.124 s, system: 0.218 s], Allocated memory: 23.8 MB, transactions: 0, ObjectMaps: 0.0859 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-28 15:58:28,994 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.782 s, CPU [user: 0.231 s, system: 0.335 s], Allocated memory: 70.2 MB, transactions: 0, ObjectMaps: 0.118 s [99% getAllPrimaryObjects (1x)] (12x)
2025-07-28 15:58:28,994 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.783 s, CPU [user: 0.057 s, system: 0.0827 s], Allocated memory: 7.1 MB, transactions: 0, ObjectMaps: 0.0773 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0406 s [72% log2 (5x), 14% testConnection (1x)] (7x)
2025-07-28 15:58:28,994 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.783 s, CPU [user: 0.102 s, system: 0.106 s], Allocated memory: 10.9 MB, transactions: 0, svn: 0.202 s [51% log2 (10x), 19% info (5x), 13% log (1x)] (24x), ObjectMaps: 0.0447 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-28 15:58:28,994 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.783 s, CPU [user: 0.0903 s, system: 0.157 s], Allocated memory: 14.2 MB, transactions: 0, svn: 0.0986 s [76% log2 (10x), 18% getLatestRevision (2x)] (13x), ObjectMaps: 0.0893 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-28 15:58:28,994 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.529 s [100% getAllPrimaryObjects (8x)] (61x), svn: 0.412 s [62% log2 (36x), 13% getLatestRevision (9x), 9% info (5x)] (61x)
2025-07-28 15:58:29,219 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.195 s [100% getReadConfiguration (48x)] (48x), svn: 0.0744 s [86% info (18x)] (38x)
2025-07-28 15:58:29,530 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.232 s [79% info (94x), 14% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.181 s [100% getReadConfiguration (94x)] (94x)
2025-07-28 15:58:29,756 [PolarionDocIdCreator-5] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.114 s, CPU [user: 0.0337 s, system: 0.0111 s], Allocated memory: 11.5 MB, GC: 0.012 s [100% G1 Young Generation (1x)] (1x)
2025-07-28 15:58:29,883 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.326 s [100% doFinishStartup (1x)] (1x), DB: 0.0784 s [56% update (45x), 20% query (20x), 15% execute (15x)] (122x), commit: 0.0516 s [72% Revision (1x), 28% BuildArtifact (1x)] (2x), Lucene: 0.0395 s [100% refresh (2x)] (2x), SubterraURITable: 0.0342 s [100% addIfNotExistsDB (20x)] (20x), resolve: 0.0214 s [83% BuildArtifact (11x)] (13x)
2025-07-28 15:58:33,151 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.444 s [84% info (158x)] (170x), GlobalHandler: 0.0424 s [99% applyTxChanges (1x)] (7x)
2025-07-28 15:58:33,911 [DBHistoryCreator-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.702 s, CPU [user: 0.00493 s, system: 0.00129 s], Allocated memory: 492.4 kB, transactions: 1
2025-07-28 15:58:33,911 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.702 s, CPU [user: 0.00486 s, system: 0.00167 s], Allocated memory: 591.7 kB, transactions: 1
2025-07-28 15:58:33,911 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 67, Lucene: 0.027 s [57% add (1x), 43% refresh (2x)] (3x), resolve: 0.0211 s [95% User (2x)] (4x), Incremental Baseline: 0.0204 s [100% WorkItem (20x)] (20x), persistence listener: 0.0189 s [48% indexRefreshPersistenceListener (9x), 18% PlanActivityCreator (9x), 14% TestRunActivityCreator (9x)] (63x), notification worker: 0.0177 s [51% RevisionActivityCreator (18x), 16% PlanActivityCreator (9x), 16% TestRunActivityCreator (9x)] (54x), ObjectMaps: 0.00538 s [100% getPrimaryObjectLocation (2x)] (2x), DB: 0.00513 s [54% commit (2x), 46% update (2x)] (4x), EHCache: 0.0015 s [99% GET (16x)] (36x)
2025-07-28 15:58:33,912 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.706 s, CPU [user: 0.157 s, system: 0.0274 s], Allocated memory: 18.2 MB, transactions: 22, svn: 0.635 s [99% getDatedRevision (181x)] (183x)
2025-07-28 15:58:34,331 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6614029732442_0_6614029732442_0_: finished. Total: 1.17 s, CPU [user: 0.369 s, system: 0.0819 s], Allocated memory: 53.3 MB, svn: 0.679 s [51% getDatedRevision (181x), 32% getDir2 content (25x)] (307x), resolve: 0.352 s [100% Category (96x)] (96x), ObjectMaps: 0.115 s [42% getPrimaryObjectProperty (96x), 34% getPrimaryObjectLocation (96x), 24% getLastPromoted (96x)] (388x), GlobalHandler: 0.0681 s [90% applyTxChanges (2x)] (100x)
2025-07-28 15:58:34,586 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6614029874c70_0_6614029874c70_0_: finished. Total: 0.134 s, CPU [user: 0.0628 s, system: 0.0117 s], Allocated memory: 8.4 MB, RepositoryConfigService: 0.0604 s [54% getReadConfiguration (162x), 46% getReadUserConfiguration (10x)] (172x), svn: 0.0563 s [58% info (19x), 37% getFile content (16x)] (37x), resolve: 0.0393 s [100% User (9x)] (9x), ObjectMaps: 0.0197 s [45% getPrimaryObjectProperty (9x), 36% getPrimaryObjectLocation (9x)] (37x)
2025-07-28 15:58:34,886 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66140298a8072_0_66140298a8072_0_: finished. Total: 0.229 s, CPU [user: 0.0688 s, system: 0.00808 s], Allocated memory: 19.9 MB, svn: 0.18 s [61% getDir2 content (17x), 39% getFile content (44x)] (62x), RepositoryConfigService: 0.104 s [99% getReadConfiguration (170x)] (192x)
2025-07-28 15:58:35,675 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66140298e1873_0_66140298e1873_0_: finished. Total: 0.789 s, CPU [user: 0.355 s, system: 0.0226 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.584 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.432 s [64% getFile content (412x), 36% getDir2 content (21x)] (434x)
2025-07-28 15:58:36,074 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66140299c4c76_0_66140299c4c76_0_: finished. Total: 0.279 s, CPU [user: 0.115 s, system: 0.00717 s], Allocated memory: 384.1 MB, svn: 0.18 s [50% getDir2 content (20x), 50% getFile content (185x)] (206x), RepositoryConfigService: 0.169 s [96% getReadConfiguration (2787x)] (3025x)
2025-07-28 15:58:36,075 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 2.92 s, CPU [user: 1.07 s, system: 0.148 s], Allocated memory: 1.6 GB, transactions: 10, svn: 1.71 s [39% getDir2 content (114x), 36% getFile content (809x), 20% getDatedRevision (181x)] (1144x), RepositoryConfigService: 0.989 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.452 s [78% Category (96x), 13% Project (6x)] (117x), ObjectMaps: 0.155 s [46% getPrimaryObjectProperty (110x), 33% getPrimaryObjectLocation (116x), 22% getLastPromoted (110x)] (452x)
2025-07-28 15:58:36,075 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 53, svn: 2.35 s [42% getDatedRevision (362x), 29% getDir2 content (114x), 26% getFile content (809x)] (1328x), RepositoryConfigService: 0.989 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.452 s [78% Category (96x), 13% Project (6x)] (118x), ObjectMaps: 0.155 s [46% getPrimaryObjectProperty (110x), 33% getPrimaryObjectLocation (116x), 22% getLastPromoted (110x)] (452x)
