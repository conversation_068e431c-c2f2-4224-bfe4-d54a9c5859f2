2025-07-29 18:34:08,279 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Using logging context STANDALONE
2025-07-29 18:34:08,281 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Launchers manager started...
2025-07-29 18:34:08,281 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Using home directory /opt/polarion/polarion
2025-07-29 18:34:08,281 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Using root directory /opt/polarion
2025-07-29 18:34:08,281 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Using workspace directory /opt/polarion/data/workspace
2025-07-29 18:34:08,281 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Using config directory /Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion
2025-07-29 18:34:08,281 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Loading external properties ...
2025-07-29 18:34:08,281 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Using external property file /opt/polarion/etc/polarion.properties
2025-07-29 18:34:08,281 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Loading internal properties ...
2025-07-29 18:34:08,283 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Host: zhangwentiandeMac-mini-2.local (*************)
2025-07-29 18:34:08,286 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Product: com.polarion.alm
2025-07-29 18:34:08,286 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Version: 3.22.1
2025-07-29 18:34:08,286 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Build: 20220419-1528-22_R1-be3adceb
2025-07-29 18:34:08,288 [main] WARN  com.polarion.core.boot.impl.AppLaunchersManager - missing subfolder 'eclipse' under extension directory: /opt/polarion/polarion/extensions/fasnote
2025-07-29 18:34:08,288 [main] WARN  com.polarion.core.boot.impl.AppLaunchersManager - missing subfolder 'eclipse' under extension directory: /opt/polarion/polarion/extensions/2404
2025-07-29 18:34:08,288 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Extensions: [exts]
2025-07-29 18:34:08,291 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Workspace location: /opt/polarion/data/workspace
2025-07-29 18:34:08,291 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Workspace lock acquired
2025-07-29 18:34:08,291 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Found applications: [polarion.server, polarion.coordinator, polarion.rt]
2025-07-29 18:34:08,291 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Starting application: polarion.server
2025-07-29 18:34:08,297 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Application extension successfully read
2025-07-29 18:34:08,302 [main] INFO  com.polarion.platform.internal.SystemStatistics - Initializing monitoring, isThreadCpuTimeSupported: true, isThreadContentionMonitoringSupported: true, isThreadAllocatedMemorySupported: true
2025-07-29 18:34:08,302 [main] INFO  com.polarion.platform.internal.SystemStatistics - State before enabling: isThreadCpuTimeEnabled: true, isThreadContentionMonitoringEnabled: false, isThreadAllocatedMemoryEnabled: true
2025-07-29 18:34:08,303 [main] INFO  com.polarion.platform.internal.SystemStatistics - State after enabling: isThreadCpuTimeEnabled: true, isThreadContentionMonitoringEnabled: false, isThreadAllocatedMemoryEnabled: true
2025-07-29 18:34:08,306 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 18:34:08,306 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-29 18:34:08,306 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 18:34:08,306 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-29 18:34:08,306 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-29 18:34:08,306 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:34:08,306 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-29 18:34:08,307 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - **** Java system properties listing: 
2025-07-29 18:34:08,325 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - adminPasswd = admin
2025-07-29 18:34:08,325 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - adminUser = admin
2025-07-29 18:34:08,325 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - announcer.smtp.auth = false
2025-07-29 18:34:08,325 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - announcer.smtp.host = 
2025-07-29 18:34:08,325 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - announcer.smtp.password = **PASSWORD**HIDDEN**
2025-07-29 18:34:08,325 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - announcer.smtp.port = 25
2025-07-29 18:34:08,325 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - announcer.smtp.user = 
2025-07-29 18:34:08,325 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - awt.toolkit = sun.lwawt.macosx.LWCToolkit
2025-07-29 18:34:08,325 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - base.url = http://localhost
2025-07-29 18:34:08,325 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - bfh.jobs.workdir = /opt/polarion/data/workspace/polarion-data/jobs
2025-07-29 18:34:08,325 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - BIRDir = /opt/polarion/data/BIR
2025-07-29 18:34:08,325 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - calculated.fields.mode = async
2025-07-29 18:34:08,325 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.activation.activationHelpLink = https://polarion.plm.automation.siemens.com/getlicense
2025-07-29 18:34:08,325 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.activation.server = https://license.polarion.com/licenseGenerator/generator/generate
2025-07-29 18:34:08,325 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.alm.ui.gravatar.enabled = false
2025-07-29 18:34:08,326 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.alm.ui.gravatar.url = http://www.gravatar.com/avatar/$emailHash$?d=identicon&s=50
2025-07-29 18:34:08,326 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.application = polarion.server
2025-07-29 18:34:08,326 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.config = /Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion
2025-07-29 18:34:08,326 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.data = /opt/polarion/data
2025-07-29 18:34:08,326 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.eclipse = /opt/polarion/polarion
2025-07-29 18:34:08,326 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.home = /opt/polarion/polarion
2025-07-29 18:34:08,326 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.licenseDir = /opt/polarion/polarion/license
2025-07-29 18:34:08,326 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.platform.internalPG = polarion:polarion@localhost:5434
2025-07-29 18:34:08,326 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.platform.monitoring.notifications.disabled = true
2025-07-29 18:34:08,326 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.platform.monitoring.notifications.receivers = 
2025-07-29 18:34:08,326 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.platform.monitoring.notifications.sender = 
2025-07-29 18:34:08,326 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.platform.monitoring.notifications.subject.prefix = 
2025-07-29 18:34:08,326 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.platform.persistence.notifications.disabled = true
2025-07-29 18:34:08,326 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.propertyFile = /opt/polarion/etc/polarion.properties
2025-07-29 18:34:08,326 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.root = /opt/polarion
2025-07-29 18:34:08,326 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.workspace = /opt/polarion/data/workspace
2025-07-29 18:34:08,326 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.collaborationNotifications.enabled = true
2025-07-29 18:34:08,326 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.document.listStyle = 1ai
2025-07-29 18:34:08,326 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.loggingContext = STANDALONE
2025-07-29 18:34:08,326 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.preview.thumbnailsDataDir = /opt/polarion/data/workspace/previews-data/thumbnails
2025-07-29 18:34:08,326 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.rest.cors.allowedOrigins = *
2025-07-29 18:34:08,326 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.rest.enabled = true
2025-07-29 18:34:08,326 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.rest.swaggerUi.enabled = true
2025-07-29 18:34:08,326 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.xcelerator.accEndpointUrl = https://acc.collab.sws.siemens.com
2025-07-29 18:34:08,326 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.xcelerator.baseDomain = sws.siemens.com
2025-07-29 18:34:08,326 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.xcelerator.shareEndpointUrl = https://share.sws.siemens.com
2025-07-29 18:34:08,326 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - content.types.user.table = /opt/polarion/polarion/plugins/com.polarion.core.boot_3.22.1/content-types.properties
2025-07-29 18:34:08,326 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - controlHostname = localhost
2025-07-29 18:34:08,326 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - controlPort = 8887
2025-07-29 18:34:08,326 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - createproject.default.location = Sandbox/
2025-07-29 18:34:08,326 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - createproject.default.useUserId = true
2025-07-29 18:34:08,326 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - createproject.limitedAccessMessage = You may create a project in the Sandbox project group (only). Please fill in the required properties below. For example:<br/><table><tr><td>Location:</td><td>Sandbox/MyFirstProject</td></tr><tr><td>ID:</td><td>MyFirstProject</td></tr></table><br/>Or use the suggested defaults.
2025-07-29 18:34:08,326 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - debug = false
2025-07-29 18:34:08,326 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - debug.license.validation = true
2025-07-29 18:34:08,326 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - debug.machine.code.generation = true
2025-07-29 18:34:08,326 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - debug.security.validation = true
2025-07-29 18:34:08,326 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - default.project.template.ALM = alm_vmodel
2025-07-29 18:34:08,326 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - default.project.template.Pro = alm_vmodel
2025-07-29 18:34:08,326 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - default.project.template.QA = qa_vmodel
2025-07-29 18:34:08,326 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - default.project.template.Requirements = req_vmodel
2025-07-29 18:34:08,326 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - default.project.template.XBase = alm_vmodel
2025-07-29 18:34:08,326 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - default.project.template.XEnterprise = alm_vmodel
2025-07-29 18:34:08,326 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - default.project.template.XPro = alm_vmodel
2025-07-29 18:34:08,326 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - derby.system.home = /opt/polarion/data/logs/derby
2025-07-29 18:34:08,327 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - eclipse.application = com.polarion.core.boot.app
2025-07-29 18:34:08,327 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - eclipse.commands = -application
com.polarion.core.boot.app
-data
/opt/polarion/data/workspace
-configuration
file:///Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/
-dev
file:///Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/dev.properties
-os
linux
-ws
linux
-arch
arm64
-appId
polarion.server

2025-07-29 18:34:08,327 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - eclipse.home.location = file:/opt/polarion/polarion/plugins/
2025-07-29 18:34:08,327 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - eclipse.p2.data.area = @config.dir/.p2
2025-07-29 18:34:08,327 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - eclipse.pde.launch = true
2025-07-29 18:34:08,327 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - eclipse.startTime = *************
2025-07-29 18:34:08,327 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - eclipse.stateSaveDelayInterval = 30000
2025-07-29 18:34:08,327 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - enableCreateAccountForm = false
2025-07-29 18:34:08,327 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - equinox.init.uuid = true
2025-07-29 18:34:08,327 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - error.report.email = 
2025-07-29 18:34:08,327 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - file.encoding = UTF-8
2025-07-29 18:34:08,327 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - file.separator = /
2025-07-29 18:34:08,327 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - ftp.nonProxyHosts = 127.0.0.1|***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|**********/16|*.**********/16|localhost|*.localhost|local|*.local|crashlytics.com|*.crashlytics.com|<local>|*.<local>
2025-07-29 18:34:08,327 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - gopherProxySet = false
2025-07-29 18:34:08,327 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - gosh.args = --nointeractive
2025-07-29 18:34:08,327 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - htpasswd.path = htpasswd
2025-07-29 18:34:08,327 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - http.nonProxyHosts = 127.0.0.1|***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|**********/16|*.**********/16|localhost|*.localhost|local|*.local|crashlytics.com|*.crashlytics.com|<local>|*.<local>
2025-07-29 18:34:08,327 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - index.activities = /opt/polarion/data/workspace/polarion-data/index
2025-07-29 18:34:08,327 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.awt.graphicsenv = sun.awt.CGraphicsEnvironment
2025-07-29 18:34:08,327 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.awt.headless = true
2025-07-29 18:34:08,327 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.awt.printerjob = sun.lwawt.macosx.CPrinterJob
2025-07-29 18:34:08,327 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.class.path = /opt/polarion/polarion/plugins/org.eclipse.equinox.launcher_1.5.0.v20180512-1130.jar:
2025-07-29 18:34:08,328 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.class.version = 55.0
2025-07-29 18:34:08,328 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.home = /Users/<USER>/Library/Java/JavaVirtualMachines/ms-11.0.27/Contents/Home
2025-07-29 18:34:08,328 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.io.tmpdir = /var/folders/z_/shw6wc7d7ps_fjvv781t4gt80000gn/T/
2025-07-29 18:34:08,328 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.library.path = /Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:.
2025-07-29 18:34:08,328 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.runtime.name = OpenJDK Runtime Environment
2025-07-29 18:34:08,328 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.runtime.version = 11.0.27+6-LTS
2025-07-29 18:34:08,328 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.security.policy = /opt/polarion/polarion/policy
2025-07-29 18:34:08,328 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.specification.maintenance.version = 3
2025-07-29 18:34:08,328 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.specification.name = Java Platform API Specification
2025-07-29 18:34:08,328 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.specification.vendor = Oracle Corporation
2025-07-29 18:34:08,328 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.specification.version = 11
2025-07-29 18:34:08,328 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vendor = Microsoft
2025-07-29 18:34:08,328 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vendor.url = https://www.microsoft.com
2025-07-29 18:34:08,328 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vendor.url.bug = https://github.com/microsoft/openjdk/issues
2025-07-29 18:34:08,328 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vendor.version = Microsoft-11367290
2025-07-29 18:34:08,328 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.version = 11.0.27
2025-07-29 18:34:08,328 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.version.date = 2025-04-15
2025-07-29 18:34:08,328 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.compressedOopsMode = Zero based
2025-07-29 18:34:08,328 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.info = mixed mode
2025-07-29 18:34:08,328 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.name = OpenJDK 64-Bit Server VM
2025-07-29 18:34:08,328 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.specification.name = Java Virtual Machine Specification
2025-07-29 18:34:08,328 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.specification.vendor = Oracle Corporation
2025-07-29 18:34:08,328 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.specification.version = 11
2025-07-29 18:34:08,328 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.vendor = Microsoft
2025-07-29 18:34:08,328 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.version = 11.0.27+6-LTS
2025-07-29 18:34:08,328 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - javasvn.timeout = 10000
2025-07-29 18:34:08,328 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - jdk.debug = release
2025-07-29 18:34:08,328 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - ldap.bind.password = **PASSWORD**HIDDEN**
2025-07-29 18:34:08,328 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.audit.enabled = true
2025-07-29 18:34:08,328 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.auto.scan.enabled = true
2025-07-29 18:34:08,328 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.cache.size = 100
2025-07-29 18:34:08,329 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.cache.ttl = 1800
2025-07-29 18:34:08,329 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.check.interval = 0
2025-07-29 18:34:08,329 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.allow.expired = true
2025-07-29 18:34:08,329 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.allow.local.files = true
2025-07-29 18:34:08,329 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.default.features = all
2025-07-29 18:34:08,329 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.default.max.users = 10
2025-07-29 18:34:08,329 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.default.plugin.id = com.fasnote.alm.plugin.manage
2025-07-29 18:34:08,329 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.mode = true
2025-07-29 18:34:08,329 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.show.machine.code = true
2025-07-29 18:34:08,329 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.skip.machine.binding = true
2025-07-29 18:34:08,329 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.skip.network.validation = true
2025-07-29 18:34:08,329 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.directory = dev-licenses
2025-07-29 18:34:08,329 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.encryption.enabled = false
2025-07-29 18:34:08,329 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.hot.reload.enabled = true
2025-07-29 18:34:08,329 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.log.level = DEBUG
2025-07-29 18:34:08,329 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.machine.binding.enabled = false
2025-07-29 18:34:08,329 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.max.plugins = 1000
2025-07-29 18:34:08,329 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.scan.interval = 60
2025-07-29 18:34:08,329 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.signature.validation.enabled = false
2025-07-29 18:34:08,329 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.validation.timeout = 1000
2025-07-29 18:34:08,329 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - licenseForNewUserAccount = 
2025-07-29 18:34:08,329 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - line.separator = 

2025-07-29 18:34:08,329 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - log4j2.contextSelector = org.apache.logging.log4j.core.selector.BasicContextSelector
2025-07-29 18:34:08,329 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - log4j2.loggerContextFactory = org.apache.logging.log4j.core.impl.Log4jContextFactory
2025-07-29 18:34:08,329 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - logDir = /opt/polarion/data/workspace/.metadata/
2025-07-29 18:34:08,329 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - login = polarion
2025-07-29 18:34:08,329 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - mavenConfigDir = /opt/polarion/polarion/../maven
2025-07-29 18:34:08,329 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - minimalPasswordLength = **PASSWORD**HIDDEN**
2025-07-29 18:34:08,329 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.eclipse.equinox.simpleconfigurator.configUrl = file:/Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/org.eclipse.equinox.simpleconfigurator/bundles.info
2025-07-29 18:34:08,329 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.eclipse.lyo.oslc4j.strictDatatypes = false
2025-07-29 18:34:08,329 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.executionenvironment = OSGi/Minimum-1.0, OSGi/Minimum-1.1, OSGi/Minimum-1.2, JavaSE/compact1-1.8, JavaSE/compact2-1.8, JavaSE/compact3-1.8, JRE-1.1, J2SE-1.2, J2SE-1.3, J2SE-1.4, J2SE-1.5, JavaSE-1.6, JavaSE-1.7, JavaSE-1.8, JavaSE-9, JavaSE-10, JavaSE-11
2025-07-29 18:34:08,329 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.language = zh
2025-07-29 18:34:08,329 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.os.name = MacOSX
2025-07-29 18:34:08,329 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.os.version = 15.5.0
2025-07-29 18:34:08,329 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.processor = aarch64
2025-07-29 18:34:08,329 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.storage = /Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion
2025-07-29 18:34:08,329 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.system.capabilities = osgi.ee; osgi.ee="OSGi/Minimum"; version:List<Version>="1.0, 1.1, 1.2", osgi.ee; osgi.ee="JRE"; version:List<Version>="1.0, 1.1", osgi.ee; osgi.ee="JavaSE"; version:List<Version>="1.0, 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 1.7, 1.8, 9.0, 10.0, 11.0",osgi.ee; osgi.ee="JavaSE/compact1"; version:List<Version>="1.8, 9.0, 10.0, 11.0",osgi.ee; osgi.ee="JavaSE/compact2"; version:List<Version>="1.8, 9.0, 10.0, 11.0",osgi.ee; osgi.ee="JavaSE/compact3"; version:List<Version>="1.8, 9.0, 10.0, 11.0"
2025-07-29 18:34:08,329 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.system.packages = com.sun.jarsigner, com.sun.java.accessibility.util, com.sun.javadoc, com.sun.jdi, com.sun.jdi.connect, com.sun.jdi.connect.spi, com.sun.jdi.event, com.sun.jdi.request, com.sun.jndi.ldap.spi, com.sun.management, com.sun.net.httpserver, com.sun.net.httpserver.spi, com.sun.nio.file, com.sun.nio.sctp, com.sun.security.auth, com.sun.security.auth.callback, com.sun.security.auth.login, com.sun.security.auth.module, com.sun.security.jgss, com.sun.source.doctree, com.sun.source.tree, com.sun.source.util, com.sun.tools.attach, com.sun.tools.attach.spi, com.sun.tools.javac, com.sun.tools.javadoc, com.sun.tools.jconsole, java.applet, java.awt, java.awt.color, java.awt.datatransfer, java.awt.desktop, java.awt.dnd, java.awt.event, java.awt.font, java.awt.geom, java.awt.im, java.awt.im.spi, java.awt.image, java.awt.image.renderable, java.awt.print, java.beans, java.beans.beancontext, java.io, java.lang, java.lang.annotation, java.lang.instrument, java.lang.invoke, java.lang.management, java.lang.module, java.lang.ref, java.lang.reflect, java.math, java.net, java.net.http, java.net.spi, java.nio, java.nio.channels, java.nio.channels.spi, java.nio.charset, java.nio.charset.spi, java.nio.file, java.nio.file.attribute, java.nio.file.spi, java.rmi, java.rmi.activation, java.rmi.dgc, java.rmi.registry, java.rmi.server, java.security, java.security.acl, java.security.cert, java.security.interfaces, java.security.spec, java.sql, java.text, java.text.spi, java.time, java.time.chrono, java.time.format, java.time.temporal, java.time.zone, java.util, java.util.concurrent, java.util.concurrent.atomic, java.util.concurrent.locks, java.util.function, java.util.jar, java.util.logging, java.util.prefs, java.util.regex, java.util.spi, java.util.stream, java.util.zip, javax.accessibility, javax.annotation.processing, javax.crypto, javax.crypto.interfaces, javax.crypto.spec, javax.imageio, javax.imageio.event, javax.imageio.metadata, javax.imageio.plugins.bmp, javax.imageio.plugins.jpeg, javax.imageio.plugins.tiff, javax.imageio.spi, javax.imageio.stream, javax.lang.model, javax.lang.model.element, javax.lang.model.type, javax.lang.model.util, javax.management, javax.management.loading, javax.management.modelmbean, javax.management.monitor, javax.management.openmbean, javax.management.relation, javax.management.remote, javax.management.remote.rmi, javax.management.timer, javax.naming, javax.naming.directory, javax.naming.event, javax.naming.ldap, javax.naming.spi, javax.net, javax.net.ssl, javax.print, javax.print.attribute, javax.print.attribute.standard, javax.print.event, javax.rmi.ssl, javax.script, javax.security.auth, javax.security.auth.callback, javax.security.auth.kerberos, javax.security.auth.login, javax.security.auth.spi, javax.security.auth.x500, javax.security.cert, javax.security.sasl, javax.smartcardio, javax.sound.midi, javax.sound.midi.spi, javax.sound.sampled, javax.sound.sampled.spi, javax.sql, javax.sql.rowset, javax.sql.rowset.serial, javax.sql.rowset.spi, javax.swing, javax.swing.border, javax.swing.colorchooser, javax.swing.event, javax.swing.filechooser, javax.swing.plaf, javax.swing.plaf.basic, javax.swing.plaf.metal, javax.swing.plaf.multi, javax.swing.plaf.nimbus, javax.swing.plaf.synth, javax.swing.table, javax.swing.text, javax.swing.text.html, javax.swing.text.html.parser, javax.swing.text.rtf, javax.swing.tree, javax.swing.undo, javax.tools, javax.transaction.xa, javax.xml, javax.xml.catalog, javax.xml.crypto, javax.xml.crypto.dom, javax.xml.crypto.dsig, javax.xml.crypto.dsig.dom, javax.xml.crypto.dsig.keyinfo, javax.xml.crypto.dsig.spec, javax.xml.datatype, javax.xml.namespace, javax.xml.parsers, javax.xml.stream, javax.xml.stream.events, javax.xml.stream.util, javax.xml.transform, javax.xml.transform.dom, javax.xml.transform.sax, javax.xml.transform.stax, javax.xml.transform.stream, javax.xml.validation, javax.xml.xpath, jdk.dynalink, jdk.dynalink.beans, jdk.dynalink.linker, jdk.dynalink.linker.support, jdk.dynalink.support, jdk.javadoc.doclet, jdk.jfr, jdk.jfr.consumer, jdk.jshell, jdk.jshell.execution, jdk.jshell.spi, jdk.jshell.tool, jdk.management.jfr, jdk.nashorn.api.scripting, jdk.nashorn.api.tree, jdk.net, jdk.nio, jdk.security.jarsigner, jdk.swing.interop, netscape.javascript, org.ietf.jgss, org.w3c.dom, org.w3c.dom.bootstrap, org.w3c.dom.css, org.w3c.dom.events, org.w3c.dom.html, org.w3c.dom.ls, org.w3c.dom.ranges, org.w3c.dom.stylesheets, org.w3c.dom.traversal, org.w3c.dom.views, org.w3c.dom.xpath, org.xml.sax, org.xml.sax.ext, org.xml.sax.helpers, sun.misc, sun.reflect
2025-07-29 18:34:08,329 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.uuid = 314b979a-f8c8-41e2-9c99-21e4c0e67683
2025-07-29 18:34:08,329 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.vendor = Eclipse
2025-07-29 18:34:08,329 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.version = 1.9.0
2025-07-29 18:34:08,329 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.supports.framework.extension = true
2025-07-29 18:34:08,330 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.supports.framework.fragment = true
2025-07-29 18:34:08,330 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.supports.framework.requirebundle = true
2025-07-29 18:34:08,330 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.xsocket.connection.client.readbuffer.usedirect = true
2025-07-29 18:34:08,330 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.xsocket.connection.server.readbuffer.usedirect = true
2025-07-29 18:34:08,330 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - os.arch = aarch64
2025-07-29 18:34:08,330 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - os.name = Mac OS X
2025-07-29 18:34:08,330 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - os.version = 15.5
2025-07-29 18:34:08,330 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.arch = arm64
2025-07-29 18:34:08,330 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.bundles = reference:file:/opt/polarion/polarion/plugins/org.eclipse.equinox.simpleconfigurator_1.3.0.v20180502-1828.jar@1:start
2025-07-29 18:34:08,330 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.bundles.defaultStartLevel = 4
2025-07-29 18:34:08,330 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.checkConfiguration = true
2025-07-29 18:34:08,330 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.compatibility.bootdelegation = true
2025-07-29 18:34:08,330 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.compatibility.bootdelegation.default = true
2025-07-29 18:34:08,330 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.configuration.area = file:/Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/
2025-07-29 18:34:08,330 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.configuration.cascaded = false
2025-07-29 18:34:08,330 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.dev = file:///Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/dev.properties
2025-07-29 18:34:08,330 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.framework = file:/opt/polarion/polarion/plugins/org.eclipse.osgi_3.13.0.v20180409-1500.jar
2025-07-29 18:34:08,330 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.framework.shape = jar
2025-07-29 18:34:08,330 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.framework.useSystemProperties = true
2025-07-29 18:34:08,330 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.install.area = file:/opt/polarion/polarion/plugins/
2025-07-29 18:34:08,330 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.instance.area = file:/opt/polarion/data/workspace/
2025-07-29 18:34:08,330 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.logfile = /opt/polarion/data/workspace/.metadata/.log
2025-07-29 18:34:08,330 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.nl = zh_CN_#Hans
2025-07-29 18:34:08,330 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.os = linux
2025-07-29 18:34:08,330 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.syspath = /opt/polarion/polarion/plugins
2025-07-29 18:34:08,330 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.tracefile = /opt/polarion/data/workspace/.metadata/trace.log
2025-07-29 18:34:08,330 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.ws = linux
2025-07-29 18:34:08,330 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - password = **PASSWORD**HIDDEN**
2025-07-29 18:34:08,330 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - path.separator = :
2025-07-29 18:34:08,330 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - pdfbox.fontcache = /opt/polarion/data/workspace/polarion-data
2025-07-29 18:34:08,330 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - pdfexport.config = /opt/polarion/polarion/configuration/pdfexport.xml
2025-07-29 18:34:08,330 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - polarion.build.default.deploy.repository.id = polarion-shared
2025-07-29 18:34:08,330 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - polarion.build.default.deploy.repository.url = file:///opt/polarion/data/shared-maven-repo
2025-07-29 18:34:08,330 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - polarion.build.maven.location.maven2 = /opt/polarion/polarion/../maven/distribution
2025-07-29 18:34:08,330 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - polarion.global.doc.cache.size = 100
2025-07-29 18:34:08,330 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - polarion.global.doc.cache.with.history = false
2025-07-29 18:34:08,330 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - polarion.tx.doc.cache.size = 100
2025-07-29 18:34:08,330 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - productLink.com.polarion.alm = https://polarion.plm.automation.siemens.com/products/polarion-alm
2025-07-29 18:34:08,330 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - productLink.com.polarion.qa = https://polarion.plm.automation.siemens.com/products/polarion-qa
2025-07-29 18:34:08,330 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - productLink.com.polarion.requirements = https://polarion.plm.automation.siemens.com/products/polarion-requirements
2025-07-29 18:34:08,330 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - repo = http://localhost/repo
2025-07-29 18:34:08,330 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - rolesForNewUserAccount = user
2025-07-29 18:34:08,330 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - RRDir = /opt/polarion/data/RR
2025-07-29 18:34:08,330 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - SDKDir = /opt/polarion/polarion/SDK
2025-07-29 18:34:08,330 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - secure.approvals = false
2025-07-29 18:34:08,330 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - shutdownCatchPhrase = shutdown
2025-07-29 18:34:08,330 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - simple.profiler.enabled = false
2025-07-29 18:34:08,330 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - skip.data.preloading = false
2025-07-29 18:34:08,330 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - socksNonProxyHosts = 127.0.0.1|***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|**********/16|*.**********/16|localhost|*.localhost|local|*.local|crashlytics.com|*.crashlytics.com|<local>|*.<local>
2025-07-29 18:34:08,330 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - stderr.encoding = UTF-8
2025-07-29 18:34:08,330 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - stdout.encoding = UTF-8
2025-07-29 18:34:08,330 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - storeUrl.com.polarion.requirements = https://polarion.plm.automation.siemens.com/products/licensing?product=REQUIREMENTS
2025-07-29 18:34:08,330 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.arch.data.model = 64
2025-07-29 18:34:08,330 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.boot.library.path = /Users/<USER>/Library/Java/JavaVirtualMachines/ms-11.0.27/Contents/Home/lib
2025-07-29 18:34:08,330 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.cpu.endian = little
2025-07-29 18:34:08,330 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.cpu.isalist = 
2025-07-29 18:34:08,330 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.io.unicode.encoding = UnicodeBig
2025-07-29 18:34:08,331 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.java.command = org.eclipse.equinox.launcher.Main -application com.polarion.core.boot.app -data /opt/polarion/data/workspace -configuration file:///Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/ -dev file:///Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/dev.properties -os linux -ws linux -arch arm64 -appId polarion.server
2025-07-29 18:34:08,331 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.java.launcher = SUN_STANDARD
2025-07-29 18:34:08,331 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.jnu.encoding = UTF-8
2025-07-29 18:34:08,331 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.management.compiler = HotSpot 64-Bit Tiered Compilers
2025-07-29 18:34:08,331 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.os.patch.level = unknown
2025-07-29 18:34:08,331 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - support.contact = https://polarion.plm.automation.siemens.com/techsupport/resources
2025-07-29 18:34:08,331 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - support.license.email = <EMAIL>
2025-07-29 18:34:08,331 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - support.sales.email = <EMAIL>
2025-07-29 18:34:08,331 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - svn.access.file = /opt/polarion/data/svn/access
2025-07-29 18:34:08,331 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - svn.passwd.file = /opt/polarion/data/svn/passwd
2025-07-29 18:34:08,331 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - svnkit.http.encoding = UTF-8
2025-07-29 18:34:08,331 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - svnkit.library.gnome-keyring.enabled = false
2025-07-29 18:34:08,331 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - TomcatService.ajp13-port = 8889
2025-07-29 18:34:08,331 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - TomcatService.request.safeListedHosts = 0.0.0.0
2025-07-29 18:34:08,331 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - user.country = CN
2025-07-29 18:34:08,331 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - user.dir = /Applications/Eclipse JEE.app/Contents/MacOS
2025-07-29 18:34:08,331 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - user.home = /Users/<USER>
2025-07-29 18:34:08,331 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - user.language = zh
2025-07-29 18:34:08,331 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - user.name = zhangwentian
2025-07-29 18:34:08,331 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - user.script = Hans
2025-07-29 18:34:08,331 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - user.timezone = Asia/Shanghai
2025-07-29 18:34:08,331 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - userAccountVault = /opt/polarion/data/workspace/user-account-vault
2025-07-29 18:34:08,331 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - workDir = /opt/polarion/data/workspace/polarion-data
2025-07-29 18:34:08,331 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - **** END of Java system properties
2025-07-29 18:34:08,333 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - XML parsers factory: com.sun.org.apache.xerces.internal.jaxp.DocumentBuilderFactoryImpl
2025-07-29 18:34:08,334 [main] INFO  com.polarion.psvn.launcher.internal.platform.PlatformService - Starting Platform...
2025-07-29 18:34:08,340 [main] INFO  PolarionLicensing - Searching for valid license file in /opt/polarion/polarion/license
2025-07-29 18:34:08,341 [main] INFO  PolarionLicensing - Trying to load license file polarion.lic
2025-07-29 18:34:08,342 [main] INFO  PolarionLicensing - The license file contains the following fields:
2025-07-29 18:34:08,342 [main] INFO  PolarionLicensing - *** License fields ***
2025-07-29 18:34:08,342 [main] INFO  PolarionLicensing - VariantsNamedUsers = 3
2025-07-29 18:34:08,342 [main] INFO  PolarionLicensing - almNamedUsers = 3
2025-07-29 18:34:08,342 [main] INFO  PolarionLicensing - dateCreated = 23.07.2025
2025-07-29 18:34:08,342 [main] INFO  PolarionLicensing - expirationDate = 21.08.2025
2025-07-29 18:34:08,342 [main] INFO  PolarionLicensing - hardwareKey = 8AG9-261C-1962
2025-07-29 18:34:08,342 [main] INFO  PolarionLicensing - licenseFormat = 2022
2025-07-29 18:34:08,342 [main] INFO  PolarionLicensing - licenseType = EVAL
2025-07-29 18:34:08,342 [main] INFO  PolarionLicensing - multiInstanceRunningInstances = 3
2025-07-29 18:34:08,342 [main] INFO  PolarionLicensing - userCompany = Polarion Eval
2025-07-29 18:34:08,342 [main] INFO  PolarionLicensing - *** License fields END ***
2025-07-29 18:34:08,356 [main] INFO  PolarionLicensing - Removing allocations by null
2025-07-29 18:34:08,357 [main] INFO  PolarionLicensing - STATS:concurrentVariantsUser,current:0,peak:0,limit:0
2025-07-29 18:34:08,357 [main] INFO  PolarionLicensing - 0 namedReviewerUser assignments (out of 0) loaded: []
2025-07-29 18:34:08,357 [main] INFO  PolarionLicensing - 0 concurrentReviewerUser assignments (out of 0) loaded: []
2025-07-29 18:34:08,357 [main] INFO  PolarionLicensing - STATS:concurrentReviewerUser,current:0,peak:0,limit:0
2025-07-29 18:34:08,357 [main] INFO  PolarionLicensing - 0 namedXBaseUser assignments (out of 0) loaded: []
2025-07-29 18:34:08,357 [main] INFO  PolarionLicensing - 0 concurrentXBaseUser assignments (out of 0) loaded: []
2025-07-29 18:34:08,357 [main] INFO  PolarionLicensing - STATS:concurrentXBaseUser,current:0,peak:0,limit:0
2025-07-29 18:34:08,357 [main] INFO  PolarionLicensing - 0 namedXProUser assignments (out of 0) loaded: []
2025-07-29 18:34:08,357 [main] INFO  PolarionLicensing - 0 concurrentXProUser assignments (out of 0) loaded: []
2025-07-29 18:34:08,357 [main] INFO  PolarionLicensing - STATS:concurrentXProUser,current:0,peak:0,limit:0
2025-07-29 18:34:08,357 [main] INFO  PolarionLicensing - 0 namedXEnterpriseUser assignments (out of 0) loaded: []
2025-07-29 18:34:08,357 [main] INFO  PolarionLicensing - 0 concurrentXEnterpriseUser assignments (out of 0) loaded: []
2025-07-29 18:34:08,357 [main] INFO  PolarionLicensing - STATS:concurrentXEnterpriseUser,current:0,peak:0,limit:0
2025-07-29 18:34:08,357 [main] INFO  PolarionLicensing - 0 namedProUser assignments (out of 0) loaded: []
2025-07-29 18:34:08,357 [main] INFO  PolarionLicensing - 0 concurrentProUser assignments (out of 0) loaded: []
2025-07-29 18:34:08,357 [main] INFO  PolarionLicensing - STATS:concurrentProUser,current:0,peak:0,limit:0
2025-07-29 18:34:08,357 [main] INFO  PolarionLicensing - 0 namedRequirementsUser assignments (out of 0) loaded: []
2025-07-29 18:34:08,357 [main] INFO  PolarionLicensing - 0 concurrentRequirementsUser assignments (out of 0) loaded: []
2025-07-29 18:34:08,357 [main] INFO  PolarionLicensing - STATS:concurrentRequirementsUser,current:0,peak:0,limit:0
2025-07-29 18:34:08,357 [main] INFO  PolarionLicensing - 0 namedQAUser assignments (out of 0) loaded: []
2025-07-29 18:34:08,357 [main] INFO  PolarionLicensing - 0 concurrentQAUser assignments (out of 0) loaded: []
2025-07-29 18:34:08,357 [main] INFO  PolarionLicensing - STATS:concurrentQAUser,current:0,peak:0,limit:0
2025-07-29 18:34:08,358 [main] INFO  PolarionLicensing - 3 namedALMUser assignments (out of 3) loaded: [admin, ou_d6f3139d36fb2978b33a8f870096b9e3, mTest]
2025-07-29 18:34:08,358 [main] INFO  PolarionLicensing - 0 concurrentALMUser assignments (out of 0) loaded: []
2025-07-29 18:34:08,358 [main] INFO  PolarionLicensing - STATS:concurrentALMUser,current:0,peak:0,limit:0
2025-07-29 18:34:08,358 [main] INFO  PolarionLicensing - 
*******************************************************************
 Polarion successfully activated
*******************************************************************
2025-07-29 18:34:08,412 [main] INFO  com.polarion.platform.internal.i18n.LanguageContributor - Localization file /META-INF/messages_en.properties read successfully (7789 messages)
2025-07-29 18:34:08,438 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Processing bundles:
2025-07-29 18:34:08,438 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [0] - org.eclipse.osgi
2025-07-29 18:34:08,438 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [1] - org.eclipse.equinox.simpleconfigurator
2025-07-29 18:34:08,438 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [2] - antlr
2025-07-29 18:34:08,438 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [3] - antlr4
2025-07-29 18:34:08,438 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [4] - antlr4-runtime
2025-07-29 18:34:08,438 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [5] - bcprov
2025-07-29 18:34:08,438 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [6] - com.auth0.java-jwt
2025-07-29 18:34:08,439 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [7] - com.fasnote.alm.injection
2025-07-29 18:34:08,439 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [8] - com.fasnote.alm.plugin.manage
2025-07-29 18:34:08,439 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [9] - com.fasnote.alm.queryexpander
2025-07-29 18:34:08,440 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.fasnote.alm.queryexpander to HiveMind
2025-07-29 18:34:08,440 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [10] - com.fasnote.alm.test
2025-07-29 18:34:08,442 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [11] - com.fasterxml.classmate
2025-07-29 18:34:08,442 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [12] - com.fasterxml.jackson
2025-07-29 18:34:08,442 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [13] - com.fasterxml.jackson.dataformat.jackson-dataformat-yaml
2025-07-29 18:34:08,442 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [14] - com.fasterxml.jackson.jaxrs
2025-07-29 18:34:08,442 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [15] - com.fasterxml.woodstox
2025-07-29 18:34:08,442 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [16] - com.google.gson
2025-07-29 18:34:08,443 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [17] - com.google.guava
2025-07-29 18:34:08,443 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [18] - com.google.guava.failureaccess
2025-07-29 18:34:08,443 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [19] - com.ibm.icu.icu4j
2025-07-29 18:34:08,443 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [20] - com.icl.saxon
2025-07-29 18:34:08,443 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [21] - com.jayway.jsonpath.json-path
2025-07-29 18:34:08,443 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [22] - com.jcraft.jsch
2025-07-29 18:34:08,443 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [23] - com.networknt.json-schema-validator
2025-07-29 18:34:08,443 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [24] - com.nimbusds.content-type
2025-07-29 18:34:08,443 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [25] - com.nimbusds.nimbus-jose-jwt
2025-07-29 18:34:08,444 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [26] - com.opensymphony.quartz
2025-07-29 18:34:08,444 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [27] - com.polarion.alm.ProjectPlanGantt_new
2025-07-29 18:34:08,444 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.ProjectPlanGantt_new to HiveMind
2025-07-29 18:34:08,444 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [28] - com.polarion.alm.builder
2025-07-29 18:34:08,445 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.builder to HiveMind
2025-07-29 18:34:08,445 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [29] - com.polarion.alm.checker
2025-07-29 18:34:08,445 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.checker to HiveMind
2025-07-29 18:34:08,445 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [30] - com.polarion.alm.extension.vcontext
2025-07-29 18:34:08,445 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.extension.vcontext to HiveMind
2025-07-29 18:34:08,445 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [31] - com.polarion.alm.impex
2025-07-29 18:34:08,446 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.impex to HiveMind
2025-07-29 18:34:08,446 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [32] - com.polarion.alm.install
2025-07-29 18:34:08,446 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [33] - com.polarion.alm.oslc
2025-07-29 18:34:08,447 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.oslc to HiveMind
2025-07-29 18:34:08,447 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [34] - com.polarion.alm.projects
2025-07-29 18:34:08,447 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.projects to HiveMind
2025-07-29 18:34:08,447 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [35] - com.polarion.alm.qcentre
2025-07-29 18:34:08,447 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.qcentre to HiveMind
2025-07-29 18:34:08,447 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [36] - com.polarion.alm.tracker
2025-07-29 18:34:08,448 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.tracker to HiveMind
2025-07-29 18:34:08,448 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [37] - com.polarion.alm.ui
2025-07-29 18:34:08,451 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.ui to HiveMind
2025-07-29 18:34:08,451 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [38] - com.polarion.alm.ui.diagrams.mxgraph
2025-07-29 18:34:08,451 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [39] - com.polarion.alm.wiki
2025-07-29 18:34:08,452 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.wiki to HiveMind
2025-07-29 18:34:08,452 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [40] - com.polarion.alm.ws
2025-07-29 18:34:08,452 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [41] - com.polarion.alm.ws.client
2025-07-29 18:34:08,453 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [42] - com.polarion.cluster
2025-07-29 18:34:08,453 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.cluster to HiveMind
2025-07-29 18:34:08,453 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [43] - com.polarion.core.boot
2025-07-29 18:34:08,453 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [44] - com.polarion.core.util
2025-07-29 18:34:08,454 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [45] - com.polarion.fop
2025-07-29 18:34:08,454 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [46] - com.polarion.platform
2025-07-29 18:34:08,454 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform to HiveMind
2025-07-29 18:34:08,454 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [47] - com.polarion.platform.guice
2025-07-29 18:34:08,455 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [48] - com.polarion.platform.hivemind
2025-07-29 18:34:08,455 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.hivemind to HiveMind
2025-07-29 18:34:08,455 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [49] - com.polarion.platform.jobs
2025-07-29 18:34:08,455 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.jobs to HiveMind
2025-07-29 18:34:08,455 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [50] - com.polarion.platform.monitoring
2025-07-29 18:34:08,456 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.monitoring to HiveMind
2025-07-29 18:34:08,456 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [51] - com.polarion.platform.persistence
2025-07-29 18:34:08,456 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.persistence to HiveMind
2025-07-29 18:34:08,456 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [52] - com.polarion.platform.repository
2025-07-29 18:34:08,456 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.repository to HiveMind
2025-07-29 18:34:08,456 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [53] - com.polarion.platform.repository.driver.svn
2025-07-29 18:34:08,457 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.repository.driver.svn to HiveMind
2025-07-29 18:34:08,457 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [54] - com.polarion.platform.repository.external
2025-07-29 18:34:08,457 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.repository.external to HiveMind
2025-07-29 18:34:08,457 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [55] - com.polarion.platform.repository.external.git
2025-07-29 18:34:08,457 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.repository.external.git to HiveMind
2025-07-29 18:34:08,457 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [56] - com.polarion.platform.repository.external.svn
2025-07-29 18:34:08,457 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.repository.external.svn to HiveMind
2025-07-29 18:34:08,457 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [57] - com.polarion.platform.sql
2025-07-29 18:34:08,457 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [58] - com.polarion.portal.tomcat
2025-07-29 18:34:08,460 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [59] - com.polarion.psvn.launcher
2025-07-29 18:34:08,460 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.psvn.launcher to HiveMind
2025-07-29 18:34:08,460 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [60] - com.polarion.psvn.translations.en
2025-07-29 18:34:08,460 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [61] - com.polarion.purevariants
2025-07-29 18:34:08,461 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.purevariants to HiveMind
2025-07-29 18:34:08,461 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [62] - com.polarion.qcentre
2025-07-29 18:34:08,467 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [63] - com.polarion.scripting
2025-07-29 18:34:08,473 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.scripting to HiveMind
2025-07-29 18:34:08,473 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [64] - com.polarion.scripting.servlet
2025-07-29 18:34:08,473 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [65] - com.polarion.subterra.base
2025-07-29 18:34:08,473 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [66] - com.polarion.subterra.index
2025-07-29 18:34:08,474 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.subterra.index to HiveMind
2025-07-29 18:34:08,474 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [67] - com.polarion.subterra.persistence
2025-07-29 18:34:08,474 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.subterra.persistence to HiveMind
2025-07-29 18:34:08,474 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [68] - com.polarion.subterra.persistence.document
2025-07-29 18:34:08,475 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.subterra.persistence.document to HiveMind
2025-07-29 18:34:08,475 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [69] - com.polarion.synchronizer
2025-07-29 18:34:08,475 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.synchronizer to HiveMind
2025-07-29 18:34:08,475 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [70] - com.polarion.synchronizer.proxy.feishu
2025-07-29 18:34:08,475 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [71] - com.polarion.synchronizer.proxy.hpalm
2025-07-29 18:34:08,479 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [72] - com.polarion.synchronizer.proxy.jira
2025-07-29 18:34:08,479 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [73] - com.polarion.synchronizer.proxy.polarion
2025-07-29 18:34:08,479 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [74] - com.polarion.synchronizer.proxy.reqif
2025-07-29 18:34:08,488 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [75] - com.polarion.synchronizer.ui
2025-07-29 18:34:08,488 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [76] - com.polarion.usdp.persistence
2025-07-29 18:34:08,489 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.usdp.persistence to HiveMind
2025-07-29 18:34:08,489 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [77] - com.polarion.xray.doc.user
2025-07-29 18:34:08,489 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [78] - com.siemens.des.logger.api
2025-07-29 18:34:08,489 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [79] - com.siemens.plm.bitools.analytics
2025-07-29 18:34:08,489 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [80] - com.siemens.polarion.ct.collectors.git
2025-07-29 18:34:08,489 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.siemens.polarion.ct.collectors.git to HiveMind
2025-07-29 18:34:08,489 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [81] - com.siemens.polarion.eclipse.configurator
2025-07-29 18:34:08,490 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [82] - com.siemens.polarion.integration.ci
2025-07-29 18:34:08,490 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.siemens.polarion.integration.ci to HiveMind
2025-07-29 18:34:08,490 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [83] - com.siemens.polarion.previewer
2025-07-29 18:34:08,491 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.siemens.polarion.previewer to HiveMind
2025-07-29 18:34:08,491 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [84] - com.siemens.polarion.previewer.external
2025-07-29 18:34:08,491 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.siemens.polarion.previewer.external to HiveMind
2025-07-29 18:34:08,491 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [85] - com.siemens.polarion.rest
2025-07-29 18:34:08,491 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [86] - com.siemens.polarion.rt
2025-07-29 18:34:08,492 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [87] - com.siemens.polarion.rt.api
2025-07-29 18:34:08,492 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [88] - com.siemens.polarion.rt.collectors.git
2025-07-29 18:34:08,492 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [89] - com.siemens.polarion.rt.communication.common
2025-07-29 18:34:08,492 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [90] - com.siemens.polarion.rt.communication.polarion
2025-07-29 18:34:08,492 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.siemens.polarion.rt.communication.polarion to HiveMind
2025-07-29 18:34:08,492 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [91] - com.siemens.polarion.rt.communication.rt
2025-07-29 18:34:08,493 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [92] - com.siemens.polarion.rt.parsers.c
2025-07-29 18:34:08,493 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [93] - com.siemens.polarion.rt.ui
2025-07-29 18:34:08,493 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [94] - com.siemens.polarion.synchronizer.proxy.tfs
2025-07-29 18:34:08,493 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [95] - com.sun.activation.javax.activation
2025-07-29 18:34:08,494 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [96] - com.sun.istack.commons-runtime
2025-07-29 18:34:08,494 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [97] - com.sun.jna
2025-07-29 18:34:08,494 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [98] - com.sun.jna.platform
2025-07-29 18:34:08,494 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [99] - com.sun.xml.bind.jaxb-impl
2025-07-29 18:34:08,494 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [100] - com.trilead.ssh2
2025-07-29 18:34:08,494 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [101] - com.zaxxer.hikariCP
2025-07-29 18:34:08,494 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [102] - des-sdk-core
2025-07-29 18:34:08,494 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [103] - des-sdk-dss
2025-07-29 18:34:08,494 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [104] - io.github.resilience4j.circuitbreaker
2025-07-29 18:34:08,495 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [105] - io.github.resilience4j.core
2025-07-29 18:34:08,495 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [106] - io.github.resilience4j.retry
2025-07-29 18:34:08,495 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [107] - io.swagger
2025-07-29 18:34:08,495 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [108] - io.vavr
2025-07-29 18:34:08,495 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [109] - jakaroma
2025-07-29 18:34:08,495 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [110] - jakarta.validation.validation-api
2025-07-29 18:34:08,495 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [111] - javassist
2025-07-29 18:34:08,495 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [112] - javax.annotation-api
2025-07-29 18:34:08,495 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [113] - javax.cache
2025-07-29 18:34:08,495 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [114] - javax.el
2025-07-29 18:34:08,495 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [115] - javax.inject
2025-07-29 18:34:08,495 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [116] - javax.servlet
2025-07-29 18:34:08,495 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [117] - javax.servlet.jsp
2025-07-29 18:34:08,495 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [118] - javax.transaction
2025-07-29 18:34:08,495 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [119] - jaxb-api
2025-07-29 18:34:08,496 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [120] - jcip-annotations
2025-07-29 18:34:08,496 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [121] - jcl.over.slf4j
2025-07-29 18:34:08,496 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [122] - jul.to.slf4j
2025-07-29 18:34:08,496 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [123] - kuromoji-core
2025-07-29 18:34:08,496 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [124] - kuromoji-ipadic
2025-07-29 18:34:08,496 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [125] - lang-tag
2025-07-29 18:34:08,496 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [126] - net.htmlparser.jericho
2025-07-29 18:34:08,496 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [127] - net.java.dev.jna
2025-07-29 18:34:08,496 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [128] - net.minidev.accessors-smart
2025-07-29 18:34:08,496 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [129] - net.minidev.asm
2025-07-29 18:34:08,496 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [130] - net.minidev.json-smart
2025-07-29 18:34:08,497 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [131] - net.n3.nanoxml
2025-07-29 18:34:08,497 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [132] - net.sourceforge.cssparser
2025-07-29 18:34:08,497 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [133] - nu.xom
2025-07-29 18:34:08,497 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [134] - oauth2-oidc-sdk
2025-07-29 18:34:08,497 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [135] - org.apache.ant
2025-07-29 18:34:08,504 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [136] - org.apache.avro
2025-07-29 18:34:08,504 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [137] - org.apache.axis
2025-07-29 18:34:08,507 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [138] - org.apache.batik
2025-07-29 18:34:08,507 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [139] - org.apache.commons.codec
2025-07-29 18:34:08,507 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [140] - org.apache.commons.collections
2025-07-29 18:34:08,508 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [141] - org.apache.commons.commons-beanutils
2025-07-29 18:34:08,508 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [142] - org.apache.commons.commons-collections4
2025-07-29 18:34:08,508 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [143] - org.apache.commons.commons-compress
2025-07-29 18:34:08,508 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [144] - org.apache.commons.commons-fileupload
2025-07-29 18:34:08,508 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [145] - org.apache.commons.digester
2025-07-29 18:34:08,508 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [146] - org.apache.commons.exec
2025-07-29 18:34:08,508 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [147] - org.apache.commons.io
2025-07-29 18:34:08,508 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [148] - org.apache.commons.lang
2025-07-29 18:34:08,508 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [149] - org.apache.commons.lang3
2025-07-29 18:34:08,508 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [150] - org.apache.commons.logging
2025-07-29 18:34:08,509 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [151] - org.apache.curator
2025-07-29 18:34:08,514 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [152] - org.apache.fop
2025-07-29 18:34:08,514 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [153] - org.apache.hivemind
2025-07-29 18:34:08,514 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle org.apache.hivemind to HiveMind
2025-07-29 18:34:08,514 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [154] - org.apache.httpcomponents.httpclient
2025-07-29 18:34:08,515 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [155] - org.apache.httpcomponents.httpcore
2025-07-29 18:34:08,515 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [156] - org.apache.jasper.glassfish
2025-07-29 18:34:08,517 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [157] - org.apache.kafka.clients
2025-07-29 18:34:08,517 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [158] - org.apache.kafka.streams
2025-07-29 18:34:08,517 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [159] - org.apache.logging.log4j.1.2-api
2025-07-29 18:34:08,517 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [160] - org.apache.logging.log4j.api
2025-07-29 18:34:08,518 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [161] - org.apache.logging.log4j.apiconf
2025-07-29 18:34:08,518 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [162] - org.apache.logging.log4j.core
2025-07-29 18:34:08,518 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [163] - org.apache.logging.log4j.slf4j-impl
2025-07-29 18:34:08,518 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [164] - org.apache.lucene.analyzers-common
2025-07-29 18:34:08,519 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [165] - org.apache.lucene.analyzers-common
2025-07-29 18:34:08,520 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [166] - org.apache.lucene.analyzers-smartcn
2025-07-29 18:34:08,520 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [167] - org.apache.lucene.core
2025-07-29 18:34:08,520 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [168] - org.apache.lucene.core
2025-07-29 18:34:08,522 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [169] - org.apache.lucene.grouping
2025-07-29 18:34:08,522 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [170] - org.apache.lucene.queryparser
2025-07-29 18:34:08,522 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [171] - org.apache.oro
2025-07-29 18:34:08,522 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [172] - org.apache.pdfbox.fontbox
2025-07-29 18:34:08,522 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [173] - org.apache.poi
2025-07-29 18:34:08,531 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [174] - org.apache.tika
2025-07-29 18:34:09,158 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [175] - org.apache.xalan
2025-07-29 18:34:09,159 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [176] - org.apache.xercesImpl
2025-07-29 18:34:09,159 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [177] - org.apache.xml.serializer
2025-07-29 18:34:09,159 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [178] - org.apache.xmlgraphics.commons
2025-07-29 18:34:09,159 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [179] - org.apache.zookeeper
2025-07-29 18:34:09,160 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [180] - org.codehaus.groovy
2025-07-29 18:34:09,160 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [181] - org.codehaus.jettison
2025-07-29 18:34:09,160 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [182] - org.dom4j
2025-07-29 18:34:09,160 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [183] - org.eclipse.core.contenttype
2025-07-29 18:34:09,160 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [184] - org.eclipse.core.expressions
2025-07-29 18:34:09,160 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [185] - org.eclipse.core.filesystem
2025-07-29 18:34:09,160 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [186] - org.eclipse.core.jobs
2025-07-29 18:34:09,160 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [187] - org.eclipse.core.net
2025-07-29 18:34:09,160 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [188] - org.eclipse.core.resources
2025-07-29 18:34:09,161 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [189] - org.eclipse.core.runtime
2025-07-29 18:34:09,161 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [190] - org.eclipse.equinox.app
2025-07-29 18:34:09,161 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [191] - org.eclipse.equinox.common
2025-07-29 18:34:09,161 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [192] - org.eclipse.equinox.event
2025-07-29 18:34:09,161 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [193] - org.eclipse.equinox.http.registry
2025-07-29 18:34:09,161 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [194] - org.eclipse.equinox.http.servlet
2025-07-29 18:34:09,161 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [195] - org.eclipse.equinox.jsp.jasper
2025-07-29 18:34:09,161 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [196] - org.eclipse.equinox.jsp.jasper.registry
2025-07-29 18:34:09,161 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [197] - org.eclipse.equinox.launcher
2025-07-29 18:34:09,161 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [198] - org.eclipse.equinox.preferences
2025-07-29 18:34:09,161 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [199] - org.eclipse.equinox.registry
2025-07-29 18:34:09,161 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [200] - org.eclipse.equinox.security
2025-07-29 18:34:09,161 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [201] - org.eclipse.help
2025-07-29 18:34:09,161 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [202] - org.eclipse.help.base
2025-07-29 18:34:09,162 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [203] - org.eclipse.help.webapp
2025-07-29 18:34:09,162 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [204] - org.eclipse.jgit
2025-07-29 18:34:09,162 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [205] - org.eclipse.osgi.services
2025-07-29 18:34:09,162 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [206] - org.eclipse.osgi.util
2025-07-29 18:34:09,162 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [207] - org.ehcache
2025-07-29 18:34:09,164 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [208] - org.gitlab.java-gitlab-api
2025-07-29 18:34:09,164 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [209] - org.glassfish.jersey
2025-07-29 18:34:09,164 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [210] - org.hibernate.annotations
2025-07-29 18:34:09,164 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [211] - org.hibernate.core
2025-07-29 18:34:09,165 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [212] - org.hibernate.entitymanager
2025-07-29 18:34:09,165 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [213] - org.hibernate.hikaricp
2025-07-29 18:34:09,165 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [214] - org.hibernate.jpa.2.1.api
2025-07-29 18:34:09,165 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [215] - org.jboss.logging
2025-07-29 18:34:09,165 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [216] - org.jvnet.mimepull
2025-07-29 18:34:09,165 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [217] - org.objectweb.asm
2025-07-29 18:34:09,165 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [218] - org.objectweb.jotm
2025-07-29 18:34:09,165 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [219] - org.opensaml
2025-07-29 18:34:09,182 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [220] - org.polarion.svncommons
2025-07-29 18:34:09,182 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [221] - org.polarion.svnwebclient
2025-07-29 18:34:09,182 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [222] - org.postgesql
2025-07-29 18:34:09,183 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [223] - org.projectlombok.lombok
2025-07-29 18:34:09,193 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [224] - org.rocksdb.rocksdbjni
2025-07-29 18:34:09,193 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [225] - org.springframework.data.core
2025-07-29 18:34:09,194 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [226] - org.springframework.data.jpa
2025-07-29 18:34:09,194 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [227] - org.springframework.spring-aop
2025-07-29 18:34:09,194 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [228] - org.springframework.spring-beans
2025-07-29 18:34:09,194 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [229] - org.springframework.spring-context
2025-07-29 18:34:09,194 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [230] - org.springframework.spring-core
2025-07-29 18:34:09,195 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [231] - org.springframework.spring-expression
2025-07-29 18:34:09,195 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [232] - org.springframework.spring-jdbc
2025-07-29 18:34:09,195 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [233] - org.springframework.spring-orm
2025-07-29 18:34:09,195 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [234] - org.springframework.spring-test
2025-07-29 18:34:09,195 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [235] - org.springframework.spring-tx
2025-07-29 18:34:09,195 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [236] - org.springframework.spring-web
2025-07-29 18:34:09,195 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [237] - org.springframework.spring-webmvc
2025-07-29 18:34:09,195 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [238] - org.tmatesoft.sqljet
2025-07-29 18:34:09,195 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [239] - org.tmatesoft.svnkit
2025-07-29 18:34:09,196 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [240] - saaj-api
2025-07-29 18:34:09,196 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [241] - sdk-lifecycle-collab
2025-07-29 18:34:09,196 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [242] - sdk-lifecycle-docmgmt
2025-07-29 18:34:09,196 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [243] - siemens.des.clientsecurity
2025-07-29 18:34:09,196 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [244] - slf4j.api
2025-07-29 18:34:09,196 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [245] - xml-apis
2025-07-29 18:34:09,196 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [246] - xml.apis.ext
2025-07-29 18:34:09,197 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [247] - xstream
2025-07-29 18:34:09,396 [main] INFO  com.polarion.core.util.remote.server.SocketRemoteControlServer - Remote control server socket is ready to listen on localhost/127.0.0.1:8887
2025-07-29 18:34:09,396 [xServer:8887] INFO  org.xsocket.connection.Server - server listening on localhost:8887 (xSocket 2.5.3)
2025-07-29 18:34:09,603 [main] INFO  com.polarion.platform.sql.internal.SqlModule - Initializing database...
2025-07-29 18:34:09,657 [main] INFO  com.polarion.platform.sql.internal.PgServerInfo - PG server listening on localhost:5435
2025-07-29 18:34:11,823 [main] INFO  com.polarion.platform.internal.cache.CacheConfigurator - EHCache uses internal configuration
2025-07-29 18:34:12,089 [main] WARN  org.ehcache.impl.internal.executor.PooledExecutionService - No default pool configured, services requiring thread pools must be configured explicitly using named thread pools
2025-07-29 18:34:12,163 [main] INFO  org.ehcache.sizeof.filters.AnnotationSizeOfFilter - Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-07-29 18:34:12,168 [main] INFO  org.ehcache.sizeof.impl.JvmInformation - Detected JVM data model settings of: 64-Bit OpenJDK JVM with Compressed OOPs
2025-07-29 18:34:12,180 [main] INFO  org.ehcache.sizeof.impl.AgentLoader - Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-07-29 18:34:12,244 [main] INFO  com.polarion.platform.internal.cache.CachingProviderHandler - All the caches have been destroyed because of not clean shutdown. You can ignore this message if Polarion started in reindex mode.
2025-07-29 18:34:12,281 [main] INFO  com.polarion.platform.sql.internal.PgConnection - JDBC url of database 'polarion' is: *****************************************
2025-07-29 18:34:12,283 [main] INFO  com.polarion.platform.sql.internal.PgConnection - JDBC url of database 'polarion' is: *****************************************
2025-07-29 18:34:12,288 [main] INFO  com.polarion.platform.sql.internal.PgConnection - JDBC url of database 'polarion_history' is: *************************************************
2025-07-29 18:34:12,347 [main] INFO  com.polarion.platform.sql.internal.SqlModule - Initializing database finished [ TIME 2.74 s. ]
2025-07-29 18:34:12,510 [main] INFO  com.polarion.platform.cluster.ClusterService - Initializing cluster service
2025-07-29 18:34:12,511 [main] INFO  com.polarion.platform.cluster.ClusterService - Cluster service is disabled.
2025-07-29 18:34:12,708 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-29 18:34:12,735 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool - pg polarion_history - Starting...
2025-07-29 18:34:12,803 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool - pg polarion_history - Start completed.
2025-07-29 18:34:12,849 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.14 s. ]
2025-07-29 18:34:12,849 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-29 18:34:12,859 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool - pg polarion - Starting...
2025-07-29 18:34:12,865 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool - pg polarion - Start completed.
2025-07-29 18:34:12,888 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0394 s. ]
2025-07-29 18:34:12,888 [main] INFO  com.polarion.psvn.launcher.internal.platform.PlatformService - Platform boot started
2025-07-29 18:34:12,932 [main] INFO  com.polarion.platform.repository.driver.svn.internal.security.SVNWatcher - SVN auth file watcher started with a period of 3000 milliseconds
2025-07-29 18:34:12,939 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-29 18:34:12,966 [main] INFO  com.polarion.platform.internal.security.auth.LoginFlowUserPassword - User polarion authenticated from system
2025-07-29 18:34:13,005 [main] INFO  com.polarion.platform.internal.security.auth.LoginFlowUserPassword - User polarion logged in from system
2025-07-29 18:34:13,010 [main | u:p] INFO  com.polarion.platform.internal.service.repository.PlatformRepositoryService - Repository Service Created
2025-07-29 18:34:13,011 [main | u:p] INFO  com.polarion.platform.internal.service.repository.PlatformRepositoryService - Repository Service Initialized
2025-07-29 18:34:13,016 [main | u:p] INFO  com.polarion.core.util.profiling.SimpleProfiler - Initialization
2025-07-29 18:34:13,027 [main | u:p] INFO  org.objectweb.jotm - JOTM started with a local transaction factory which is not bound.
2025-07-29 18:34:13,027 [main | u:p] INFO  org.objectweb.jotm - CAROL initialization
2025-07-29 18:34:13,032 [main | u:p] INFO  com.polarion.platform.internal.service.repository.listeners.job.PullingJob - lastFullyProcessedRevision [204]
2025-07-29 18:34:13,037 [main | u:p] INFO  com.polarion.platform.internal.service.repository.PlatformRepositoryService - END initializeService
2025-07-29 18:34:13,041 [main | u:p] INFO  com.polarion.psvn.launcher.internal.platform.PlatformService - Polarion startup estimation:  [ TIME 121 s. ]
2025-07-29 18:34:13,041 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 121 s. ]
2025-07-29 18:34:13,047 [main | u:p] INFO  com.polarion.platform.monitoring - Full monitoring results are stored in file /opt/polarion/data/workspace/monitoring/results.txt
2025-07-29 18:34:13,103 [main | u:p] INFO  com.polarion.platform.monitoring - Executing actions from stage PREBOOT
2025-07-29 18:34:13,106 [main | u:p] INFO  com.polarion.platform.monitoring - Executing action 'system.info.disk.data.free'
2025-07-29 18:34:13,107 [main | u:p] INFO  com.polarion.platform.monitoring - system.info.disk.data.free (Free data disk '/opt/polarion/data' space) = Disk Space 
1,426.93 GB
 [Tue Jul 29 18:34:13 CST 2025]
2025-07-29 18:34:13,337 [main | u:p] INFO  com.polarion.platform.monitoring - Executing action 'system.info.jvm.version'
2025-07-29 18:34:13,337 [main | u:p] INFO  com.polarion.platform.monitoring - system.info.jvm.version (JVM Version) = JVM Version 
Microsoft OpenJDK 64-Bit Server VM 11.0.27+6-LTS
 [Tue Jul 29 18:34:13 CST 2025]
2025-07-29 18:34:13,344 [main | u:p] INFO  com.polarion.platform.monitoring - Executing action 'system.info.os.version'
2025-07-29 18:34:13,345 [main | u:p] INFO  com.polarion.platform.monitoring - system.info.os.version (OS Version) = OS Version 
Mac OS X 15.5 aarch64
 [Tue Jul 29 18:34:13 CST 2025]
2025-07-29 18:34:13,349 [main | u:p] INFO  com.polarion.platform.monitoring - Finished with actions from stage PREBOOT: {OK=3}
2025-07-29 18:34:13,350 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.05 s. ]
2025-07-29 18:34:13,350 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0344 s [59% update (144x), 40% query (12x)] (221x), svn: 0.0133 s [57% getLatestRevision (2x), 26% testConnection (1x)] (4x)
2025-07-29 18:34:13,350 [main | u:p] INFO  com.polarion.psvn.launcher.internal.platform.PlatformService - calling ILowLevelPersistence.boot to start persistence
2025-07-29 18:34:13,365 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Persistence initialization started
2025-07-29 18:34:13,426 [main | u:p] INFO  com.polarion.subterra.base.internal.location.LocationCacheContext - Registered invalidationListener: com.polarion.platform.repository.internal.config.RepositoryConfigService$1@739979eb
2025-07-29 18:34:13,458 [main | u:p] INFO  com.polarion.platform.persistence.internal.CustomFieldsService - Custom fields control field is not set for prototype: BaselineCollection
2025-07-29 18:34:13,458 [main | u:p] INFO  com.polarion.platform.persistence.internal.CustomFieldsService - Custom fields control field is not set for prototype: TestRun
2025-07-29 18:34:13,458 [main | u:p] INFO  com.polarion.platform.persistence.internal.CustomFieldsService - Custom fields control field is not set for prototype: Plan
2025-07-29 18:34:13,481 [main | u:p] INFO  com.polarion.platform.repository.internal.context.RepositoryContextService - Context recognition started
2025-07-29 18:34:13,481 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:34:13,481 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-29 18:34:13,543 [main | u:p] INFO  com.polarion.platform.repository.internal.context.RepositoryContextService - Context recognition finished [ TIME 0.0616 s. ]
2025-07-29 18:34:13,543 [main | u:p] INFO  com.polarion.platform.repository.internal.context.RepositoryContextService - Context tree: 
ROOT_CTX_NAME (ContextNature[Root], ContextId[context [global]])
+-default (ContextNature[Repository], ContextId[cluster default, context [global]])
  +-WBS (ContextNature[Project], ContextId[cluster default, context WBS])
  +-Demo Projects (ContextNature[ProjectGroup], ContextId[cluster default, context --Demo Projects])
  | +-drivepilot (ContextNature[Project], ContextId[cluster default, context drivepilot])
  | +-elibrary (ContextNature[Project], ContextId[cluster default, context elibrary])
  +-library (ContextNature[Project], ContextId[cluster default, context library])
  +-WBSdev (ContextNature[Project], ContextId[cluster default, context WBSdev])
  +-hesai (ContextNature[Project], ContextId[cluster default, context hesai])
2025-07-29 18:34:13,543 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.19 s. ]
2025-07-29 18:34:13,543 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0694 s [54% info (3x), 42% getDir2 content (2x)] (6x)
2025-07-29 18:34:13,543 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:34:13,543 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-29 18:34:13,543 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Startup workers for phase 3: 6
2025-07-29 18:34:13,549 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (1/9)
2025-07-29 18:34:13,549 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context library] (1/9) ...
2025-07-29 18:34:13,549 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (4/9)
2025-07-29 18:34:13,549 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (3/9)
2025-07-29 18:34:13,549 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context [global]] (4/9) ...
2025-07-29 18:34:13,550 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context WBSdev] (3/9) ...
2025-07-29 18:34:13,549 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (6/9)
2025-07-29 18:34:13,549 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (5/9)
2025-07-29 18:34:13,550 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[context [global]] (6/9) ...
2025-07-29 18:34:13,549 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (2/9)
2025-07-29 18:34:13,550 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context WBS] (5/9) ...
2025-07-29 18:34:13,550 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context --Demo Projects] (2/9) ...
2025-07-29 18:34:13,556 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[context [global]] (6/9) TOOK  [ TIME 0.00588 s. ]
2025-07-29 18:34:13,556 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-07-29 18:34:13,556 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context hesai] (7/9) ...
2025-07-29 18:34:13,667 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/Demo Projects/.polarion'
2025-07-29 18:34:13,674 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/Demo Projects/modules'
2025-07-29 18:34:13,680 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/Demo Projects/documents'
2025-07-29 18:34:13,688 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/Demo Projects/_wiki'
2025-07-29 18:34:13,689 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context --Demo Projects contains 0 primary objects (work items+comments).
2025-07-29 18:34:13,690 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context --Demo Projects] (2/9) TOOK  [ TIME 0.14 s. ]
2025-07-29 18:34:13,690 [LowLevelDataService-contextInitializer-4 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-29 18:34:13,690 [LowLevelDataService-contextInitializer-4 | u:p | context: drivepilot] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context drivepilot] (8/9) ...
2025-07-29 18:34:13,744 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/library/documents'
2025-07-29 18:34:13,754 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/WBS/documents'
2025-07-29 18:34:13,803 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context library contains 287 primary objects (work items+comments).
2025-07-29 18:34:13,803 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context library] (1/9) TOOK  [ TIME 0.254 s. ]
2025-07-29 18:34:13,803 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-29 18:34:13,803 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context elibrary] (9/9) ...
2025-07-29 18:34:13,806 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context WBS contains 344 primary objects (work items+comments).
2025-07-29 18:34:13,806 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context WBS] (5/9) TOOK  [ TIME 0.256 s. ]
2025-07-29 18:34:13,830 [LowLevelDataService-contextInitializer-4 | u:p | context: drivepilot] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/Demo Projects/drivepilot/documents'
2025-07-29 18:34:13,883 [LowLevelDataService-contextInitializer-4 | u:p | context: drivepilot] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context drivepilot contains 461 primary objects (work items+comments).
2025-07-29 18:34:13,884 [LowLevelDataService-contextInitializer-4 | u:p | context: drivepilot] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context drivepilot] (8/9) TOOK  [ TIME 0.194 s. ]
2025-07-29 18:34:13,888 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/Demo Projects/elibrary/documents'
2025-07-29 18:34:13,906 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context elibrary contains 334 primary objects (work items+comments).
2025-07-29 18:34:13,906 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context elibrary] (9/9) TOOK  [ TIME 0.103 s. ]
2025-07-29 18:34:13,922 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/hesai/documents'
2025-07-29 18:34:13,993 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context hesai contains 1148 primary objects (work items+comments).
2025-07-29 18:34:13,993 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context hesai] (7/9) TOOK  [ TIME 0.437 s. ]
2025-07-29 18:34:14,092 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context null contains 2214 primary objects (work items+comments).
2025-07-29 18:34:14,092 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context [global]] (4/9) TOOK  [ TIME 0.543 s. ]
2025-07-29 18:34:14,277 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/WBSdev/documents'
2025-07-29 18:34:14,542 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context WBSdev contains 3321 primary objects (work items+comments).
2025-07-29 18:34:14,542 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context WBSdev] (3/9) TOOK  [ TIME 0.993 s. ]
2025-07-29 18:34:14,544 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.998 s, CPU [user: 0.109 s, system: 0.195 s], Allocated memory: 24.1 MB, transactions: 0, ObjectMaps: 0.0641 s [100% getAllPrimaryObjects (1x)] (10x)
2025-07-29 18:34:14,544 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.998 s, CPU [user: 0.0724 s, system: 0.105 s], Allocated memory: 10.7 MB, transactions: 0, svn: 0.0824 s [81% log2 (10x)] (13x)
2025-07-29 18:34:14,544 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.998 s, CPU [user: 0.199 s, system: 0.268 s], Allocated memory: 53.0 MB, transactions: 0, ObjectMaps: 0.146 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-29 18:34:14,544 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.997 s, CPU [user: 0.271 s, system: 0.35 s], Allocated memory: 68.7 MB, transactions: 0, ObjectMaps: 0.262 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.142 s [74% log2 (5x), 14% testConnection (1x)] (7x)
2025-07-29 18:34:14,545 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.998 s, CPU [user: 0.0796 s, system: 0.119 s], Allocated memory: 12.1 MB, transactions: 0, ObjectMaps: 0.0701 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.0673 s [74% log2 (10x), 17% getLatestRevision (2x)] (13x)
2025-07-29 18:34:14,545 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.998 s, CPU [user: 0.0696 s, system: 0.0875 s], Allocated memory: 9.2 MB, transactions: 0, svn: 0.0859 s [31% info (5x), 30% log2 (5x), 18% log (1x), 10% getLatestRevision (2x)] (18x)
2025-07-29 18:34:14,545 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 1.0 s. ]
2025-07-29 18:34:14,545 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.632 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.419 s [65% log2 (36x), 13% getLatestRevision (9x), 11% testConnection (6x)] (61x)
2025-07-29 18:34:14,560 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.RevisionsPersistenceModule - Processing new revisions [START].
2025-07-29 18:34:14,560 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:34:14,560 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-29 18:34:14,678 [main | u:p] INFO  TXLOGGER - Tx 66156fd0ab801_0_66156fd0ab801_0_: finished. Total: 0.103 s, CPU [user: 0.08 s, system: 0.0049 s], Allocated memory: 21.8 MB
2025-07-29 18:34:14,689 [main | u:p | u:p] ERROR com.polarion.platform.repository.internal.config.RepositoryConfigService$ConfigProblemCatcher - Failed to work with configuration from location /WBS/.polarion/repositories/repositories.xml:
[/WBS/.polarion/repositories/repositories.xml]: 
---- Debugging information ----
cause-exception     : com.thoughtworks.xstream.mapper.CannotResolveClassException
cause-message       : AutoBranchGitLab
class               : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
required-type       : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
converter-type      : com.thoughtworks.xstream.converters.collections.ArrayConverter
version             : 1.4.17
-------------------------------
com.polarion.platform.repository.config.RepositoryConfigurationException: [/WBS/.polarion/repositories/repositories.xml]: 
---- Debugging information ----
cause-exception     : com.thoughtworks.xstream.mapper.CannotResolveClassException
cause-message       : AutoBranchGitLab
class               : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
required-type       : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
converter-type      : com.thoughtworks.xstream.converters.collections.ArrayConverter
version             : 1.4.17
-------------------------------
	at com.polarion.platform.repository.spi.config.AbstractDataHandler.readLocation(AbstractDataHandler.java:87) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.spi.config.AbstractDataHandler.readLocations(AbstractDataHandler.java:61) ~[platform-repository.jar:?]
	at $IDataHandler_19855bf2ff7.readLocations($IDataHandler_19855bf2ff7.java) ~[?:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.readLocations(RepositoryConfigService.java:291) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService$3.runImpl(RepositoryConfigService.java:328) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService$SVNAccessAction$1.runWEx(RepositoryConfigService.java:113) ~[platform-repository.jar:?]
	at com.polarion.core.util.RunnableWEx.runWRet(RunnableWEx.java:61) ~[util.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService$SVNAccessAction.run(RepositoryConfigService.java:123) ~[platform-repository.jar:?]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:?]
	at javax.security.auth.Subject.doAs(Subject.java:361) ~[?:?]
	at com.polarion.platform.internal.security.SubjectNDC.doAs(SubjectNDC.java:58) ~[platform.jar:?]
	at com.polarion.platform.internal.security.SecurityService.doAsUser(SecurityService.java:422) ~[platform.jar:?]
	at com.polarion.platform.internal.security.SecurityService.doAsSystemUser(SecurityService.java:412) ~[platform.jar:?]
	at $ISecurityService_19855bf2e12.doAsSystemUser($ISecurityService_19855bf2e12.java) ~[?:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.readConfiguration(RepositoryConfigService.java:324) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.getConfigurationImpl(RepositoryConfigService.java:239) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.getReadConfigurationImpl(RepositoryConfigService.java:199) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.getReadConfiguration(RepositoryConfigService.java:177) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.addConfigurationListener(RepositoryConfigService.java:263) ~[platform-repository.jar:?]
	at $IRepositoryConfigService_19855bf2e20.addConfigurationListener($IRepositoryConfigService_19855bf2e20.java) ~[?:?]
	at com.polarion.platform.repository.external.internal.ExternalRepositoryProviderRegistry.initialize(ExternalRepositoryProviderRegistry.java:146) ~[platform-repository.jar:?]
	at $IExternalRepositoryProviderRegistry_19855bf2edc.initialize($IExternalRepositoryProviderRegistry_19855bf2edc.java) ~[?:?]
	at $IExternalRepositoryProviderRegistry_19855bf2edb.initialize($IExternalRepositoryProviderRegistry_19855bf2edb.java) ~[?:?]
	at com.polarion.platform.persistence.internal.pe.RevisionsPersistenceModule.initModule(RevisionsPersistenceModule.java:353) ~[platform-persistence.jar:?]
	at $IObjectPersistenceModule_19855bf2fcb.initModule($IObjectPersistenceModule_19855bf2fcb.java) ~[?:?]
	at com.polarion.subterra.persistence.internal.PersistenceEngine.initModule(PersistenceEngine.java:251) ~[subterra-uniform-persistence.jar:?]
	at $IPersistenceEngine_19855bf2fb3.initModule($IPersistenceEngine_19855bf2fb3.java) ~[?:?]
	at com.polarion.platform.persistence.internal.pe.LowLevelDataService.boot(LowLevelDataService.java:352) ~[platform-persistence.jar:?]
	at $ILowLevelPersistence_19855bf2ed8.boot($ILowLevelPersistence_19855bf2ed8.java) ~[?:?]
	at $ILowLevelPersistence_19855bf2ed7.boot($ILowLevelPersistence_19855bf2ed7.java) ~[?:?]
	at com.polarion.psvn.launcher.internal.platform.PlatformService.lambda$0(PlatformService.java:294) ~[launcher.jar:?]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:?]
	at javax.security.auth.Subject.doAs(Subject.java:423) [?:?]
	at com.polarion.platform.internal.security.SubjectNDC.doAs(SubjectNDC.java:69) [platform.jar:?]
	at com.polarion.platform.internal.security.SecurityService.doAsUser(SecurityService.java:417) [platform.jar:?]
	at com.polarion.platform.internal.security.SecurityService.doAsSystemUser(SecurityService.java:407) [platform.jar:?]
	at $ISecurityService_19855bf2e13.doAsSystemUser($ISecurityService_19855bf2e13.java) [?:?]
	at $ISecurityService_19855bf2e12.doAsSystemUser($ISecurityService_19855bf2e12.java) [?:?]
	at com.polarion.psvn.launcher.internal.platform.PlatformService.bootPlatform(PlatformService.java:286) [launcher.jar:?]
	at com.polarion.psvn.launcher.internal.platform.PlatformService.start(PlatformService.java:92) [launcher.jar:?]
	at com.polarion.psvn.launcher.PolarionSVNApplication.runImpl(PolarionSVNApplication.java:139) [launcher.jar:?]
	at com.polarion.psvn.launcher.PolarionSVNApplication.run(PolarionSVNApplication.java:94) [launcher.jar:?]
	at com.polarion.core.boot.launchers.BasicAppLauncher.launch(BasicAppLauncher.java:53) [boot.jar:?]
	at com.polarion.core.boot.impl.AppLaunchersManager.start(AppLaunchersManager.java:184) [boot.jar:?]
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:196) [org.eclipse.equinox.app_1.3.500.v20171221-2204.jar:?]
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:134) [org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:104) [org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:388) [org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:243) [org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:566) ~[?:?]
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:656) [org.eclipse.equinox.launcher_1.5.0.v20180512-1130.jar:?]
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:592) [org.eclipse.equinox.launcher_1.5.0.v20180512-1130.jar:?]
	at org.eclipse.equinox.launcher.Main.run(Main.java:1498) [org.eclipse.equinox.launcher_1.5.0.v20180512-1130.jar:?]
	at org.eclipse.equinox.launcher.Main.main(Main.java:1471) [org.eclipse.equinox.launcher_1.5.0.v20180512-1130.jar:?]
Caused by: com.thoughtworks.xstream.converters.ConversionException: 
---- Debugging information ----
cause-exception     : com.thoughtworks.xstream.mapper.CannotResolveClassException
cause-message       : AutoBranchGitLab
class               : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
required-type       : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
converter-type      : com.thoughtworks.xstream.converters.collections.ArrayConverter
version             : 1.4.17
-------------------------------
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convert(TreeUnmarshaller.java:77) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convertAnother(TreeUnmarshaller.java:66) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convertAnother(TreeUnmarshaller.java:50) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.start(TreeUnmarshaller.java:134) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.AbstractTreeMarshallingStrategy.unmarshal(AbstractTreeMarshallingStrategy.java:32) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.unmarshal(XStream.java:1431) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.unmarshal(XStream.java:1411) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.fromXML(XStream.java:1305) ~[xstream-1.4.17.jar:1.4.17]
	at com.polarion.platform.repository.external.internal.RepositoriesDataHandler.processData(RepositoriesDataHandler.java:119) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.spi.config.AbstractDataHandler.readLocation(AbstractDataHandler.java:83) ~[platform-repository.jar:?]
	... 56 more
Caused by: com.thoughtworks.xstream.mapper.CannotResolveClassException: AutoBranchGitLab
	at com.thoughtworks.xstream.mapper.DefaultMapper.realClass(DefaultMapper.java:81) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.DynamicProxyMapper.realClass(DynamicProxyMapper.java:55) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.PackageAliasingMapper.realClass(PackageAliasingMapper.java:88) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.ClassAliasingMapper.realClass(ClassAliasingMapper.java:79) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.ArrayMapper.realClass(ArrayMapper.java:74) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.SecurityMapper.realClass(SecurityMapper.java:71) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.CachingMapper.realClass(CachingMapper.java:47) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.util.HierarchicalStreams.readClassType(HierarchicalStreams.java:29) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.converters.collections.AbstractCollectionConverter.readBareItem(AbstractCollectionConverter.java:131) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.converters.collections.AbstractCollectionConverter.readItem(AbstractCollectionConverter.java:117) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.converters.collections.AbstractCollectionConverter.readCompleteItem(AbstractCollectionConverter.java:147) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.converters.collections.ArrayConverter.unmarshal(ArrayConverter.java:54) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convert(TreeUnmarshaller.java:72) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convertAnother(TreeUnmarshaller.java:66) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convertAnother(TreeUnmarshaller.java:50) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.start(TreeUnmarshaller.java:134) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.AbstractTreeMarshallingStrategy.unmarshal(AbstractTreeMarshallingStrategy.java:32) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.unmarshal(XStream.java:1431) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.unmarshal(XStream.java:1411) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.fromXML(XStream.java:1305) ~[xstream-1.4.17.jar:1.4.17]
	at com.polarion.platform.repository.external.internal.RepositoriesDataHandler.processData(RepositoriesDataHandler.java:119) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.spi.config.AbstractDataHandler.readLocation(AbstractDataHandler.java:83) ~[platform-repository.jar:?]
	... 56 more
2025-07-29 18:34:14,794 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-29 18:34:14,807 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.RevisionsPersistenceModule - Processing new revisions from repository default in context ContextId[context [global]] finished
2025-07-29 18:34:14,808 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.RevisionsPersistenceModule - Processing new revisions [FINISHED].
2025-07-29 18:34:14,808 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.26 s. ]
2025-07-29 18:34:14,808 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.218 s [100% getReadConfiguration (48x)] (48x), svn: 0.0721 s [83% info (18x)] (38x)
2025-07-29 18:34:14,831 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:34:14,831 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-29 18:34:14,831 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Inspecting repository for build artifacts-related changes
2025-07-29 18:34:14,832 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[context [global]]
2025-07-29 18:34:14,832 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[context [global]]
2025-07-29 18:34:14,833 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[context [global]] has been successfully processed
2025-07-29 18:34:14,834 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[context [global]] finished [ TIME 0.00281 s. ]
2025-07-29 18:34:14,834 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-29 18:34:14,834 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context [global]]
2025-07-29 18:34:14,834 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context [global]]
2025-07-29 18:34:14,856 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context [global]] has been successfully processed
2025-07-29 18:34:14,883 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context [global]] finished [ TIME 0.0483 s. ]
2025-07-29 18:34:14,883 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-29 18:34:14,883 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context WBS]
2025-07-29 18:34:14,883 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context WBS]
2025-07-29 18:34:14,907 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context WBS] has been successfully processed
2025-07-29 18:34:14,933 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context WBS] finished [ TIME 0.0501 s. ]
2025-07-29 18:34:14,933 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-07-29 18:34:14,933 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context --Demo Projects]
2025-07-29 18:34:14,933 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context --Demo Projects]
2025-07-29 18:34:14,947 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context --Demo Projects] has been successfully processed
2025-07-29 18:34:14,961 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context --Demo Projects] finished [ TIME 0.0282 s. ]
2025-07-29 18:34:14,962 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (4/9)
2025-07-29 18:34:14,962 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context library]
2025-07-29 18:34:14,962 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context library]
2025-07-29 18:34:14,972 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context library] has been successfully processed
2025-07-29 18:34:14,985 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context library] finished [ TIME 0.0238 s. ]
2025-07-29 18:34:14,985 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (5/9)
2025-07-29 18:34:14,985 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context WBSdev]
2025-07-29 18:34:14,985 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context WBSdev]
2025-07-29 18:34:15,001 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context WBSdev] has been successfully processed
2025-07-29 18:34:15,021 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context WBSdev] finished [ TIME 0.0357 s. ]
2025-07-29 18:34:15,021 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (6/9)
2025-07-29 18:34:15,021 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context hesai]
2025-07-29 18:34:15,021 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context hesai]
2025-07-29 18:34:15,037 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context hesai] has been successfully processed
2025-07-29 18:34:15,057 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context hesai] finished [ TIME 0.0358 s. ]
2025-07-29 18:34:15,057 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-07-29 18:34:15,057 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context drivepilot]
2025-07-29 18:34:15,057 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context drivepilot]
2025-07-29 18:34:15,074 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context drivepilot] has been successfully processed
2025-07-29 18:34:15,090 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context drivepilot] finished [ TIME 0.0328 s. ]
2025-07-29 18:34:15,090 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-29 18:34:15,090 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context elibrary]
2025-07-29 18:34:15,090 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context elibrary]
2025-07-29 18:34:15,101 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context elibrary] has been successfully processed
2025-07-29 18:34:15,125 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context elibrary] finished [ TIME 0.0343 s. ]
2025-07-29 18:34:15,125 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-29 18:34:15,125 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... repository inspection finished [ TIME 0.293 s. ]
2025-07-29 18:34:15,125 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.32 s. ]
2025-07-29 18:34:15,125 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.251 s [76% info (94x), 16% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.19 s [100% getReadConfiguration (54x)] (54x)
2025-07-29 18:34:15,125 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:34:15,125 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-29 18:34:15,125 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Inspecting BIR for new or removed builds
2025-07-29 18:34:15,150 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... 0 build(s) were removed (including calculations from previous run)
2025-07-29 18:34:15,150 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... 0 build(s) were added or modified
2025-07-29 18:34:15,151 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... BIR inspection finished [ TIME 0.0256 s. ]
2025-07-29 18:34:15,151 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.03 s. ]
2025-07-29 18:34:15,151 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:34:15,151 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-29 18:34:15,151 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.PersistenceEngineListener - Flushing startup index events, starting iterations.
2025-07-29 18:34:15,151 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.PersistenceEngineListener - Iteration 1 - processing 5 events
2025-07-29 18:34:15,159 [main | u:p] INFO  com.polarion.alm.tracker.internal.planning.PlanFieldsProvider - livePlanXMLLocation: Location[path /default/.reports/xml/live-plan.xml]
2025-07-29 18:34:15,192 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.PersistenceEngineListener -  - reindexing 1 existing objects and 0 deleted objects.
2025-07-29 18:34:15,272 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-29 18:34:15,275 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-29 18:34:15,276 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: WikiPage
2025-07-29 18:34:15,276 [main | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Revision
2025-07-29 18:34:15,276 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Category
2025-07-29 18:34:15,276 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Attachment
2025-07-29 18:34:15,286 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Attachment
2025-07-29 18:34:15,286 [PolarionDocIdCreator-6] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: RichPageAttachment
2025-07-29 18:34:15,291 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: WorkItem
2025-07-29 18:34:15,309 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: WikiPage
2025-07-29 18:34:15,310 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Category
2025-07-29 18:34:15,310 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: User
2025-07-29 18:34:15,313 [PolarionDocIdCreator-6] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: RichPageAttachment
2025-07-29 18:34:15,314 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: User
2025-07-29 18:34:15,318 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: WorkItem
2025-07-29 18:34:15,318 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: UserGroup
2025-07-29 18:34:15,319 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: UserGroup
2025-07-29 18:34:15,319 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: BaselineCollection
2025-07-29 18:34:15,320 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: TestRun
2025-07-29 18:34:15,325 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: TestRun
2025-07-29 18:34:15,325 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Build
2025-07-29 18:34:15,326 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Build
2025-07-29 18:34:15,327 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: ModuleComment
2025-07-29 18:34:15,345 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: ModuleComment
2025-07-29 18:34:15,345 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: BuildArtifact
2025-07-29 18:34:15,346 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: BaselineCollection
2025-07-29 18:34:15,346 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Comment
2025-07-29 18:34:15,346 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: BuildArtifact
2025-07-29 18:34:15,347 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: DocumentWorkflowSignature
2025-07-29 18:34:15,347 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Comment
2025-07-29 18:34:15,348 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: DocumentWorkflowSignature
2025-07-29 18:34:15,348 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: WikiPageAttachment
2025-07-29 18:34:15,348 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Baseline
2025-07-29 18:34:15,348 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: TimePoint
2025-07-29 18:34:15,349 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: WikiPageAttachment
2025-07-29 18:34:15,350 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: TimePoint
2025-07-29 18:34:15,352 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: WorkRecord
2025-07-29 18:34:15,354 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: WorkRecord
2025-07-29 18:34:15,354 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: WorkItem-OutlineNumbers
2025-07-29 18:34:15,355 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Baseline
2025-07-29 18:34:15,355 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: TestRunAttachment
2025-07-29 18:34:15,355 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: WorkItem-OutlineNumbers
2025-07-29 18:34:15,355 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: ModuleAttachment
2025-07-29 18:34:15,356 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: TestRunAttachment
2025-07-29 18:34:15,355 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Project
2025-07-29 18:34:15,356 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: TestRunComment
2025-07-29 18:34:15,356 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Plan
2025-07-29 18:34:15,356 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: TestRunComment
2025-07-29 18:34:15,357 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Revision
2025-07-29 18:34:15,361 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Project
2025-07-29 18:34:15,361 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: RichPage
2025-07-29 18:34:15,362 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Revision
2025-07-29 18:34:15,362 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}20
2025-07-29 18:34:15,362 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}9
2025-07-29 18:34:15,362 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}19
2025-07-29 18:34:15,362 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}10
2025-07-29 18:34:15,362 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}15
2025-07-29 18:34:15,362 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}16
2025-07-29 18:34:15,362 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}17
2025-07-29 18:34:15,362 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}18
2025-07-29 18:34:15,362 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}7
2025-07-29 18:34:15,362 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}8
2025-07-29 18:34:15,362 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}26
2025-07-29 18:34:15,362 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}32
2025-07-29 18:34:15,362 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}23
2025-07-29 18:34:15,362 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}21
2025-07-29 18:34:15,362 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}22
2025-07-29 18:34:15,362 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}24
2025-07-29 18:34:15,363 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}25
2025-07-29 18:34:15,363 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}31
2025-07-29 18:34:15,363 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}35
2025-07-29 18:34:15,363 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}37
2025-07-29 18:34:15,363 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}36
2025-07-29 18:34:15,363 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}39
2025-07-29 18:34:15,363 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}40
2025-07-29 18:34:15,363 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}41
2025-07-29 18:34:15,363 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}42
2025-07-29 18:34:15,363 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}43
2025-07-29 18:34:15,363 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}46
2025-07-29 18:34:15,363 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}47
2025-07-29 18:34:15,363 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}59
2025-07-29 18:34:15,363 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}51
2025-07-29 18:34:15,363 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}52
2025-07-29 18:34:15,363 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}53
2025-07-29 18:34:15,363 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}55
2025-07-29 18:34:15,363 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}60
2025-07-29 18:34:15,363 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}61
2025-07-29 18:34:15,363 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}65
2025-07-29 18:34:15,363 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}66
2025-07-29 18:34:15,363 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}77
2025-07-29 18:34:15,363 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}67
2025-07-29 18:34:15,363 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}69
2025-07-29 18:34:15,363 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}79
2025-07-29 18:34:15,363 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}80
2025-07-29 18:34:15,363 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}81
2025-07-29 18:34:15,363 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}82
2025-07-29 18:34:15,363 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}83
2025-07-29 18:34:15,363 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}84
2025-07-29 18:34:15,363 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}85
2025-07-29 18:34:15,363 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}86
2025-07-29 18:34:15,363 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}87
2025-07-29 18:34:15,363 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}88
2025-07-29 18:34:15,363 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}89
2025-07-29 18:34:15,363 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}91
2025-07-29 18:34:15,363 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}92
2025-07-29 18:34:15,363 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}93
2025-07-29 18:34:15,363 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/WBS:local/${Revision}09c2010030e517ae250d033127310dd72f63a0ef
2025-07-29 18:34:15,363 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/WBS:local/${Revision}7789a94e058df81e542433b71b0f51142728d99e
2025-07-29 18:34:15,363 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}99
2025-07-29 18:34:15,363 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}103
2025-07-29 18:34:15,363 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}100
2025-07-29 18:34:15,363 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}101
2025-07-29 18:34:15,363 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}96
2025-07-29 18:34:15,364 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}97
2025-07-29 18:34:15,364 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}108
2025-07-29 18:34:15,364 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}105
2025-07-29 18:34:15,364 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}107
2025-07-29 18:34:15,364 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}114
2025-07-29 18:34:15,364 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}115
2025-07-29 18:34:15,364 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}116
2025-07-29 18:34:15,364 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}117
2025-07-29 18:34:15,364 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}118
2025-07-29 18:34:15,364 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}110
2025-07-29 18:34:15,364 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}119
2025-07-29 18:34:15,364 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}120
2025-07-29 18:34:15,364 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}121
2025-07-29 18:34:15,364 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}122
2025-07-29 18:34:15,364 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}123
2025-07-29 18:34:15,364 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}124
2025-07-29 18:34:15,364 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}125
2025-07-29 18:34:15,364 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}126
2025-07-29 18:34:15,364 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}127
2025-07-29 18:34:15,364 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}130
2025-07-29 18:34:15,364 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}136
2025-07-29 18:34:15,364 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}128
2025-07-29 18:34:15,364 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}129
2025-07-29 18:34:15,364 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}131
2025-07-29 18:34:15,364 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}132
2025-07-29 18:34:15,364 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}133
2025-07-29 18:34:15,364 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}134
2025-07-29 18:34:15,364 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}135
2025-07-29 18:34:15,364 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}142
2025-07-29 18:34:15,364 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}137
2025-07-29 18:34:15,364 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}138
2025-07-29 18:34:15,364 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}140
2025-07-29 18:34:15,364 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}141
2025-07-29 18:34:15,364 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}143
2025-07-29 18:34:15,364 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}144
2025-07-29 18:34:15,364 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}145
2025-07-29 18:34:15,364 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}147
2025-07-29 18:34:15,364 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}148
2025-07-29 18:34:15,364 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}149
2025-07-29 18:34:15,364 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}151
2025-07-29 18:34:15,364 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}152
2025-07-29 18:34:15,364 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}154
2025-07-29 18:34:15,364 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}156
2025-07-29 18:34:15,364 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}158
2025-07-29 18:34:15,364 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}159
2025-07-29 18:34:15,364 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}171
2025-07-29 18:34:15,364 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}166
2025-07-29 18:34:15,364 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}167
2025-07-29 18:34:15,364 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}174
2025-07-29 18:34:15,364 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}176
2025-07-29 18:34:15,364 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}169
2025-07-29 18:34:15,364 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}160
2025-07-29 18:34:15,365 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}162
2025-07-29 18:34:15,365 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}164
2025-07-29 18:34:15,365 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}182
2025-07-29 18:34:15,365 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}183
2025-07-29 18:34:15,365 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}185
2025-07-29 18:34:15,365 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}186
2025-07-29 18:34:15,365 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}187
2025-07-29 18:34:15,365 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}188
2025-07-29 18:34:15,365 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}177
2025-07-29 18:34:15,365 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}178
2025-07-29 18:34:15,365 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}179
2025-07-29 18:34:15,365 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}196
2025-07-29 18:34:15,365 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}200
2025-07-29 18:34:15,365 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}189
2025-07-29 18:34:15,365 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}197
2025-07-29 18:34:15,365 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}198
2025-07-29 18:34:15,365 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}201
2025-07-29 18:34:15,365 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}202
2025-07-29 18:34:15,365 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}203
2025-07-29 18:34:15,365 [PolarionDocIdCreator-5] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}204
2025-07-29 18:34:15,365 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Module
2025-07-29 18:34:15,367 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: ModuleAttachment
2025-07-29 18:34:15,369 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.109 s, CPU [user: 0.0234 s, system: 0.00666 s], Allocated memory: 10.5 MB, GC: 0.014 s [100% G1 Young Generation (1x)] (1x)
2025-07-29 18:34:15,371 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Plan
2025-07-29 18:34:15,372 [PolarionDocIdCreator-6] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: ProjectGroup
2025-07-29 18:34:15,373 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Module
2025-07-29 18:34:15,374 [PolarionDocIdCreator-6] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: ProjectGroup
2025-07-29 18:34:15,376 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: RichPage
2025-07-29 18:34:15,383 [PolarionDocIdCreator-1] INFO  com.polarion.subterra.index.impl.lucene.baseline.PolarionDocIdCreator - Bloom filter loading for 28 indices took  [ TIME 0.125 s. ]
2025-07-29 18:34:15,396 [main | u:p] INFO  com.polarion.platform.persistence.internal.calcfields.DelegatingCalculatedFieldsListener - Calculated fields mode: async
2025-07-29 18:34:15,398 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.PersistenceEngineListener - Flushing took  [ TIME 0.247 s. ]
2025-07-29 18:34:15,398 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.25 s. ]
2025-07-29 18:34:15,398 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.247 s [100% doFinishStartup (1x)] (1x), commit: 0.0411 s [100% Revision (1x)] (1x), Lucene: 0.0355 s [100% refresh (1x)] (1x), DB: 0.0266 s [56% execute (1x), 24% update (3x), 15% query (1x)] (8x), derivedLinkedRevisionsContributor: 0.025 s [100% objectsToInv (1x)] (1x)
2025-07-29 18:34:15,398 [main | u:p] INFO  com.polarion.platform.internal.service.repository.ListenerManager - Starting the pulling job for repository: default
2025-07-29 18:34:15,398 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Persistence initialization finished [ TIME 2.03 s. ]
2025-07-29 18:34:15,398 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:34:15,398 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-29 18:34:15,399 [main | u:p] INFO  com.polarion.platform.persistence.internal.calcfields.CalculatedFieldsStorage - Checking integrity of calculated fields storage /opt/polarion/data/workspace/polarion-data/calculated-fields
2025-07-29 18:34:15,404 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-29 18:34:15,404 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:34:15,404 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-29 18:34:15,421 [main | u:p] INFO  com.polarion.platform.jobs.internal.service.scheduler.JobSchedulerService - Updating local scheduler state: start
2025-07-29 18:34:15,428 [main | u:p | u:p] INFO  org.quartz.simpl.SimpleThreadPool - Job execution threads will use class loader of thread: main | u:p | u:p
2025-07-29 18:34:15,431 [main | u:p | u:p] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
2025-07-29 18:34:15,431 [main | u:p | u:p] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'DefaultQuartzScheduler' initialized from default resource file in Quartz package: 'quartz.properties'
2025-07-29 18:34:15,431 [main | u:p | u:p] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 1.4.2
2025-07-29 18:34:15,431 [main | u:p | u:p] INFO  org.quartz.core.QuartzScheduler - Scheduler DefaultQuartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-29 18:34:15,431 [main | u:p | u:p] INFO  org.quartz.core.QuartzScheduler - Scheduler DefaultQuartzScheduler_$_NON_CLUSTERED paused.
2025-07-29 18:34:15,432 [main | u:p | u:p] INFO  org.quartz.core.QuartzScheduler - Scheduler DefaultQuartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-29 18:34:15,433 [main | u:p | u:p] INFO  org.quartz.simpl.SimpleThreadPool - Job execution threads will use class loader of thread: main | u:p | u:p
2025-07-29 18:34:15,433 [main | u:p | u:p] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
2025-07-29 18:34:15,434 [main | u:p | u:p] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'DefaultQuartzScheduler' initialized from default resource file in Quartz package: 'quartz.properties'
2025-07-29 18:34:15,434 [main | u:p | u:p] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 1.4.2
2025-07-29 18:34:15,434 [main | u:p | u:p] INFO  com.polarion.platform.jobs.internal.service.scheduler.JobSchedulerService - 15 scheduled job(s) configured
2025-07-29 18:34:15,440 [main | u:p | u:p] INFO  org.quartz.core.QuartzScheduler - Scheduler DefaultQuartzScheduler_$_NON_CLUSTERED started.
2025-07-29 18:34:15,663 [main] INFO  com.polarion.psvn.launcher.internal.platform.PlatformService - Platform boot finished
2025-07-29 18:34:15,663 [main] INFO  com.polarion.psvn.launcher.internal.platform.PlatformService - Platform started
2025-07-29 18:34:15,674 [main] INFO  com.polarion.portal.tomcat.TomcatPlugin - Tomcat home directory was set to /opt/polarion/data/workspace/.metadata/.plugins/com.polarion.portal.tomcat
2025-07-29 18:34:15,679 [main] INFO  com.polarion.psvn.launcher.internal.tomcat.TomcatService - Starting Tomcat...
2025-07-29 18:34:15,749 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: webui, contextRoot: webapp/webui, plugin: com.polarion.alm.ui, priority: -10]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:34:15,749 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion, contextRoot: webapp/authapp, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:34:15,749 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/.well-known, contextRoot: webapp/well-known, plugin: com.polarion.platform, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:34:15,750 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/ProjectPlanGantt, contextRoot: webapp, plugin: com.polarion.alm.ProjectPlanGantt_new, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:34:15,750 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/activate, contextRoot: webapp/activation, plugin: com.polarion.psvn.launcher, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:34:15,750 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/announcements, contextRoot: webapp/announcements, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:34:15,750 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/bir, contextRoot: webapp/bir, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:34:15,750 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/codemirror-modes, contextRoot: webapp/codemirror-modes, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:34:15,750 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/doorsconnector, contextRoot: webapp, plugin: com.polarion.synchronizer, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:34:15,750 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/export, contextRoot: webapp/export, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:34:15,750 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/fileupload, contextRoot: webapp/fileupload, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:34:15,750 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/gwt, contextRoot: war, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:34:15,750 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/icons, contextRoot: webapp/icons, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:34:15,750 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/internal-login, contextRoot: webapp/internal-login, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:34:15,750 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/module-attachment, contextRoot: webapp/attachments, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:34:15,750 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/modulehome, contextRoot: webapp/module-home, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:34:15,750 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/mxgraph, contextRoot: draw.io/war, plugin: com.polarion.alm.ui.diagrams.mxgraph, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:34:15,751 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/page-attachment, contextRoot: webapp/attachments, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:34:15,751 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/perf-testing, contextRoot: webapp/perf-testing, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:34:15,751 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/plugin-manage, contextRoot: webapp, plugin: com.fasnote.alm.plugin.manage, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:34:15,751 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/print, contextRoot: webapp/print, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:34:15,751 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/register, contextRoot: webapp/register, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:34:15,751 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/rest, contextRoot: webapp, plugin: com.siemens.polarion.rest, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:34:15,751 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/ria, contextRoot: webapp/ria, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:34:15,751 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/richpagehome, contextRoot: webapp/richpage-home, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:34:15,751 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/rt, contextRoot: src/main/webapp, plugin: com.siemens.polarion.rt, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:34:15,751 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/rt-connect, contextRoot: ws, plugin: com.siemens.polarion.rt.communication.polarion, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:34:15,751 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/scripting, contextRoot: webapp/scripting, plugin: com.polarion.scripting.servlet, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:34:15,751 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/sdk, contextRoot: webapp/sdk, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:34:15,752 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/svnwebclient, contextRoot: webapp, plugin: org.polarion.svnwebclient, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:34:15,752 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/swagger, contextRoot: webapp/swagger, plugin: com.siemens.polarion.rest, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:34:15,752 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/synchronizer, contextRoot: webapp, plugin: com.polarion.synchronizer.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:34:15,752 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/template-download, contextRoot: webapp/project-template, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:34:15,752 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/testrun-attachment, contextRoot: webapp/attachments, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:34:15,752 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/tour, contextRoot: webapp/tour, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:34:15,752 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/wi-attachment, contextRoot: webapp/attachments, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:34:15,752 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/wi-attachment-auth, contextRoot: webapp/wi-attachment-auth, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:34:15,752 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/widget-resource, contextRoot: webapp/widget-resource, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:34:15,752 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/wiki, contextRoot: src/main/webapp, plugin: com.polarion.alm.wiki, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:34:15,752 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/workreport, contextRoot: webapp/workreport, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:34:15,752 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/ws, contextRoot: ws, plugin: com.polarion.alm.ws, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:34:15,752 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/xunitimport, contextRoot: webapp/xunitimport, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:34:15,752 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/oslc, contextRoot: webapp, plugin: com.polarion.alm.oslc, priority: 1]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:34:15,798 [main] INFO  org.apache.coyote.ajp.AjpNioProtocol - Initializing ProtocolHandler ["ajp-nio-127.0.0.1-8889"]
2025-07-29 18:34:15,804 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-29 18:34:15,804 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.53]
2025-07-29 18:34:15,826 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@6c138e] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:34:15,826 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@6432ce09] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:34:15,826 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@2b15284d] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:34:15,826 [Catalina-utility-4] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@55726832] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:34:15,827 [Catalina-utility-2] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@78863bf8] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:34:15,829 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@1af03f1a] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:34:15,841 [Catalina-utility-6] INFO  org.apache.catalina.startup.ContextConfig - No global web.xml found
2025-07-29 18:34:15,851 [Catalina-utility-1] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:34:15,851 [Catalina-utility-2] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:34:15,852 [Catalina-utility-4] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:34:15,852 [Catalina-utility-4] WARN  org.apache.catalina.startup.ContextConfig - Security role name [admin] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:34:15,874 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@38aeebb2] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:34:15,874 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@1c79d400] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:34:15,875 [Catalina-utility-4] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@6098c5d8] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:34:15,875 [Catalina-utility-2] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@4847d817] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:34:15,876 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@63b7dc64] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:34:15,876 [Catalina-utility-3] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:34:15,879 [Catalina-utility-1] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:34:15,881 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@6b67805a] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:34:15,882 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@7ec287d] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:34:15,883 [Catalina-utility-5] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:34:15,883 [Catalina-utility-2] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@40ffbed1] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:34:15,884 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@73f4c35f] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:34:15,885 [Catalina-utility-3] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:34:15,888 [Catalina-utility-4] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@4e5f391c] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:34:15,889 [Catalina-utility-2] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@3a09f2f5] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:34:15,890 [Catalina-utility-1] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:34:15,890 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@225be644] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:34:15,892 [Catalina-utility-4] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:34:15,893 [Catalina-utility-3] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:34:15,894 [Catalina-utility-6] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:34:15,896 [Catalina-utility-1] INFO  org.apache.jasper.servlet.TldScanner - At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-07-29 18:34:15,897 [Catalina-utility-2] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:34:15,898 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@1cad68ca] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:34:15,902 [Catalina-utility-3] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:34:15,903 [Catalina-utility-6] INFO  org.apache.tomcat.dbcp.dbcp2.BasicDataSourceFactory - Name = XWikiDS Ignoring unknown property: value of "DB Connection" for "description" property
2025-07-29 18:34:15,906 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@4e4fe7ba] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:34:15,909 [Catalina-utility-2] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@3e4123ae] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:34:15,910 [Catalina-utility-3] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:34:15,915 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@4b7a8683] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:34:15,920 [Catalina-utility-3] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:34:15,923 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@63d3006a] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:34:15,927 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@9bd49ca] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:34:15,935 [Catalina-utility-4] INFO  org.apache.jasper.servlet.TldScanner - At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-07-29 18:34:15,935 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@682c2bab] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:34:15,941 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@44efba4] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:34:15,942 [Catalina-utility-4] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@60c69660] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:34:15,947 [Catalina-utility-2] INFO  com.polarion.portal.velocity.VelocityPathManager - VelocityTemplatesPath=/opt/polarion/polarion/plugins/com.polarion.alm.ui_3.22.1/webapp/authapp/, /opt/polarion/polarion/plugins/com.polarion.alm.ui_3.22.1/webapp/webui/, /opt/polarion/polarion/plugins/com.polarion.alm.wiki_3.22.1/src/main/webapp/, ., /opt/polarion/polarion/plugins/com.polarion.alm.ui_3.22.1/webapp/webui/
2025-07-29 18:34:15,950 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@22f07a00] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:34:15,968 [Catalina-utility-3] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:34:15,978 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@57bb97ff] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:34:15,983 [Catalina-utility-3] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:34:15,987 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@51485383] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:34:15,993 [Catalina-utility-5] INFO  org.polarion.svncommons.commentscache.CommentsCache - Initializing comments cache. Id: http://localhost/repo, repository: http://localhost/repo/, url: http://localhost/repo/, cache directory: /opt/polarion/data/workspace/polarion-data/log-messages-cache, cache page size: 100
2025-07-29 18:34:15,999 [Catalina-utility-3] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:34:16,004 [Catalina-utility-6] INFO  org.apache.jasper.servlet.TldScanner - At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-07-29 18:34:16,005 [Catalina-utility-3] INFO  org.apache.jasper.servlet.TldScanner - At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-07-29 18:34:16,025 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@522e238c] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:34:16,029 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@7ace0b5d] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:34:16,033 [Catalina-utility-3] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:34:16,038 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@229dfe7e] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:34:16,041 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@6dfbd571] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:34:16,046 [Catalina-utility-5] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:34:16,064 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@47ca28c1] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:34:16,066 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@527afebf] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:34:16,069 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@d1fb877] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:34:16,077 [Catalina-utility-3] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:34:16,081 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@5aa686cf] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:34:16,083 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@1edd6c8c] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:34:16,085 [Catalina-utility-5] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:34:16,087 [Catalina-utility-3] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:34:16,101 [Catalina-utility-5] INFO  org.apache.jasper.servlet.TldScanner - At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-07-29 18:34:16,112 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@27eda07] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:34:16,119 [Catalina-utility-5] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:34:16,123 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@287f0369] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:34:16,124 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@7b1ca32d] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:34:16,126 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@7220e4d5] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:34:16,126 [Catalina-utility-6] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:34:16,301 [Catalina-utility-4] INFO  com.siemens.polarion.rt.config.RtAppConfig - RT server config is created
2025-07-29 18:34:16,356 [Catalina-utility-4] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Started.
2025-07-29 18:34:17,022 [Catalina-utility-4] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Initialization of RT parsers...
2025-07-29 18:34:17,025 [Catalina-utility-4] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.java (contributed by 'com.siemens.polarion.rt[86]')
2025-07-29 18:34:17,025 [Catalina-utility-4] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.xml (contributed by 'com.siemens.polarion.rt[86]')
2025-07-29 18:34:17,026 [Catalina-utility-4] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.c (contributed by 'com.siemens.polarion.rt.parsers.c[92]')
2025-07-29 18:34:17,030 [Catalina-utility-4] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Initialization of RT collectors...
2025-07-29 18:34:17,031 [Catalina-utility-4] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Found collector for subversion (contributed by 'com.siemens.polarion.rt[86]')
2025-07-29 18:34:17,031 [Catalina-utility-4] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Found collector for git (contributed by 'com.siemens.polarion.rt.collectors.git[88]')
2025-07-29 18:34:17,118 [main] INFO  org.apache.coyote.ajp.AjpNioProtocol - Starting ProtocolHandler ["ajp-nio-127.0.0.1-8889"]
2025-07-29 18:34:17,125 [main] INFO  com.polarion.psvn.launcher.internal.tomcat.TomcatService - Tomcat is listening on port 8889 using AJP/1.3 protocol with 600000 timeout in millis
2025-07-29 18:34:17,125 [main] INFO  com.polarion.psvn.launcher.internal.help.HelpService - Starting Help Service...
2025-07-29 18:34:17,128 [main] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@5b871b4d] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:34:17,132 [main] INFO  com.polarion.psvn.launcher.internal.help.HelpService - Help Service started
2025-07-29 18:34:17,191 [main | u:p] INFO  com.xpn.xwiki.XWiki - xwiki.cfg taken from /WEB-INF/xwiki.cfg because the XWikiConfig variable is not set in the context
2025-07-29 18:34:17,757 [Thread-33] INFO  class com.polarion.alm.server.util.ChartExporterStartup - Chart renderer says: Server started on 127.0.0.1:34567
2025-07-29 18:34:17,940 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtCommunicationStartup - Rt configuration local mediator is started
2025-07-29 18:34:18,055 [ajp-nio-127.0.0.1-8889-exec-1 | cID:55bf5034-c0a8d700-5b34efe2-0145f409] INFO  com.siemens.polarion.rt.dataprovider.controller.RtDataProviderController - RT server is notified to update all configurations.
2025-07-29 18:34:18,075 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtNotifier - RT server was successfully notified of configuration change.
2025-07-29 18:34:18,075 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtCommunicationStartup - Rt notification service is started
2025-07-29 18:34:18,115 [Thread-37] INFO  com.polarion.platform.jobs.info - Job "Attachment Indexer" has id 55bf507b-c0a8d700-5b34efe2-8b3cb875
2025-07-29 18:34:18,117 [Thread-37] INFO  com.polarion.platform.jobs.info - State of job "Attachment Indexer" changed to UNSCHEDULED
2025-07-29 18:34:18,117 [Thread-37] INFO  com.polarion.platform.jobs.info - Working directory of root job "Attachment Indexer" is /opt/polarion/data/workspace/polarion-data/jobs/20250729-1834
2025-07-29 18:34:18,118 [Thread-37] INFO  com.polarion.platform.jobs.info - Job "Attachment Indexer" runs as user "polarion"
2025-07-29 18:34:18,119 [Thread-37 | u:p] INFO  com.polarion.platform.jobs.info - State of job "Attachment Indexer" changed to ACTIVATING
2025-07-29 18:34:18,121 [Thread-37 | u:p] INFO  com.polarion.platform.jobs.info - State of job "Attachment Indexer" changed to WAITING
2025-07-29 18:34:18,122 [main | u:p] INFO  com.polarion.platform.monitoring - Next slow periodic actions will be executed on Wed Jul 30 01:00:18 CST 2025
2025-07-29 18:34:18,122 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.alm.tracker.internal.HttpsConfiguratorStartup successfully initialized
2025-07-29 18:34:18,124 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.alm.server.util.ChartExporterStartup successfully initialized
2025-07-29 18:34:18,124 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.alm.wiki.WikiPlugin successfully initialized
2025-07-29 18:34:18,124 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.alm.qcentre.internal.QCentreStartup successfully initialized
2025-07-29 18:34:18,124 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.siemens.polarion.rt.communication.connection.RtCommunicationStartup successfully initialized
2025-07-29 18:34:18,125 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.platform.internal.startup.NotificationServerStartup successfully initialized
2025-07-29 18:34:18,125 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.subterra.index.impl.IndexingJobsStartup successfully initialized
2025-07-29 18:34:18,125 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.alm.ui.server.ServerStartup successfully initialized
2025-07-29 18:34:18,125 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.alm.server.util.FormulaServerStartup successfully initialized
2025-07-29 18:34:18,126 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.platform.monitoring.internal.MonitoringServiceStart successfully initialized
2025-07-29 18:34:18,127 [main] INFO  com.polarion.platform.monitoring - Executing actions from stage POSTBOOT
2025-07-29 18:34:18,127 [main] INFO  com.polarion.platform.monitoring - Executing action 'polarion.info.cache.statistics'
2025-07-29 18:34:18,131 [Thread-37] INFO  com.polarion.platform.jobs.info - Job "DB History Creator" has id 55bf5091-c0a8d700-5b34efe2-39819f34
2025-07-29 18:34:18,131 [Thread-37] INFO  com.polarion.platform.jobs.info - State of job "DB History Creator" changed to UNSCHEDULED
2025-07-29 18:34:18,131 [Thread-37] INFO  com.polarion.platform.jobs.info - Working directory of root job "DB History Creator" is /opt/polarion/data/workspace/polarion-data/jobs/20250729-1834_0
2025-07-29 18:34:18,132 [Thread-37] INFO  com.polarion.platform.jobs.info - Job "DB History Creator" runs as user "polarion"
2025-07-29 18:34:18,133 [Worker-0: Attachment Indexer | u:p] INFO  com.polarion.platform.jobs.info - State of job "Attachment Indexer" changed to RUNNING
2025-07-29 18:34:18,134 [Thread-37 | u:p] INFO  com.polarion.platform.jobs.info - State of job "DB History Creator" changed to ACTIVATING
2025-07-29 18:34:18,135 [Thread-37 | u:p] INFO  com.polarion.platform.jobs.info - State of job "DB History Creator" changed to WAITING
2025-07-29 18:34:18,153 [Worker-1: DB History Creator | u:p] INFO  com.polarion.platform.jobs.info - State of job "DB History Creator" changed to RUNNING
2025-07-29 18:34:18,154 [Thread-36] INFO  com.polarion.core.util.process.JavaRunner - Executing /Users/<USER>/Library/Java/JavaVirtualMachines/ms-11.0.27/Contents/Home/bin/java
  -- args [-jar, /opt/polarion/polarion/plugins/com.polarion.platform_3.22.1/services/notification-service/NotificationService.jar, --server.port=40608, --jwksUrl=http://localhost/polarion/.well-known/jwks.json]
  -- env null
  -- dir /var/folders/z_/shw6wc7d7ps_fjvv781t4gt80000gn/T/polarionSpringServerSubprocess14155886824286623586.tmp
2025-07-29 18:34:18,158 [main] INFO  com.polarion.platform.monitoring - polarion.info.cache.statistics (Statistics of caches) = Statistics of caches 
                         CACHE       HITS     MISSES  HIT_RATIO       GETS       PUTS    REMOVAL   EVICTION 
     attachments-content-cache          0          0          0%          0          0          0          0 
                baseline-cache          0          0          0%          0          0          0          0 
                            bq          0          0          0%          0          0          0          0 
                dao-attachment          0          0          0%          0          0          0          0 
                   dao-comment          0          0          0%          0          0          0          0 
                    dao-global          0          4          0%          4          0          0          0 
                    dao-module          0          0          0%          0          0          0          0 
          dao-moduleattachment          0          0          0%          0          0          0          0 
             dao-modulecomment          0          0          0%          0          0          0          0 
                      dao-plan          0          0          0%          0          0          0          0 
                   dao-project          0          0          0%          0          0          0          0 
              dao-projectgroup          0          6          0%          6          3          0          0 
                  dao-richpage          0          0          0%          0          0          0          0 
        dao-richpageattachment          0          0          0%          0          0          0          0 
                   dao-testrun          0          0          0%          0          0          0          0 
         dao-testrunattachment          0          0          0%          0          0          0          0 
                      dao-user          0          0          0%          0          0          0          0 
                  dao-wikipage          0          0          0%          0          0          0          0 
        dao-wikipageattachment          0          0          0%          0          0          0          0 
                  dao-workitem          0          0          0%          0          0          0          0 
      derived-linked-revisions          0          0          0%          0          0          0          0 
                  formulas-svg          0          0          0%          0          0          0          0 
                github-commits          0          0          0%          0          0          0          0 
            historical-queries          0          0          0%          0          0          0          0 
      historical-queries-sizes          0          0          0%          0          0          0          0 
        oslc-linked-item-cache          0          0          0%          0          0          0          0 
               plan-statistics          0          0          0%          0          0          0          0 
                   rmd-default          3          5         38%          8          6          0          0 
                   ss-combined          0          0          0%          0          0          0          0 
                    ss-context          0          0          0%          0          0          0          0 
                     wiki-page          2          8         20%         10          9          1          0 
              wiki-page-exists          0          0          0%          0          9          1          0 

 [Tue Jul 29 18:34:18 CST 2025]
2025-07-29 18:34:18,163 [main] INFO  com.polarion.platform.monitoring - Finished with actions from stage POSTBOOT: {OK=1}
2025-07-29 18:34:18,179 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.77 s. ]
2025-07-29 18:34:18,179 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.584 s [93% info (158x)] (168x)
2025-07-29 18:34:18,180 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 18:34:18,180 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 9.86 s. ]
2025-07-29 18:34:18,180 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 18:34:18,192 [Worker-0: Attachment Indexer | u:p] INFO  com.polarion.platform.jobs.info - State of job "Attachment Indexer" changed to FINISHED
2025-07-29 18:34:18,192 [Worker-0: Attachment Indexer | u:p] INFO  com.polarion.platform.jobs.info - Status of job "Attachment Indexer" is OK
2025-07-29 18:34:18,193 [PreLoadDataService] INFO  com.polarion.psvn.launcher.internal.data.PreLoadDataService - Preloading data ...
2025-07-29 18:34:18,452 [PreLoadDataService | u:p] DEBUG com.fasnote.alm.queryexpander.queryExpanderInterceptorFactory - Creating SingletonProxy for service com.fasnote.alm.queryexpander.queryExpanderInterceptorFactory
2025-07-29 18:34:18,453 [PreLoadDataService | u:p] DEBUG com.fasnote.alm.queryexpander.queryExpanderInterceptorFactory - Constructing core service implementation for service com.fasnote.alm.queryexpander.queryExpanderInterceptorFactory
2025-07-29 18:34:18,492 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: TimePoint
2025-07-29 18:34:18,493 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: TimePoint
2025-07-29 18:34:18,498 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Category
2025-07-29 18:34:18,504 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Category
2025-07-29 18:34:18,569 [Activities-Bulk-Publisher] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Activities
2025-07-29 18:34:18,604 [Thread-43] INFO  class com.polarion.alm.server.util.FormulaServerStartup - Formula renderer says: Server started on 127.0.0.1:34568
2025-07-29 18:34:18,868 [Thread-40] INFO  NotificationService - 
2025-07-29 18:34:18,868 [Thread-40] INFO  NotificationService -   .   ____          _            __ _ _
2025-07-29 18:34:18,868 [Thread-40] INFO  NotificationService -  /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
2025-07-29 18:34:18,868 [Thread-40] INFO  NotificationService - ( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
2025-07-29 18:34:18,868 [Thread-40] INFO  NotificationService -  \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
2025-07-29 18:34:18,868 [Thread-40] INFO  NotificationService -   '  |____| .__|_| |_|_| |_\__, | / / / /
2025-07-29 18:34:18,868 [Thread-40] INFO  NotificationService -  =========|_|==============|___/=/_/_/_/
2025-07-29 18:34:18,869 [Thread-40] INFO  NotificationService -  :: Spring Boot ::                (v2.6.6)
2025-07-29 18:34:18,869 [Thread-40] INFO  NotificationService - 
2025-07-29 18:34:18,959 [Thread-40] INFO  NotificationService - [main] INFO  c.s.polarion.service.notification.Application - Starting Application using Java 11.0.27 on zhangwentiandeMac-mini-2.local with PID 55173 (/opt/polarion/polarion/plugins/com.polarion.platform_3.22.1/services/notification-service/NotificationService.jar started by zhangwentian in /private/var/folders/z_/shw6wc7d7ps_fjvv781t4gt80000gn/T/polarionSpringServerSubprocess14155886824286623586.tmp)
2025-07-29 18:34:18,960 [Thread-40] INFO  NotificationService - [main] INFO  c.s.polarion.service.notification.Application - No active profile set, falling back to 1 default profile: "default"
2025-07-29 18:34:19,044 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  com.polarion.subterra.index.impl.ObjectIndex - Preloading of baselines: 22, for prototypes: WorkItem; with days range: 180d, took  [ TIME 0.733 s. ] 
2025-07-29 18:34:19,045 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.747 s, CPU [user: 0.00605 s, system: 0.00143 s], Allocated memory: 531.4 kB, transactions: 1
2025-07-29 18:34:19,046 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 27, notification worker: 0.0621 s [58% RevisionActivityCreator (2x), 13% WorkItemActivityCreator (1x), 10% PlanActivityCreator (1x)] (6x), persistence listener: 0.0469 s [89% indexRefreshPersistenceListener (1x)] (7x), Incremental Baseline: 0.0347 s [100% WorkItem (21x)] (21x), resolve: 0.0252 s [54% User (1x), 46% Revision (2x)] (3x), Lucene: 0.0128 s [75% add (1x), 25% refresh (1x)] (2x), PullingJob: 0.00704 s [100% collectChanges (1x)] (1x), svn: 0.0068 s [56% getLatestRevision (1x), 44% testConnection (1x)] (2x), EHCache: 0.00525 s [100% GET (15x)] (36x)
2025-07-29 18:34:19,047 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.893 s, CPU [user: 0.155 s, system: 0.0274 s], Allocated memory: 18.5 MB, transactions: 23, svn: 0.657 s [99% getDatedRevision (181x)] (183x), Lucene: 0.0576 s [77% buildBaselineSnapshots (1x), 23% buildBaseline (22x)] (23x)
2025-07-29 18:34:19,048 [Worker-1: DB History Creator | u:p] INFO  com.polarion.platform.jobs.info - State of job "DB History Creator" changed to FINISHED
2025-07-29 18:34:19,048 [Worker-1: DB History Creator | u:p] INFO  com.polarion.platform.jobs.info - Status of job "DB History Creator" is OK
2025-07-29 18:34:19,479 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.ObjectIndex - Preloading of baselines: 22, for prototypes: WorkItem; with days range: 180d, took  [ TIME 0.435 s. ] 
2025-07-29 18:34:19,482 [Thread-40] INFO  NotificationService - [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 40608 (http)
2025-07-29 18:34:19,490 [Thread-40] INFO  NotificationService - [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-40608"]
2025-07-29 18:34:19,494 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66156fd434840_0_66156fd434840_0_: finished. Total: 1.3 s, CPU [user: 0.385 s, system: 0.0943 s], Allocated memory: 54.2 MB, svn: 0.782 s [54% getDatedRevision (181x), 28% getDir2 content (25x)] (307x), resolve: 0.428 s [100% Category (96x)] (96x), ObjectMaps: 0.144 s [47% getPrimaryObjectProperty (96x), 32% getPrimaryObjectLocation (96x), 21% getLastPromoted (96x)] (388x)
2025-07-29 18:34:19,490 [Thread-40] INFO  NotificationService - [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-29 18:34:19,497 [Thread-40] INFO  NotificationService - [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-07-29 18:34:19,503 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: ProjectGroup
2025-07-29 18:34:19,509 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: ProjectGroup
2025-07-29 18:34:19,592 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Project
2025-07-29 18:34:19,592 [Thread-40] INFO  NotificationService - [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-29 18:34:19,592 [Thread-40] INFO  NotificationService - [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 582 ms
2025-07-29 18:34:19,596 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Project
2025-07-29 18:34:19,608 [Thread-40] INFO  NotificationService - [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing AtmosphereFramework
2025-07-29 18:34:19,636 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: User
2025-07-29 18:34:19,640 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: User
2025-07-29 18:34:19,767 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66156fd59d048_0_66156fd59d048_0_: finished. Total: 0.131 s, CPU [user: 0.0626 s, system: 0.0109 s], Allocated memory: 8.4 MB, RepositoryConfigService: 0.0604 s [54% getReadConfiguration (162x), 46% getReadUserConfiguration (10x)] (172x), svn: 0.0549 s [54% info (19x), 38% getFile content (16x)] (37x), resolve: 0.0396 s [100% User (9x)] (9x), ObjectMaps: 0.0209 s [49% getPrimaryObjectProperty (9x), 34% getPrimaryObjectLocation (9x)] (37x)
2025-07-29 18:34:19,803 [Thread-40] INFO  NotificationService - [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-40608"]
2025-07-29 18:34:19,829 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Atmosphere is using org.atmosphere.cpr.DefaultAnnotationProcessor for processing annotation
2025-07-29 18:34:19,830 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.DefaultAnnotationProcessor - AnnotationProcessor class org.atmosphere.cpr.DefaultAnnotationProcessor$BytecodeBasedAnnotationProcessor being used
2025-07-29 18:34:19,842 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AnnotationHandler - Found Annotation in class com.siemens.polarion.service.notification.NotificationService being scanned: interface org.atmosphere.config.service.ManagedService
2025-07-29 18:34:19,845 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.annotation.AnnotationUtil - Adding class org.atmosphere.interceptor.AtmosphereResourceLifecycleInterceptor
2025-07-29 18:34:19,845 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.annotation.AnnotationUtil - Adding class org.atmosphere.client.TrackMessageSizeInterceptor
2025-07-29 18:34:19,846 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.annotation.AnnotationUtil - Adding class org.atmosphere.interceptor.SuspendTrackerInterceptor
2025-07-29 18:34:19,847 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.annotation.AnnotationUtil - Adding class org.atmosphere.config.managed.ManagedServiceInterceptor
2025-07-29 18:34:19,852 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.annotation.AnnotationUtil - Adding class com.siemens.polarion.service.notification.JwtVerificationInterceptor
2025-07-29 18:34:19,856 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.util.ForkJoinPool - Using ForkJoinPool  java.util.concurrent.ForkJoinPool. Set the org.atmosphere.cpr.broadcaster.maxAsyncWriteThreads to -1 to fully use its power.
2025-07-29 18:34:19,861 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereHandler org.atmosphere.config.managed.ManagedAtmosphereHandler mapped to context-path /notification and Broadcaster Class org.atmosphere.cpr.DefaultBroadcaster
2025-07-29 18:34:19,861 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor [Atmosphere LifeCycle,  Track Message Size Interceptor using |, UUID Tracking Interceptor, @ManagedService Interceptor, @Service Event Listeners, com.siemens.polarion.service.notification.JwtVerificationInterceptor] mapped to AtmosphereHandler org.atmosphere.config.managed.ManagedAtmosphereHandler
2025-07-29 18:34:19,868 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Auto detecting WebSocketHandler in /WEB-INF/classes/
2025-07-29 18:34:19,869 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed WebSocketProtocol org.atmosphere.websocket.protocol.SimpleHttpProtocol 
2025-07-29 18:34:19,870 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.container.JSR356AsyncSupport - JSR 356 Mapping path /notification
2025-07-29 18:34:19,875 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installing Default AtmosphereInterceptors
2025-07-29 18:34:19,875 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.CorsInterceptor : CORS Interceptor Support
2025-07-29 18:34:19,875 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.CacheHeadersInterceptor : Default Response's Headers Interceptor
2025-07-29 18:34:19,875 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.PaddingAtmosphereInterceptor : Browser Padding Interceptor Support
2025-07-29 18:34:19,876 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.AndroidAtmosphereInterceptor : Android Interceptor Support
2025-07-29 18:34:19,876 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.HeartbeatInterceptor : Heartbeat Interceptor Support
2025-07-29 18:34:19,877 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.SSEAtmosphereInterceptor : SSE Interceptor Support
2025-07-29 18:34:19,877 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.JSONPAtmosphereInterceptor : JSONP Interceptor Support
2025-07-29 18:34:19,877 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.JavaScriptProtocol : Atmosphere JavaScript Protocol
2025-07-29 18:34:19,877 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.WebSocketMessageSuspendInterceptor : org.atmosphere.interceptor.WebSocketMessageSuspendInterceptor
2025-07-29 18:34:19,877 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.OnDisconnectInterceptor : Browser disconnection detection
2025-07-29 18:34:19,878 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.IdleResourceInterceptor : org.atmosphere.interceptor.IdleResourceInterceptor
2025-07-29 18:34:19,878 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Set org.atmosphere.cpr.AtmosphereInterceptor.disableDefaults to disable them.
2025-07-29 18:34:19,878 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor CORS Interceptor Support with priority FIRST_BEFORE_DEFAULT 
2025-07-29 18:34:19,879 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor Default Response's Headers Interceptor with priority AFTER_DEFAULT 
2025-07-29 18:34:19,879 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor Browser Padding Interceptor Support with priority AFTER_DEFAULT 
2025-07-29 18:34:19,879 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor Android Interceptor Support with priority AFTER_DEFAULT 
2025-07-29 18:34:19,879 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.interceptor.HeartbeatInterceptor - HeartbeatInterceptor configured with padding value 'X', client frequency 30 seconds and server frequency 120 seconds
2025-07-29 18:34:19,879 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor Heartbeat Interceptor Support with priority AFTER_DEFAULT 
2025-07-29 18:34:19,879 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor SSE Interceptor Support with priority AFTER_DEFAULT 
2025-07-29 18:34:19,879 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor JSONP Interceptor Support with priority AFTER_DEFAULT 
2025-07-29 18:34:19,879 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor Atmosphere JavaScript Protocol with priority AFTER_DEFAULT 
2025-07-29 18:34:19,879 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor org.atmosphere.interceptor.WebSocketMessageSuspendInterceptor with priority AFTER_DEFAULT 
2025-07-29 18:34:19,879 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor Browser disconnection detection with priority AFTER_DEFAULT 
2025-07-29 18:34:19,879 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor org.atmosphere.interceptor.IdleResourceInterceptor with priority BEFORE_DEFAULT 
2025-07-29 18:34:19,879 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Using EndpointMapper class org.atmosphere.util.DefaultEndpointMapper
2025-07-29 18:34:19,879 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Using BroadcasterCache: org.atmosphere.cache.UUIDBroadcasterCache
2025-07-29 18:34:19,879 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Default Broadcaster Class: org.atmosphere.cpr.DefaultBroadcaster
2025-07-29 18:34:19,879 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Broadcaster Shared List Resources: false
2025-07-29 18:34:19,879 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Broadcaster Polling Wait Time 100
2025-07-29 18:34:19,879 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Shared ExecutorService supported: true
2025-07-29 18:34:19,879 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Messaging ExecutorService Pool Size unavailable - Not instance of ThreadPoolExecutor
2025-07-29 18:34:19,880 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Async I/O Thread Pool Size: 200
2025-07-29 18:34:19,880 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Using BroadcasterFactory: org.atmosphere.cpr.DefaultBroadcasterFactory
2025-07-29 18:34:19,880 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Using AtmosphereResurceFactory: org.atmosphere.cpr.DefaultAtmosphereResourceFactory
2025-07-29 18:34:19,880 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Using WebSocketProcessor: org.atmosphere.websocket.DefaultWebSocketProcessor
2025-07-29 18:34:19,882 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Invoke AtmosphereInterceptor on WebSocket message true
2025-07-29 18:34:19,882 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - HttpSession supported: false
2025-07-29 18:34:19,882 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Atmosphere is using Spring Web ObjectFactory for dependency injection and object creation
2025-07-29 18:34:19,882 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Atmosphere is using async support: org.atmosphere.container.JSR356AsyncSupport running under container: Apache Tomcat/9.0.60 using javax.servlet/3.0 and jsr356/WebSocket API
2025-07-29 18:34:19,882 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Atmosphere Framework 2.6.4 started.
2025-07-29 18:34:19,882 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 
2025-07-29 18:34:19,882 [Thread-40] INFO  NotificationService - 
2025-07-29 18:34:19,882 [Thread-40] INFO  NotificationService - 	For Atmosphere Framework Commercial Support, visit 
2025-07-29 18:34:19,882 [Thread-40] INFO  NotificationService - 	http://www.async-io.org/ or send an <NAME_EMAIL>
2025-07-29 18:34:19,882 [Thread-40] INFO  NotificationService - 
2025-07-29 18:34:19,885 [Thread-40] INFO  NotificationService - [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 40608 (http) with context path ''
2025-07-29 18:34:19,893 [Thread-40] INFO  NotificationService - [main] INFO  c.s.polarion.service.notification.Application - Started Application in 1.34 seconds (JVM running for 1.712)
2025-07-29 18:34:19,940 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testtype) created
2025-07-29 18:34:19,945 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (subtype) created
2025-07-29 18:34:19,949 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66156fd5c6c4a_0_66156fd5c6c4a_0_: finished. Total: 0.145 s, CPU [user: 0.0454 s, system: 0.00413 s], Allocated memory: 19.9 MB, svn: 0.119 s [79% getDir2 content (17x), 21% getFile content (44x)] (62x), RepositoryConfigService: 0.0397 s [98% getReadConfiguration (170x)] (192x)
2025-07-29 18:34:20,143 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (YesNo) created
2025-07-29 18:34:20,153 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (software_VerificationMethod) created
2025-07-29 18:34:20,162 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (checklist) created
2025-07-29 18:34:20,172 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (commonreqproperty) created
2025-07-29 18:34:20,231 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (objectoriented) created
2025-07-29 18:34:20,232 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (submodule) created
2025-07-29 18:34:20,233 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (yesno) created
2025-07-29 18:34:20,235 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (cICategory) created
2025-07-29 18:34:20,237 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (wpFormat) created
2025-07-29 18:34:20,240 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (trigger) created
2025-07-29 18:34:20,250 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (ASILLevel) created
2025-07-29 18:34:20,253 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (CSRelated) created
2025-07-29 18:34:20,272 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (project_Module) created
2025-07-29 18:34:20,309 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (规格对象类型) created
2025-07-29 18:34:20,315 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (jenkins_job) created
2025-07-29 18:34:20,315 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (truefalse) created
2025-07-29 18:34:20,318 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (takeOnGroups) created
2025-07-29 18:34:20,333 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testcasetype) created
2025-07-29 18:34:20,344 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (processGroup) created
2025-07-29 18:34:20,348 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (changeManagement) created
2025-07-29 18:34:20,356 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (seriousness) created
2025-07-29 18:34:20,366 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (softReqClass) created
2025-07-29 18:34:20,377 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (SWDetailDesign) created
2025-07-29 18:34:20,382 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (PhaseChecklists) created
2025-07-29 18:34:20,386 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (PassNotpass) created
2025-07-29 18:34:20,391 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (baseLineType) created
2025-07-29 18:34:20,395 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (boolYesOrNo) created
2025-07-29 18:34:20,398 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testlevel) created
2025-07-29 18:34:20,402 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (source) created
2025-07-29 18:34:20,434 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (objectType) created
2025-07-29 18:34:20,442 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (atppblversion) created
2025-07-29 18:34:20,450 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (aSIL) created
2025-07-29 18:34:20,462 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (EE) created
2025-07-29 18:34:20,467 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (issueType) created
2025-07-29 18:34:20,472 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (SYS_reqClassification) created
2025-07-29 18:34:20,480 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (oem_2Status) created
2025-07-29 18:34:20,485 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (class) created
2025-07-29 18:34:20,489 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (promotionState) created
2025-07-29 18:34:20,494 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (git_project) created
2025-07-29 18:34:20,506 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (storageType) created
2025-07-29 18:34:20,511 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (issueproperty) created
2025-07-29 18:34:20,517 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (commonissueclass) created
2025-07-29 18:34:20,522 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (AgreeDisagree) created
2025-07-29 18:34:20,527 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (SA_Category) created
2025-07-29 18:34:20,533 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (relevance) created
2025-07-29 18:34:20,540 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (implementationPhase) created
2025-07-29 18:34:20,544 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (supplier_2Status) created
2025-07-29 18:34:20,555 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Testtype) created
2025-07-29 18:34:20,559 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (conf_baselineTime) created
2025-07-29 18:34:20,567 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (levelneed) created
2025-07-29 18:34:20,568 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (finalresult) created
2025-07-29 18:34:20,571 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testability) created
2025-07-29 18:34:20,576 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (solution) created
2025-07-29 18:34:20,579 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Responsible) created
2025-07-29 18:34:20,583 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (verificationstatus) created
2025-07-29 18:34:20,595 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (hsiassigngroup) created
2025-07-29 18:34:20,599 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (reqCategory) created
2025-07-29 18:34:20,604 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (baseLineName) created
2025-07-29 18:34:20,609 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (taskType) created
2025-07-29 18:34:20,632 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (changeReason) created
2025-07-29 18:34:20,639 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (objectmodule) created
2025-07-29 18:34:20,646 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (TestCaseOutputMethod) created
2025-07-29 18:34:20,651 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (SoftwareFeature) created
2025-07-29 18:34:20,673 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (ResponsibleGroup) created
2025-07-29 18:34:20,679 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (hsifunctionmodule) created
2025-07-29 18:34:20,684 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (FwReqSource) created
2025-07-29 18:34:20,689 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (occurPhase) created
2025-07-29 18:34:20,693 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (compiletask) created
2025-07-29 18:34:20,696 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (REQBVerificationMethod) created
2025-07-29 18:34:20,700 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (functionmodule) created
2025-07-29 18:34:20,705 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (variant) created
2025-07-29 18:34:20,707 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Fusatype) created
2025-07-29 18:34:20,714 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (hardwareversion) created
2025-07-29 18:34:20,720 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (appversion) created
2025-07-29 18:34:20,721 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (casefirstmodule) created
2025-07-29 18:34:20,724 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (auditType) created
2025-07-29 18:34:20,730 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Samplestage) created
2025-07-29 18:34:20,733 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (casesecondmodule) created
2025-07-29 18:34:20,736 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (issue_source) created
2025-07-29 18:34:20,740 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (ifNeedRegressionTesting) created
2025-07-29 18:34:20,743 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (atpsfsversion) created
2025-07-29 18:34:20,746 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (CustomerAllocation) created
2025-07-29 18:34:20,749 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (issuesubclass) created
2025-07-29 18:34:20,752 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (QANC_importance) created
2025-07-29 18:34:20,755 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (reviewMethod) created
2025-07-29 18:34:20,758 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (QANC_findType) created
2025-07-29 18:34:20,762 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (editType) created
2025-07-29 18:34:20,767 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testingobjects) created
2025-07-29 18:34:20,770 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testcaselevel) created
2025-07-29 18:34:20,774 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (supplierproblem) created
2025-07-29 18:34:20,777 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (reqattribute) created
2025-07-29 18:34:20,780 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (fsigroup) created
2025-07-29 18:34:20,786 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (project_reqsource) created
2025-07-29 18:34:20,789 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (preset) created
2025-07-29 18:34:20,794 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Mechverificationmethod) created
2025-07-29 18:34:20,797 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (CPMToTPM) created
2025-07-29 18:34:20,799 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (REQBType) created
2025-07-29 18:34:20,802 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testcasesign) created
2025-07-29 18:34:20,807 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (verificationphase) created
2025-07-29 18:34:20,815 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (processArea) created
2025-07-29 18:34:20,819 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (artifactType) created
2025-07-29 18:34:20,830 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Classification) created
2025-07-29 18:34:20,835 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (verificationmethod) created
2025-07-29 18:34:20,838 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (changeType) created
2025-07-29 18:34:20,841 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (hardwareAndSoftwareSubType) created
2025-07-29 18:34:20,846 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (SWIntegrationVerificationMethod) created
2025-07-29 18:34:20,848 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (category) created
2025-07-29 18:34:20,866 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (REQBCategory) created
2025-07-29 18:34:20,873 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (softreqclass) created
2025-07-29 18:34:20,877 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (TestMethod) created
2025-07-29 18:34:20,880 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (reType) created
2025-07-29 18:34:20,885 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (VerificationCriteria) created
2025-07-29 18:34:20,891 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (baseLinechecklist) created
2025-07-29 18:34:20,895 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Category) created
2025-07-29 18:34:20,901 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (SWUnitTestDerivingMethods) created
2025-07-29 18:34:20,906 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (firmware_Category) created
2025-07-29 18:34:20,913 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testMethod) created
2025-07-29 18:34:20,923 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (QAPorcessAreas) created
2025-07-29 18:34:20,928 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (findSource) created
2025-07-29 18:34:20,935 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66156fd5eb44b_0_66156fd5eb44b_0_: finished. Total: 0.986 s, CPU [user: 0.424 s, system: 0.0308 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.826 s [98% getReadConfiguration (8682x)] (9021x), svn: 0.518 s [80% getFile content (412x)] (434x)
2025-07-29 18:34:21,015 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (tshirt-sizes) created
2025-07-29 18:34:21,016 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (reqtype) created
2025-07-29 18:34:21,176 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Feasibility) created
2025-07-29 18:34:21,178 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (locaMod) created
2025-07-29 18:34:21,179 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (unitTestCaseType) created
2025-07-29 18:34:21,181 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (ControlLevel) created
2025-07-29 18:34:21,182 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (NCitemSev) created
2025-07-29 18:34:21,183 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (signType) created
2025-07-29 18:34:21,186 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (WBSCategory) created
2025-07-29 18:34:21,188 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (swTestCaseEnv) created
2025-07-29 18:34:21,189 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (verifiability) created
2025-07-29 18:34:21,190 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (@ProjectUser) created
2025-07-29 18:34:21,192 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (standardReq) created
2025-07-29 18:34:21,195 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (statusa) created
2025-07-29 18:34:21,196 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (@WorkItems[type:configurationitemversion]) created
2025-07-29 18:34:21,197 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (CIRevisionStatus) created
2025-07-29 18:34:21,199 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (dogTimeout) created
2025-07-29 18:34:21,200 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (REQCategory) created
2025-07-29 18:34:21,203 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (proStage) created
2025-07-29 18:34:21,204 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (BaselineType) created
2025-07-29 18:34:21,205 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (custConfStat) created
2025-07-29 18:34:21,206 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (sofReqVer) created
2025-07-29 18:34:21,209 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Source) created
2025-07-29 18:34:21,212 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (scCategory) created
2025-07-29 18:34:21,213 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (softTestCaseType) created
2025-07-29 18:34:21,214 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (solAdv) created
2025-07-29 18:34:21,215 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (unitTestCaseMet) created
2025-07-29 18:34:21,216 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (sysTestCaseMeth) created
2025-07-29 18:34:21,217 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (softTestCaseMe) created
2025-07-29 18:34:21,218 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (softTestCaseEnv) created
2025-07-29 18:34:21,222 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (auditTarget) created
2025-07-29 18:34:21,223 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (@ReviewForm) created
2025-07-29 18:34:21,230 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (swTestCaseType) created
2025-07-29 18:34:21,232 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (@Collection) created
2025-07-29 18:34:21,232 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (submissionStage) created
2025-07-29 18:34:21,234 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (sysTestCaseMet) created
2025-07-29 18:34:21,235 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (demandType) created
2025-07-29 18:34:21,237 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (swTestCaseMet) created
2025-07-29 18:34:21,238 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (taskUrgen) created
2025-07-29 18:34:21,239 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (solveMethod) created
2025-07-29 18:34:21,240 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (audMethod) created
2025-07-29 18:34:21,241 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (desStat) created
2025-07-29 18:34:21,245 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (scType) created
2025-07-29 18:34:21,246 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (sysTestCaseType) created
2025-07-29 18:34:21,247 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (releaseType) created
2025-07-29 18:34:21,248 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (unitTestCaseEnv) created
2025-07-29 18:34:21,249 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (targetStage) created
2025-07-29 18:34:21,250 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (ClassificationType) created
2025-07-29 18:34:21,251 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testItem) created
2025-07-29 18:34:21,252 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (InfoSecurity) created
2025-07-29 18:34:21,254 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Verification) created
2025-07-29 18:34:21,255 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (triggerMod) created
2025-07-29 18:34:21,256 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (verMethod) created
2025-07-29 18:34:21,258 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (diagramCategory) created
2025-07-29 18:34:21,260 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (assSubsystem) created
2025-07-29 18:34:21,262 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (OccurrenceProbability) created
2025-07-29 18:34:21,264 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (developmentMethod) created
2025-07-29 18:34:21,265 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (portType) created
2025-07-29 18:34:21,267 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (checkType) created
2025-07-29 18:34:21,268 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (demandStatus) created
2025-07-29 18:34:21,269 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (important) created
2025-07-29 18:34:21,270 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (triggerMec) created
2025-07-29 18:34:21,271 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (sysTestCaseTy) created
2025-07-29 18:34:21,273 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (recentPre) created
2025-07-29 18:34:21,277 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (TestCaseDesignMethod) created
2025-07-29 18:34:21,280 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testCasePri) created
2025-07-29 18:34:21,283 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (relObj) created
2025-07-29 18:34:21,284 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (proSer) created
2025-07-29 18:34:21,286 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (TestProblemType) created
2025-07-29 18:34:21,287 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (chipName) created
2025-07-29 18:34:21,289 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (auditTiming) created
2025-07-29 18:34:21,291 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66156fd6ff04e_0_66156fd6ff04e_0_: finished. Total: 0.239 s, CPU [user: 0.0961 s, system: 0.0059 s], Allocated memory: 384.7 MB, RepositoryConfigService: 0.155 s [97% getReadConfiguration (2787x)] (3025x), svn: 0.152 s [54% getFile content (185x), 46% getDir2 content (20x)] (206x)
2025-07-29 18:34:21,291 [PreLoadDataService] INFO  com.polarion.psvn.launcher.internal.data.PreLoadDataService - Preloading data FINISHED took  [ TIME 3.1 s. ]
2025-07-29 18:34:21,291 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 3.1 s, CPU [user: 1.1 s, system: 0.159 s], Allocated memory: 1.6 GB, transactions: 10, svn: 1.83 s [39% getFile content (809x), 34% getDir2 content (114x), 23% getDatedRevision (181x)] (1144x), RepositoryConfigService: 1.15 s [95% getReadConfiguration (12019x)] (12691x), resolve: 0.504 s [85% Category (96x)] (117x), ObjectMaps: 0.178 s [48% getPrimaryObjectProperty (110x), 32% getPrimaryObjectLocation (116x)] (452x)
2025-07-29 18:34:21,291 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 55, svn: 2.48 s [43% getDatedRevision (362x), 28% getFile content (809x), 25% getDir2 content (114x)] (1327x), RepositoryConfigService: 1.15 s [95% getReadConfiguration (12019x)] (12691x), resolve: 0.505 s [85% Category (96x)] (118x), ObjectMaps: 0.178 s [48% getPrimaryObjectProperty (110x), 32% getPrimaryObjectLocation (116x)] (452x)
2025-07-29 18:34:28,183 [Thread-36] INFO  NotificationService - Notification service was started successfully.
2025-07-29 18:38:45,846 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55c36484-c0a8d700-5b34efe2-887999ac] INFO  com.polarion.platform.realm.PolarionRealm - authenticate:admin
2025-07-29 18:38:45,887 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55c36484-c0a8d700-5b34efe2-887999ac | u:p] INFO  com.polarion.platform.internal.security.auth.LoginFlowUserPassword - User admin authenticated from portal/127.0.0.1
2025-07-29 18:38:45,928 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55c36484-c0a8d700-5b34efe2-887999ac | u:p] INFO  com.polarion.platform.internal.security.auth.LoginFlowUserPassword - User admin logged in from portal/127.0.0.1
2025-07-29 18:38:45,934 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55c36484-c0a8d700-5b34efe2-887999ac] INFO  remember-me-log - Login using remember me for username: admin
2025-07-29 18:38:45,936 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55c36484-c0a8d700-5b34efe2-887999ac] INFO  PolarionLicensing - User 'admin' logged in with named ALM
2025-07-29 18:38:45,965 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55c36484-c0a8d700-5b34efe2-887999ac] INFO  TXLOGGER - Summary for 'servlet /polarion/': Total: 0.583 s, CPU [user: 0.218 s, system: 0.101 s], Allocated memory: 36.6 MB, transactions: 2, PolarionAuthenticator: 0.544 s [100% authenticate (1x)] (1x)
2025-07-29 18:38:47,258 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55c36b42-c0a8d700-5b34efe2-987cb6b6 | u:admin] INFO  com.polarion.alm.tracker.internal.XProductsService - Current license type: ALM. License limited prototypes: false. 
2025-07-29 18:38:47,293 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55c36b42-c0a8d700-5b34efe2-987cb6b6 | u:admin] INFO  com.polarion.alm.ui.server.diagrams.internal.DiagramEditorsRegistry - Registered diagram editor mxg (com.polarion.alm.ui.diagrams.mxgraph.internal.MxGraphDiagramEditor)
2025-07-29 18:38:47,299 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55c36b42-c0a8d700-5b34efe2-987cb6b6 | u:admin] INFO  TXLOGGER - Tx 661570dae7851_0_661570dae7851_0_: finished. Total: 0.101 s, CPU [user: 0.0434 s, system: 0.0116 s], Allocated memory: 5.9 MB, svn: 0.0296 s [35% info (2x), 24% getDir2 content (1x), 19% getLatestRevision (1x), 18% log (1x)] (7x), resolve: 0.0221 s [99% Project (4x)] (5x), RepositoryConfigService: 0.0182 s [100% getReadConfiguration (10x)] (15x), ObjectMaps: 0.0145 s [48% getPrimaryObjectProperty (1x), 28% getPrimaryObjectLocations (1x), 17% getPrimaryObjectLocation (1x)] (6x)
2025-07-29 18:38:47,414 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55c36b42-c0a8d700-5b34efe2-987cb6b6] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/PortalDataService': Total: 0.307 s, CPU [user: 0.163 s, system: 0.0339 s], Allocated memory: 68.7 MB, transactions: 1, RPC: 0.18 s [50% encodeResponse (1x), 36% decodeRequest (1x)] (4x), PortalDataService: 0.101 s [100% getInitData (1x)] (1x), GC: 0.037 s [100% G1 Young Generation (1x)] (1x), svn: 0.0296 s [35% info (2x), 24% getDir2 content (1x), 19% getLatestRevision (1x), 18% log (1x)] (7x), resolve: 0.0221 s [99% Project (4x)] (5x), RepositoryConfigService: 0.0182 s [100% getReadConfiguration (10x)] (15x)
2025-07-29 18:38:47,693 [ajp-nio-127.0.0.1-8889-exec-8 | cID:55c36ca1-c0a8d700-5b34efe2-cc289aa5 | u:admin] INFO  TXLOGGER - Tx 661570db2b452_0_661570db2b452_0_: finished. Total: 0.224 s, CPU [user: 0.0644 s, system: 0.0124 s], Allocated memory: 8.8 MB, svn: 0.0694 s [45% info (6x), 25% getFile content (4x), 18% testConnection (1x)] (13x), RepositoryConfigService: 0.0406 s [56% getReadConfiguration (13x), 44% getReadUserConfiguration (4x)] (22x), resolve: 0.0379 s [100% RichPage (1x)] (1x)
2025-07-29 18:38:47,710 [ajp-nio-127.0.0.1-8889-exec-8 | cID:55c36ca1-c0a8d700-5b34efe2-cc289aa5] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/PortalDataService': Total: 0.253 s, CPU [user: 0.0788 s, system: 0.0167 s], Allocated memory: 11.0 MB, transactions: 1, PortalDataService: 0.225 s [100% requestPortalSite (1x)] (1x), svn: 0.0694 s [45% info (6x), 25% getFile content (4x), 18% testConnection (1x)] (13x), RepositoryConfigService: 0.0406 s [56% getReadConfiguration (13x), 44% getReadUserConfiguration (4x)] (22x), resolve: 0.0379 s [100% RichPage (1x)] (1x), RPC: 0.0265 s [59% encodeResponse (1x), 38% decodeRequest (1x)] (4x)
2025-07-29 18:38:47,935 [ajp-nio-127.0.0.1-8889-exec-12 | cID:55c36e30-c0a8d700-5b34efe2-9dc70c27 | u:admin] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: WorkItem
2025-07-29 18:38:47,956 [ajp-nio-127.0.0.1-8889-exec-12 | cID:55c36e30-c0a8d700-5b34efe2-9dc70c27 | u:admin] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: WorkItem
2025-07-29 18:38:48,054 [ajp-nio-127.0.0.1-8889-exec-12 | cID:55c36e30-c0a8d700-5b34efe2-9dc70c27] INFO  TXLOGGER - Summary for 'servlet /polarion/svnwebclient/': Total: 0.198 s, CPU [user: 0.0705 s, system: 0.021 s], Allocated memory: 17.7 MB, transactions: 17, Lucene: 0.0268 s [100% search (6x)] (6x), resolve: 0.0258 s [91% Revision (6x)] (23x), svn: 0.0187 s [71% log (3x), 29% testConnection (1x)] (4x)
2025-07-29 18:38:49,749 [ajp-nio-127.0.0.1-8889-exec-15 | cID:55c374e0-c0a8d700-5b34efe2-a9b4ad44] INFO  TXLOGGER - Summary for 'servlet /polarion/svnwebclient/directoryContent.jsp?url=.polarion': Total: 0.177 s, CPU [user: 0.0487 s, system: 0.0304 s], Allocated memory: 7.9 MB, transactions: 41, resolve: 0.0378 s [91% Revision (7x)] (48x), svn: 0.0254 s [81% log (4x)] (5x)
2025-07-29 18:39:01,169 [ajp-nio-127.0.0.1-8889-exec-8 | cID:55c3a1cf-c0a8d700-5b34efe2-104810b4] WARN  org.glassfish.jersey.internal.Errors - The following warnings have been detected: WARNING: The (sub)resource method createTemporaryProxy in com.polarion.synchronizer.ui.MetaDataResource contains empty path annotation.

2025-07-29 18:39:01,251 [ajp-nio-127.0.0.1-8889-exec-8 | cID:55c3a1cf-c0a8d700-5b34efe2-104810b4] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/global/canModifyConfiguration': Total: 0.179 s, CPU [user: 0.0773 s, system: 0.0238 s], Allocated memory: 9.5 MB, transactions: 0
2025-07-29 18:39:01,251 [ajp-nio-127.0.0.1-8889-exec-11 | cID:55c3a202-c0a8d700-5b34efe2-b4b318f1] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/canModifyConfiguration': Total: 0.128 s, CPU [user: 0.015 s, system: 0.00878 s], Allocated memory: 1.3 MB, transactions: 0
2025-07-29 18:39:01,280 [ajp-nio-127.0.0.1-8889-exec-6 | cID:55c3a204-c0a8d700-5b34efe2-d77466fc] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxy_contributions?_=1753785541040': Total: 0.156 s, CPU [user: 0.0416 s, system: 0.0101 s], Allocated memory: 2.0 MB, transactions: 0
2025-07-29 18:39:01,304 [ajp-nio-127.0.0.1-8889-exec-4 | cID:55c3a203-c0a8d700-5b34efe2-187cf7af | u:admin | GET:/polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1753785541041 | u:p] ERROR com.polarion.platform.repository.internal.config.RepositoryConfigService$ConfigProblemCatcher - Failed to work with configuration from location /.polarion/synchronizer/configuration.xml:
[/.polarion/synchronizer/configuration.xml]: 3 counts of IllegalAnnotationExceptions
com.polarion.platform.repository.config.RepositoryConfigurationException: [/.polarion/synchronizer/configuration.xml]: 3 counts of IllegalAnnotationExceptions
	at com.polarion.platform.repository.spi.config.AbstractDataHandler.readLocation(AbstractDataHandler.java:87) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.spi.config.AbstractDataHandler.readLocations(AbstractDataHandler.java:61) ~[platform-repository.jar:?]
	at com.polarion.synchronizer.internal.configuration.ConfigurationDataHandler.readLocations(ConfigurationDataHandler.java:126) ~[synchronizer.jar:?]
	at $IDataHandler_19855bf2fe2.readLocations($IDataHandler_19855bf2fe2.java) ~[?:?]
	at $IDataHandler_19855bf2fe1.readLocations($IDataHandler_19855bf2fe1.java) ~[?:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.readLocations(RepositoryConfigService.java:291) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService$3.runImpl(RepositoryConfigService.java:328) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService$SVNAccessAction$1.runWEx(RepositoryConfigService.java:113) ~[platform-repository.jar:?]
	at com.polarion.core.util.RunnableWEx.runWRet(RunnableWEx.java:61) ~[util.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService$SVNAccessAction.run(RepositoryConfigService.java:123) ~[platform-repository.jar:?]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:?]
	at javax.security.auth.Subject.doAs(Subject.java:361) ~[?:?]
	at com.polarion.platform.internal.security.SubjectNDC.doAs(SubjectNDC.java:58) ~[platform.jar:?]
	at com.polarion.platform.internal.security.SecurityService.doAsUser(SecurityService.java:422) ~[platform.jar:?]
	at com.polarion.platform.internal.security.SecurityService.doAsSystemUser(SecurityService.java:412) ~[platform.jar:?]
	at $ISecurityService_19855bf2e12.doAsSystemUser($ISecurityService_19855bf2e12.java) ~[?:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.readConfiguration(RepositoryConfigService.java:324) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.getConfigurationImpl(RepositoryConfigService.java:239) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.getReadConfigurationImpl(RepositoryConfigService.java:199) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.getReadConfiguration(RepositoryConfigService.java:177) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.getReadConfiguration(RepositoryConfigService.java:170) ~[platform-repository.jar:?]
	at $IRepositoryConfigService_19855bf2e20.getReadConfiguration($IRepositoryConfigService_19855bf2e20.java) ~[?:?]
	at com.polarion.synchronizer.configuration.ConfigurationHelper.loadConfiguration(ConfigurationHelper.java:55) ~[synchronizer.jar:?]
	at com.polarion.synchronizer.ui.SyncConfigurationResource.getSyncPairs(SyncConfigurationResource.java:68) ~[synchronizer-ui.jar:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:566) ~[?:?]
	at org.glassfish.jersey.server.model.internal.ResourceMethodInvocationHandlerFactory.lambda$static$0(ResourceMethodInvocationHandlerFactory.java:52) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.server.model.internal.AbstractJavaResourceMethodDispatcher$1.run(AbstractJavaResourceMethodDispatcher.java:124) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.server.model.internal.AbstractJavaResourceMethodDispatcher.invoke(AbstractJavaResourceMethodDispatcher.java:167) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.server.model.internal.JavaResourceMethodDispatcherProvider$TypeOutInvoker.doDispatch(JavaResourceMethodDispatcherProvider.java:219) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.server.model.internal.AbstractJavaResourceMethodDispatcher.dispatch(AbstractJavaResourceMethodDispatcher.java:79) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.server.model.ResourceMethodInvoker.invoke(ResourceMethodInvoker.java:469) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.server.model.ResourceMethodInvoker.apply(ResourceMethodInvoker.java:391) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.server.model.ResourceMethodInvoker.apply(ResourceMethodInvoker.java:80) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.server.ServerRuntime$1.run(ServerRuntime.java:253) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.internal.Errors$1.call(Errors.java:248) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.internal.Errors$1.call(Errors.java:244) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.internal.Errors.process(Errors.java:292) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.internal.Errors.process(Errors.java:274) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.internal.Errors.process(Errors.java:244) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.process.internal.RequestScope.runInScope(RequestScope.java:265) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.server.ServerRuntime.process(ServerRuntime.java:232) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.server.ApplicationHandler.handle(ApplicationHandler.java:680) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.servlet.WebComponent.serviceImpl(WebComponent.java:394) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.servlet.WebComponent.service(WebComponent.java:346) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.servlet.ServletContainer.service(ServletContainer.java:366) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.servlet.ServletContainer.service(ServletContainer.java:319) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.servlet.ServletContainer.service(ServletContainer.java:205) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227) ~[catalina.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[catalina.jar:9.0.53]
	at com.polarion.portal.tomcat.servlets.DoAsFilter.doFilterWithUriNDC(DoAsFilter.java:112) ~[platform.jar:?]
	at com.polarion.portal.tomcat.servlets.DoAsFilter.lambda$0(DoAsFilter.java:83) ~[platform.jar:?]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:?]
	at javax.security.auth.Subject.doAs(Subject.java:423) [?:?]
	at com.polarion.platform.internal.security.SubjectNDC.doAs(SubjectNDC.java:69) [platform.jar:?]
	at com.polarion.portal.tomcat.servlets.DoAsFilter.doFilterHttpRequest(DoAsFilter.java:82) [platform.jar:?]
	at com.polarion.portal.tomcat.servlets.DoAsFilter.doFilterRequest(DoAsFilter.java:69) [platform.jar:?]
	at com.polarion.portal.tomcat.servlets.DoAsFilter.doFilter(DoAsFilter.java:59) [platform.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [catalina.jar:9.0.53]
	at com.polarion.portal.tomcat.servlets.SecurityCheckFilter.doFilter(SecurityCheckFilter.java:46) [portal-tomcat.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:659) [catalina.jar:9.0.53]
	at com.polarion.platform.security.auth.PolarionAuthenticator.invokeInternal(PolarionAuthenticator.java:248) [platform.jar:?]
	at com.polarion.platform.security.auth.PolarionAuthenticator.invoke(PolarionAuthenticator.java:242) [platform.jar:?]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) [catalina.jar:9.0.53]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.SingleSignOn.invoke(SingleSignOn.java:312) [catalina.jar:9.0.53]
	at com.polarion.platform.session.PolarionLocalSingleSignOn.invoke(PolarionLocalSingleSignOn.java:164) [platform.jar:?]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) [catalina.jar:9.0.53]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357) [catalina.jar:9.0.53]
	at org.apache.coyote.ajp.AjpProcessor.service(AjpProcessor.java:433) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1726) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-util.jar:9.0.53]
	at java.lang.Thread.run(Thread.java:829) [?:?]
Caused by: com.sun.xml.bind.v2.runtime.IllegalAnnotationsException: 3 counts of IllegalAnnotationExceptions
	at com.sun.xml.bind.v2.runtime.IllegalAnnotationsException$Builder.check(IllegalAnnotationsException.java:106) ~[jaxb-impl-2.4.0-b180830.0438-patched.jar:2.4.0-b180830.0438]
	at com.sun.xml.bind.v2.runtime.JAXBContextImpl.getTypeInfoSet(JAXBContextImpl.java:471) ~[jaxb-impl-2.4.0-b180830.0438-patched.jar:2.4.0-b180830.0438]
	at com.sun.xml.bind.v2.runtime.JAXBContextImpl.<init>(JAXBContextImpl.java:303) ~[jaxb-impl-2.4.0-b180830.0438-patched.jar:2.4.0-b180830.0438]
	at com.sun.xml.bind.v2.runtime.JAXBContextImpl.<init>(JAXBContextImpl.java:139) ~[jaxb-impl-2.4.0-b180830.0438-patched.jar:2.4.0-b180830.0438]
	at com.sun.xml.bind.v2.runtime.JAXBContextImpl$JAXBContextBuilder.build(JAXBContextImpl.java:1156) ~[jaxb-impl-2.4.0-b180830.0438-patched.jar:2.4.0-b180830.0438]
	at com.sun.xml.bind.v2.ContextFactory.createContext(ContextFactory.java:165) ~[jaxb-impl-2.4.0-b180830.0438-patched.jar:2.4.0-b180830.0438]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:566) ~[?:?]
	at javax.xml.bind.ContextFinder.newInstance(ContextFinder.java:288) ~[jaxb-api-2.4.0-b180830.0359-patched.jar:2.3.0]
	at javax.xml.bind.ContextFinder.newInstance(ContextFinder.java:277) ~[jaxb-api-2.4.0-b180830.0359-patched.jar:2.3.0]
	at javax.xml.bind.ContextFinder.find(ContextFinder.java:412) ~[jaxb-api-2.4.0-b180830.0359-patched.jar:2.3.0]
	at javax.xml.bind.JAXBContext.newInstance(JAXBContext.java:721) ~[jaxb-api-2.4.0-b180830.0359-patched.jar:2.3.0]
	at javax.xml.bind.JAXBContext.newInstance(JAXBContext.java:662) ~[jaxb-api-2.4.0-b180830.0359-patched.jar:2.3.0]
	at com.polarion.synchronizer.internal.configuration.ConfigurationDataHandler.loadJaxbContext(ConfigurationDataHandler.java:108) ~[synchronizer.jar:?]
	at com.polarion.synchronizer.internal.configuration.ConfigurationDataHandler.processData(ConfigurationDataHandler.java:94) ~[synchronizer.jar:?]
	at com.polarion.platform.repository.spi.config.AbstractDataHandler.readLocation(AbstractDataHandler.java:83) ~[platform-repository.jar:?]
	... 84 more
2025-07-29 18:39:01,332 [ajp-nio-127.0.0.1-8889-exec-4 | cID:55c3a203-c0a8d700-5b34efe2-187cf7af] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1753785541041': Total: 0.209 s, CPU [user: 0.0509 s, system: 0.0219 s], Allocated memory: 7.5 MB, transactions: 1, RepositoryConfigService: 0.101 s [100% getReadConfiguration (1x)] (1x), svn: 0.0115 s [77% testConnection (1x), 23% getFile content (1x)] (3x)
2025-07-29 18:39:01,350 [ajp-nio-127.0.0.1-8889-exec-3 | cID:55c3a204-c0a8d700-5b34efe2-b9b3286d | u:admin | GET:/polarion/synchronizer/rest/projects/WBSdev/connections?_=1753785541042 | u:p] ERROR com.polarion.platform.repository.internal.config.RepositoryConfigService$ConfigProblemCatcher - Failed to work with configuration from location /.polarion/synchronizer/configuration.xml:
[/.polarion/synchronizer/configuration.xml]: 3 counts of IllegalAnnotationExceptions
com.polarion.platform.repository.config.RepositoryConfigurationException: [/.polarion/synchronizer/configuration.xml]: 3 counts of IllegalAnnotationExceptions
	at com.polarion.platform.repository.spi.config.AbstractDataHandler.readLocation(AbstractDataHandler.java:87) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.spi.config.AbstractDataHandler.readLocations(AbstractDataHandler.java:61) ~[platform-repository.jar:?]
	at com.polarion.synchronizer.internal.configuration.ConfigurationDataHandler.readLocations(ConfigurationDataHandler.java:126) ~[synchronizer.jar:?]
	at $IDataHandler_19855bf2fe1.readLocations($IDataHandler_19855bf2fe1.java) ~[?:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.readLocations(RepositoryConfigService.java:291) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService$3.runImpl(RepositoryConfigService.java:328) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService$SVNAccessAction$1.runWEx(RepositoryConfigService.java:113) ~[platform-repository.jar:?]
	at com.polarion.core.util.RunnableWEx.runWRet(RunnableWEx.java:61) ~[util.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService$SVNAccessAction.run(RepositoryConfigService.java:123) ~[platform-repository.jar:?]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:?]
	at javax.security.auth.Subject.doAs(Subject.java:361) ~[?:?]
	at com.polarion.platform.internal.security.SubjectNDC.doAs(SubjectNDC.java:58) ~[platform.jar:?]
	at com.polarion.platform.internal.security.SecurityService.doAsUser(SecurityService.java:422) ~[platform.jar:?]
	at com.polarion.platform.internal.security.SecurityService.doAsSystemUser(SecurityService.java:412) ~[platform.jar:?]
	at $ISecurityService_19855bf2e12.doAsSystemUser($ISecurityService_19855bf2e12.java) ~[?:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.readConfiguration(RepositoryConfigService.java:324) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.getConfigurationImpl(RepositoryConfigService.java:239) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.getReadConfigurationImpl(RepositoryConfigService.java:199) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.getReadConfiguration(RepositoryConfigService.java:177) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.getReadConfiguration(RepositoryConfigService.java:170) ~[platform-repository.jar:?]
	at $IRepositoryConfigService_19855bf2e20.getReadConfiguration($IRepositoryConfigService_19855bf2e20.java) ~[?:?]
	at com.polarion.synchronizer.configuration.ConfigurationHelper.loadConfiguration(ConfigurationHelper.java:55) ~[synchronizer.jar:?]
	at com.polarion.synchronizer.ui.SyncConfigurationResource.getConnections(SyncConfigurationResource.java:158) ~[synchronizer-ui.jar:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:566) ~[?:?]
	at org.glassfish.jersey.server.model.internal.ResourceMethodInvocationHandlerFactory.lambda$static$0(ResourceMethodInvocationHandlerFactory.java:52) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.server.model.internal.AbstractJavaResourceMethodDispatcher$1.run(AbstractJavaResourceMethodDispatcher.java:124) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.server.model.internal.AbstractJavaResourceMethodDispatcher.invoke(AbstractJavaResourceMethodDispatcher.java:167) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.server.model.internal.JavaResourceMethodDispatcherProvider$TypeOutInvoker.doDispatch(JavaResourceMethodDispatcherProvider.java:219) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.server.model.internal.AbstractJavaResourceMethodDispatcher.dispatch(AbstractJavaResourceMethodDispatcher.java:79) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.server.model.ResourceMethodInvoker.invoke(ResourceMethodInvoker.java:469) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.server.model.ResourceMethodInvoker.apply(ResourceMethodInvoker.java:391) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.server.model.ResourceMethodInvoker.apply(ResourceMethodInvoker.java:80) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.server.ServerRuntime$1.run(ServerRuntime.java:253) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.internal.Errors$1.call(Errors.java:248) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.internal.Errors$1.call(Errors.java:244) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.internal.Errors.process(Errors.java:292) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.internal.Errors.process(Errors.java:274) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.internal.Errors.process(Errors.java:244) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.process.internal.RequestScope.runInScope(RequestScope.java:265) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.server.ServerRuntime.process(ServerRuntime.java:232) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.server.ApplicationHandler.handle(ApplicationHandler.java:680) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.servlet.WebComponent.serviceImpl(WebComponent.java:394) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.servlet.WebComponent.service(WebComponent.java:346) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.servlet.ServletContainer.service(ServletContainer.java:366) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.servlet.ServletContainer.service(ServletContainer.java:319) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.servlet.ServletContainer.service(ServletContainer.java:205) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227) ~[catalina.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[catalina.jar:9.0.53]
	at com.polarion.portal.tomcat.servlets.DoAsFilter.doFilterWithUriNDC(DoAsFilter.java:112) ~[platform.jar:?]
	at com.polarion.portal.tomcat.servlets.DoAsFilter.lambda$0(DoAsFilter.java:83) ~[platform.jar:?]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:?]
	at javax.security.auth.Subject.doAs(Subject.java:423) [?:?]
	at com.polarion.platform.internal.security.SubjectNDC.doAs(SubjectNDC.java:69) [platform.jar:?]
	at com.polarion.portal.tomcat.servlets.DoAsFilter.doFilterHttpRequest(DoAsFilter.java:82) [platform.jar:?]
	at com.polarion.portal.tomcat.servlets.DoAsFilter.doFilterRequest(DoAsFilter.java:69) [platform.jar:?]
	at com.polarion.portal.tomcat.servlets.DoAsFilter.doFilter(DoAsFilter.java:59) [platform.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [catalina.jar:9.0.53]
	at com.polarion.portal.tomcat.servlets.SecurityCheckFilter.doFilter(SecurityCheckFilter.java:46) [portal-tomcat.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:659) [catalina.jar:9.0.53]
	at com.polarion.platform.security.auth.PolarionAuthenticator.invokeInternal(PolarionAuthenticator.java:248) [platform.jar:?]
	at com.polarion.platform.security.auth.PolarionAuthenticator.invoke(PolarionAuthenticator.java:242) [platform.jar:?]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) [catalina.jar:9.0.53]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.SingleSignOn.invoke(SingleSignOn.java:312) [catalina.jar:9.0.53]
	at com.polarion.platform.session.PolarionLocalSingleSignOn.invoke(PolarionLocalSingleSignOn.java:164) [platform.jar:?]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) [catalina.jar:9.0.53]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357) [catalina.jar:9.0.53]
	at org.apache.coyote.ajp.AjpProcessor.service(AjpProcessor.java:433) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1726) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-util.jar:9.0.53]
	at java.lang.Thread.run(Thread.java:829) [?:?]
Caused by: com.sun.xml.bind.v2.runtime.IllegalAnnotationsException: 3 counts of IllegalAnnotationExceptions
	at com.sun.xml.bind.v2.runtime.IllegalAnnotationsException$Builder.check(IllegalAnnotationsException.java:106) ~[jaxb-impl-2.4.0-b180830.0438-patched.jar:2.4.0-b180830.0438]
	at com.sun.xml.bind.v2.runtime.JAXBContextImpl.getTypeInfoSet(JAXBContextImpl.java:471) ~[jaxb-impl-2.4.0-b180830.0438-patched.jar:2.4.0-b180830.0438]
	at com.sun.xml.bind.v2.runtime.JAXBContextImpl.<init>(JAXBContextImpl.java:303) ~[jaxb-impl-2.4.0-b180830.0438-patched.jar:2.4.0-b180830.0438]
	at com.sun.xml.bind.v2.runtime.JAXBContextImpl.<init>(JAXBContextImpl.java:139) ~[jaxb-impl-2.4.0-b180830.0438-patched.jar:2.4.0-b180830.0438]
	at com.sun.xml.bind.v2.runtime.JAXBContextImpl$JAXBContextBuilder.build(JAXBContextImpl.java:1156) ~[jaxb-impl-2.4.0-b180830.0438-patched.jar:2.4.0-b180830.0438]
	at com.sun.xml.bind.v2.ContextFactory.createContext(ContextFactory.java:165) ~[jaxb-impl-2.4.0-b180830.0438-patched.jar:2.4.0-b180830.0438]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:566) ~[?:?]
	at javax.xml.bind.ContextFinder.newInstance(ContextFinder.java:288) ~[jaxb-api-2.4.0-b180830.0359-patched.jar:2.3.0]
	at javax.xml.bind.ContextFinder.newInstance(ContextFinder.java:277) ~[jaxb-api-2.4.0-b180830.0359-patched.jar:2.3.0]
	at javax.xml.bind.ContextFinder.find(ContextFinder.java:412) ~[jaxb-api-2.4.0-b180830.0359-patched.jar:2.3.0]
	at javax.xml.bind.JAXBContext.newInstance(JAXBContext.java:721) ~[jaxb-api-2.4.0-b180830.0359-patched.jar:2.3.0]
	at javax.xml.bind.JAXBContext.newInstance(JAXBContext.java:662) ~[jaxb-api-2.4.0-b180830.0359-patched.jar:2.3.0]
	at com.polarion.synchronizer.internal.configuration.ConfigurationDataHandler.loadJaxbContext(ConfigurationDataHandler.java:108) ~[synchronizer.jar:?]
	at com.polarion.synchronizer.internal.configuration.ConfigurationDataHandler.processData(ConfigurationDataHandler.java:94) ~[synchronizer.jar:?]
	at com.polarion.platform.repository.spi.config.AbstractDataHandler.readLocation(AbstractDataHandler.java:83) ~[platform-repository.jar:?]
	... 83 more
2025-07-29 18:39:01,356 [ajp-nio-127.0.0.1-8889-exec-3 | cID:55c3a204-c0a8d700-5b34efe2-b9b3286d] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections?_=1753785541042': Total: 0.232 s, CPU [user: 0.0286 s, system: 0.00925 s], Allocated memory: 2.7 MB, transactions: 1, RepositoryConfigService: 0.127 s [100% getReadConfiguration (1x)] (1x)
2025-07-29 18:39:17,346 [Thread-32] INFO  class com.polarion.alm.server.util.ChartExporterStartup - Chart renderer was started successfully.
2025-07-29 18:39:18,246 [Thread-38] INFO  class com.polarion.alm.server.util.FormulaServerStartup - Formula renderer was started successfully.
2025-07-29 18:44:18,124 [Monitoring] INFO  com.polarion.platform.monitoring - Executing actions from stage PERIODIC with speed FAST
2025-07-29 18:44:18,164 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'polarion.sso.statistics'
2025-07-29 18:44:18,166 [Monitoring] INFO  com.polarion.platform.monitoring - polarion.sso.statistics (Statistics of all active sso sessions) = Statistics of logged users 
Logged active users:1
 [Tue Jul 29 18:44:18 CST 2025]
2025-07-29 18:44:18,207 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.disk.data.free'
2025-07-29 18:44:18,208 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.disk.data.free (Free data disk '/opt/polarion/data' space) = Disk Space 
1,425.87 GB
 [Tue Jul 29 18:44:18 CST 2025]
2025-07-29 18:44:18,427 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.full.cycles'
2025-07-29 18:44:18,428 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.full.cycles (Full Garbage Collector cycles) = Full Garbage Collector cycles 
Full GC cycles: 0; Time: 0.0 s.
 [Tue Jul 29 18:44:18 CST 2025]
2025-07-29 18:44:18,429 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.normal.cycles'
2025-07-29 18:44:18,430 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.normal.cycles (Normal Garbage Collector cycles) = Normal Garbage Collector cycles 
Normal GC cycles: 15; Time: 0.173 s.
 [Tue Jul 29 18:44:18 CST 2025]
2025-07-29 18:44:18,432 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.nonheap.free'
2025-07-29 18:44:18,435 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.nonheap.free (Free non-heap memory) = Java Metaspace Memory 
Unlimited
 [Tue Jul 29 18:44:18 CST 2025]
2025-07-29 18:44:18,650 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.oldGen.free'
2025-07-29 18:44:18,657 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.oldGen.free (Free Old Gen memory after GC) = Java Heap Memory 
Old Gen pool has  100.00% memory free after GC.
 [Tue Jul 29 18:44:18 CST 2025]
2025-07-29 18:44:18,861 [Monitoring] INFO  com.polarion.platform.monitoring - Finished with actions from stage PERIODIC: {OK=6}
