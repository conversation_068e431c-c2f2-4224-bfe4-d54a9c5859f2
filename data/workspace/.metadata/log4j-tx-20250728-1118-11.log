2025-07-28 11:18:15,904 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0553 s [52% query (12x), 48% update (144x)] (221x), svn: 0.011 s [58% getLatestRevision (2x), 32% testConnection (1x)] (4x)
2025-07-28 11:18:16,020 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0442 s [57% info (3x), 33% getDir2 content (2x)] (6x)
2025-07-28 11:18:16,741 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.715 s, CPU [user: 0.226 s, system: 0.345 s], Allocated memory: 70.2 MB, transactions: 0, ObjectMaps: 0.123 s [98% getAllPrimaryObjects (1x)] (14x)
2025-07-28 11:18:16,741 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.715 s, CPU [user: 0.0853 s, system: 0.17 s], Allocated memory: 14.3 MB, transactions: 0, ObjectMaps: 0.0719 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.0633 s [76% log2 (10x), 14% getLatestRevision (2x)] (13x)
2025-07-28 11:18:16,740 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.714 s, CPU [user: 0.0738 s, system: 0.103 s], Allocated memory: 10.9 MB, transactions: 0, svn: 0.119 s [49% log2 (10x), 20% log (1x), 15% info (5x)] (24x), ObjectMaps: 0.069 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-28 11:18:16,741 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.715 s, CPU [user: 0.101 s, system: 0.225 s], Allocated memory: 23.8 MB, transactions: 0, ObjectMaps: 0.0685 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0417 s [84% log2 (5x)] (7x)
2025-07-28 11:18:16,740 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.714 s, CPU [user: 0.049 s, system: 0.117 s], Allocated memory: 6.8 MB, transactions: 0, ObjectMaps: 0.0786 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0377 s [80% log2 (5x)] (7x)
2025-07-28 11:18:16,741 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.715 s, CPU [user: 0.179 s, system: 0.285 s], Allocated memory: 53.1 MB, transactions: 0, ObjectMaps: 0.114 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-28 11:18:16,742 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.525 s [100% getAllPrimaryObjects (8x)] (63x), svn: 0.305 s [65% log2 (36x), 11% getLatestRevision (9x), 9% testConnection (6x)] (61x)
2025-07-28 11:18:17,010 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.232 s [100% getReadConfiguration (48x)] (48x), svn: 0.113 s [89% info (18x)] (38x)
2025-07-28 11:18:17,357 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.267 s [75% info (94x), 15% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.208 s [100% getReadConfiguration (54x)] (54x)
2025-07-28 11:18:17,579 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.109 s, CPU [user: 0.0333 s, system: 0.00903 s], Allocated memory: 11.2 MB, GC: 0.012 s [100% G1 Young Generation (1x)] (1x)
2025-07-28 11:18:17,637 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.266 s [100% doFinishStartup (1x)] (1x), commit: 0.0717 s [100% Revision (1x)] (1x), Lucene: 0.0338 s [100% refresh (1x)] (1x), DB: 0.0295 s [35% update (3x), 33% execute (1x), 23% query (1x)] (8x), derivedLinkedRevisionsContributor: 0.0144 s [100% objectsToInv (1x)] (1x)
2025-07-28 11:18:20,008 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.362 s [88% info (158x)] (168x)
2025-07-28 11:18:20,672 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.6 s, CPU [user: 0.00516 s, system: 0.00118 s], Allocated memory: 552.7 kB, transactions: 1
2025-07-28 11:18:20,673 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 25, Incremental Baseline: 0.0207 s [100% WorkItem (19x)] (19x), notification worker: 0.0155 s [73% RevisionActivityCreator (2x), 10% WorkItemActivityCreator (1x)] (6x), persistence listener: 0.0148 s [68% indexRefreshPersistenceListener (1x), 14% WorkItemActivityCreator (1x)] (7x), resolve: 0.0123 s [76% User (1x), 24% Revision (2x)] (3x), Lucene: 0.0108 s [66% add (1x), 34% refresh (1x)] (2x), PullingJob: 0.00859 s [100% collectChanges (1x)] (1x), svn: 0.00832 s [54% getLatestRevision (1x), 46% testConnection (1x)] (2x), EHCache: 0.0043 s [100% GET (15x)] (34x), ObjectMaps: 0.00302 s [100% getPrimaryObjectLocation (1x)] (1x), permissions: 0.00145 s [100% readInstance (2x)] (2x)
2025-07-28 11:18:20,674 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.671 s, CPU [user: 0.131 s, system: 0.0258 s], Allocated memory: 17.9 MB, transactions: 21, svn: 0.538 s [98% getDatedRevision (181x)] (183x)
2025-07-28 11:18:20,947 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6613c2741b840_0_6613c2741b840_0_: finished. Total: 0.933 s, CPU [user: 0.317 s, system: 0.0785 s], Allocated memory: 53.2 MB, svn: 0.549 s [46% getDatedRevision (181x), 32% getDir2 content (25x), 20% getFile content (98x)] (307x), resolve: 0.339 s [100% Category (96x)] (96x), ObjectMaps: 0.11 s [43% getPrimaryObjectProperty (96x), 32% getPrimaryObjectLocation (96x), 25% getLastPromoted (96x)] (388x)
2025-07-28 11:18:21,152 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6613c27518c48_0_6613c27518c48_0_: finished. Total: 0.125 s, CPU [user: 0.0558 s, system: 0.0105 s], Allocated memory: 8.4 MB, svn: 0.0553 s [51% info (19x), 40% getFile content (16x)] (37x), RepositoryConfigService: 0.0497 s [61% getReadUserConfiguration (10x), 39% getReadConfiguration (162x)] (172x), resolve: 0.0445 s [100% User (9x)] (9x), ObjectMaps: 0.0186 s [56% getPrimaryObjectProperty (9x), 30% getPrimaryObjectLocation (9x)] (37x)
2025-07-28 11:18:21,199 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.019873046875
2025-07-28 11:18:21,350 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6613c27542c4a_0_6613c27542c4a_0_: finished. Total: 0.154 s, CPU [user: 0.0557 s, system: 0.00618 s], Allocated memory: 19.9 MB, svn: 0.113 s [68% getDir2 content (17x), 32% getFile content (44x)] (62x), RepositoryConfigService: 0.0611 s [98% getReadConfiguration (170x)] (192x)
2025-07-28 11:18:22,195 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6613c2756984b_0_6613c2756984b_0_: finished. Total: 0.845 s, CPU [user: 0.363 s, system: 0.0305 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.589 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.484 s [58% getFile content (412x), 42% getDir2 content (21x)] (434x)
2025-07-28 11:18:22,547 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6613c2765784e_0_6613c2765784e_0_: finished. Total: 0.244 s, CPU [user: 0.0931 s, system: 0.00548 s], Allocated memory: 384.7 MB, svn: 0.163 s [53% getDir2 content (20x), 47% getFile content (185x)] (206x), RepositoryConfigService: 0.145 s [97% getReadConfiguration (2787x)] (3025x)
2025-07-28 11:18:22,547 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 2.54 s, CPU [user: 0.966 s, system: 0.143 s], Allocated memory: 1.6 GB, transactions: 10, svn: 1.51 s [42% getDir2 content (114x), 37% getFile content (809x), 17% getDatedRevision (181x)] (1144x), RepositoryConfigService: 0.91 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.424 s [80% Category (96x)] (117x), ObjectMaps: 0.142 s [46% getPrimaryObjectProperty (110x), 32% getPrimaryObjectLocation (116x), 22% getLastPromoted (110x)] (452x)
2025-07-28 11:18:22,547 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 51, svn: 2.04 s [38% getDatedRevision (362x), 31% getDir2 content (114x), 28% getFile content (809x)] (1327x), RepositoryConfigService: 0.91 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.424 s [80% Category (96x)] (118x), ObjectMaps: 0.142 s [46% getPrimaryObjectProperty (110x), 32% getPrimaryObjectLocation (116x), 22% getLastPromoted (110x)] (452x)
