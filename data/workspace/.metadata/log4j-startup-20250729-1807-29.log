2025-07-29 18:07:29,147 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 18:07:29,147 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-29 18:07:29,147 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 18:07:29,147 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-29 18:07:29,147 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-29 18:07:29,147 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:07:29,147 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-29 18:07:33,451 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-29 18:07:33,583 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.131 s. ]
2025-07-29 18:07:33,583 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-29 18:07:33,620 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0369 s. ]
2025-07-29 18:07:33,670 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-29 18:07:33,778 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 12 s. ]
2025-07-29 18:07:33,975 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.83 s. ]
2025-07-29 18:07:34,056 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:07:34,056 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-29 18:07:34,076 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.1 s. ]
2025-07-29 18:07:34,076 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:07:34,076 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-29 18:07:34,081 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (2/9)
2025-07-29 18:07:34,081 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (3/9)
2025-07-29 18:07:34,081 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (6/9)
2025-07-29 18:07:34,081 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (4/9)
2025-07-29 18:07:34,081 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (5/9)
2025-07-29 18:07:34,081 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (1/9)
2025-07-29 18:07:34,087 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-07-29 18:07:34,239 [LowLevelDataService-contextInitializer-4 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-29 18:07:34,328 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-29 18:07:34,799 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.72 s. ]
2025-07-29 18:07:34,810 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:07:34,810 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-29 18:07:35,032 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-29 18:07:35,044 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.24 s. ]
2025-07-29 18:07:35,068 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:07:35,068 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-29 18:07:35,072 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-29 18:07:35,126 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-29 18:07:35,177 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-07-29 18:07:35,203 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (4/9)
2025-07-29 18:07:35,226 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (5/9)
2025-07-29 18:07:35,264 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (6/9)
2025-07-29 18:07:35,309 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-07-29 18:07:35,357 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-29 18:07:35,388 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-29 18:07:35,388 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.34 s. ]
2025-07-29 18:07:35,388 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:07:35,388 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-29 18:07:35,401 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-07-29 18:07:35,401 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:07:35,401 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-29 18:07:35,514 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-29 18:07:35,521 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-29 18:07:35,665 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.26 s. ]
2025-07-29 18:07:35,666 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:07:35,666 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-29 18:07:35,676 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-29 18:07:35,676 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:07:35,676 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-29 18:07:38,145 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.47 s. ]
2025-07-29 18:07:38,145 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 18:07:38,145 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 9 s. ]
2025-07-29 18:07:38,145 [main] INFO  com.polarion.platform.startup - ****************************************************************
