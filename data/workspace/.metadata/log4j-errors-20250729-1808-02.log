2025-07-29 18:08:08,307 [main | u:p | u:p] ERROR com.polarion.platform.repository.internal.config.RepositoryConfigService$ConfigProblemCatcher - Failed to work with configuration from location /WBS/.polarion/repositories/repositories.xml:
[/WBS/.polarion/repositories/repositories.xml]: 
---- Debugging information ----
cause-exception     : com.thoughtworks.xstream.mapper.CannotResolveClassException
cause-message       : AutoBranchGitLab
class               : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
required-type       : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
converter-type      : com.thoughtworks.xstream.converters.collections.ArrayConverter
version             : 1.4.17
-------------------------------
com.polarion.platform.repository.config.RepositoryConfigurationException: [/WBS/.polarion/repositories/repositories.xml]: 
---- Debugging information ----
cause-exception     : com.thoughtworks.xstream.mapper.CannotResolveClassException
cause-message       : AutoBranchGitLab
class               : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
required-type       : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
converter-type      : com.thoughtworks.xstream.converters.collections.ArrayConverter
version             : 1.4.17
-------------------------------
	at com.polarion.platform.repository.spi.config.AbstractDataHandler.readLocation(AbstractDataHandler.java:87) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.spi.config.AbstractDataHandler.readLocations(AbstractDataHandler.java:61) ~[platform-repository.jar:?]
	at $IDataHandler_19855a74aa1.readLocations($IDataHandler_19855a74aa1.java) ~[?:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.readLocations(RepositoryConfigService.java:291) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService$3.runImpl(RepositoryConfigService.java:328) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService$SVNAccessAction$1.runWEx(RepositoryConfigService.java:113) ~[platform-repository.jar:?]
	at com.polarion.core.util.RunnableWEx.runWRet(RunnableWEx.java:61) ~[util.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService$SVNAccessAction.run(RepositoryConfigService.java:123) ~[platform-repository.jar:?]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:?]
	at javax.security.auth.Subject.doAs(Subject.java:361) ~[?:?]
	at com.polarion.platform.internal.security.SubjectNDC.doAs(SubjectNDC.java:58) ~[platform.jar:?]
	at com.polarion.platform.internal.security.SecurityService.doAsUser(SecurityService.java:422) ~[platform.jar:?]
	at com.polarion.platform.internal.security.SecurityService.doAsSystemUser(SecurityService.java:412) ~[platform.jar:?]
	at $ISecurityService_19855a748bc.doAsSystemUser($ISecurityService_19855a748bc.java) ~[?:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.readConfiguration(RepositoryConfigService.java:324) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.getConfigurationImpl(RepositoryConfigService.java:239) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.getReadConfigurationImpl(RepositoryConfigService.java:199) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.getReadConfiguration(RepositoryConfigService.java:177) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.addConfigurationListener(RepositoryConfigService.java:263) ~[platform-repository.jar:?]
	at $IRepositoryConfigService_19855a748ca.addConfigurationListener($IRepositoryConfigService_19855a748ca.java) ~[?:?]
	at com.polarion.platform.repository.external.internal.ExternalRepositoryProviderRegistry.initialize(ExternalRepositoryProviderRegistry.java:146) ~[platform-repository.jar:?]
	at $IExternalRepositoryProviderRegistry_19855a74986.initialize($IExternalRepositoryProviderRegistry_19855a74986.java) ~[?:?]
	at $IExternalRepositoryProviderRegistry_19855a74985.initialize($IExternalRepositoryProviderRegistry_19855a74985.java) ~[?:?]
	at com.polarion.platform.persistence.internal.pe.RevisionsPersistenceModule.initModule(RevisionsPersistenceModule.java:353) ~[platform-persistence.jar:?]
	at $IObjectPersistenceModule_19855a74a75.initModule($IObjectPersistenceModule_19855a74a75.java) ~[?:?]
	at com.polarion.subterra.persistence.internal.PersistenceEngine.initModule(PersistenceEngine.java:251) ~[subterra-uniform-persistence.jar:?]
	at $IPersistenceEngine_19855a74a5d.initModule($IPersistenceEngine_19855a74a5d.java) ~[?:?]
	at com.polarion.platform.persistence.internal.pe.LowLevelDataService.boot(LowLevelDataService.java:352) ~[platform-persistence.jar:?]
	at $ILowLevelPersistence_19855a74982.boot($ILowLevelPersistence_19855a74982.java) ~[?:?]
	at $ILowLevelPersistence_19855a74981.boot($ILowLevelPersistence_19855a74981.java) ~[?:?]
	at com.polarion.psvn.launcher.internal.platform.PlatformService.lambda$0(PlatformService.java:294) ~[launcher.jar:?]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:?]
	at javax.security.auth.Subject.doAs(Subject.java:423) [?:?]
	at com.polarion.platform.internal.security.SubjectNDC.doAs(SubjectNDC.java:69) [platform.jar:?]
	at com.polarion.platform.internal.security.SecurityService.doAsUser(SecurityService.java:417) [platform.jar:?]
	at com.polarion.platform.internal.security.SecurityService.doAsSystemUser(SecurityService.java:407) [platform.jar:?]
	at $ISecurityService_19855a748bd.doAsSystemUser($ISecurityService_19855a748bd.java) [?:?]
	at $ISecurityService_19855a748bc.doAsSystemUser($ISecurityService_19855a748bc.java) [?:?]
	at com.polarion.psvn.launcher.internal.platform.PlatformService.bootPlatform(PlatformService.java:286) [launcher.jar:?]
	at com.polarion.psvn.launcher.internal.platform.PlatformService.start(PlatformService.java:92) [launcher.jar:?]
	at com.polarion.psvn.launcher.PolarionSVNApplication.runImpl(PolarionSVNApplication.java:139) [launcher.jar:?]
	at com.polarion.psvn.launcher.PolarionSVNApplication.run(PolarionSVNApplication.java:94) [launcher.jar:?]
	at com.polarion.core.boot.launchers.BasicAppLauncher.launch(BasicAppLauncher.java:53) [boot.jar:?]
	at com.polarion.core.boot.impl.AppLaunchersManager.start(AppLaunchersManager.java:184) [boot.jar:?]
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:196) [org.eclipse.equinox.app_1.3.500.v20171221-2204.jar:?]
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:134) [org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:104) [org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:388) [org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:243) [org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:566) ~[?:?]
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:656) [org.eclipse.equinox.launcher_1.5.0.v20180512-1130.jar:?]
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:592) [org.eclipse.equinox.launcher_1.5.0.v20180512-1130.jar:?]
	at org.eclipse.equinox.launcher.Main.run(Main.java:1498) [org.eclipse.equinox.launcher_1.5.0.v20180512-1130.jar:?]
	at org.eclipse.equinox.launcher.Main.main(Main.java:1471) [org.eclipse.equinox.launcher_1.5.0.v20180512-1130.jar:?]
Caused by: com.thoughtworks.xstream.converters.ConversionException: 
---- Debugging information ----
cause-exception     : com.thoughtworks.xstream.mapper.CannotResolveClassException
cause-message       : AutoBranchGitLab
class               : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
required-type       : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
converter-type      : com.thoughtworks.xstream.converters.collections.ArrayConverter
version             : 1.4.17
-------------------------------
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convert(TreeUnmarshaller.java:77) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convertAnother(TreeUnmarshaller.java:66) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convertAnother(TreeUnmarshaller.java:50) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.start(TreeUnmarshaller.java:134) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.AbstractTreeMarshallingStrategy.unmarshal(AbstractTreeMarshallingStrategy.java:32) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.unmarshal(XStream.java:1431) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.unmarshal(XStream.java:1411) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.fromXML(XStream.java:1305) ~[xstream-1.4.17.jar:1.4.17]
	at com.polarion.platform.repository.external.internal.RepositoriesDataHandler.processData(RepositoriesDataHandler.java:119) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.spi.config.AbstractDataHandler.readLocation(AbstractDataHandler.java:83) ~[platform-repository.jar:?]
	... 56 more
Caused by: com.thoughtworks.xstream.mapper.CannotResolveClassException: AutoBranchGitLab
	at com.thoughtworks.xstream.mapper.DefaultMapper.realClass(DefaultMapper.java:81) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.DynamicProxyMapper.realClass(DynamicProxyMapper.java:55) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.PackageAliasingMapper.realClass(PackageAliasingMapper.java:88) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.ClassAliasingMapper.realClass(ClassAliasingMapper.java:79) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.ArrayMapper.realClass(ArrayMapper.java:74) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.SecurityMapper.realClass(SecurityMapper.java:71) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.CachingMapper.realClass(CachingMapper.java:47) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.util.HierarchicalStreams.readClassType(HierarchicalStreams.java:29) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.converters.collections.AbstractCollectionConverter.readBareItem(AbstractCollectionConverter.java:131) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.converters.collections.AbstractCollectionConverter.readItem(AbstractCollectionConverter.java:117) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.converters.collections.AbstractCollectionConverter.readCompleteItem(AbstractCollectionConverter.java:147) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.converters.collections.ArrayConverter.unmarshal(ArrayConverter.java:54) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convert(TreeUnmarshaller.java:72) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convertAnother(TreeUnmarshaller.java:66) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convertAnother(TreeUnmarshaller.java:50) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.start(TreeUnmarshaller.java:134) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.AbstractTreeMarshallingStrategy.unmarshal(AbstractTreeMarshallingStrategy.java:32) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.unmarshal(XStream.java:1431) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.unmarshal(XStream.java:1411) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.fromXML(XStream.java:1305) ~[xstream-1.4.17.jar:1.4.17]
	at com.polarion.platform.repository.external.internal.RepositoriesDataHandler.processData(RepositoriesDataHandler.java:119) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.spi.config.AbstractDataHandler.readLocation(AbstractDataHandler.java:83) ~[platform-repository.jar:?]
	... 56 more
2025-07-29 18:08:09,484 [Catalina-utility-2] FATAL org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/polarion/oauth-feishu] - For security constraints with URL pattern [/userinfo] only the HTTP methods [POST GET] are covered. All other methods are uncovered.
2025-07-29 18:33:40,526 [Catalina-utility-2] FATAL org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/polarion/oauth-feishu] - Servlet [FeishuUserInfoServlet] in web application [/polarion/oauth-feishu] threw load() exception
java.lang.ClassNotFoundException: com.fasnote.alm.auth.feishu.FeishuUserInfoServlet
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1407) ~[catalina.jar:9.0.53]
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1215) ~[catalina.jar:9.0.53]
	at org.apache.catalina.core.DefaultInstanceManager.loadClass(DefaultInstanceManager.java:538) ~[catalina.jar:9.0.53]
	at org.apache.catalina.core.DefaultInstanceManager.loadClassMaybePrivileged(DefaultInstanceManager.java:519) ~[catalina.jar:9.0.53]
	at org.apache.catalina.core.DefaultInstanceManager.newInstance(DefaultInstanceManager.java:149) ~[catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1049) ~[catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:989) ~[catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardContext.loadOnStartup(StandardContext.java:4957) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5264) [catalina.jar:9.0.53]
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1396) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1386) [catalina.jar:9.0.53]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628) [?:?]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-util.jar:9.0.53]
	at java.lang.Thread.run(Thread.java:829) [?:?]
2025-07-29 18:33:40,684 [ajp-nio-127.0.0.1-8889-exec-1 | cID:55bebdf6-c0a8d700-1acc4c5a-87fccb9b] FATAL org.apache.catalina.core.ContainerBase.[Tomcat].[localhost] - Exception Processing /polarion/svnwebclient/fileDownload.jsp
org.apache.hivemind.ApplicationRuntimeException: Unable to construct service com.polarion.platform.authenticatorProviderManager: Unable to construct service com.fasnote.alm.auth.feishu.feishuAuthenticatorManagerInterceptorFactory: Error building service com.fasnote.alm.auth.feishu.feishuAuthenticatorManagerInterceptorFactory: Unable to convert type 'com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory' to a Java class, either as is, or in package com.fasnote.alm.auth.feishu.
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructNewServiceImplementation(AbstractServiceModelImpl.java:163) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructServiceImplementation(AbstractServiceModelImpl.java:141) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.SingletonServiceModel.getActualServiceImplementation(SingletonServiceModel.java:69) ~[hivemind-1.1.1.jar:?]
	at $IAuthenticatorManager_19855a74bbc._service($IAuthenticatorManager_19855a74bbc.java) ~[?:?]
	at $IAuthenticatorManager_19855a74bbc.getAuthenticators($IAuthenticatorManager_19855a74bbc.java) ~[?:?]
	at $IAuthenticatorManager_19855a74bbb.getAuthenticators($IAuthenticatorManager_19855a74bbb.java) ~[?:?]
	at com.polarion.platform.security.auth.impl.ContributedPolarionAuthenticator.isExpectedCallback(ContributedPolarionAuthenticator.java:33) ~[platform.jar:?]
	at com.polarion.platform.security.auth.PolarionAuthenticator.authenticateImpl(PolarionAuthenticator.java:127) ~[platform.jar:?]
	at com.polarion.platform.security.auth.PolarionAuthenticator.doAuthenticate(PolarionAuthenticator.java:105) ~[platform.jar:?]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:624) ~[catalina.jar:9.0.53]
	at com.polarion.platform.security.auth.PolarionAuthenticator.invokeInternal(PolarionAuthenticator.java:248) ~[platform.jar:?]
	at com.polarion.platform.security.auth.PolarionAuthenticator.invoke(PolarionAuthenticator.java:242) ~[platform.jar:?]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) [catalina.jar:9.0.53]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.SingleSignOn.invoke(SingleSignOn.java:312) [catalina.jar:9.0.53]
	at com.polarion.platform.session.PolarionLocalSingleSignOn.invoke(PolarionLocalSingleSignOn.java:164) [platform.jar:?]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) [catalina.jar:9.0.53]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357) [catalina.jar:9.0.53]
	at org.apache.coyote.ajp.AjpProcessor.service(AjpProcessor.java:433) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1726) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-util.jar:9.0.53]
	at java.lang.Thread.run(Thread.java:829) [?:?]
Caused by: org.apache.hivemind.ApplicationRuntimeException: Unable to construct service com.fasnote.alm.auth.feishu.feishuAuthenticatorManagerInterceptorFactory: Error building service com.fasnote.alm.auth.feishu.feishuAuthenticatorManagerInterceptorFactory: Unable to convert type 'com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory' to a Java class, either as is, or in package com.fasnote.alm.auth.feishu.
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructNewServiceImplementation(AbstractServiceModelImpl.java:163) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructServiceImplementation(AbstractServiceModelImpl.java:141) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.SingletonServiceModel.getActualServiceImplementation(SingletonServiceModel.java:69) ~[hivemind-1.1.1.jar:?]
	at $ServiceInterceptorFactory_19855a74bce._service($ServiceInterceptorFactory_19855a74bce.java) ~[?:?]
	at $ServiceInterceptorFactory_19855a74bce.createInterceptor($ServiceInterceptorFactory_19855a74bce.java) ~[?:?]
	at $ServiceInterceptorFactory_19855a74bcc.createInterceptor($ServiceInterceptorFactory_19855a74bcc.java) ~[?:?]
	at org.apache.hivemind.impl.ServiceInterceptorContributionImpl.createInterceptor(ServiceInterceptorContributionImpl.java:95) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.InterceptorStackImpl.process(InterceptorStackImpl.java:116) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.addInterceptors(AbstractServiceModelImpl.java:85) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructNewServiceImplementation(AbstractServiceModelImpl.java:159) ~[polarion-hivemind.jar:?]
	... 26 more
Caused by: org.apache.hivemind.ApplicationRuntimeException: Error building service com.fasnote.alm.auth.feishu.feishuAuthenticatorManagerInterceptorFactory: Unable to convert type 'com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory' to a Java class, either as is, or in package com.fasnote.alm.auth.feishu.
	at org.apache.hivemind.impl.InvokeFactoryServiceConstructor.constructCoreServiceImplementation(InvokeFactoryServiceConstructor.java:66) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructCoreServiceImplementation(AbstractServiceModelImpl.java:108) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructNewServiceImplementation(AbstractServiceModelImpl.java:157) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructServiceImplementation(AbstractServiceModelImpl.java:141) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.SingletonServiceModel.getActualServiceImplementation(SingletonServiceModel.java:69) ~[hivemind-1.1.1.jar:?]
	at $ServiceInterceptorFactory_19855a74bce._service($ServiceInterceptorFactory_19855a74bce.java) ~[?:?]
	at $ServiceInterceptorFactory_19855a74bce.createInterceptor($ServiceInterceptorFactory_19855a74bce.java) ~[?:?]
	at $ServiceInterceptorFactory_19855a74bcc.createInterceptor($ServiceInterceptorFactory_19855a74bcc.java) ~[?:?]
	at org.apache.hivemind.impl.ServiceInterceptorContributionImpl.createInterceptor(ServiceInterceptorContributionImpl.java:95) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.InterceptorStackImpl.process(InterceptorStackImpl.java:116) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.addInterceptors(AbstractServiceModelImpl.java:85) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructNewServiceImplementation(AbstractServiceModelImpl.java:159) ~[polarion-hivemind.jar:?]
	... 26 more
Caused by: org.apache.hivemind.ApplicationRuntimeException: Error building service com.fasnote.alm.auth.feishu.feishuAuthenticatorManagerInterceptorFactory: Unable to convert type 'com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory' to a Java class, either as is, or in package com.fasnote.alm.auth.feishu.
	at org.apache.hivemind.service.impl.BuilderFactoryLogic.createService(BuilderFactoryLogic.java:87) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.service.impl.BuilderFactory.createCoreServiceImplementation(BuilderFactory.java:42) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.InvokeFactoryServiceConstructor.constructCoreServiceImplementation(InvokeFactoryServiceConstructor.java:62) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructCoreServiceImplementation(AbstractServiceModelImpl.java:108) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructNewServiceImplementation(AbstractServiceModelImpl.java:157) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructServiceImplementation(AbstractServiceModelImpl.java:141) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.SingletonServiceModel.getActualServiceImplementation(SingletonServiceModel.java:69) ~[hivemind-1.1.1.jar:?]
	at $ServiceInterceptorFactory_19855a74bce._service($ServiceInterceptorFactory_19855a74bce.java) ~[?:?]
	at $ServiceInterceptorFactory_19855a74bce.createInterceptor($ServiceInterceptorFactory_19855a74bce.java) ~[?:?]
	at $ServiceInterceptorFactory_19855a74bcc.createInterceptor($ServiceInterceptorFactory_19855a74bcc.java) ~[?:?]
	at org.apache.hivemind.impl.ServiceInterceptorContributionImpl.createInterceptor(ServiceInterceptorContributionImpl.java:95) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.InterceptorStackImpl.process(InterceptorStackImpl.java:116) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.addInterceptors(AbstractServiceModelImpl.java:85) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructNewServiceImplementation(AbstractServiceModelImpl.java:159) ~[polarion-hivemind.jar:?]
	... 26 more
Caused by: org.apache.hivemind.ApplicationRuntimeException: Unable to convert type 'com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory' to a Java class, either as is, or in package com.fasnote.alm.auth.feishu.
	at org.apache.hivemind.impl.ModuleImpl.findTypeInClassResolver(ModuleImpl.java:219) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.ModuleImpl.resolveType(ModuleImpl.java:203) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.service.impl.BuilderFactoryLogic.instantiateCoreServiceInstance(BuilderFactoryLogic.java:100) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.service.impl.BuilderFactoryLogic.createService(BuilderFactoryLogic.java:75) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.service.impl.BuilderFactory.createCoreServiceImplementation(BuilderFactory.java:42) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.InvokeFactoryServiceConstructor.constructCoreServiceImplementation(InvokeFactoryServiceConstructor.java:62) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructCoreServiceImplementation(AbstractServiceModelImpl.java:108) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructNewServiceImplementation(AbstractServiceModelImpl.java:157) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructServiceImplementation(AbstractServiceModelImpl.java:141) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.SingletonServiceModel.getActualServiceImplementation(SingletonServiceModel.java:69) ~[hivemind-1.1.1.jar:?]
	at $ServiceInterceptorFactory_19855a74bce._service($ServiceInterceptorFactory_19855a74bce.java) ~[?:?]
	at $ServiceInterceptorFactory_19855a74bce.createInterceptor($ServiceInterceptorFactory_19855a74bce.java) ~[?:?]
	at $ServiceInterceptorFactory_19855a74bcc.createInterceptor($ServiceInterceptorFactory_19855a74bcc.java) ~[?:?]
	at org.apache.hivemind.impl.ServiceInterceptorContributionImpl.createInterceptor(ServiceInterceptorContributionImpl.java:95) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.InterceptorStackImpl.process(InterceptorStackImpl.java:116) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.addInterceptors(AbstractServiceModelImpl.java:85) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructNewServiceImplementation(AbstractServiceModelImpl.java:159) ~[polarion-hivemind.jar:?]
	... 26 more
2025-07-29 18:33:47,099 [ajp-nio-127.0.0.1-8889-exec-4 | cID:55bed756-c0a8d700-1acc4c5a-b849dda3] FATAL org.apache.catalina.core.ContainerBase.[Tomcat].[localhost] - Exception Processing /polarion/svnwebclient/
org.apache.hivemind.ApplicationRuntimeException: Unable to construct service com.polarion.platform.authenticatorProviderManager: Unable to construct service com.fasnote.alm.auth.feishu.feishuAuthenticatorManagerInterceptorFactory: Error building service com.fasnote.alm.auth.feishu.feishuAuthenticatorManagerInterceptorFactory: Unable to convert type 'com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory' to a Java class, either as is, or in package com.fasnote.alm.auth.feishu.
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructNewServiceImplementation(AbstractServiceModelImpl.java:163) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructServiceImplementation(AbstractServiceModelImpl.java:141) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.SingletonServiceModel.getActualServiceImplementation(SingletonServiceModel.java:69) ~[hivemind-1.1.1.jar:?]
	at $IAuthenticatorManager_19855a74bbc._service($IAuthenticatorManager_19855a74bbc.java) ~[?:?]
	at $IAuthenticatorManager_19855a74bbc.getAuthenticators($IAuthenticatorManager_19855a74bbc.java) ~[?:?]
	at $IAuthenticatorManager_19855a74bbb.getAuthenticators($IAuthenticatorManager_19855a74bbb.java) ~[?:?]
	at com.polarion.platform.security.auth.impl.ContributedPolarionAuthenticator.isExpectedCallback(ContributedPolarionAuthenticator.java:33) ~[platform.jar:?]
	at com.polarion.platform.security.auth.PolarionAuthenticator.authenticateImpl(PolarionAuthenticator.java:127) ~[platform.jar:?]
	at com.polarion.platform.security.auth.PolarionAuthenticator.doAuthenticate(PolarionAuthenticator.java:105) ~[platform.jar:?]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:624) ~[catalina.jar:9.0.53]
	at com.polarion.platform.security.auth.PolarionAuthenticator.invokeInternal(PolarionAuthenticator.java:248) ~[platform.jar:?]
	at com.polarion.platform.security.auth.PolarionAuthenticator.invoke(PolarionAuthenticator.java:242) ~[platform.jar:?]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) [catalina.jar:9.0.53]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.SingleSignOn.invoke(SingleSignOn.java:312) [catalina.jar:9.0.53]
	at com.polarion.platform.session.PolarionLocalSingleSignOn.invoke(PolarionLocalSingleSignOn.java:164) [platform.jar:?]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) [catalina.jar:9.0.53]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357) [catalina.jar:9.0.53]
	at org.apache.coyote.ajp.AjpProcessor.service(AjpProcessor.java:433) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1726) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-util.jar:9.0.53]
	at java.lang.Thread.run(Thread.java:829) [?:?]
Caused by: org.apache.hivemind.ApplicationRuntimeException: Unable to construct service com.fasnote.alm.auth.feishu.feishuAuthenticatorManagerInterceptorFactory: Error building service com.fasnote.alm.auth.feishu.feishuAuthenticatorManagerInterceptorFactory: Unable to convert type 'com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory' to a Java class, either as is, or in package com.fasnote.alm.auth.feishu.
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructNewServiceImplementation(AbstractServiceModelImpl.java:163) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructServiceImplementation(AbstractServiceModelImpl.java:141) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.SingletonServiceModel.getActualServiceImplementation(SingletonServiceModel.java:69) ~[hivemind-1.1.1.jar:?]
	at $ServiceInterceptorFactory_19855a74bce._service($ServiceInterceptorFactory_19855a74bce.java) ~[?:?]
	at $ServiceInterceptorFactory_19855a74bce.createInterceptor($ServiceInterceptorFactory_19855a74bce.java) ~[?:?]
	at $ServiceInterceptorFactory_19855a74bcc.createInterceptor($ServiceInterceptorFactory_19855a74bcc.java) ~[?:?]
	at org.apache.hivemind.impl.ServiceInterceptorContributionImpl.createInterceptor(ServiceInterceptorContributionImpl.java:95) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.InterceptorStackImpl.process(InterceptorStackImpl.java:116) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.addInterceptors(AbstractServiceModelImpl.java:85) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructNewServiceImplementation(AbstractServiceModelImpl.java:159) ~[polarion-hivemind.jar:?]
	... 26 more
Caused by: org.apache.hivemind.ApplicationRuntimeException: Error building service com.fasnote.alm.auth.feishu.feishuAuthenticatorManagerInterceptorFactory: Unable to convert type 'com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory' to a Java class, either as is, or in package com.fasnote.alm.auth.feishu.
	at org.apache.hivemind.impl.InvokeFactoryServiceConstructor.constructCoreServiceImplementation(InvokeFactoryServiceConstructor.java:66) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructCoreServiceImplementation(AbstractServiceModelImpl.java:108) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructNewServiceImplementation(AbstractServiceModelImpl.java:157) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructServiceImplementation(AbstractServiceModelImpl.java:141) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.SingletonServiceModel.getActualServiceImplementation(SingletonServiceModel.java:69) ~[hivemind-1.1.1.jar:?]
	at $ServiceInterceptorFactory_19855a74bce._service($ServiceInterceptorFactory_19855a74bce.java) ~[?:?]
	at $ServiceInterceptorFactory_19855a74bce.createInterceptor($ServiceInterceptorFactory_19855a74bce.java) ~[?:?]
	at $ServiceInterceptorFactory_19855a74bcc.createInterceptor($ServiceInterceptorFactory_19855a74bcc.java) ~[?:?]
	at org.apache.hivemind.impl.ServiceInterceptorContributionImpl.createInterceptor(ServiceInterceptorContributionImpl.java:95) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.InterceptorStackImpl.process(InterceptorStackImpl.java:116) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.addInterceptors(AbstractServiceModelImpl.java:85) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructNewServiceImplementation(AbstractServiceModelImpl.java:159) ~[polarion-hivemind.jar:?]
	... 26 more
Caused by: org.apache.hivemind.ApplicationRuntimeException: Error building service com.fasnote.alm.auth.feishu.feishuAuthenticatorManagerInterceptorFactory: Unable to convert type 'com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory' to a Java class, either as is, or in package com.fasnote.alm.auth.feishu.
	at org.apache.hivemind.service.impl.BuilderFactoryLogic.createService(BuilderFactoryLogic.java:87) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.service.impl.BuilderFactory.createCoreServiceImplementation(BuilderFactory.java:42) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.InvokeFactoryServiceConstructor.constructCoreServiceImplementation(InvokeFactoryServiceConstructor.java:62) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructCoreServiceImplementation(AbstractServiceModelImpl.java:108) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructNewServiceImplementation(AbstractServiceModelImpl.java:157) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructServiceImplementation(AbstractServiceModelImpl.java:141) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.SingletonServiceModel.getActualServiceImplementation(SingletonServiceModel.java:69) ~[hivemind-1.1.1.jar:?]
	at $ServiceInterceptorFactory_19855a74bce._service($ServiceInterceptorFactory_19855a74bce.java) ~[?:?]
	at $ServiceInterceptorFactory_19855a74bce.createInterceptor($ServiceInterceptorFactory_19855a74bce.java) ~[?:?]
	at $ServiceInterceptorFactory_19855a74bcc.createInterceptor($ServiceInterceptorFactory_19855a74bcc.java) ~[?:?]
	at org.apache.hivemind.impl.ServiceInterceptorContributionImpl.createInterceptor(ServiceInterceptorContributionImpl.java:95) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.InterceptorStackImpl.process(InterceptorStackImpl.java:116) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.addInterceptors(AbstractServiceModelImpl.java:85) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructNewServiceImplementation(AbstractServiceModelImpl.java:159) ~[polarion-hivemind.jar:?]
	... 26 more
Caused by: org.apache.hivemind.ApplicationRuntimeException: Unable to convert type 'com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory' to a Java class, either as is, or in package com.fasnote.alm.auth.feishu.
	at org.apache.hivemind.impl.ModuleImpl.findTypeInClassResolver(ModuleImpl.java:219) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.ModuleImpl.resolveType(ModuleImpl.java:203) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.service.impl.BuilderFactoryLogic.instantiateCoreServiceInstance(BuilderFactoryLogic.java:100) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.service.impl.BuilderFactoryLogic.createService(BuilderFactoryLogic.java:75) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.service.impl.BuilderFactory.createCoreServiceImplementation(BuilderFactory.java:42) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.InvokeFactoryServiceConstructor.constructCoreServiceImplementation(InvokeFactoryServiceConstructor.java:62) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructCoreServiceImplementation(AbstractServiceModelImpl.java:108) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructNewServiceImplementation(AbstractServiceModelImpl.java:157) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructServiceImplementation(AbstractServiceModelImpl.java:141) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.SingletonServiceModel.getActualServiceImplementation(SingletonServiceModel.java:69) ~[hivemind-1.1.1.jar:?]
	at $ServiceInterceptorFactory_19855a74bce._service($ServiceInterceptorFactory_19855a74bce.java) ~[?:?]
	at $ServiceInterceptorFactory_19855a74bce.createInterceptor($ServiceInterceptorFactory_19855a74bce.java) ~[?:?]
	at $ServiceInterceptorFactory_19855a74bcc.createInterceptor($ServiceInterceptorFactory_19855a74bcc.java) ~[?:?]
	at org.apache.hivemind.impl.ServiceInterceptorContributionImpl.createInterceptor(ServiceInterceptorContributionImpl.java:95) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.InterceptorStackImpl.process(InterceptorStackImpl.java:116) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.addInterceptors(AbstractServiceModelImpl.java:85) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructNewServiceImplementation(AbstractServiceModelImpl.java:159) ~[polarion-hivemind.jar:?]
	... 26 more
2025-07-29 18:33:49,722 [ajp-nio-127.0.0.1-8889-exec-6 | cID:55bee197-c0a8d700-1acc4c5a-b5da5f3b] FATAL org.apache.catalina.core.ContainerBase.[Tomcat].[localhost] - Exception Processing /polarion/svnwebclient/
org.apache.hivemind.ApplicationRuntimeException: Unable to construct service com.polarion.platform.authenticatorProviderManager: Unable to construct service com.fasnote.alm.auth.feishu.feishuAuthenticatorManagerInterceptorFactory: Error building service com.fasnote.alm.auth.feishu.feishuAuthenticatorManagerInterceptorFactory: Unable to convert type 'com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory' to a Java class, either as is, or in package com.fasnote.alm.auth.feishu.
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructNewServiceImplementation(AbstractServiceModelImpl.java:163) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructServiceImplementation(AbstractServiceModelImpl.java:141) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.SingletonServiceModel.getActualServiceImplementation(SingletonServiceModel.java:69) ~[hivemind-1.1.1.jar:?]
	at $IAuthenticatorManager_19855a74bbc._service($IAuthenticatorManager_19855a74bbc.java) ~[?:?]
	at $IAuthenticatorManager_19855a74bbc.getAuthenticators($IAuthenticatorManager_19855a74bbc.java) ~[?:?]
	at $IAuthenticatorManager_19855a74bbb.getAuthenticators($IAuthenticatorManager_19855a74bbb.java) ~[?:?]
	at com.polarion.platform.security.auth.impl.ContributedPolarionAuthenticator.isExpectedCallback(ContributedPolarionAuthenticator.java:33) ~[platform.jar:?]
	at com.polarion.platform.security.auth.PolarionAuthenticator.authenticateImpl(PolarionAuthenticator.java:127) ~[platform.jar:?]
	at com.polarion.platform.security.auth.PolarionAuthenticator.doAuthenticate(PolarionAuthenticator.java:105) ~[platform.jar:?]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:624) ~[catalina.jar:9.0.53]
	at com.polarion.platform.security.auth.PolarionAuthenticator.invokeInternal(PolarionAuthenticator.java:248) ~[platform.jar:?]
	at com.polarion.platform.security.auth.PolarionAuthenticator.invoke(PolarionAuthenticator.java:242) ~[platform.jar:?]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) [catalina.jar:9.0.53]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.SingleSignOn.invoke(SingleSignOn.java:312) [catalina.jar:9.0.53]
	at com.polarion.platform.session.PolarionLocalSingleSignOn.invoke(PolarionLocalSingleSignOn.java:164) [platform.jar:?]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) [catalina.jar:9.0.53]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357) [catalina.jar:9.0.53]
	at org.apache.coyote.ajp.AjpProcessor.service(AjpProcessor.java:433) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1726) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-util.jar:9.0.53]
	at java.lang.Thread.run(Thread.java:829) [?:?]
Caused by: org.apache.hivemind.ApplicationRuntimeException: Unable to construct service com.fasnote.alm.auth.feishu.feishuAuthenticatorManagerInterceptorFactory: Error building service com.fasnote.alm.auth.feishu.feishuAuthenticatorManagerInterceptorFactory: Unable to convert type 'com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory' to a Java class, either as is, or in package com.fasnote.alm.auth.feishu.
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructNewServiceImplementation(AbstractServiceModelImpl.java:163) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructServiceImplementation(AbstractServiceModelImpl.java:141) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.SingletonServiceModel.getActualServiceImplementation(SingletonServiceModel.java:69) ~[hivemind-1.1.1.jar:?]
	at $ServiceInterceptorFactory_19855a74bce._service($ServiceInterceptorFactory_19855a74bce.java) ~[?:?]
	at $ServiceInterceptorFactory_19855a74bce.createInterceptor($ServiceInterceptorFactory_19855a74bce.java) ~[?:?]
	at $ServiceInterceptorFactory_19855a74bcc.createInterceptor($ServiceInterceptorFactory_19855a74bcc.java) ~[?:?]
	at org.apache.hivemind.impl.ServiceInterceptorContributionImpl.createInterceptor(ServiceInterceptorContributionImpl.java:95) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.InterceptorStackImpl.process(InterceptorStackImpl.java:116) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.addInterceptors(AbstractServiceModelImpl.java:85) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructNewServiceImplementation(AbstractServiceModelImpl.java:159) ~[polarion-hivemind.jar:?]
	... 26 more
Caused by: org.apache.hivemind.ApplicationRuntimeException: Error building service com.fasnote.alm.auth.feishu.feishuAuthenticatorManagerInterceptorFactory: Unable to convert type 'com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory' to a Java class, either as is, or in package com.fasnote.alm.auth.feishu.
	at org.apache.hivemind.impl.InvokeFactoryServiceConstructor.constructCoreServiceImplementation(InvokeFactoryServiceConstructor.java:66) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructCoreServiceImplementation(AbstractServiceModelImpl.java:108) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructNewServiceImplementation(AbstractServiceModelImpl.java:157) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructServiceImplementation(AbstractServiceModelImpl.java:141) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.SingletonServiceModel.getActualServiceImplementation(SingletonServiceModel.java:69) ~[hivemind-1.1.1.jar:?]
	at $ServiceInterceptorFactory_19855a74bce._service($ServiceInterceptorFactory_19855a74bce.java) ~[?:?]
	at $ServiceInterceptorFactory_19855a74bce.createInterceptor($ServiceInterceptorFactory_19855a74bce.java) ~[?:?]
	at $ServiceInterceptorFactory_19855a74bcc.createInterceptor($ServiceInterceptorFactory_19855a74bcc.java) ~[?:?]
	at org.apache.hivemind.impl.ServiceInterceptorContributionImpl.createInterceptor(ServiceInterceptorContributionImpl.java:95) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.InterceptorStackImpl.process(InterceptorStackImpl.java:116) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.addInterceptors(AbstractServiceModelImpl.java:85) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructNewServiceImplementation(AbstractServiceModelImpl.java:159) ~[polarion-hivemind.jar:?]
	... 26 more
Caused by: org.apache.hivemind.ApplicationRuntimeException: Error building service com.fasnote.alm.auth.feishu.feishuAuthenticatorManagerInterceptorFactory: Unable to convert type 'com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory' to a Java class, either as is, or in package com.fasnote.alm.auth.feishu.
	at org.apache.hivemind.service.impl.BuilderFactoryLogic.createService(BuilderFactoryLogic.java:87) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.service.impl.BuilderFactory.createCoreServiceImplementation(BuilderFactory.java:42) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.InvokeFactoryServiceConstructor.constructCoreServiceImplementation(InvokeFactoryServiceConstructor.java:62) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructCoreServiceImplementation(AbstractServiceModelImpl.java:108) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructNewServiceImplementation(AbstractServiceModelImpl.java:157) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructServiceImplementation(AbstractServiceModelImpl.java:141) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.SingletonServiceModel.getActualServiceImplementation(SingletonServiceModel.java:69) ~[hivemind-1.1.1.jar:?]
	at $ServiceInterceptorFactory_19855a74bce._service($ServiceInterceptorFactory_19855a74bce.java) ~[?:?]
	at $ServiceInterceptorFactory_19855a74bce.createInterceptor($ServiceInterceptorFactory_19855a74bce.java) ~[?:?]
	at $ServiceInterceptorFactory_19855a74bcc.createInterceptor($ServiceInterceptorFactory_19855a74bcc.java) ~[?:?]
	at org.apache.hivemind.impl.ServiceInterceptorContributionImpl.createInterceptor(ServiceInterceptorContributionImpl.java:95) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.InterceptorStackImpl.process(InterceptorStackImpl.java:116) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.addInterceptors(AbstractServiceModelImpl.java:85) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructNewServiceImplementation(AbstractServiceModelImpl.java:159) ~[polarion-hivemind.jar:?]
	... 26 more
Caused by: org.apache.hivemind.ApplicationRuntimeException: Unable to convert type 'com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory' to a Java class, either as is, or in package com.fasnote.alm.auth.feishu.
	at org.apache.hivemind.impl.ModuleImpl.findTypeInClassResolver(ModuleImpl.java:219) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.ModuleImpl.resolveType(ModuleImpl.java:203) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.service.impl.BuilderFactoryLogic.instantiateCoreServiceInstance(BuilderFactoryLogic.java:100) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.service.impl.BuilderFactoryLogic.createService(BuilderFactoryLogic.java:75) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.service.impl.BuilderFactory.createCoreServiceImplementation(BuilderFactory.java:42) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.InvokeFactoryServiceConstructor.constructCoreServiceImplementation(InvokeFactoryServiceConstructor.java:62) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructCoreServiceImplementation(AbstractServiceModelImpl.java:108) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructNewServiceImplementation(AbstractServiceModelImpl.java:157) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructServiceImplementation(AbstractServiceModelImpl.java:141) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.SingletonServiceModel.getActualServiceImplementation(SingletonServiceModel.java:69) ~[hivemind-1.1.1.jar:?]
	at $ServiceInterceptorFactory_19855a74bce._service($ServiceInterceptorFactory_19855a74bce.java) ~[?:?]
	at $ServiceInterceptorFactory_19855a74bce.createInterceptor($ServiceInterceptorFactory_19855a74bce.java) ~[?:?]
	at $ServiceInterceptorFactory_19855a74bcc.createInterceptor($ServiceInterceptorFactory_19855a74bcc.java) ~[?:?]
	at org.apache.hivemind.impl.ServiceInterceptorContributionImpl.createInterceptor(ServiceInterceptorContributionImpl.java:95) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.InterceptorStackImpl.process(InterceptorStackImpl.java:116) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.addInterceptors(AbstractServiceModelImpl.java:85) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructNewServiceImplementation(AbstractServiceModelImpl.java:159) ~[polarion-hivemind.jar:?]
	... 26 more
