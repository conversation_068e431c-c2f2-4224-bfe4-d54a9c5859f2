2025-07-29 18:56:40,194 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 18:56:40,195 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-29 18:56:40,195 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 18:56:40,195 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-29 18:56:40,195 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-29 18:56:40,195 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:56:40,195 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-29 18:56:44,303 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-29 18:56:44,440 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.137 s. ]
2025-07-29 18:56:44,440 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-29 18:56:44,486 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0453 s. ]
2025-07-29 18:56:44,545 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-29 18:56:44,664 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 107 s. ]
2025-07-29 18:56:44,870 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.68 s. ]
2025-07-29 18:56:44,950 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:56:44,950 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-29 18:56:44,978 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.11 s. ]
2025-07-29 18:56:44,978 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:56:44,978 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-29 18:56:44,982 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (1/9)
2025-07-29 18:56:44,982 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (3/9)
2025-07-29 18:56:44,982 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (4/9)
2025-07-29 18:56:44,982 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (5/9)
2025-07-29 18:56:44,982 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (2/9)
2025-07-29 18:56:44,983 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (6/9)
2025-07-29 18:56:44,988 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-07-29 18:56:45,111 [LowLevelDataService-contextInitializer-4 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-29 18:56:45,200 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-29 18:56:45,648 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.67 s. ]
2025-07-29 18:56:45,659 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:56:45,659 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-29 18:56:45,862 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-29 18:56:45,872 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.22 s. ]
2025-07-29 18:56:45,895 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:56:45,895 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-29 18:56:45,898 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-29 18:56:45,942 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-29 18:56:45,980 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-07-29 18:56:46,000 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (4/9)
2025-07-29 18:56:46,016 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (5/9)
2025-07-29 18:56:46,041 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (6/9)
2025-07-29 18:56:46,065 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-07-29 18:56:46,089 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-29 18:56:46,114 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-29 18:56:46,114 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.24 s. ]
2025-07-29 18:56:46,114 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:56:46,114 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-29 18:56:46,127 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-07-29 18:56:46,127 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:56:46,127 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-29 18:56:46,224 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-29 18:56:46,227 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-29 18:56:46,344 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.22 s. ]
2025-07-29 18:56:46,344 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:56:46,344 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-29 18:56:46,354 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-29 18:56:46,355 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:56:46,355 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-29 18:57:16,492 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 30.14 s. ]
2025-07-29 18:57:16,493 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 18:57:16,493 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 36.3 s. ]
2025-07-29 18:57:16,493 [main] INFO  com.polarion.platform.startup - ****************************************************************
