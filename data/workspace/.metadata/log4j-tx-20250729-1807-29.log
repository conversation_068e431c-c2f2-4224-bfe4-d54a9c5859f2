2025-07-29 18:07:33,975 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0392 s [64% update (144x), 36% query (12x)] (221x), svn: 0.0127 s [59% getLatestRevision (2x), 33% testConnection (1x)] (4x)
2025-07-29 18:07:34,076 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0252 s [54% getDir2 content (2x), 34% info (3x)] (6x)
2025-07-29 18:07:34,798 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.718 s, CPU [user: 0.22 s, system: 0.317 s], Allocated memory: 68.4 MB, transactions: 0, ObjectMaps: 0.123 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-29 18:07:34,798 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.72 s, CPU [user: 0.183 s, system: 0.26 s], Allocated memory: 53.1 MB, transactions: 0, ObjectMaps: 0.113 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-29 18:07:34,799 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.719 s, CPU [user: 0.0712 s, system: 0.0978 s], Allocated memory: 11.0 MB, transactions: 0, svn: 0.108 s [84% log2 (10x)] (13x)
2025-07-29 18:07:34,799 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.72 s, CPU [user: 0.111 s, system: 0.193 s], Allocated memory: 24.1 MB, transactions: 0, svn: 0.0834 s [71% log2 (5x), 20% getLatestRevision (1x)] (7x), ObjectMaps: 0.069 s [100% getAllPrimaryObjects (1x)] (10x)
2025-07-29 18:07:34,799 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.719 s, CPU [user: 0.0612 s, system: 0.0851 s], Allocated memory: 9.2 MB, transactions: 0, svn: 0.101 s [45% log2 (5x), 19% info (5x), 16% log (1x), 13% getLatestRevision (2x)] (18x), ObjectMaps: 0.0694 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-29 18:07:34,798 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.719 s, CPU [user: 0.0784 s, system: 0.132 s], Allocated memory: 12.1 MB, transactions: 0, svn: 0.137 s [85% log2 (10x)] (13x), ObjectMaps: 0.0718 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-29 18:07:34,799 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.472 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.468 s [71% log2 (36x), 14% getLatestRevision (9x)] (61x)
2025-07-29 18:07:35,044 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.21 s [100% getReadConfiguration (48x)] (48x), svn: 0.0755 s [84% info (18x)] (38x)
2025-07-29 18:07:35,388 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.277 s [76% info (94x), 17% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.207 s [100% getReadConfiguration (54x)] (54x)
2025-07-29 18:07:35,609 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.106 s, CPU [user: 0.026 s, system: 0.00838 s], Allocated memory: 10.6 MB
2025-07-29 18:07:35,665 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.263 s [100% doFinishStartup (1x)] (1x), commit: 0.0731 s [100% Revision (1x)] (1x), Lucene: 0.0384 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.0146 s [100% objectsToInv (1x)] (1x)
2025-07-29 18:07:38,145 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.297 s [90% info (158x)] (168x)
2025-07-29 18:07:39,065 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.842 s, CPU [user: 0.00648 s, system: 0.00119 s], Allocated memory: 531.2 kB, transactions: 1
2025-07-29 18:07:39,066 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 28, Incremental Baseline: 0.0288 s [100% WorkItem (21x)] (21x), notification worker: 0.023 s [59% RevisionActivityCreator (2x), 17% WorkItemActivityCreator (1x), 16% PlanActivityCreator (1x)] (6x), Lucene: 0.0181 s [77% add (1x), 23% refresh (1x)] (2x), resolve: 0.0168 s [61% User (1x), 39% Revision (2x)] (3x), persistence listener: 0.0139 s [76% indexRefreshPersistenceListener (1x), 15% WorkItemActivityCreator (1x)] (7x), PullingJob: 0.00678 s [100% collectChanges (1x)] (1x), svn: 0.00667 s [55% testConnection (1x), 45% getLatestRevision (1x)] (2x), Full Baseline: 0.00524 s [100% WorkItem (1x)] (1x), ObjectMaps: 0.00208 s [100% getPrimaryObjectLocation (1x)] (1x)
2025-07-29 18:07:39,067 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.925 s, CPU [user: 0.174 s, system: 0.0258 s], Allocated memory: 18.3 MB, transactions: 22, svn: 0.761 s [99% getDatedRevision (181x)] (183x), Lucene: 0.0481 s [77% buildBaselineSnapshots (1x), 23% buildBaseline (22x)] (23x)
2025-07-29 18:07:39,614 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661569b9a9c40_0_661569b9a9c40_0_: finished. Total: 1.46 s, CPU [user: 0.441 s, system: 0.0916 s], Allocated memory: 54.6 MB, svn: 0.877 s [59% getDatedRevision (181x), 25% getDir2 content (25x)] (307x), resolve: 0.494 s [100% Category (96x)] (96x), ObjectMaps: 0.145 s [40% getPrimaryObjectLocation (96x), 39% getPrimaryObjectProperty (96x), 20% getLastPromoted (96x)] (388x)
2025-07-29 18:07:39,857 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661569bb31448_0_661569bb31448_0_: finished. Total: 0.139 s, CPU [user: 0.0671 s, system: 0.0107 s], Allocated memory: 8.4 MB, RepositoryConfigService: 0.0611 s [52% getReadConfiguration (162x), 48% getReadUserConfiguration (10x)] (172x), svn: 0.0568 s [53% info (19x), 40% getFile content (16x)] (37x), resolve: 0.0438 s [100% User (9x)] (9x), ObjectMaps: 0.0231 s [44% getPrimaryObjectProperty (9x), 41% getPrimaryObjectLocation (9x)] (37x)
2025-07-29 18:07:40,075 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661569bb6004a_0_661569bb6004a_0_: finished. Total: 0.17 s, CPU [user: 0.0487 s, system: 0.00459 s], Allocated memory: 19.9 MB, svn: 0.139 s [82% getDir2 content (17x)] (62x), RepositoryConfigService: 0.0417 s [98% getReadConfiguration (170x)] (192x)
2025-07-29 18:07:40,738 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661569bb8ac4b_0_661569bb8ac4b_0_: finished. Total: 0.663 s, CPU [user: 0.336 s, system: 0.0131 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.512 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.326 s [68% getFile content (412x), 32% getDir2 content (21x)] (434x)
2025-07-29 18:07:41,201 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661569bc5c44e_0_661569bc5c44e_0_: finished. Total: 0.287 s, CPU [user: 0.118 s, system: 0.00745 s], Allocated memory: 387.2 MB, RepositoryConfigService: 0.185 s [97% getReadConfiguration (2787x)] (3025x), svn: 0.179 s [52% getFile content (185x), 47% getDir2 content (20x)] (206x)
2025-07-29 18:07:41,201 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 3.05 s, CPU [user: 1.13 s, system: 0.143 s], Allocated memory: 1.6 GB, transactions: 10, svn: 1.79 s [37% getDir2 content (114x), 31% getFile content (809x), 29% getDatedRevision (181x)] (1144x), RepositoryConfigService: 0.881 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.598 s [83% Category (96x)] (117x), ObjectMaps: 0.194 s [43% getPrimaryObjectProperty (110x), 38% getPrimaryObjectLocation (116x)] (452x)
2025-07-29 18:07:41,201 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 54, svn: 2.55 s [50% getDatedRevision (362x), 26% getDir2 content (114x), 22% getFile content (809x)] (1327x), RepositoryConfigService: 0.881 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.598 s [83% Category (96x)] (118x), ObjectMaps: 0.194 s [43% getPrimaryObjectProperty (110x), 38% getPrimaryObjectLocation (116x)] (452x)
