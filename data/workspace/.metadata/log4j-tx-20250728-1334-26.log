2025-07-28 13:34:31,449 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.036 s [55% update (144x), 44% query (12x)] (221x), svn: 0.0116 s [59% getLatestRevision (2x), 29% testConnection (1x)] (4x)
2025-07-28 13:34:31,563 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0346 s [58% getDir2 content (2x), 35% info (3x)] (6x)
2025-07-28 13:34:32,331 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.764 s, CPU [user: 0.108 s, system: 0.229 s], Allocated memory: 23.7 MB, transactions: 0, ObjectMaps: 0.0777 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-28 13:34:32,331 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.764 s, CPU [user: 0.064 s, system: 0.0902 s], Allocated memory: 8.6 MB, transactions: 0, svn: 0.0753 s [83% log2 (10x)] (13x), ObjectMaps: 0.062 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-28 13:34:32,331 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.764 s, CPU [user: 0.0831 s, system: 0.175 s], Allocated memory: 14.3 MB, transactions: 0, ObjectMaps: 0.0776 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.0545 s [70% log2 (10x), 23% getLatestRevision (2x)] (13x)
2025-07-28 13:34:32,331 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.764 s, CPU [user: 0.0638 s, system: 0.11 s], Allocated memory: 9.7 MB, transactions: 0, svn: 0.0906 s [31% info (5x), 25% log2 (5x), 18% log (1x), 11% getLatestRevision (2x)] (18x), ObjectMaps: 0.0696 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-28 13:34:32,332 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.764 s, CPU [user: 0.194 s, system: 0.285 s], Allocated memory: 52.8 MB, transactions: 0, ObjectMaps: 0.119 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-28 13:34:32,332 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.764 s, CPU [user: 0.249 s, system: 0.357 s], Allocated memory: 69.9 MB, transactions: 0, ObjectMaps: 0.136 s [99% getAllPrimaryObjects (1x)] (14x), svn: 0.0405 s [75% log2 (5x), 13% getLatestRevision (1x)] (7x)
2025-07-28 13:34:32,332 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.542 s [100% getAllPrimaryObjects (8x)] (63x), svn: 0.3 s [59% log2 (36x), 14% getLatestRevision (9x), 9% info (5x)] (61x)
2025-07-28 13:34:32,574 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.208 s [100% getReadConfiguration (48x)] (48x), svn: 0.0813 s [83% info (18x)] (38x)
2025-07-28 13:34:32,848 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.208 s [72% info (94x), 20% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.156 s [100% getReadConfiguration (54x)] (54x)
2025-07-28 13:34:33,093 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.216 s [100% doFinishStartup (1x)] (1x), commit: 0.0537 s [100% Revision (1x)] (1x), Lucene: 0.0314 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.0179 s [100% objectsToInv (1x)] (1x)
