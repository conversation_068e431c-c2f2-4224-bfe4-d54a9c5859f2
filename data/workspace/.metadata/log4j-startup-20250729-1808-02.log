2025-07-29 18:08:02,373 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 18:08:02,373 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-29 18:08:02,373 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 18:08:02,373 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-29 18:08:02,373 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-29 18:08:02,373 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:08:02,374 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-29 18:08:06,749 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-29 18:08:06,885 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.136 s. ]
2025-07-29 18:08:06,885 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-29 18:08:06,931 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0454 s. ]
2025-07-29 18:08:06,983 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-29 18:08:07,157 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 12 s. ]
2025-07-29 18:08:07,392 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.02 s. ]
2025-07-29 18:08:07,495 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:08:07,495 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-29 18:08:07,520 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.13 s. ]
2025-07-29 18:08:07,520 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:08:07,520 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-29 18:08:07,528 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (5/9)
2025-07-29 18:08:07,528 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (6/9)
2025-07-29 18:08:07,528 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (1/9)
2025-07-29 18:08:07,528 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (2/9)
2025-07-29 18:08:07,528 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (3/9)
2025-07-29 18:08:07,528 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (4/9)
2025-07-29 18:08:07,538 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-07-29 18:08:07,676 [LowLevelDataService-contextInitializer-4 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-29 18:08:07,784 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-29 18:08:08,192 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.67 s. ]
2025-07-29 18:08:08,203 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:08:08,203 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-29 18:08:08,409 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-29 18:08:08,421 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.23 s. ]
2025-07-29 18:08:08,452 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:08:08,453 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-29 18:08:08,456 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-29 18:08:08,506 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-29 18:08:08,554 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-07-29 18:08:08,581 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (4/9)
2025-07-29 18:08:08,607 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (5/9)
2025-07-29 18:08:08,642 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (6/9)
2025-07-29 18:08:08,667 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-07-29 18:08:08,711 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-29 18:08:08,738 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-29 18:08:08,738 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.32 s. ]
2025-07-29 18:08:08,739 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:08:08,739 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-29 18:08:08,752 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-07-29 18:08:08,752 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:08:08,752 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-29 18:08:08,861 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-29 18:08:08,865 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-29 18:08:09,024 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.27 s. ]
2025-07-29 18:08:09,025 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:08:09,025 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-29 18:08:09,033 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-29 18:08:09,033 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:08:09,033 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-29 18:33:41,780 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 1532.74 s. ]
2025-07-29 18:33:41,780 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 18:33:41,780 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 1540 s. ]
2025-07-29 18:33:41,780 [main] INFO  com.polarion.platform.startup - ****************************************************************
