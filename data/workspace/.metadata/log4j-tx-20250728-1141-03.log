2025-07-28 11:41:07,918 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0537 s [61% update (144x), 39% query (12x)] (221x), svn: 0.0218 s [53% getLatestRevision (2x), 35% testConnection (1x)] (4x)
2025-07-28 11:41:08,021 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0326 s [59% getDir2 content (2x), 33% info (3x)] (6x)
2025-07-28 11:41:08,740 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.716 s, CPU [user: 0.0471 s, system: 0.111 s], Allocated memory: 6.8 MB, transactions: 0, ObjectMaps: 0.0624 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-28 11:41:08,740 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.717 s, CPU [user: 0.18 s, system: 0.308 s], Allocated memory: 53.1 MB, transactions: 0, ObjectMaps: 0.107 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-28 11:41:08,740 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.717 s, CPU [user: 0.218 s, system: 0.367 s], Allocated memory: 70.2 MB, transactions: 0, ObjectMaps: 0.119 s [99% getAllPrimaryObjects (1x)] (14x)
2025-07-28 11:41:08,741 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.716 s, CPU [user: 0.0607 s, system: 0.117 s], Allocated memory: 8.8 MB, transactions: 0, svn: 0.0675 s [81% log2 (10x)] (13x), ObjectMaps: 0.0609 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-28 11:41:08,741 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.716 s, CPU [user: 0.116 s, system: 0.243 s], Allocated memory: 26.1 MB, transactions: 0, svn: 0.0813 s [41% info (5x), 20% log (1x), 19% log2 (5x), 9% getLatestRevision (2x)] (18x), ObjectMaps: 0.0714 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-28 11:41:08,741 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.717 s, CPU [user: 0.0806 s, system: 0.185 s], Allocated memory: 14.3 MB, transactions: 0, ObjectMaps: 0.0865 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.0565 s [78% log2 (10x), 13% getLatestRevision (2x)] (13x)
2025-07-28 11:41:08,741 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.507 s [100% getAllPrimaryObjects (8x)] (63x), svn: 0.272 s [58% log2 (36x), 12% getLatestRevision (9x), 12% info (5x)] (61x)
2025-07-28 11:41:08,950 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.182 s [100% getReadConfiguration (48x)] (48x), svn: 0.0679 s [88% info (18x)] (38x)
2025-07-28 11:41:09,189 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.18 s [73% info (94x), 19% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.134 s [100% getReadConfiguration (54x)] (54x)
2025-07-28 11:41:09,389 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.188 s [100% doFinishStartup (1x)] (1x), commit: 0.0477 s [100% Revision (1x)] (1x), Lucene: 0.0297 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.015 s [100% objectsToInv (1x)] (1x)
