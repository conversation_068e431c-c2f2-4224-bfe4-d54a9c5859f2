2025-07-29 18:05:54,296 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Using logging context STANDALONE
2025-07-29 18:05:54,298 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Launchers manager started...
2025-07-29 18:05:54,298 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Using home directory /opt/polarion/polarion
2025-07-29 18:05:54,298 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Using root directory /opt/polarion
2025-07-29 18:05:54,298 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Using workspace directory /opt/polarion/data/workspace
2025-07-29 18:05:54,298 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Using config directory /Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion
2025-07-29 18:05:54,298 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Loading external properties ...
2025-07-29 18:05:54,298 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Using external property file /opt/polarion/etc/polarion.properties
2025-07-29 18:05:54,298 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Loading internal properties ...
2025-07-29 18:05:54,300 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Host: zhangwentiandeMac-mini-2.local (*************)
2025-07-29 18:05:54,304 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Product: com.polarion.alm
2025-07-29 18:05:54,304 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Version: 3.22.1
2025-07-29 18:05:54,304 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Build: 20220419-1528-22_R1-be3adceb
2025-07-29 18:05:54,305 [main] WARN  com.polarion.core.boot.impl.AppLaunchersManager - missing subfolder 'eclipse' under extension directory: /opt/polarion/polarion/extensions/fasnote
2025-07-29 18:05:54,306 [main] WARN  com.polarion.core.boot.impl.AppLaunchersManager - missing subfolder 'eclipse' under extension directory: /opt/polarion/polarion/extensions/2404
2025-07-29 18:05:54,306 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Extensions: [exts]
2025-07-29 18:05:54,308 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Workspace location: /opt/polarion/data/workspace
2025-07-29 18:05:54,308 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Workspace lock acquired
2025-07-29 18:05:54,308 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Found applications: [polarion.server, polarion.coordinator, polarion.rt]
2025-07-29 18:05:54,308 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Starting application: polarion.server
2025-07-29 18:05:54,313 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Application extension successfully read
2025-07-29 18:05:54,319 [main] INFO  com.polarion.platform.internal.SystemStatistics - Initializing monitoring, isThreadCpuTimeSupported: true, isThreadContentionMonitoringSupported: true, isThreadAllocatedMemorySupported: true
2025-07-29 18:05:54,319 [main] INFO  com.polarion.platform.internal.SystemStatistics - State before enabling: isThreadCpuTimeEnabled: true, isThreadContentionMonitoringEnabled: false, isThreadAllocatedMemoryEnabled: true
2025-07-29 18:05:54,319 [main] INFO  com.polarion.platform.internal.SystemStatistics - State after enabling: isThreadCpuTimeEnabled: true, isThreadContentionMonitoringEnabled: false, isThreadAllocatedMemoryEnabled: true
2025-07-29 18:05:54,322 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 18:05:54,322 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-29 18:05:54,322 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 18:05:54,322 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-29 18:05:54,322 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-29 18:05:54,322 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:05:54,322 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-29 18:05:54,323 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - **** Java system properties listing: 
2025-07-29 18:05:54,342 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - adminPasswd = admin
2025-07-29 18:05:54,342 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - adminUser = admin
2025-07-29 18:05:54,342 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - announcer.smtp.auth = false
2025-07-29 18:05:54,342 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - announcer.smtp.host = 
2025-07-29 18:05:54,342 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - announcer.smtp.password = **PASSWORD**HIDDEN**
2025-07-29 18:05:54,342 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - announcer.smtp.port = 25
2025-07-29 18:05:54,342 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - announcer.smtp.user = 
2025-07-29 18:05:54,342 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - awt.toolkit = sun.lwawt.macosx.LWCToolkit
2025-07-29 18:05:54,342 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - base.url = http://localhost
2025-07-29 18:05:54,343 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - bfh.jobs.workdir = /opt/polarion/data/workspace/polarion-data/jobs
2025-07-29 18:05:54,343 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - BIRDir = /opt/polarion/data/BIR
2025-07-29 18:05:54,343 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - calculated.fields.mode = async
2025-07-29 18:05:54,343 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.activation.activationHelpLink = https://polarion.plm.automation.siemens.com/getlicense
2025-07-29 18:05:54,343 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.activation.server = https://license.polarion.com/licenseGenerator/generator/generate
2025-07-29 18:05:54,343 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.alm.ui.gravatar.enabled = false
2025-07-29 18:05:54,343 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.alm.ui.gravatar.url = http://www.gravatar.com/avatar/$emailHash$?d=identicon&s=50
2025-07-29 18:05:54,343 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.application = polarion.server
2025-07-29 18:05:54,343 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.config = /Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion
2025-07-29 18:05:54,343 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.data = /opt/polarion/data
2025-07-29 18:05:54,343 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.eclipse = /opt/polarion/polarion
2025-07-29 18:05:54,343 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.home = /opt/polarion/polarion
2025-07-29 18:05:54,343 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.licenseDir = /opt/polarion/polarion/license
2025-07-29 18:05:54,343 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.platform.internalPG = polarion:polarion@localhost:5434
2025-07-29 18:05:54,343 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.platform.monitoring.notifications.disabled = true
2025-07-29 18:05:54,343 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.platform.monitoring.notifications.receivers = 
2025-07-29 18:05:54,343 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.platform.monitoring.notifications.sender = 
2025-07-29 18:05:54,343 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.platform.monitoring.notifications.subject.prefix = 
2025-07-29 18:05:54,343 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.platform.persistence.notifications.disabled = true
2025-07-29 18:05:54,343 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.propertyFile = /opt/polarion/etc/polarion.properties
2025-07-29 18:05:54,343 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.root = /opt/polarion
2025-07-29 18:05:54,343 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.workspace = /opt/polarion/data/workspace
2025-07-29 18:05:54,343 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.collaborationNotifications.enabled = true
2025-07-29 18:05:54,343 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.document.listStyle = 1ai
2025-07-29 18:05:54,343 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.loggingContext = STANDALONE
2025-07-29 18:05:54,343 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.preview.thumbnailsDataDir = /opt/polarion/data/workspace/previews-data/thumbnails
2025-07-29 18:05:54,343 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.rest.cors.allowedOrigins = *
2025-07-29 18:05:54,343 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.rest.enabled = true
2025-07-29 18:05:54,343 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.rest.swaggerUi.enabled = true
2025-07-29 18:05:54,343 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.xcelerator.accEndpointUrl = https://acc.collab.sws.siemens.com
2025-07-29 18:05:54,343 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.xcelerator.baseDomain = sws.siemens.com
2025-07-29 18:05:54,343 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.xcelerator.shareEndpointUrl = https://share.sws.siemens.com
2025-07-29 18:05:54,343 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - content.types.user.table = /opt/polarion/polarion/plugins/com.polarion.core.boot_3.22.1/content-types.properties
2025-07-29 18:05:54,343 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - controlHostname = localhost
2025-07-29 18:05:54,343 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - controlPort = 8887
2025-07-29 18:05:54,343 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - createproject.default.location = Sandbox/
2025-07-29 18:05:54,343 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - createproject.default.useUserId = true
2025-07-29 18:05:54,343 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - createproject.limitedAccessMessage = You may create a project in the Sandbox project group (only). Please fill in the required properties below. For example:<br/><table><tr><td>Location:</td><td>Sandbox/MyFirstProject</td></tr><tr><td>ID:</td><td>MyFirstProject</td></tr></table><br/>Or use the suggested defaults.
2025-07-29 18:05:54,343 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - debug = false
2025-07-29 18:05:54,343 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - debug.license.validation = true
2025-07-29 18:05:54,343 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - debug.machine.code.generation = true
2025-07-29 18:05:54,343 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - debug.security.validation = true
2025-07-29 18:05:54,343 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - default.project.template.ALM = alm_vmodel
2025-07-29 18:05:54,344 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - default.project.template.Pro = alm_vmodel
2025-07-29 18:05:54,344 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - default.project.template.QA = qa_vmodel
2025-07-29 18:05:54,344 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - default.project.template.Requirements = req_vmodel
2025-07-29 18:05:54,344 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - default.project.template.XBase = alm_vmodel
2025-07-29 18:05:54,344 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - default.project.template.XEnterprise = alm_vmodel
2025-07-29 18:05:54,344 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - default.project.template.XPro = alm_vmodel
2025-07-29 18:05:54,344 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - derby.system.home = /opt/polarion/data/logs/derby
2025-07-29 18:05:54,344 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - eclipse.application = com.polarion.core.boot.app
2025-07-29 18:05:54,344 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - eclipse.commands = -application
com.polarion.core.boot.app
-data
/opt/polarion/data/workspace
-configuration
file:///Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/
-dev
file:///Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/dev.properties
-os
linux
-ws
linux
-arch
arm64
-appId
polarion.server

2025-07-29 18:05:54,344 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - eclipse.home.location = file:/opt/polarion/polarion/plugins/
2025-07-29 18:05:54,344 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - eclipse.p2.data.area = @config.dir/.p2
2025-07-29 18:05:54,344 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - eclipse.pde.launch = true
2025-07-29 18:05:54,344 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - eclipse.startTime = *************
2025-07-29 18:05:54,344 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - eclipse.stateSaveDelayInterval = 30000
2025-07-29 18:05:54,344 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - enableCreateAccountForm = false
2025-07-29 18:05:54,344 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - equinox.init.uuid = true
2025-07-29 18:05:54,344 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - error.report.email = 
2025-07-29 18:05:54,344 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - file.encoding = UTF-8
2025-07-29 18:05:54,344 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - file.separator = /
2025-07-29 18:05:54,344 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - ftp.nonProxyHosts = 127.0.0.1|***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|**********/16|*.**********/16|localhost|*.localhost|local|*.local|crashlytics.com|*.crashlytics.com|<local>|*.<local>
2025-07-29 18:05:54,344 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - gopherProxySet = false
2025-07-29 18:05:54,344 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - gosh.args = --nointeractive
2025-07-29 18:05:54,344 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - htpasswd.path = htpasswd
2025-07-29 18:05:54,344 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - http.nonProxyHosts = 127.0.0.1|***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|**********/16|*.**********/16|localhost|*.localhost|local|*.local|crashlytics.com|*.crashlytics.com|<local>|*.<local>
2025-07-29 18:05:54,344 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - index.activities = /opt/polarion/data/workspace/polarion-data/index
2025-07-29 18:05:54,344 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.awt.graphicsenv = sun.awt.CGraphicsEnvironment
2025-07-29 18:05:54,344 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.awt.headless = true
2025-07-29 18:05:54,344 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.awt.printerjob = sun.lwawt.macosx.CPrinterJob
2025-07-29 18:05:54,344 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.class.path = /opt/polarion/polarion/plugins/org.eclipse.equinox.launcher_1.5.0.v20180512-1130.jar:
2025-07-29 18:05:54,344 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.class.version = 55.0
2025-07-29 18:05:54,344 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.home = /Users/<USER>/Library/Java/JavaVirtualMachines/ms-11.0.27/Contents/Home
2025-07-29 18:05:54,344 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.io.tmpdir = /var/folders/z_/shw6wc7d7ps_fjvv781t4gt80000gn/T/
2025-07-29 18:05:54,344 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.library.path = /Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:.
2025-07-29 18:05:54,344 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.runtime.name = OpenJDK Runtime Environment
2025-07-29 18:05:54,344 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.runtime.version = 11.0.27+6-LTS
2025-07-29 18:05:54,344 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.security.policy = /opt/polarion/polarion/policy
2025-07-29 18:05:54,344 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.specification.maintenance.version = 3
2025-07-29 18:05:54,344 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.specification.name = Java Platform API Specification
2025-07-29 18:05:54,344 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.specification.vendor = Oracle Corporation
2025-07-29 18:05:54,344 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.specification.version = 11
2025-07-29 18:05:54,344 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vendor = Microsoft
2025-07-29 18:05:54,344 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vendor.url = https://www.microsoft.com
2025-07-29 18:05:54,344 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vendor.url.bug = https://github.com/microsoft/openjdk/issues
2025-07-29 18:05:54,344 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vendor.version = Microsoft-11367290
2025-07-29 18:05:54,344 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.version = 11.0.27
2025-07-29 18:05:54,344 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.version.date = 2025-04-15
2025-07-29 18:05:54,344 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.compressedOopsMode = Zero based
2025-07-29 18:05:54,344 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.info = mixed mode
2025-07-29 18:05:54,345 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.name = OpenJDK 64-Bit Server VM
2025-07-29 18:05:54,345 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.specification.name = Java Virtual Machine Specification
2025-07-29 18:05:54,345 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.specification.vendor = Oracle Corporation
2025-07-29 18:05:54,345 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.specification.version = 11
2025-07-29 18:05:54,345 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.vendor = Microsoft
2025-07-29 18:05:54,345 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.version = 11.0.27+6-LTS
2025-07-29 18:05:54,345 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - javasvn.timeout = 10000
2025-07-29 18:05:54,345 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - jdk.debug = release
2025-07-29 18:05:54,345 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - ldap.bind.password = **PASSWORD**HIDDEN**
2025-07-29 18:05:54,345 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.audit.enabled = true
2025-07-29 18:05:54,345 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.auto.scan.enabled = true
2025-07-29 18:05:54,345 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.cache.size = 100
2025-07-29 18:05:54,345 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.cache.ttl = 1800
2025-07-29 18:05:54,345 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.check.interval = 0
2025-07-29 18:05:54,345 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.allow.expired = true
2025-07-29 18:05:54,345 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.allow.local.files = true
2025-07-29 18:05:54,345 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.default.features = all
2025-07-29 18:05:54,345 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.default.max.users = 10
2025-07-29 18:05:54,345 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.default.plugin.id = com.fasnote.alm.plugin.manage
2025-07-29 18:05:54,345 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.mode = true
2025-07-29 18:05:54,345 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.show.machine.code = true
2025-07-29 18:05:54,345 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.skip.machine.binding = true
2025-07-29 18:05:54,345 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.skip.network.validation = true
2025-07-29 18:05:54,345 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.directory = dev-licenses
2025-07-29 18:05:54,345 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.encryption.enabled = false
2025-07-29 18:05:54,345 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.hot.reload.enabled = true
2025-07-29 18:05:54,345 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.log.level = DEBUG
2025-07-29 18:05:54,345 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.machine.binding.enabled = false
2025-07-29 18:05:54,345 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.max.plugins = 1000
2025-07-29 18:05:54,345 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.scan.interval = 60
2025-07-29 18:05:54,345 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.signature.validation.enabled = false
2025-07-29 18:05:54,345 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.validation.timeout = 1000
2025-07-29 18:05:54,345 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - licenseForNewUserAccount = 
2025-07-29 18:05:54,345 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - line.separator = 

2025-07-29 18:05:54,345 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - log4j2.contextSelector = org.apache.logging.log4j.core.selector.BasicContextSelector
2025-07-29 18:05:54,345 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - log4j2.loggerContextFactory = org.apache.logging.log4j.core.impl.Log4jContextFactory
2025-07-29 18:05:54,345 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - logDir = /opt/polarion/data/workspace/.metadata/
2025-07-29 18:05:54,345 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - login = polarion
2025-07-29 18:05:54,345 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - mavenConfigDir = /opt/polarion/polarion/../maven
2025-07-29 18:05:54,345 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - minimalPasswordLength = **PASSWORD**HIDDEN**
2025-07-29 18:05:54,345 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.eclipse.equinox.simpleconfigurator.configUrl = file:/Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/org.eclipse.equinox.simpleconfigurator/bundles.info
2025-07-29 18:05:54,345 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.eclipse.lyo.oslc4j.strictDatatypes = false
2025-07-29 18:05:54,345 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.executionenvironment = OSGi/Minimum-1.0, OSGi/Minimum-1.1, OSGi/Minimum-1.2, JavaSE/compact1-1.8, JavaSE/compact2-1.8, JavaSE/compact3-1.8, JRE-1.1, J2SE-1.2, J2SE-1.3, J2SE-1.4, J2SE-1.5, JavaSE-1.6, JavaSE-1.7, JavaSE-1.8, JavaSE-9, JavaSE-10, JavaSE-11
2025-07-29 18:05:54,345 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.language = zh
2025-07-29 18:05:54,345 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.os.name = MacOSX
2025-07-29 18:05:54,345 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.os.version = 15.5.0
2025-07-29 18:05:54,345 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.processor = aarch64
2025-07-29 18:05:54,345 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.storage = /Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion
2025-07-29 18:05:54,345 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.system.capabilities = osgi.ee; osgi.ee="OSGi/Minimum"; version:List<Version>="1.0, 1.1, 1.2", osgi.ee; osgi.ee="JRE"; version:List<Version>="1.0, 1.1", osgi.ee; osgi.ee="JavaSE"; version:List<Version>="1.0, 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 1.7, 1.8, 9.0, 10.0, 11.0",osgi.ee; osgi.ee="JavaSE/compact1"; version:List<Version>="1.8, 9.0, 10.0, 11.0",osgi.ee; osgi.ee="JavaSE/compact2"; version:List<Version>="1.8, 9.0, 10.0, 11.0",osgi.ee; osgi.ee="JavaSE/compact3"; version:List<Version>="1.8, 9.0, 10.0, 11.0"
2025-07-29 18:05:54,345 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.system.packages = com.sun.jarsigner, com.sun.java.accessibility.util, com.sun.javadoc, com.sun.jdi, com.sun.jdi.connect, com.sun.jdi.connect.spi, com.sun.jdi.event, com.sun.jdi.request, com.sun.jndi.ldap.spi, com.sun.management, com.sun.net.httpserver, com.sun.net.httpserver.spi, com.sun.nio.file, com.sun.nio.sctp, com.sun.security.auth, com.sun.security.auth.callback, com.sun.security.auth.login, com.sun.security.auth.module, com.sun.security.jgss, com.sun.source.doctree, com.sun.source.tree, com.sun.source.util, com.sun.tools.attach, com.sun.tools.attach.spi, com.sun.tools.javac, com.sun.tools.javadoc, com.sun.tools.jconsole, java.applet, java.awt, java.awt.color, java.awt.datatransfer, java.awt.desktop, java.awt.dnd, java.awt.event, java.awt.font, java.awt.geom, java.awt.im, java.awt.im.spi, java.awt.image, java.awt.image.renderable, java.awt.print, java.beans, java.beans.beancontext, java.io, java.lang, java.lang.annotation, java.lang.instrument, java.lang.invoke, java.lang.management, java.lang.module, java.lang.ref, java.lang.reflect, java.math, java.net, java.net.http, java.net.spi, java.nio, java.nio.channels, java.nio.channels.spi, java.nio.charset, java.nio.charset.spi, java.nio.file, java.nio.file.attribute, java.nio.file.spi, java.rmi, java.rmi.activation, java.rmi.dgc, java.rmi.registry, java.rmi.server, java.security, java.security.acl, java.security.cert, java.security.interfaces, java.security.spec, java.sql, java.text, java.text.spi, java.time, java.time.chrono, java.time.format, java.time.temporal, java.time.zone, java.util, java.util.concurrent, java.util.concurrent.atomic, java.util.concurrent.locks, java.util.function, java.util.jar, java.util.logging, java.util.prefs, java.util.regex, java.util.spi, java.util.stream, java.util.zip, javax.accessibility, javax.annotation.processing, javax.crypto, javax.crypto.interfaces, javax.crypto.spec, javax.imageio, javax.imageio.event, javax.imageio.metadata, javax.imageio.plugins.bmp, javax.imageio.plugins.jpeg, javax.imageio.plugins.tiff, javax.imageio.spi, javax.imageio.stream, javax.lang.model, javax.lang.model.element, javax.lang.model.type, javax.lang.model.util, javax.management, javax.management.loading, javax.management.modelmbean, javax.management.monitor, javax.management.openmbean, javax.management.relation, javax.management.remote, javax.management.remote.rmi, javax.management.timer, javax.naming, javax.naming.directory, javax.naming.event, javax.naming.ldap, javax.naming.spi, javax.net, javax.net.ssl, javax.print, javax.print.attribute, javax.print.attribute.standard, javax.print.event, javax.rmi.ssl, javax.script, javax.security.auth, javax.security.auth.callback, javax.security.auth.kerberos, javax.security.auth.login, javax.security.auth.spi, javax.security.auth.x500, javax.security.cert, javax.security.sasl, javax.smartcardio, javax.sound.midi, javax.sound.midi.spi, javax.sound.sampled, javax.sound.sampled.spi, javax.sql, javax.sql.rowset, javax.sql.rowset.serial, javax.sql.rowset.spi, javax.swing, javax.swing.border, javax.swing.colorchooser, javax.swing.event, javax.swing.filechooser, javax.swing.plaf, javax.swing.plaf.basic, javax.swing.plaf.metal, javax.swing.plaf.multi, javax.swing.plaf.nimbus, javax.swing.plaf.synth, javax.swing.table, javax.swing.text, javax.swing.text.html, javax.swing.text.html.parser, javax.swing.text.rtf, javax.swing.tree, javax.swing.undo, javax.tools, javax.transaction.xa, javax.xml, javax.xml.catalog, javax.xml.crypto, javax.xml.crypto.dom, javax.xml.crypto.dsig, javax.xml.crypto.dsig.dom, javax.xml.crypto.dsig.keyinfo, javax.xml.crypto.dsig.spec, javax.xml.datatype, javax.xml.namespace, javax.xml.parsers, javax.xml.stream, javax.xml.stream.events, javax.xml.stream.util, javax.xml.transform, javax.xml.transform.dom, javax.xml.transform.sax, javax.xml.transform.stax, javax.xml.transform.stream, javax.xml.validation, javax.xml.xpath, jdk.dynalink, jdk.dynalink.beans, jdk.dynalink.linker, jdk.dynalink.linker.support, jdk.dynalink.support, jdk.javadoc.doclet, jdk.jfr, jdk.jfr.consumer, jdk.jshell, jdk.jshell.execution, jdk.jshell.spi, jdk.jshell.tool, jdk.management.jfr, jdk.nashorn.api.scripting, jdk.nashorn.api.tree, jdk.net, jdk.nio, jdk.security.jarsigner, jdk.swing.interop, netscape.javascript, org.ietf.jgss, org.w3c.dom, org.w3c.dom.bootstrap, org.w3c.dom.css, org.w3c.dom.events, org.w3c.dom.html, org.w3c.dom.ls, org.w3c.dom.ranges, org.w3c.dom.stylesheets, org.w3c.dom.traversal, org.w3c.dom.views, org.w3c.dom.xpath, org.xml.sax, org.xml.sax.ext, org.xml.sax.helpers, sun.misc, sun.reflect
2025-07-29 18:05:54,345 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.uuid = f5015c1e-7286-43a2-ba9f-9fe4c1011674
2025-07-29 18:05:54,345 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.vendor = Eclipse
2025-07-29 18:05:54,346 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.version = 1.9.0
2025-07-29 18:05:54,346 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.supports.framework.extension = true
2025-07-29 18:05:54,346 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.supports.framework.fragment = true
2025-07-29 18:05:54,346 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.supports.framework.requirebundle = true
2025-07-29 18:05:54,346 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.xsocket.connection.client.readbuffer.usedirect = true
2025-07-29 18:05:54,346 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.xsocket.connection.server.readbuffer.usedirect = true
2025-07-29 18:05:54,346 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - os.arch = aarch64
2025-07-29 18:05:54,346 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - os.name = Mac OS X
2025-07-29 18:05:54,346 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - os.version = 15.5
2025-07-29 18:05:54,346 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.arch = arm64
2025-07-29 18:05:54,346 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.bundles = reference:file:/opt/polarion/polarion/plugins/org.eclipse.equinox.simpleconfigurator_1.3.0.v20180502-1828.jar@1:start
2025-07-29 18:05:54,346 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.bundles.defaultStartLevel = 4
2025-07-29 18:05:54,346 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.checkConfiguration = true
2025-07-29 18:05:54,346 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.compatibility.bootdelegation = true
2025-07-29 18:05:54,346 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.compatibility.bootdelegation.default = true
2025-07-29 18:05:54,346 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.configuration.area = file:/Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/
2025-07-29 18:05:54,346 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.configuration.cascaded = false
2025-07-29 18:05:54,346 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.dev = file:///Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/dev.properties
2025-07-29 18:05:54,346 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.framework = file:/opt/polarion/polarion/plugins/org.eclipse.osgi_3.13.0.v20180409-1500.jar
2025-07-29 18:05:54,346 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.framework.shape = jar
2025-07-29 18:05:54,346 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.framework.useSystemProperties = true
2025-07-29 18:05:54,346 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.install.area = file:/opt/polarion/polarion/plugins/
2025-07-29 18:05:54,346 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.instance.area = file:/opt/polarion/data/workspace/
2025-07-29 18:05:54,346 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.logfile = /opt/polarion/data/workspace/.metadata/.log
2025-07-29 18:05:54,346 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.nl = zh_CN_#Hans
2025-07-29 18:05:54,346 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.os = linux
2025-07-29 18:05:54,346 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.syspath = /opt/polarion/polarion/plugins
2025-07-29 18:05:54,346 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.tracefile = /opt/polarion/data/workspace/.metadata/trace.log
2025-07-29 18:05:54,346 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.ws = linux
2025-07-29 18:05:54,346 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - password = **PASSWORD**HIDDEN**
2025-07-29 18:05:54,346 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - path.separator = :
2025-07-29 18:05:54,346 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - pdfbox.fontcache = /opt/polarion/data/workspace/polarion-data
2025-07-29 18:05:54,346 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - pdfexport.config = /opt/polarion/polarion/configuration/pdfexport.xml
2025-07-29 18:05:54,346 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - polarion.build.default.deploy.repository.id = polarion-shared
2025-07-29 18:05:54,346 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - polarion.build.default.deploy.repository.url = file:///opt/polarion/data/shared-maven-repo
2025-07-29 18:05:54,346 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - polarion.build.maven.location.maven2 = /opt/polarion/polarion/../maven/distribution
2025-07-29 18:05:54,346 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - polarion.global.doc.cache.size = 100
2025-07-29 18:05:54,346 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - polarion.global.doc.cache.with.history = false
2025-07-29 18:05:54,346 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - polarion.tx.doc.cache.size = 100
2025-07-29 18:05:54,346 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - productLink.com.polarion.alm = https://polarion.plm.automation.siemens.com/products/polarion-alm
2025-07-29 18:05:54,346 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - productLink.com.polarion.qa = https://polarion.plm.automation.siemens.com/products/polarion-qa
2025-07-29 18:05:54,346 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - productLink.com.polarion.requirements = https://polarion.plm.automation.siemens.com/products/polarion-requirements
2025-07-29 18:05:54,346 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - repo = http://localhost/repo
2025-07-29 18:05:54,346 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - rolesForNewUserAccount = user
2025-07-29 18:05:54,346 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - RRDir = /opt/polarion/data/RR
2025-07-29 18:05:54,346 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - SDKDir = /opt/polarion/polarion/SDK
2025-07-29 18:05:54,346 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - secure.approvals = false
2025-07-29 18:05:54,346 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - shutdownCatchPhrase = shutdown
2025-07-29 18:05:54,346 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - simple.profiler.enabled = false
2025-07-29 18:05:54,346 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - skip.data.preloading = false
2025-07-29 18:05:54,346 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - socksNonProxyHosts = 127.0.0.1|***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|**********/16|*.**********/16|localhost|*.localhost|local|*.local|crashlytics.com|*.crashlytics.com|<local>|*.<local>
2025-07-29 18:05:54,346 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - stderr.encoding = UTF-8
2025-07-29 18:05:54,346 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - stdout.encoding = UTF-8
2025-07-29 18:05:54,346 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - storeUrl.com.polarion.requirements = https://polarion.plm.automation.siemens.com/products/licensing?product=REQUIREMENTS
2025-07-29 18:05:54,346 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.arch.data.model = 64
2025-07-29 18:05:54,346 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.boot.library.path = /Users/<USER>/Library/Java/JavaVirtualMachines/ms-11.0.27/Contents/Home/lib
2025-07-29 18:05:54,346 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.cpu.endian = little
2025-07-29 18:05:54,346 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.cpu.isalist = 
2025-07-29 18:05:54,347 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.io.unicode.encoding = UnicodeBig
2025-07-29 18:05:54,347 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.java.command = org.eclipse.equinox.launcher.Main -application com.polarion.core.boot.app -data /opt/polarion/data/workspace -configuration file:///Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/ -dev file:///Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/dev.properties -os linux -ws linux -arch arm64 -appId polarion.server
2025-07-29 18:05:54,347 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.java.launcher = SUN_STANDARD
2025-07-29 18:05:54,347 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.jnu.encoding = UTF-8
2025-07-29 18:05:54,347 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.management.compiler = HotSpot 64-Bit Tiered Compilers
2025-07-29 18:05:54,347 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.os.patch.level = unknown
2025-07-29 18:05:54,347 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - support.contact = https://polarion.plm.automation.siemens.com/techsupport/resources
2025-07-29 18:05:54,347 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - support.license.email = <EMAIL>
2025-07-29 18:05:54,347 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - support.sales.email = <EMAIL>
2025-07-29 18:05:54,347 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - svn.access.file = /opt/polarion/data/svn/access
2025-07-29 18:05:54,347 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - svn.passwd.file = /opt/polarion/data/svn/passwd
2025-07-29 18:05:54,347 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - svnkit.http.encoding = UTF-8
2025-07-29 18:05:54,347 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - svnkit.library.gnome-keyring.enabled = false
2025-07-29 18:05:54,347 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - TomcatService.ajp13-port = 8889
2025-07-29 18:05:54,347 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - TomcatService.request.safeListedHosts = 0.0.0.0
2025-07-29 18:05:54,347 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - user.country = CN
2025-07-29 18:05:54,347 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - user.dir = /Applications/Eclipse JEE.app/Contents/MacOS
2025-07-29 18:05:54,347 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - user.home = /Users/<USER>
2025-07-29 18:05:54,347 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - user.language = zh
2025-07-29 18:05:54,347 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - user.name = zhangwentian
2025-07-29 18:05:54,347 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - user.script = Hans
2025-07-29 18:05:54,347 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - user.timezone = Asia/Shanghai
2025-07-29 18:05:54,347 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - userAccountVault = /opt/polarion/data/workspace/user-account-vault
2025-07-29 18:05:54,347 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - workDir = /opt/polarion/data/workspace/polarion-data
2025-07-29 18:05:54,347 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - **** END of Java system properties
2025-07-29 18:05:54,348 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - XML parsers factory: com.sun.org.apache.xerces.internal.jaxp.DocumentBuilderFactoryImpl
2025-07-29 18:05:54,349 [main] INFO  com.polarion.psvn.launcher.internal.platform.PlatformService - Starting Platform...
2025-07-29 18:05:54,356 [main] INFO  PolarionLicensing - Searching for valid license file in /opt/polarion/polarion/license
2025-07-29 18:05:54,357 [main] INFO  PolarionLicensing - Trying to load license file polarion.lic
2025-07-29 18:05:54,359 [main] INFO  PolarionLicensing - The license file contains the following fields:
2025-07-29 18:05:54,359 [main] INFO  PolarionLicensing - *** License fields ***
2025-07-29 18:05:54,359 [main] INFO  PolarionLicensing - VariantsNamedUsers = 3
2025-07-29 18:05:54,359 [main] INFO  PolarionLicensing - almNamedUsers = 3
2025-07-29 18:05:54,359 [main] INFO  PolarionLicensing - dateCreated = 23.07.2025
2025-07-29 18:05:54,359 [main] INFO  PolarionLicensing - expirationDate = 21.08.2025
2025-07-29 18:05:54,359 [main] INFO  PolarionLicensing - hardwareKey = 8AG9-261C-1962
2025-07-29 18:05:54,359 [main] INFO  PolarionLicensing - licenseFormat = 2022
2025-07-29 18:05:54,359 [main] INFO  PolarionLicensing - licenseType = EVAL
2025-07-29 18:05:54,359 [main] INFO  PolarionLicensing - multiInstanceRunningInstances = 3
2025-07-29 18:05:54,359 [main] INFO  PolarionLicensing - userCompany = Polarion Eval
2025-07-29 18:05:54,359 [main] INFO  PolarionLicensing - *** License fields END ***
2025-07-29 18:05:54,375 [main] INFO  PolarionLicensing - Removing allocations by null
2025-07-29 18:05:54,376 [main] INFO  PolarionLicensing - STATS:concurrentVariantsUser,current:0,peak:0,limit:0
2025-07-29 18:05:54,376 [main] INFO  PolarionLicensing - 0 namedReviewerUser assignments (out of 0) loaded: []
2025-07-29 18:05:54,376 [main] INFO  PolarionLicensing - 0 concurrentReviewerUser assignments (out of 0) loaded: []
2025-07-29 18:05:54,376 [main] INFO  PolarionLicensing - STATS:concurrentReviewerUser,current:0,peak:0,limit:0
2025-07-29 18:05:54,376 [main] INFO  PolarionLicensing - 0 namedXBaseUser assignments (out of 0) loaded: []
2025-07-29 18:05:54,376 [main] INFO  PolarionLicensing - 0 concurrentXBaseUser assignments (out of 0) loaded: []
2025-07-29 18:05:54,376 [main] INFO  PolarionLicensing - STATS:concurrentXBaseUser,current:0,peak:0,limit:0
2025-07-29 18:05:54,376 [main] INFO  PolarionLicensing - 0 namedXProUser assignments (out of 0) loaded: []
2025-07-29 18:05:54,376 [main] INFO  PolarionLicensing - 0 concurrentXProUser assignments (out of 0) loaded: []
2025-07-29 18:05:54,376 [main] INFO  PolarionLicensing - STATS:concurrentXProUser,current:0,peak:0,limit:0
2025-07-29 18:05:54,376 [main] INFO  PolarionLicensing - 0 namedXEnterpriseUser assignments (out of 0) loaded: []
2025-07-29 18:05:54,376 [main] INFO  PolarionLicensing - 0 concurrentXEnterpriseUser assignments (out of 0) loaded: []
2025-07-29 18:05:54,376 [main] INFO  PolarionLicensing - STATS:concurrentXEnterpriseUser,current:0,peak:0,limit:0
2025-07-29 18:05:54,376 [main] INFO  PolarionLicensing - 0 namedProUser assignments (out of 0) loaded: []
2025-07-29 18:05:54,376 [main] INFO  PolarionLicensing - 0 concurrentProUser assignments (out of 0) loaded: []
2025-07-29 18:05:54,376 [main] INFO  PolarionLicensing - STATS:concurrentProUser,current:0,peak:0,limit:0
2025-07-29 18:05:54,376 [main] INFO  PolarionLicensing - 0 namedRequirementsUser assignments (out of 0) loaded: []
2025-07-29 18:05:54,376 [main] INFO  PolarionLicensing - 0 concurrentRequirementsUser assignments (out of 0) loaded: []
2025-07-29 18:05:54,377 [main] INFO  PolarionLicensing - STATS:concurrentRequirementsUser,current:0,peak:0,limit:0
2025-07-29 18:05:54,377 [main] INFO  PolarionLicensing - 0 namedQAUser assignments (out of 0) loaded: []
2025-07-29 18:05:54,377 [main] INFO  PolarionLicensing - 0 concurrentQAUser assignments (out of 0) loaded: []
2025-07-29 18:05:54,377 [main] INFO  PolarionLicensing - STATS:concurrentQAUser,current:0,peak:0,limit:0
2025-07-29 18:05:54,377 [main] INFO  PolarionLicensing - 3 namedALMUser assignments (out of 3) loaded: [admin, ou_d6f3139d36fb2978b33a8f870096b9e3, mTest]
2025-07-29 18:05:54,377 [main] INFO  PolarionLicensing - 0 concurrentALMUser assignments (out of 0) loaded: []
2025-07-29 18:05:54,377 [main] INFO  PolarionLicensing - STATS:concurrentALMUser,current:0,peak:0,limit:0
2025-07-29 18:05:54,377 [main] INFO  PolarionLicensing - 
*******************************************************************
 Polarion successfully activated
*******************************************************************
2025-07-29 18:05:54,425 [main] INFO  com.polarion.platform.internal.i18n.LanguageContributor - Localization file /META-INF/messages_en.properties read successfully (7789 messages)
2025-07-29 18:05:54,448 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Processing bundles:
2025-07-29 18:05:54,448 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [0] - org.eclipse.osgi
2025-07-29 18:05:54,448 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [1] - org.eclipse.equinox.simpleconfigurator
2025-07-29 18:05:54,448 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [2] - antlr
2025-07-29 18:05:54,448 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [3] - antlr4
2025-07-29 18:05:54,448 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [4] - antlr4-runtime
2025-07-29 18:05:54,449 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [5] - bcprov
2025-07-29 18:05:54,449 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [6] - com.auth0.java-jwt
2025-07-29 18:05:54,449 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [7] - com.fasnote.alm.auth.feishu
2025-07-29 18:05:54,452 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.fasnote.alm.auth.feishu to HiveMind
2025-07-29 18:05:54,452 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [8] - com.fasnote.alm.injection
2025-07-29 18:05:54,452 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [9] - com.fasnote.alm.plugin.manage
2025-07-29 18:05:54,452 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [10] - com.fasnote.alm.queryexpander
2025-07-29 18:05:54,453 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.fasnote.alm.queryexpander to HiveMind
2025-07-29 18:05:54,453 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [11] - com.fasnote.alm.test
2025-07-29 18:05:54,454 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [12] - com.fasterxml.classmate
2025-07-29 18:05:54,454 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [13] - com.fasterxml.jackson
2025-07-29 18:05:54,455 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [14] - com.fasterxml.jackson.dataformat.jackson-dataformat-yaml
2025-07-29 18:05:54,455 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [15] - com.fasterxml.jackson.jaxrs
2025-07-29 18:05:54,455 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [16] - com.fasterxml.woodstox
2025-07-29 18:05:54,455 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [17] - com.google.gson
2025-07-29 18:05:54,455 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [18] - com.google.guava
2025-07-29 18:05:54,455 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [19] - com.google.guava.failureaccess
2025-07-29 18:05:54,455 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [20] - com.ibm.icu.icu4j
2025-07-29 18:05:54,456 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [21] - com.icl.saxon
2025-07-29 18:05:54,456 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [22] - com.jayway.jsonpath.json-path
2025-07-29 18:05:54,456 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [23] - com.jcraft.jsch
2025-07-29 18:05:54,456 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [24] - com.networknt.json-schema-validator
2025-07-29 18:05:54,456 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [25] - com.nimbusds.content-type
2025-07-29 18:05:54,456 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [26] - com.nimbusds.nimbus-jose-jwt
2025-07-29 18:05:54,456 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [27] - com.opensymphony.quartz
2025-07-29 18:05:54,456 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [28] - com.polarion.alm.ProjectPlanGantt_new
2025-07-29 18:05:54,457 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.ProjectPlanGantt_new to HiveMind
2025-07-29 18:05:54,457 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [29] - com.polarion.alm.builder
2025-07-29 18:05:54,457 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.builder to HiveMind
2025-07-29 18:05:54,457 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [30] - com.polarion.alm.checker
2025-07-29 18:05:54,457 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.checker to HiveMind
2025-07-29 18:05:54,457 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [31] - com.polarion.alm.extension.vcontext
2025-07-29 18:05:54,457 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.extension.vcontext to HiveMind
2025-07-29 18:05:54,457 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [32] - com.polarion.alm.impex
2025-07-29 18:05:54,458 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.impex to HiveMind
2025-07-29 18:05:54,458 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [33] - com.polarion.alm.install
2025-07-29 18:05:54,458 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [34] - com.polarion.alm.oslc
2025-07-29 18:05:54,460 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.oslc to HiveMind
2025-07-29 18:05:54,460 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [35] - com.polarion.alm.projects
2025-07-29 18:05:54,461 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.projects to HiveMind
2025-07-29 18:05:54,461 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [36] - com.polarion.alm.qcentre
2025-07-29 18:05:54,461 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.qcentre to HiveMind
2025-07-29 18:05:54,461 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [37] - com.polarion.alm.tracker
2025-07-29 18:05:54,461 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.tracker to HiveMind
2025-07-29 18:05:54,461 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [38] - com.polarion.alm.ui
2025-07-29 18:05:54,464 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.ui to HiveMind
2025-07-29 18:05:54,464 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [39] - com.polarion.alm.ui.diagrams.mxgraph
2025-07-29 18:05:54,464 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [40] - com.polarion.alm.wiki
2025-07-29 18:05:54,465 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.wiki to HiveMind
2025-07-29 18:05:54,465 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [41] - com.polarion.alm.ws
2025-07-29 18:05:54,465 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [42] - com.polarion.alm.ws.client
2025-07-29 18:05:54,466 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [43] - com.polarion.cluster
2025-07-29 18:05:54,466 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.cluster to HiveMind
2025-07-29 18:05:54,466 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [44] - com.polarion.core.boot
2025-07-29 18:05:54,466 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [45] - com.polarion.core.util
2025-07-29 18:05:54,466 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [46] - com.polarion.fop
2025-07-29 18:05:54,467 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [47] - com.polarion.platform
2025-07-29 18:05:54,467 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform to HiveMind
2025-07-29 18:05:54,467 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [48] - com.polarion.platform.guice
2025-07-29 18:05:54,467 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [49] - com.polarion.platform.hivemind
2025-07-29 18:05:54,467 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.hivemind to HiveMind
2025-07-29 18:05:54,467 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [50] - com.polarion.platform.jobs
2025-07-29 18:05:54,468 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.jobs to HiveMind
2025-07-29 18:05:54,468 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [51] - com.polarion.platform.monitoring
2025-07-29 18:05:54,468 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.monitoring to HiveMind
2025-07-29 18:05:54,468 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [52] - com.polarion.platform.persistence
2025-07-29 18:05:54,468 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.persistence to HiveMind
2025-07-29 18:05:54,468 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [53] - com.polarion.platform.repository
2025-07-29 18:05:54,469 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.repository to HiveMind
2025-07-29 18:05:54,469 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [54] - com.polarion.platform.repository.driver.svn
2025-07-29 18:05:54,469 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.repository.driver.svn to HiveMind
2025-07-29 18:05:54,469 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [55] - com.polarion.platform.repository.external
2025-07-29 18:05:54,469 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.repository.external to HiveMind
2025-07-29 18:05:54,469 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [56] - com.polarion.platform.repository.external.git
2025-07-29 18:05:54,469 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.repository.external.git to HiveMind
2025-07-29 18:05:54,469 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [57] - com.polarion.platform.repository.external.svn
2025-07-29 18:05:54,470 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.repository.external.svn to HiveMind
2025-07-29 18:05:54,470 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [58] - com.polarion.platform.sql
2025-07-29 18:05:54,470 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [59] - com.polarion.portal.tomcat
2025-07-29 18:05:54,473 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [60] - com.polarion.psvn.launcher
2025-07-29 18:05:54,473 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.psvn.launcher to HiveMind
2025-07-29 18:05:54,473 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [61] - com.polarion.psvn.translations.en
2025-07-29 18:05:54,473 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [62] - com.polarion.purevariants
2025-07-29 18:05:54,474 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.purevariants to HiveMind
2025-07-29 18:05:54,474 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [63] - com.polarion.qcentre
2025-07-29 18:05:54,474 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [64] - com.polarion.scripting
2025-07-29 18:05:54,477 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.scripting to HiveMind
2025-07-29 18:05:54,477 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [65] - com.polarion.scripting.servlet
2025-07-29 18:05:54,477 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [66] - com.polarion.subterra.base
2025-07-29 18:05:54,477 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [67] - com.polarion.subterra.index
2025-07-29 18:05:54,478 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.subterra.index to HiveMind
2025-07-29 18:05:54,478 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [68] - com.polarion.subterra.persistence
2025-07-29 18:05:54,478 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.subterra.persistence to HiveMind
2025-07-29 18:05:54,478 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [69] - com.polarion.subterra.persistence.document
2025-07-29 18:05:54,479 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.subterra.persistence.document to HiveMind
2025-07-29 18:05:54,479 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [70] - com.polarion.synchronizer
2025-07-29 18:05:54,479 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.synchronizer to HiveMind
2025-07-29 18:05:54,479 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [71] - com.polarion.synchronizer.proxy.feishu
2025-07-29 18:05:54,479 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [72] - com.polarion.synchronizer.proxy.hpalm
2025-07-29 18:05:54,483 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [73] - com.polarion.synchronizer.proxy.jira
2025-07-29 18:05:54,483 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [74] - com.polarion.synchronizer.proxy.polarion
2025-07-29 18:05:54,483 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [75] - com.polarion.synchronizer.proxy.reqif
2025-07-29 18:05:54,492 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [76] - com.polarion.synchronizer.ui
2025-07-29 18:05:54,492 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [77] - com.polarion.usdp.persistence
2025-07-29 18:05:54,492 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.usdp.persistence to HiveMind
2025-07-29 18:05:54,492 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [78] - com.polarion.xray.doc.user
2025-07-29 18:05:54,492 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [79] - com.siemens.des.logger.api
2025-07-29 18:05:54,492 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [80] - com.siemens.plm.bitools.analytics
2025-07-29 18:05:54,492 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [81] - com.siemens.polarion.ct.collectors.git
2025-07-29 18:05:54,493 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.siemens.polarion.ct.collectors.git to HiveMind
2025-07-29 18:05:54,493 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [82] - com.siemens.polarion.eclipse.configurator
2025-07-29 18:05:54,494 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [83] - com.siemens.polarion.integration.ci
2025-07-29 18:05:54,494 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.siemens.polarion.integration.ci to HiveMind
2025-07-29 18:05:54,494 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [84] - com.siemens.polarion.previewer
2025-07-29 18:05:54,494 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.siemens.polarion.previewer to HiveMind
2025-07-29 18:05:54,494 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [85] - com.siemens.polarion.previewer.external
2025-07-29 18:05:54,494 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.siemens.polarion.previewer.external to HiveMind
2025-07-29 18:05:54,494 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [86] - com.siemens.polarion.rest
2025-07-29 18:05:54,495 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [87] - com.siemens.polarion.rt
2025-07-29 18:05:54,495 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [88] - com.siemens.polarion.rt.api
2025-07-29 18:05:54,495 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [89] - com.siemens.polarion.rt.collectors.git
2025-07-29 18:05:54,495 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [90] - com.siemens.polarion.rt.communication.common
2025-07-29 18:05:54,496 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [91] - com.siemens.polarion.rt.communication.polarion
2025-07-29 18:05:54,496 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.siemens.polarion.rt.communication.polarion to HiveMind
2025-07-29 18:05:54,496 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [92] - com.siemens.polarion.rt.communication.rt
2025-07-29 18:05:54,496 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [93] - com.siemens.polarion.rt.parsers.c
2025-07-29 18:05:54,496 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [94] - com.siemens.polarion.rt.ui
2025-07-29 18:05:54,496 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [95] - com.siemens.polarion.synchronizer.proxy.tfs
2025-07-29 18:05:54,497 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [96] - com.sun.activation.javax.activation
2025-07-29 18:05:54,497 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [97] - com.sun.istack.commons-runtime
2025-07-29 18:05:54,497 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [98] - com.sun.jna
2025-07-29 18:05:54,497 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [99] - com.sun.jna.platform
2025-07-29 18:05:54,498 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [100] - com.sun.xml.bind.jaxb-impl
2025-07-29 18:05:54,498 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [101] - com.trilead.ssh2
2025-07-29 18:05:54,498 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [102] - com.zaxxer.hikariCP
2025-07-29 18:05:54,498 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [103] - des-sdk-core
2025-07-29 18:05:54,498 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [104] - des-sdk-dss
2025-07-29 18:05:54,498 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [105] - io.github.resilience4j.circuitbreaker
2025-07-29 18:05:54,498 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [106] - io.github.resilience4j.core
2025-07-29 18:05:54,498 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [107] - io.github.resilience4j.retry
2025-07-29 18:05:54,498 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [108] - io.swagger
2025-07-29 18:05:54,498 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [109] - io.vavr
2025-07-29 18:05:54,498 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [110] - jakaroma
2025-07-29 18:05:54,498 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [111] - jakarta.validation.validation-api
2025-07-29 18:05:54,498 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [112] - javassist
2025-07-29 18:05:54,498 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [113] - javax.annotation-api
2025-07-29 18:05:54,499 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [114] - javax.cache
2025-07-29 18:05:54,499 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [115] - javax.el
2025-07-29 18:05:54,499 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [116] - javax.inject
2025-07-29 18:05:54,499 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [117] - javax.servlet
2025-07-29 18:05:54,499 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [118] - javax.servlet.jsp
2025-07-29 18:05:54,499 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [119] - javax.transaction
2025-07-29 18:05:54,499 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [120] - jaxb-api
2025-07-29 18:05:54,499 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [121] - jcip-annotations
2025-07-29 18:05:54,499 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [122] - jcl.over.slf4j
2025-07-29 18:05:54,499 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [123] - jul.to.slf4j
2025-07-29 18:05:54,499 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [124] - kuromoji-core
2025-07-29 18:05:54,500 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [125] - kuromoji-ipadic
2025-07-29 18:05:54,500 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [126] - lang-tag
2025-07-29 18:05:54,500 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [127] - net.htmlparser.jericho
2025-07-29 18:05:54,500 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [128] - net.java.dev.jna
2025-07-29 18:05:54,500 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [129] - net.minidev.accessors-smart
2025-07-29 18:05:54,500 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [130] - net.minidev.asm
2025-07-29 18:05:54,500 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [131] - net.minidev.json-smart
2025-07-29 18:05:54,500 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [132] - net.n3.nanoxml
2025-07-29 18:05:54,500 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [133] - net.sourceforge.cssparser
2025-07-29 18:05:54,500 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [134] - nu.xom
2025-07-29 18:05:54,500 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [135] - oauth2-oidc-sdk
2025-07-29 18:05:54,501 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [136] - org.apache.ant
2025-07-29 18:05:54,507 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [137] - org.apache.avro
2025-07-29 18:05:54,507 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [138] - org.apache.axis
2025-07-29 18:05:54,508 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [139] - org.apache.batik
2025-07-29 18:05:54,508 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [140] - org.apache.commons.codec
2025-07-29 18:05:54,508 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [141] - org.apache.commons.collections
2025-07-29 18:05:54,508 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [142] - org.apache.commons.commons-beanutils
2025-07-29 18:05:54,508 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [143] - org.apache.commons.commons-collections4
2025-07-29 18:05:54,509 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [144] - org.apache.commons.commons-compress
2025-07-29 18:05:54,509 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [145] - org.apache.commons.commons-fileupload
2025-07-29 18:05:54,509 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [146] - org.apache.commons.digester
2025-07-29 18:05:54,509 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [147] - org.apache.commons.exec
2025-07-29 18:05:54,509 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [148] - org.apache.commons.io
2025-07-29 18:05:54,509 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [149] - org.apache.commons.lang
2025-07-29 18:05:54,509 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [150] - org.apache.commons.lang3
2025-07-29 18:05:54,509 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [151] - org.apache.commons.logging
2025-07-29 18:05:54,509 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [152] - org.apache.curator
2025-07-29 18:05:54,514 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [153] - org.apache.fop
2025-07-29 18:05:54,515 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [154] - org.apache.hivemind
2025-07-29 18:05:54,515 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle org.apache.hivemind to HiveMind
2025-07-29 18:05:54,515 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [155] - org.apache.httpcomponents.httpclient
2025-07-29 18:05:54,516 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [156] - org.apache.httpcomponents.httpcore
2025-07-29 18:05:54,516 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [157] - org.apache.jasper.glassfish
2025-07-29 18:05:54,516 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [158] - org.apache.kafka.clients
2025-07-29 18:05:54,516 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [159] - org.apache.kafka.streams
2025-07-29 18:05:54,516 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [160] - org.apache.logging.log4j.1.2-api
2025-07-29 18:05:54,516 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [161] - org.apache.logging.log4j.api
2025-07-29 18:05:54,517 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [162] - org.apache.logging.log4j.apiconf
2025-07-29 18:05:54,517 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [163] - org.apache.logging.log4j.core
2025-07-29 18:05:54,520 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [164] - org.apache.logging.log4j.slf4j-impl
2025-07-29 18:05:54,520 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [165] - org.apache.lucene.analyzers-common
2025-07-29 18:05:54,520 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [166] - org.apache.lucene.analyzers-common
2025-07-29 18:05:54,521 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [167] - org.apache.lucene.analyzers-smartcn
2025-07-29 18:05:54,521 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [168] - org.apache.lucene.core
2025-07-29 18:05:54,521 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [169] - org.apache.lucene.core
2025-07-29 18:05:54,524 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [170] - org.apache.lucene.grouping
2025-07-29 18:05:54,524 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [171] - org.apache.lucene.queryparser
2025-07-29 18:05:54,524 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [172] - org.apache.oro
2025-07-29 18:05:54,524 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [173] - org.apache.pdfbox.fontbox
2025-07-29 18:05:54,524 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [174] - org.apache.poi
2025-07-29 18:05:54,526 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [175] - org.apache.tika
2025-07-29 18:05:55,048 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [176] - org.apache.xalan
2025-07-29 18:05:55,049 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [177] - org.apache.xercesImpl
2025-07-29 18:05:55,049 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [178] - org.apache.xml.serializer
2025-07-29 18:05:55,049 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [179] - org.apache.xmlgraphics.commons
2025-07-29 18:05:55,049 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [180] - org.apache.zookeeper
2025-07-29 18:05:55,049 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [181] - org.codehaus.groovy
2025-07-29 18:05:55,050 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [182] - org.codehaus.jettison
2025-07-29 18:05:55,050 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [183] - org.dom4j
2025-07-29 18:05:55,050 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [184] - org.eclipse.core.contenttype
2025-07-29 18:05:55,050 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [185] - org.eclipse.core.expressions
2025-07-29 18:05:55,050 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [186] - org.eclipse.core.filesystem
2025-07-29 18:05:55,050 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [187] - org.eclipse.core.jobs
2025-07-29 18:05:55,050 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [188] - org.eclipse.core.net
2025-07-29 18:05:55,050 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [189] - org.eclipse.core.resources
2025-07-29 18:05:55,050 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [190] - org.eclipse.core.runtime
2025-07-29 18:05:55,051 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [191] - org.eclipse.equinox.app
2025-07-29 18:05:55,051 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [192] - org.eclipse.equinox.common
2025-07-29 18:05:55,051 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [193] - org.eclipse.equinox.event
2025-07-29 18:05:55,051 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [194] - org.eclipse.equinox.http.registry
2025-07-29 18:05:55,051 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [195] - org.eclipse.equinox.http.servlet
2025-07-29 18:05:55,051 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [196] - org.eclipse.equinox.jsp.jasper
2025-07-29 18:05:55,051 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [197] - org.eclipse.equinox.jsp.jasper.registry
2025-07-29 18:05:55,051 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [198] - org.eclipse.equinox.launcher
2025-07-29 18:05:55,051 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [199] - org.eclipse.equinox.preferences
2025-07-29 18:05:55,051 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [200] - org.eclipse.equinox.registry
2025-07-29 18:05:55,051 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [201] - org.eclipse.equinox.security
2025-07-29 18:05:55,051 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [202] - org.eclipse.help
2025-07-29 18:05:55,051 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [203] - org.eclipse.help.base
2025-07-29 18:05:55,052 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [204] - org.eclipse.help.webapp
2025-07-29 18:05:55,052 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [205] - org.eclipse.jgit
2025-07-29 18:05:55,052 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [206] - org.eclipse.osgi.services
2025-07-29 18:05:55,052 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [207] - org.eclipse.osgi.util
2025-07-29 18:05:55,052 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [208] - org.ehcache
2025-07-29 18:05:55,053 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [209] - org.gitlab.java-gitlab-api
2025-07-29 18:05:55,053 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [210] - org.glassfish.jersey
2025-07-29 18:05:55,054 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [211] - org.hibernate.annotations
2025-07-29 18:05:55,054 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [212] - org.hibernate.core
2025-07-29 18:05:55,055 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [213] - org.hibernate.entitymanager
2025-07-29 18:05:55,055 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [214] - org.hibernate.hikaricp
2025-07-29 18:05:55,055 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [215] - org.hibernate.jpa.2.1.api
2025-07-29 18:05:55,055 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [216] - org.jboss.logging
2025-07-29 18:05:55,055 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [217] - org.jvnet.mimepull
2025-07-29 18:05:55,055 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [218] - org.objectweb.asm
2025-07-29 18:05:55,056 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [219] - org.objectweb.jotm
2025-07-29 18:05:55,056 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [220] - org.opensaml
2025-07-29 18:05:55,068 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [221] - org.polarion.svncommons
2025-07-29 18:05:55,068 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [222] - org.polarion.svnwebclient
2025-07-29 18:05:55,068 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [223] - org.postgesql
2025-07-29 18:05:55,068 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [224] - org.projectlombok.lombok
2025-07-29 18:05:55,079 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [225] - org.rocksdb.rocksdbjni
2025-07-29 18:05:55,079 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [226] - org.springframework.data.core
2025-07-29 18:05:55,079 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [227] - org.springframework.data.jpa
2025-07-29 18:05:55,079 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [228] - org.springframework.spring-aop
2025-07-29 18:05:55,079 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [229] - org.springframework.spring-beans
2025-07-29 18:05:55,079 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [230] - org.springframework.spring-context
2025-07-29 18:05:55,080 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [231] - org.springframework.spring-core
2025-07-29 18:05:55,080 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [232] - org.springframework.spring-expression
2025-07-29 18:05:55,080 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [233] - org.springframework.spring-jdbc
2025-07-29 18:05:55,080 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [234] - org.springframework.spring-orm
2025-07-29 18:05:55,080 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [235] - org.springframework.spring-test
2025-07-29 18:05:55,080 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [236] - org.springframework.spring-tx
2025-07-29 18:05:55,080 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [237] - org.springframework.spring-web
2025-07-29 18:05:55,080 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [238] - org.springframework.spring-webmvc
2025-07-29 18:05:55,080 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [239] - org.tmatesoft.sqljet
2025-07-29 18:05:55,080 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [240] - org.tmatesoft.svnkit
2025-07-29 18:05:55,081 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [241] - saaj-api
2025-07-29 18:05:55,081 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [242] - sdk-lifecycle-collab
2025-07-29 18:05:55,081 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [243] - sdk-lifecycle-docmgmt
2025-07-29 18:05:55,081 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [244] - siemens.des.clientsecurity
2025-07-29 18:05:55,081 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [245] - slf4j.api
2025-07-29 18:05:55,081 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [246] - xml-apis
2025-07-29 18:05:55,081 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [247] - xml.apis.ext
2025-07-29 18:05:55,082 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [248] - xstream
2025-07-29 18:05:55,269 [main] INFO  com.polarion.core.util.remote.server.SocketRemoteControlServer - Remote control server socket is ready to listen on localhost/127.0.0.1:8887
2025-07-29 18:05:55,270 [xServer:8887] INFO  org.xsocket.connection.Server - server listening on localhost:8887 (xSocket 2.5.3)
2025-07-29 18:05:55,463 [main] INFO  com.polarion.platform.sql.internal.SqlModule - Initializing database...
2025-07-29 18:05:55,518 [main] INFO  com.polarion.platform.sql.internal.PgServerInfo - PG server listening on localhost:5435
2025-07-29 18:05:57,673 [main] INFO  com.polarion.platform.internal.cache.CacheConfigurator - EHCache uses internal configuration
2025-07-29 18:05:57,976 [main] WARN  org.ehcache.impl.internal.executor.PooledExecutionService - No default pool configured, services requiring thread pools must be configured explicitly using named thread pools
2025-07-29 18:05:58,050 [main] INFO  org.ehcache.sizeof.filters.AnnotationSizeOfFilter - Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-07-29 18:05:58,055 [main] INFO  org.ehcache.sizeof.impl.JvmInformation - Detected JVM data model settings of: 64-Bit OpenJDK JVM with Compressed OOPs
2025-07-29 18:05:58,068 [main] INFO  org.ehcache.sizeof.impl.AgentLoader - Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-07-29 18:05:58,163 [main] INFO  com.polarion.platform.internal.cache.CachingProviderHandler - All the caches have been destroyed because of not clean shutdown. You can ignore this message if Polarion started in reindex mode.
2025-07-29 18:05:58,196 [main] INFO  com.polarion.platform.sql.internal.PgConnection - JDBC url of database 'polarion' is: *****************************************
2025-07-29 18:05:58,197 [main] INFO  com.polarion.platform.sql.internal.PgConnection - JDBC url of database 'polarion' is: *****************************************
2025-07-29 18:05:58,199 [main] INFO  com.polarion.platform.sql.internal.PgConnection - JDBC url of database 'polarion_history' is: *************************************************
2025-07-29 18:05:58,246 [main] INFO  com.polarion.platform.sql.internal.SqlModule - Initializing database finished [ TIME 2.78 s. ]
2025-07-29 18:05:58,421 [main] INFO  com.polarion.platform.cluster.ClusterService - Initializing cluster service
2025-07-29 18:05:58,421 [main] INFO  com.polarion.platform.cluster.ClusterService - Cluster service is disabled.
2025-07-29 18:05:58,643 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-29 18:05:58,665 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool - pg polarion_history - Starting...
2025-07-29 18:05:58,729 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool - pg polarion_history - Start completed.
2025-07-29 18:05:58,804 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.16 s. ]
2025-07-29 18:05:58,804 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-29 18:05:58,818 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool - pg polarion - Starting...
2025-07-29 18:05:58,833 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool - pg polarion - Start completed.
2025-07-29 18:05:58,912 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.108 s. ]
2025-07-29 18:05:58,912 [main] INFO  com.polarion.psvn.launcher.internal.platform.PlatformService - Platform boot started
2025-07-29 18:05:58,969 [main] INFO  com.polarion.platform.repository.driver.svn.internal.security.SVNWatcher - SVN auth file watcher started with a period of 3000 milliseconds
2025-07-29 18:05:58,981 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-29 18:05:59,040 [main] INFO  com.polarion.platform.internal.security.auth.LoginFlowUserPassword - User polarion authenticated from system
2025-07-29 18:05:59,113 [main] INFO  com.polarion.platform.internal.security.auth.LoginFlowUserPassword - User polarion logged in from system
2025-07-29 18:05:59,118 [main | u:p] INFO  com.polarion.platform.internal.service.repository.PlatformRepositoryService - Repository Service Created
2025-07-29 18:05:59,119 [main | u:p] INFO  com.polarion.platform.internal.service.repository.PlatformRepositoryService - Repository Service Initialized
2025-07-29 18:05:59,126 [main | u:p] INFO  com.polarion.core.util.profiling.SimpleProfiler - Initialization
2025-07-29 18:05:59,143 [main | u:p] INFO  org.objectweb.jotm - JOTM started with a local transaction factory which is not bound.
2025-07-29 18:05:59,143 [main | u:p] INFO  org.objectweb.jotm - CAROL initialization
2025-07-29 18:05:59,151 [main | u:p] INFO  com.polarion.platform.internal.service.repository.listeners.job.PullingJob - lastFullyProcessedRevision [204]
2025-07-29 18:05:59,162 [main | u:p] INFO  com.polarion.platform.internal.service.repository.PlatformRepositoryService - END initializeService
2025-07-29 18:05:59,173 [main | u:p] INFO  com.polarion.psvn.launcher.internal.platform.PlatformService - Polarion startup estimation:  [ TIME 12 s. ]
2025-07-29 18:05:59,173 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 12 s. ]
2025-07-29 18:05:59,185 [main | u:p] INFO  com.polarion.platform.monitoring - Full monitoring results are stored in file /opt/polarion/data/workspace/monitoring/results.txt
2025-07-29 18:05:59,240 [main | u:p] INFO  com.polarion.platform.monitoring - Executing actions from stage PREBOOT
2025-07-29 18:05:59,242 [main | u:p] INFO  com.polarion.platform.monitoring - Executing action 'system.info.disk.data.free'
2025-07-29 18:05:59,245 [main | u:p] INFO  com.polarion.platform.monitoring - system.info.disk.data.free (Free data disk '/opt/polarion/data' space) = Disk Space 
1,427.04 GB
 [Tue Jul 29 18:05:59 CST 2025]
2025-07-29 18:05:59,450 [main | u:p] INFO  com.polarion.platform.monitoring - Executing action 'system.info.jvm.version'
2025-07-29 18:05:59,450 [main | u:p] INFO  com.polarion.platform.monitoring - system.info.jvm.version (JVM Version) = JVM Version 
Microsoft OpenJDK 64-Bit Server VM 11.0.27+6-LTS
 [Tue Jul 29 18:05:59 CST 2025]
2025-07-29 18:05:59,454 [main | u:p] INFO  com.polarion.platform.monitoring - Executing action 'system.info.os.version'
2025-07-29 18:05:59,454 [main | u:p] INFO  com.polarion.platform.monitoring - system.info.os.version (OS Version) = OS Version 
Mac OS X 15.5 aarch64
 [Tue Jul 29 18:05:59 CST 2025]
2025-07-29 18:05:59,463 [main | u:p] INFO  com.polarion.platform.monitoring - Finished with actions from stage PREBOOT: {OK=3}
2025-07-29 18:05:59,463 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.15 s. ]
2025-07-29 18:05:59,464 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0797 s [65% update (144x), 35% query (12x)] (221x), svn: 0.0253 s [65% getLatestRevision (2x), 22% testConnection (1x)] (4x)
2025-07-29 18:05:59,464 [main | u:p] INFO  com.polarion.psvn.launcher.internal.platform.PlatformService - calling ILowLevelPersistence.boot to start persistence
2025-07-29 18:05:59,488 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Persistence initialization started
2025-07-29 18:05:59,569 [main | u:p] INFO  com.polarion.subterra.base.internal.location.LocationCacheContext - Registered invalidationListener: com.polarion.platform.repository.internal.config.RepositoryConfigService$1@26049ca2
2025-07-29 18:05:59,598 [main | u:p] INFO  com.polarion.platform.persistence.internal.CustomFieldsService - Custom fields control field is not set for prototype: BaselineCollection
2025-07-29 18:05:59,598 [main | u:p] INFO  com.polarion.platform.persistence.internal.CustomFieldsService - Custom fields control field is not set for prototype: TestRun
2025-07-29 18:05:59,598 [main | u:p] INFO  com.polarion.platform.persistence.internal.CustomFieldsService - Custom fields control field is not set for prototype: Plan
2025-07-29 18:05:59,613 [main | u:p] INFO  com.polarion.platform.repository.internal.context.RepositoryContextService - Context recognition started
2025-07-29 18:05:59,613 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:05:59,613 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-29 18:05:59,635 [main | u:p] INFO  com.polarion.platform.repository.internal.context.RepositoryContextService - Context recognition finished [ TIME 0.0223 s. ]
2025-07-29 18:05:59,635 [main | u:p] INFO  com.polarion.platform.repository.internal.context.RepositoryContextService - Context tree: 
ROOT_CTX_NAME (ContextNature[Root], ContextId[context [global]])
+-default (ContextNature[Repository], ContextId[cluster default, context [global]])
  +-WBS (ContextNature[Project], ContextId[cluster default, context WBS])
  +-Demo Projects (ContextNature[ProjectGroup], ContextId[cluster default, context --Demo Projects])
  | +-drivepilot (ContextNature[Project], ContextId[cluster default, context drivepilot])
  | +-elibrary (ContextNature[Project], ContextId[cluster default, context elibrary])
  +-library (ContextNature[Project], ContextId[cluster default, context library])
  +-WBSdev (ContextNature[Project], ContextId[cluster default, context WBSdev])
  +-hesai (ContextNature[Project], ContextId[cluster default, context hesai])
2025-07-29 18:05:59,635 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.17 s. ]
2025-07-29 18:05:59,636 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0299 s [56% getDir2 content (2x), 33% info (3x)] (6x)
2025-07-29 18:05:59,636 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:05:59,636 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-29 18:05:59,636 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Startup workers for phase 3: 6
2025-07-29 18:05:59,643 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (3/9)
2025-07-29 18:05:59,643 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (2/9)
2025-07-29 18:05:59,643 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context WBS] (2/9) ...
2025-07-29 18:05:59,643 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context library] (3/9) ...
2025-07-29 18:05:59,643 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (4/9)
2025-07-29 18:05:59,643 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (1/9)
2025-07-29 18:05:59,643 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (5/9)
2025-07-29 18:05:59,643 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context --Demo Projects] (4/9) ...
2025-07-29 18:05:59,643 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (6/9)
2025-07-29 18:05:59,643 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[context [global]] (1/9) ...
2025-07-29 18:05:59,643 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context [global]] (5/9) ...
2025-07-29 18:05:59,644 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context WBSdev] (6/9) ...
2025-07-29 18:05:59,652 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[context [global]] (1/9) TOOK  [ TIME 0.00792 s. ]
2025-07-29 18:05:59,653 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-07-29 18:05:59,653 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context hesai] (7/9) ...
2025-07-29 18:05:59,882 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/Demo Projects/.polarion'
2025-07-29 18:05:59,894 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/Demo Projects/modules'
2025-07-29 18:05:59,902 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/Demo Projects/documents'
2025-07-29 18:05:59,916 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/Demo Projects/_wiki'
2025-07-29 18:05:59,917 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context --Demo Projects contains 0 primary objects (work items+comments).
2025-07-29 18:05:59,917 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context --Demo Projects] (4/9) TOOK  [ TIME 0.274 s. ]
2025-07-29 18:05:59,917 [LowLevelDataService-contextInitializer-4 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-29 18:05:59,917 [LowLevelDataService-contextInitializer-4 | u:p | context: drivepilot] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context drivepilot] (8/9) ...
2025-07-29 18:05:59,932 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/WBS/documents'
2025-07-29 18:05:59,935 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/library/documents'
2025-07-29 18:06:00,003 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context library contains 287 primary objects (work items+comments).
2025-07-29 18:06:00,004 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context library] (3/9) TOOK  [ TIME 0.36 s. ]
2025-07-29 18:06:00,004 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-29 18:06:00,004 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context elibrary] (9/9) ...
2025-07-29 18:06:00,040 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context WBS contains 344 primary objects (work items+comments).
2025-07-29 18:06:00,041 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context WBS] (2/9) TOOK  [ TIME 0.398 s. ]
2025-07-29 18:06:00,080 [LowLevelDataService-contextInitializer-4 | u:p | context: drivepilot] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/Demo Projects/drivepilot/documents'
2025-07-29 18:06:00,098 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/Demo Projects/elibrary/documents'
2025-07-29 18:06:00,113 [LowLevelDataService-contextInitializer-4 | u:p | context: drivepilot] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context drivepilot contains 461 primary objects (work items+comments).
2025-07-29 18:06:00,114 [LowLevelDataService-contextInitializer-4 | u:p | context: drivepilot] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context drivepilot] (8/9) TOOK  [ TIME 0.196 s. ]
2025-07-29 18:06:00,115 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/hesai/documents'
2025-07-29 18:06:00,124 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context elibrary contains 334 primary objects (work items+comments).
2025-07-29 18:06:00,124 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context elibrary] (9/9) TOOK  [ TIME 0.12 s. ]
2025-07-29 18:06:00,274 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context hesai contains 1148 primary objects (work items+comments).
2025-07-29 18:06:00,274 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context hesai] (7/9) TOOK  [ TIME 0.622 s. ]
2025-07-29 18:06:00,352 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context null contains 2214 primary objects (work items+comments).
2025-07-29 18:06:00,352 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context [global]] (5/9) TOOK  [ TIME 0.709 s. ]
2025-07-29 18:06:00,400 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/WBSdev/documents'
2025-07-29 18:06:00,536 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context WBSdev contains 3321 primary objects (work items+comments).
2025-07-29 18:06:00,536 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context WBSdev] (6/9) TOOK  [ TIME 0.892 s. ]
2025-07-29 18:06:00,537 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.898 s, CPU [user: 0.0768 s, system: 0.0766 s], Allocated memory: 10.7 MB, transactions: 0, svn: 0.166 s [80% log2 (10x), 15% getLatestRevision (2x)] (13x)
2025-07-29 18:06:00,537 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.898 s, CPU [user: 0.226 s, system: 0.256 s], Allocated memory: 52.8 MB, transactions: 0, ObjectMaps: 0.195 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-29 18:06:00,537 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.898 s, CPU [user: 0.0516 s, system: 0.0675 s], Allocated memory: 6.9 MB, transactions: 0, ObjectMaps: 0.0951 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0661 s [82% log2 (5x)] (7x)
2025-07-29 18:06:00,537 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.894 s, CPU [user: 0.258 s, system: 0.325 s], Allocated memory: 68.4 MB, transactions: 0, ObjectMaps: 0.136 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-29 18:06:00,537 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.898 s, CPU [user: 0.166 s, system: 0.181 s], Allocated memory: 26.7 MB, transactions: 0, ObjectMaps: 0.154 s [100% getAllPrimaryObjects (1x)] (10x), svn: 0.11 s [33% info (5x), 22% log2 (5x), 18% log (1x), 11% testConnection (1x)] (18x)
2025-07-29 18:06:00,538 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.895 s, CPU [user: 0.081 s, system: 0.109 s], Allocated memory: 12.3 MB, transactions: 0, svn: 0.0887 s [81% log2 (10x)] (13x), ObjectMaps: 0.0804 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-29 18:06:00,538 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.9 s. ]
2025-07-29 18:06:00,538 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.687 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.488 s [65% log2 (36x), 13% getLatestRevision (9x), 10% testConnection (6x)] (61x)
2025-07-29 18:06:00,552 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.RevisionsPersistenceModule - Processing new revisions [START].
2025-07-29 18:06:00,552 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:06:00,552 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-29 18:06:00,807 [main | u:p] INFO  TXLOGGER - Tx 6615695a5d401_0_6615695a5d401_0_: finished. Total: 0.24 s, CPU [user: 0.124 s, system: 0.0118 s], Allocated memory: 21.8 MB
2025-07-29 18:06:00,824 [main | u:p | u:p] ERROR com.polarion.platform.repository.internal.config.RepositoryConfigService$ConfigProblemCatcher - Failed to work with configuration from location /WBS/.polarion/repositories/repositories.xml:
[/WBS/.polarion/repositories/repositories.xml]: 
---- Debugging information ----
cause-exception     : com.thoughtworks.xstream.mapper.CannotResolveClassException
cause-message       : AutoBranchGitLab
class               : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
required-type       : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
converter-type      : com.thoughtworks.xstream.converters.collections.ArrayConverter
version             : 1.4.17
-------------------------------
com.polarion.platform.repository.config.RepositoryConfigurationException: [/WBS/.polarion/repositories/repositories.xml]: 
---- Debugging information ----
cause-exception     : com.thoughtworks.xstream.mapper.CannotResolveClassException
cause-message       : AutoBranchGitLab
class               : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
required-type       : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
converter-type      : com.thoughtworks.xstream.converters.collections.ArrayConverter
version             : 1.4.17
-------------------------------
	at com.polarion.platform.repository.spi.config.AbstractDataHandler.readLocation(AbstractDataHandler.java:87) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.spi.config.AbstractDataHandler.readLocations(AbstractDataHandler.java:61) ~[platform-repository.jar:?]
	at $IDataHandler_19855a5564d.readLocations($IDataHandler_19855a5564d.java) ~[?:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.readLocations(RepositoryConfigService.java:291) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService$3.runImpl(RepositoryConfigService.java:328) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService$SVNAccessAction$1.runWEx(RepositoryConfigService.java:113) ~[platform-repository.jar:?]
	at com.polarion.core.util.RunnableWEx.runWRet(RunnableWEx.java:61) ~[util.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService$SVNAccessAction.run(RepositoryConfigService.java:123) ~[platform-repository.jar:?]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:?]
	at javax.security.auth.Subject.doAs(Subject.java:361) ~[?:?]
	at com.polarion.platform.internal.security.SubjectNDC.doAs(SubjectNDC.java:58) ~[platform.jar:?]
	at com.polarion.platform.internal.security.SecurityService.doAsUser(SecurityService.java:422) ~[platform.jar:?]
	at com.polarion.platform.internal.security.SecurityService.doAsSystemUser(SecurityService.java:412) ~[platform.jar:?]
	at $ISecurityService_19855a55468.doAsSystemUser($ISecurityService_19855a55468.java) ~[?:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.readConfiguration(RepositoryConfigService.java:324) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.getConfigurationImpl(RepositoryConfigService.java:239) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.getReadConfigurationImpl(RepositoryConfigService.java:199) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.getReadConfiguration(RepositoryConfigService.java:177) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.addConfigurationListener(RepositoryConfigService.java:263) ~[platform-repository.jar:?]
	at $IRepositoryConfigService_19855a55476.addConfigurationListener($IRepositoryConfigService_19855a55476.java) ~[?:?]
	at com.polarion.platform.repository.external.internal.ExternalRepositoryProviderRegistry.initialize(ExternalRepositoryProviderRegistry.java:146) ~[platform-repository.jar:?]
	at $IExternalRepositoryProviderRegistry_19855a55532.initialize($IExternalRepositoryProviderRegistry_19855a55532.java) ~[?:?]
	at $IExternalRepositoryProviderRegistry_19855a55531.initialize($IExternalRepositoryProviderRegistry_19855a55531.java) ~[?:?]
	at com.polarion.platform.persistence.internal.pe.RevisionsPersistenceModule.initModule(RevisionsPersistenceModule.java:353) ~[platform-persistence.jar:?]
	at $IObjectPersistenceModule_19855a55621.initModule($IObjectPersistenceModule_19855a55621.java) ~[?:?]
	at com.polarion.subterra.persistence.internal.PersistenceEngine.initModule(PersistenceEngine.java:251) ~[subterra-uniform-persistence.jar:?]
	at $IPersistenceEngine_19855a55609.initModule($IPersistenceEngine_19855a55609.java) ~[?:?]
	at com.polarion.platform.persistence.internal.pe.LowLevelDataService.boot(LowLevelDataService.java:352) ~[platform-persistence.jar:?]
	at $ILowLevelPersistence_19855a5552e.boot($ILowLevelPersistence_19855a5552e.java) ~[?:?]
	at $ILowLevelPersistence_19855a5552d.boot($ILowLevelPersistence_19855a5552d.java) ~[?:?]
	at com.polarion.psvn.launcher.internal.platform.PlatformService.lambda$0(PlatformService.java:294) ~[launcher.jar:?]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:?]
	at javax.security.auth.Subject.doAs(Subject.java:423) [?:?]
	at com.polarion.platform.internal.security.SubjectNDC.doAs(SubjectNDC.java:69) [platform.jar:?]
	at com.polarion.platform.internal.security.SecurityService.doAsUser(SecurityService.java:417) [platform.jar:?]
	at com.polarion.platform.internal.security.SecurityService.doAsSystemUser(SecurityService.java:407) [platform.jar:?]
	at $ISecurityService_19855a55469.doAsSystemUser($ISecurityService_19855a55469.java) [?:?]
	at $ISecurityService_19855a55468.doAsSystemUser($ISecurityService_19855a55468.java) [?:?]
	at com.polarion.psvn.launcher.internal.platform.PlatformService.bootPlatform(PlatformService.java:286) [launcher.jar:?]
	at com.polarion.psvn.launcher.internal.platform.PlatformService.start(PlatformService.java:92) [launcher.jar:?]
	at com.polarion.psvn.launcher.PolarionSVNApplication.runImpl(PolarionSVNApplication.java:139) [launcher.jar:?]
	at com.polarion.psvn.launcher.PolarionSVNApplication.run(PolarionSVNApplication.java:94) [launcher.jar:?]
	at com.polarion.core.boot.launchers.BasicAppLauncher.launch(BasicAppLauncher.java:53) [boot.jar:?]
	at com.polarion.core.boot.impl.AppLaunchersManager.start(AppLaunchersManager.java:184) [boot.jar:?]
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:196) [org.eclipse.equinox.app_1.3.500.v20171221-2204.jar:?]
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:134) [org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:104) [org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:388) [org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:243) [org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:566) ~[?:?]
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:656) [org.eclipse.equinox.launcher_1.5.0.v20180512-1130.jar:?]
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:592) [org.eclipse.equinox.launcher_1.5.0.v20180512-1130.jar:?]
	at org.eclipse.equinox.launcher.Main.run(Main.java:1498) [org.eclipse.equinox.launcher_1.5.0.v20180512-1130.jar:?]
	at org.eclipse.equinox.launcher.Main.main(Main.java:1471) [org.eclipse.equinox.launcher_1.5.0.v20180512-1130.jar:?]
Caused by: com.thoughtworks.xstream.converters.ConversionException: 
---- Debugging information ----
cause-exception     : com.thoughtworks.xstream.mapper.CannotResolveClassException
cause-message       : AutoBranchGitLab
class               : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
required-type       : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
converter-type      : com.thoughtworks.xstream.converters.collections.ArrayConverter
version             : 1.4.17
-------------------------------
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convert(TreeUnmarshaller.java:77) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convertAnother(TreeUnmarshaller.java:66) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convertAnother(TreeUnmarshaller.java:50) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.start(TreeUnmarshaller.java:134) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.AbstractTreeMarshallingStrategy.unmarshal(AbstractTreeMarshallingStrategy.java:32) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.unmarshal(XStream.java:1431) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.unmarshal(XStream.java:1411) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.fromXML(XStream.java:1305) ~[xstream-1.4.17.jar:1.4.17]
	at com.polarion.platform.repository.external.internal.RepositoriesDataHandler.processData(RepositoriesDataHandler.java:119) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.spi.config.AbstractDataHandler.readLocation(AbstractDataHandler.java:83) ~[platform-repository.jar:?]
	... 56 more
Caused by: com.thoughtworks.xstream.mapper.CannotResolveClassException: AutoBranchGitLab
	at com.thoughtworks.xstream.mapper.DefaultMapper.realClass(DefaultMapper.java:81) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.DynamicProxyMapper.realClass(DynamicProxyMapper.java:55) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.PackageAliasingMapper.realClass(PackageAliasingMapper.java:88) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.ClassAliasingMapper.realClass(ClassAliasingMapper.java:79) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.ArrayMapper.realClass(ArrayMapper.java:74) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.SecurityMapper.realClass(SecurityMapper.java:71) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.CachingMapper.realClass(CachingMapper.java:47) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.util.HierarchicalStreams.readClassType(HierarchicalStreams.java:29) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.converters.collections.AbstractCollectionConverter.readBareItem(AbstractCollectionConverter.java:131) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.converters.collections.AbstractCollectionConverter.readItem(AbstractCollectionConverter.java:117) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.converters.collections.AbstractCollectionConverter.readCompleteItem(AbstractCollectionConverter.java:147) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.converters.collections.ArrayConverter.unmarshal(ArrayConverter.java:54) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convert(TreeUnmarshaller.java:72) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convertAnother(TreeUnmarshaller.java:66) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convertAnother(TreeUnmarshaller.java:50) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.start(TreeUnmarshaller.java:134) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.AbstractTreeMarshallingStrategy.unmarshal(AbstractTreeMarshallingStrategy.java:32) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.unmarshal(XStream.java:1431) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.unmarshal(XStream.java:1411) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.fromXML(XStream.java:1305) ~[xstream-1.4.17.jar:1.4.17]
	at com.polarion.platform.repository.external.internal.RepositoriesDataHandler.processData(RepositoriesDataHandler.java:119) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.spi.config.AbstractDataHandler.readLocation(AbstractDataHandler.java:83) ~[platform-repository.jar:?]
	... 56 more
2025-07-29 18:06:00,993 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-29 18:06:01,007 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.RevisionsPersistenceModule - Processing new revisions from repository default in context ContextId[context [global]] finished
2025-07-29 18:06:01,008 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.RevisionsPersistenceModule - Processing new revisions [FINISHED].
2025-07-29 18:06:01,008 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.47 s. ]
2025-07-29 18:06:01,008 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.425 s [100% getReadConfiguration (48x)] (48x), svn: 0.0986 s [84% info (18x)] (38x)
2025-07-29 18:06:01,044 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:06:01,044 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-29 18:06:01,044 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Inspecting repository for build artifacts-related changes
2025-07-29 18:06:01,045 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[context [global]]
2025-07-29 18:06:01,045 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[context [global]]
2025-07-29 18:06:01,046 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[context [global]] has been successfully processed
2025-07-29 18:06:01,049 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[context [global]] finished [ TIME 0.00437 s. ]
2025-07-29 18:06:01,049 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-29 18:06:01,049 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context [global]]
2025-07-29 18:06:01,049 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context [global]]
2025-07-29 18:06:01,077 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context [global]] has been successfully processed
2025-07-29 18:06:01,109 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context [global]] finished [ TIME 0.0599 s. ]
2025-07-29 18:06:01,109 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-29 18:06:01,109 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context WBS]
2025-07-29 18:06:01,109 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context WBS]
2025-07-29 18:06:01,132 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context WBS] has been successfully processed
2025-07-29 18:06:01,165 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context WBS] finished [ TIME 0.0554 s. ]
2025-07-29 18:06:01,165 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-07-29 18:06:01,165 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context --Demo Projects]
2025-07-29 18:06:01,165 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context --Demo Projects]
2025-07-29 18:06:01,178 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context --Demo Projects] has been successfully processed
2025-07-29 18:06:01,194 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context --Demo Projects] finished [ TIME 0.0293 s. ]
2025-07-29 18:06:01,194 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (4/9)
2025-07-29 18:06:01,194 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context library]
2025-07-29 18:06:01,194 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context library]
2025-07-29 18:06:01,204 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context library] has been successfully processed
2025-07-29 18:06:01,220 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context library] finished [ TIME 0.0257 s. ]
2025-07-29 18:06:01,220 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (5/9)
2025-07-29 18:06:01,220 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context WBSdev]
2025-07-29 18:06:01,220 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context WBSdev]
2025-07-29 18:06:01,239 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context WBSdev] has been successfully processed
2025-07-29 18:06:01,264 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context WBSdev] finished [ TIME 0.0432 s. ]
2025-07-29 18:06:01,264 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (6/9)
2025-07-29 18:06:01,264 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context hesai]
2025-07-29 18:06:01,264 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context hesai]
2025-07-29 18:06:01,280 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context hesai] has been successfully processed
2025-07-29 18:06:01,299 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context hesai] finished [ TIME 0.0355 s. ]
2025-07-29 18:06:01,299 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-07-29 18:06:01,299 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context drivepilot]
2025-07-29 18:06:01,299 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context drivepilot]
2025-07-29 18:06:01,319 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context drivepilot] has been successfully processed
2025-07-29 18:06:01,351 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context drivepilot] finished [ TIME 0.0518 s. ]
2025-07-29 18:06:01,351 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-29 18:06:01,351 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context elibrary]
2025-07-29 18:06:01,351 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context elibrary]
2025-07-29 18:06:01,370 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context elibrary] has been successfully processed
2025-07-29 18:06:01,394 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context elibrary] finished [ TIME 0.043 s. ]
2025-07-29 18:06:01,394 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-29 18:06:01,394 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... repository inspection finished [ TIME 0.35 s. ]
2025-07-29 18:06:01,394 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.39 s. ]
2025-07-29 18:06:01,395 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.303 s [76% info (94x), 17% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.226 s [100% getReadConfiguration (54x)] (54x)
2025-07-29 18:06:01,395 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:06:01,395 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-29 18:06:01,395 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Inspecting BIR for new or removed builds
2025-07-29 18:06:01,409 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... 0 build(s) were removed (including calculations from previous run)
2025-07-29 18:06:01,409 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... 0 build(s) were added or modified
2025-07-29 18:06:01,409 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... BIR inspection finished [ TIME 0.0145 s. ]
2025-07-29 18:06:01,409 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-07-29 18:06:01,409 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:06:01,409 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-29 18:06:01,409 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.PersistenceEngineListener - Flushing startup index events, starting iterations.
2025-07-29 18:06:01,410 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.PersistenceEngineListener - Iteration 1 - processing 5 events
2025-07-29 18:06:01,415 [main | u:p] INFO  com.polarion.alm.tracker.internal.planning.PlanFieldsProvider - livePlanXMLLocation: Location[path /default/.reports/xml/live-plan.xml]
2025-07-29 18:06:01,438 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.PersistenceEngineListener -  - reindexing 1 existing objects and 0 deleted objects.
2025-07-29 18:06:01,517 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-29 18:06:01,520 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-29 18:06:01,523 [main | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Revision
2025-07-29 18:06:01,523 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: WikiPage
2025-07-29 18:06:01,523 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Category
2025-07-29 18:06:01,523 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Attachment
2025-07-29 18:06:01,534 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Attachment
2025-07-29 18:06:01,534 [PolarionDocIdCreator-6] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: RichPageAttachment
2025-07-29 18:06:01,538 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: WorkItem
2025-07-29 18:06:01,559 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: WikiPage
2025-07-29 18:06:01,560 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Category
2025-07-29 18:06:01,560 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: User
2025-07-29 18:06:01,563 [PolarionDocIdCreator-6] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: RichPageAttachment
2025-07-29 18:06:01,566 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: User
2025-07-29 18:06:01,568 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: UserGroup
2025-07-29 18:06:01,569 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: WorkItem
2025-07-29 18:06:01,570 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: UserGroup
2025-07-29 18:06:01,570 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: BaselineCollection
2025-07-29 18:06:01,573 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: TestRun
2025-07-29 18:06:01,581 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: TestRun
2025-07-29 18:06:01,582 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Build
2025-07-29 18:06:01,583 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Build
2025-07-29 18:06:01,583 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: BaselineCollection
2025-07-29 18:06:01,583 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: ModuleComment
2025-07-29 18:06:01,584 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Comment
2025-07-29 18:06:01,585 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Comment
2025-07-29 18:06:01,585 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: DocumentWorkflowSignature
2025-07-29 18:06:01,587 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: DocumentWorkflowSignature
2025-07-29 18:06:01,587 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: ModuleComment
2025-07-29 18:06:01,587 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: BuildArtifact
2025-07-29 18:06:01,587 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: TimePoint
2025-07-29 18:06:01,587 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: WikiPageAttachment
2025-07-29 18:06:01,588 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: TimePoint
2025-07-29 18:06:01,588 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Baseline
2025-07-29 18:06:01,588 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: WikiPageAttachment
2025-07-29 18:06:01,588 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: BuildArtifact
2025-07-29 18:06:01,592 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: WorkRecord
2025-07-29 18:06:01,593 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: WorkRecord
2025-07-29 18:06:01,594 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Baseline
2025-07-29 18:06:01,594 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: TestRunAttachment
2025-07-29 18:06:01,594 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: WorkItem-OutlineNumbers
2025-07-29 18:06:01,594 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Project
2025-07-29 18:06:01,596 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: WorkItem-OutlineNumbers
2025-07-29 18:06:01,596 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: TestRunAttachment
2025-07-29 18:06:01,596 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Plan
2025-07-29 18:06:01,596 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: TestRunComment
2025-07-29 18:06:01,596 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: ModuleAttachment
2025-07-29 18:06:01,597 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: TestRunComment
2025-07-29 18:06:01,598 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Revision
2025-07-29 18:06:01,600 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Project
2025-07-29 18:06:01,601 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: RichPage
2025-07-29 18:06:01,603 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Revision
2025-07-29 18:06:01,604 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}20
2025-07-29 18:06:01,604 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}9
2025-07-29 18:06:01,604 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}19
2025-07-29 18:06:01,604 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}10
2025-07-29 18:06:01,604 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}15
2025-07-29 18:06:01,604 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}16
2025-07-29 18:06:01,604 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}17
2025-07-29 18:06:01,604 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}18
2025-07-29 18:06:01,604 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}7
2025-07-29 18:06:01,604 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}8
2025-07-29 18:06:01,604 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}26
2025-07-29 18:06:01,604 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}32
2025-07-29 18:06:01,604 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}23
2025-07-29 18:06:01,604 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}21
2025-07-29 18:06:01,604 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}22
2025-07-29 18:06:01,604 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}24
2025-07-29 18:06:01,604 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}25
2025-07-29 18:06:01,604 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}31
2025-07-29 18:06:01,604 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}35
2025-07-29 18:06:01,605 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}37
2025-07-29 18:06:01,605 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}36
2025-07-29 18:06:01,605 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}39
2025-07-29 18:06:01,605 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}40
2025-07-29 18:06:01,605 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}41
2025-07-29 18:06:01,605 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}42
2025-07-29 18:06:01,605 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}43
2025-07-29 18:06:01,605 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}46
2025-07-29 18:06:01,605 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}47
2025-07-29 18:06:01,605 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}59
2025-07-29 18:06:01,605 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}51
2025-07-29 18:06:01,605 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}52
2025-07-29 18:06:01,605 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}53
2025-07-29 18:06:01,605 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}55
2025-07-29 18:06:01,605 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}60
2025-07-29 18:06:01,605 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}61
2025-07-29 18:06:01,605 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}65
2025-07-29 18:06:01,605 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}66
2025-07-29 18:06:01,605 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}77
2025-07-29 18:06:01,605 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}67
2025-07-29 18:06:01,605 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}69
2025-07-29 18:06:01,605 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}79
2025-07-29 18:06:01,605 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}80
2025-07-29 18:06:01,605 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}81
2025-07-29 18:06:01,605 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}82
2025-07-29 18:06:01,605 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}83
2025-07-29 18:06:01,605 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}84
2025-07-29 18:06:01,605 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}85
2025-07-29 18:06:01,605 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}86
2025-07-29 18:06:01,605 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}87
2025-07-29 18:06:01,605 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}88
2025-07-29 18:06:01,605 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}89
2025-07-29 18:06:01,605 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}91
2025-07-29 18:06:01,605 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}92
2025-07-29 18:06:01,605 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}93
2025-07-29 18:06:01,606 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/WBS:local/${Revision}09c2010030e517ae250d033127310dd72f63a0ef
2025-07-29 18:06:01,606 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/WBS:local/${Revision}7789a94e058df81e542433b71b0f51142728d99e
2025-07-29 18:06:01,606 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}99
2025-07-29 18:06:01,606 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}103
2025-07-29 18:06:01,606 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}100
2025-07-29 18:06:01,606 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}101
2025-07-29 18:06:01,606 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}96
2025-07-29 18:06:01,606 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}97
2025-07-29 18:06:01,606 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}108
2025-07-29 18:06:01,606 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}105
2025-07-29 18:06:01,606 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}107
2025-07-29 18:06:01,606 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}114
2025-07-29 18:06:01,606 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}115
2025-07-29 18:06:01,606 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}116
2025-07-29 18:06:01,606 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}117
2025-07-29 18:06:01,606 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}118
2025-07-29 18:06:01,606 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}110
2025-07-29 18:06:01,606 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}119
2025-07-29 18:06:01,606 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}120
2025-07-29 18:06:01,606 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}121
2025-07-29 18:06:01,606 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}122
2025-07-29 18:06:01,606 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}123
2025-07-29 18:06:01,606 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}124
2025-07-29 18:06:01,606 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}125
2025-07-29 18:06:01,606 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}126
2025-07-29 18:06:01,606 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}127
2025-07-29 18:06:01,606 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}130
2025-07-29 18:06:01,606 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}136
2025-07-29 18:06:01,606 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}128
2025-07-29 18:06:01,606 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}129
2025-07-29 18:06:01,606 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}131
2025-07-29 18:06:01,606 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}132
2025-07-29 18:06:01,606 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}133
2025-07-29 18:06:01,606 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}134
2025-07-29 18:06:01,606 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}135
2025-07-29 18:06:01,606 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}142
2025-07-29 18:06:01,606 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}137
2025-07-29 18:06:01,606 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}138
2025-07-29 18:06:01,606 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}140
2025-07-29 18:06:01,606 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}141
2025-07-29 18:06:01,606 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}143
2025-07-29 18:06:01,606 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}144
2025-07-29 18:06:01,606 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}145
2025-07-29 18:06:01,606 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}147
2025-07-29 18:06:01,606 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}148
2025-07-29 18:06:01,606 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}149
2025-07-29 18:06:01,606 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}151
2025-07-29 18:06:01,606 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}152
2025-07-29 18:06:01,606 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}154
2025-07-29 18:06:01,606 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}156
2025-07-29 18:06:01,606 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}158
2025-07-29 18:06:01,606 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}159
2025-07-29 18:06:01,606 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}171
2025-07-29 18:06:01,606 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}166
2025-07-29 18:06:01,606 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}167
2025-07-29 18:06:01,606 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}174
2025-07-29 18:06:01,606 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}176
2025-07-29 18:06:01,606 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}169
2025-07-29 18:06:01,606 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}160
2025-07-29 18:06:01,606 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}162
2025-07-29 18:06:01,606 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}164
2025-07-29 18:06:01,606 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}182
2025-07-29 18:06:01,606 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}183
2025-07-29 18:06:01,606 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}185
2025-07-29 18:06:01,606 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}186
2025-07-29 18:06:01,606 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}187
2025-07-29 18:06:01,607 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}188
2025-07-29 18:06:01,607 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}177
2025-07-29 18:06:01,607 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}178
2025-07-29 18:06:01,607 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}179
2025-07-29 18:06:01,607 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}196
2025-07-29 18:06:01,607 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}200
2025-07-29 18:06:01,607 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}189
2025-07-29 18:06:01,607 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}197
2025-07-29 18:06:01,607 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}198
2025-07-29 18:06:01,607 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}201
2025-07-29 18:06:01,607 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}202
2025-07-29 18:06:01,607 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}203
2025-07-29 18:06:01,607 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}204
2025-07-29 18:06:01,608 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Module
2025-07-29 18:06:01,612 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: ModuleAttachment
2025-07-29 18:06:01,612 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.102 s, CPU [user: 0.0267 s, system: 0.00665 s], Allocated memory: 10.5 MB
2025-07-29 18:06:01,612 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Plan
2025-07-29 18:06:01,613 [PolarionDocIdCreator-6] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: ProjectGroup
2025-07-29 18:06:01,616 [PolarionDocIdCreator-6] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: ProjectGroup
2025-07-29 18:06:01,619 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Module
2025-07-29 18:06:01,621 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: RichPage
2025-07-29 18:06:01,630 [PolarionDocIdCreator-1] INFO  com.polarion.subterra.index.impl.lucene.baseline.PolarionDocIdCreator - Bloom filter loading for 28 indices took  [ TIME 0.123 s. ]
2025-07-29 18:06:01,648 [main | u:p] INFO  com.polarion.platform.persistence.internal.calcfields.DelegatingCalculatedFieldsListener - Calculated fields mode: async
2025-07-29 18:06:01,650 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.PersistenceEngineListener - Flushing took  [ TIME 0.24 s. ]
2025-07-29 18:06:01,650 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.24 s. ]
2025-07-29 18:06:01,650 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.24 s [100% doFinishStartup (1x)] (1x), commit: 0.0578 s [100% Revision (1x)] (1x), Lucene: 0.0395 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.0179 s [100% objectsToInv (1x)] (1x), DB: 0.015 s [50% update (3x), 29% query (1x), 13% execute (1x)] (8x)
2025-07-29 18:06:01,650 [main | u:p] INFO  com.polarion.platform.internal.service.repository.ListenerManager - Starting the pulling job for repository: default
2025-07-29 18:06:01,650 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Persistence initialization finished [ TIME 2.16 s. ]
2025-07-29 18:06:01,650 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:06:01,650 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-29 18:06:01,651 [main | u:p] INFO  com.polarion.platform.persistence.internal.calcfields.CalculatedFieldsStorage - Checking integrity of calculated fields storage /opt/polarion/data/workspace/polarion-data/calculated-fields
2025-07-29 18:06:01,658 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-29 18:06:01,658 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:06:01,658 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-29 18:06:01,687 [main | u:p] INFO  com.polarion.platform.jobs.internal.service.scheduler.JobSchedulerService - Updating local scheduler state: start
2025-07-29 18:06:01,696 [main | u:p | u:p] INFO  org.quartz.simpl.SimpleThreadPool - Job execution threads will use class loader of thread: main | u:p | u:p
2025-07-29 18:06:01,699 [main | u:p | u:p] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
2025-07-29 18:06:01,699 [main | u:p | u:p] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'DefaultQuartzScheduler' initialized from default resource file in Quartz package: 'quartz.properties'
2025-07-29 18:06:01,699 [main | u:p | u:p] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 1.4.2
2025-07-29 18:06:01,699 [main | u:p | u:p] INFO  org.quartz.core.QuartzScheduler - Scheduler DefaultQuartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-29 18:06:01,699 [main | u:p | u:p] INFO  org.quartz.core.QuartzScheduler - Scheduler DefaultQuartzScheduler_$_NON_CLUSTERED paused.
2025-07-29 18:06:01,699 [main | u:p | u:p] INFO  org.quartz.core.QuartzScheduler - Scheduler DefaultQuartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-29 18:06:01,700 [main | u:p | u:p] INFO  org.quartz.simpl.SimpleThreadPool - Job execution threads will use class loader of thread: main | u:p | u:p
2025-07-29 18:06:01,701 [main | u:p | u:p] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
2025-07-29 18:06:01,701 [main | u:p | u:p] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'DefaultQuartzScheduler' initialized from default resource file in Quartz package: 'quartz.properties'
2025-07-29 18:06:01,701 [main | u:p | u:p] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 1.4.2
2025-07-29 18:06:01,701 [main | u:p | u:p] INFO  com.polarion.platform.jobs.internal.service.scheduler.JobSchedulerService - 15 scheduled job(s) configured
2025-07-29 18:06:01,705 [main | u:p | u:p] INFO  org.quartz.core.QuartzScheduler - Scheduler DefaultQuartzScheduler_$_NON_CLUSTERED started.
2025-07-29 18:06:01,793 [main] INFO  com.polarion.psvn.launcher.internal.platform.PlatformService - Platform boot finished
2025-07-29 18:06:01,793 [main] INFO  com.polarion.psvn.launcher.internal.platform.PlatformService - Platform started
2025-07-29 18:06:01,803 [main] INFO  com.polarion.portal.tomcat.TomcatPlugin - Tomcat home directory was set to /opt/polarion/data/workspace/.metadata/.plugins/com.polarion.portal.tomcat
2025-07-29 18:06:01,811 [main] INFO  com.polarion.psvn.launcher.internal.tomcat.TomcatService - Starting Tomcat...
2025-07-29 18:06:01,909 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: webui, contextRoot: webapp/webui, plugin: com.polarion.alm.ui, priority: -10]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:06:01,910 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion, contextRoot: webapp/authapp, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:06:01,910 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/.well-known, contextRoot: webapp/well-known, plugin: com.polarion.platform, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:06:01,910 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/ProjectPlanGantt, contextRoot: webapp, plugin: com.polarion.alm.ProjectPlanGantt_new, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:06:01,910 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/activate, contextRoot: webapp/activation, plugin: com.polarion.psvn.launcher, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:06:01,910 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/announcements, contextRoot: webapp/announcements, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:06:01,910 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/bir, contextRoot: webapp/bir, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:06:01,910 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/codemirror-modes, contextRoot: webapp/codemirror-modes, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:06:01,910 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/doorsconnector, contextRoot: webapp, plugin: com.polarion.synchronizer, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:06:01,910 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/export, contextRoot: webapp/export, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:06:01,910 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/fileupload, contextRoot: webapp/fileupload, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:06:01,911 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/gwt, contextRoot: war, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:06:01,911 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/icons, contextRoot: webapp/icons, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:06:01,911 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/internal-login, contextRoot: webapp/internal-login, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:06:01,911 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/module-attachment, contextRoot: webapp/attachments, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:06:01,911 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/modulehome, contextRoot: webapp/module-home, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:06:01,911 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/mxgraph, contextRoot: draw.io/war, plugin: com.polarion.alm.ui.diagrams.mxgraph, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:06:01,911 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/oauth-feishu, contextRoot: webapp, plugin: com.fasnote.alm.auth.feishu, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:06:01,911 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/page-attachment, contextRoot: webapp/attachments, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:06:01,911 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/perf-testing, contextRoot: webapp/perf-testing, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:06:01,911 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/plugin-manage, contextRoot: webapp, plugin: com.fasnote.alm.plugin.manage, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:06:01,911 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/print, contextRoot: webapp/print, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:06:01,911 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/register, contextRoot: webapp/register, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:06:01,911 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/rest, contextRoot: webapp, plugin: com.siemens.polarion.rest, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:06:01,912 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/ria, contextRoot: webapp/ria, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:06:01,912 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/richpagehome, contextRoot: webapp/richpage-home, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:06:01,912 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/rt, contextRoot: src/main/webapp, plugin: com.siemens.polarion.rt, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:06:01,912 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/rt-connect, contextRoot: ws, plugin: com.siemens.polarion.rt.communication.polarion, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:06:01,912 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/scripting, contextRoot: webapp/scripting, plugin: com.polarion.scripting.servlet, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:06:01,913 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/sdk, contextRoot: webapp/sdk, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:06:01,913 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/svnwebclient, contextRoot: webapp, plugin: org.polarion.svnwebclient, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:06:01,913 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/swagger, contextRoot: webapp/swagger, plugin: com.siemens.polarion.rest, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:06:01,913 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/synchronizer, contextRoot: webapp, plugin: com.polarion.synchronizer.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:06:01,913 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/template-download, contextRoot: webapp/project-template, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:06:01,914 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/testrun-attachment, contextRoot: webapp/attachments, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:06:01,914 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/tour, contextRoot: webapp/tour, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:06:01,914 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/wi-attachment, contextRoot: webapp/attachments, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:06:01,914 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/wi-attachment-auth, contextRoot: webapp/wi-attachment-auth, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:06:01,914 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/widget-resource, contextRoot: webapp/widget-resource, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:06:01,914 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/wiki, contextRoot: src/main/webapp, plugin: com.polarion.alm.wiki, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:06:01,914 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/workreport, contextRoot: webapp/workreport, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:06:01,914 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/ws, contextRoot: ws, plugin: com.polarion.alm.ws, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:06:01,914 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/xunitimport, contextRoot: webapp/xunitimport, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:06:01,915 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/oslc, contextRoot: webapp, plugin: com.polarion.alm.oslc, priority: 1]'; protocol: AJP/1.3, port: 8889
2025-07-29 18:06:01,966 [main] INFO  org.apache.coyote.ajp.AjpNioProtocol - Initializing ProtocolHandler ["ajp-nio-127.0.0.1-8889"]
2025-07-29 18:06:01,972 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-29 18:06:01,972 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.53]
2025-07-29 18:06:01,994 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@7e3a9bf] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:06:01,994 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@3a360264] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:06:01,994 [Catalina-utility-4] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@6485ad5a] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:06:01,994 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@69eb668b] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:06:01,994 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@6bcb493e] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:06:01,994 [Catalina-utility-2] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@63c9df77] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:06:02,009 [Catalina-utility-1] INFO  org.apache.catalina.startup.ContextConfig - No global web.xml found
2025-07-29 18:06:02,019 [Catalina-utility-1] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:06:02,019 [Catalina-utility-6] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:06:02,020 [Catalina-utility-4] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:06:02,021 [Catalina-utility-4] WARN  org.apache.catalina.startup.ContextConfig - Security role name [admin] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:06:02,028 [Catalina-utility-2] FATAL org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/polarion/oauth-feishu] - For security constraints with URL pattern [/userinfo] only the HTTP methods [POST GET] are covered. All other methods are uncovered.
2025-07-29 18:06:02,039 [Catalina-utility-2] INFO  com.fasnote.alm.plugin.manage.Activator - 启动许可证管理插件...
2025-07-29 18:06:02,039 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.Activator - 初始化许可证配置...
2025-07-29 18:06:02,040 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.Activator - 从环境变量加载配置完成
2025-07-29 18:06:02,040 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.Activator - 许可证配置初始化完成
2025-07-29 18:06:02,040 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.Activator - 初始化核心组件...
2025-07-29 18:06:02,046 [Catalina-utility-4] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@68a395f1] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:06:02,047 [Catalina-utility-2] INFO  com.fasnote.alm.plugin.manage.security.SecurityValidator - 初始化安全验证器
2025-07-29 18:06:02,047 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.security.SecurityValidator - 成功获取机器码
2025-07-29 18:06:02,048 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@373aa5d6] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:06:02,048 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@4d10338] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:06:02,048 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@467ba23] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:06:02,051 [Catalina-utility-5] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:06:02,053 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.security.SecurityValidator - 未能从任何安全位置加载公钥
2025-07-29 18:06:02,053 [Catalina-utility-2] WARN  com.fasnote.alm.plugin.manage.security.SecurityValidator - 未找到外部公钥配置，使用内置公钥
2025-07-29 18:06:02,054 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@45a7770d] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:06:02,054 [Catalina-utility-2] INFO  com.fasnote.alm.license.crypto.RSAKeyManager - [RSAKeyManager] 使用硬编码公钥，版本: v1
2025-07-29 18:06:02,054 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@cc1ea83] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:06:02,054 [Catalina-utility-2] INFO  com.fasnote.alm.license.crypto.RSAKeyManager - [RSAKeyManager] 成功加载硬编码公钥
2025-07-29 18:06:02,054 [Catalina-utility-4] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@5cb88b6b] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:06:02,056 [Catalina-utility-2] INFO  com.fasnote.alm.license.crypto.RSALicenseEncryption - [RSALicenseEncryption] RSA+AES混合加密系统初始化完成，密钥版本: v1
2025-07-29 18:06:02,056 [Catalina-utility-2] INFO  com.fasnote.alm.plugin.manage.security.SecurityValidator - 安全验证器初始化完成，机器码: 8AG9****1962
2025-07-29 18:06:02,057 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.Activator - 核心组件初始化完成
2025-07-29 18:06:02,057 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.Activator - 初始化服务...
2025-07-29 18:06:02,057 [Catalina-utility-3] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:06:02,058 [Catalina-utility-4] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:06:02,058 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.Activator - 服务初始化完成
2025-07-29 18:06:02,058 [Catalina-utility-2] INFO  com.fasnote.alm.plugin.manage.Activator - 开始初始化OSGi许可证框架...
2025-07-29 18:06:02,058 [Catalina-utility-6] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:06:02,061 [Catalina-utility-2] INFO  com.fasnote.alm.injection.osgi.InjectionActivator - 启动ALM依赖注入框架
2025-07-29 18:06:02,062 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@1e786c08] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:06:02,062 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@575e2c6f] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:06:02,062 [Catalina-utility-4] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@37033745] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:06:02,063 [Catalina-utility-2] INFO  com.fasnote.alm.injection.impl.DependencyInjector - 创建OSGi感知的纯粹依赖注入器
2025-07-29 18:06:02,064 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.osgi.InjectionActivator - 开始扫描模块
2025-07-29 18:06:02,064 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.osgi.InjectionActivator - 扫描模块包: com.fasnote.alm.plugin.manage.injection.module
2025-07-29 18:06:02,064 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 扫描模块包: com.fasnote.alm.plugin.manage.injection.module
2025-07-29 18:06:02,064 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.osgi.InjectionActivator - 扫描模块包: com.fasnote.alm.injection.module
2025-07-29 18:06:02,064 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 扫描模块包: com.fasnote.alm.injection.module
2025-07-29 18:06:02,064 [Catalina-utility-2] INFO  com.fasnote.alm.injection.osgi.InjectionActivator - 模块扫描完成，已安装模块数量: 0
2025-07-29 18:06:02,064 [Catalina-utility-2] INFO  com.fasnote.alm.injection.osgi.InjectionActivator - ALM依赖注入框架启动成功
2025-07-29 18:06:02,064 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.Activator - OSGi许可证框架初始化器创建完成
2025-07-29 18:06:02,064 [Catalina-utility-2] INFO  com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 初始化OSGi感知的许可证框架
2025-07-29 18:06:02,064 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.eclipse.osgi
2025-07-29 18:06:02,064 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.eclipse.osgi
2025-07-29 18:06:02,064 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.eclipse.equinox.simpleconfigurator
2025-07-29 18:06:02,064 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.eclipse.equinox.simpleconfigurator
2025-07-29 18:06:02,065 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: antlr
2025-07-29 18:06:02,065 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: antlr
2025-07-29 18:06:02,065 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: antlr4
2025-07-29 18:06:02,065 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: antlr4
2025-07-29 18:06:02,065 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: antlr4-runtime
2025-07-29 18:06:02,065 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: antlr4-runtime
2025-07-29 18:06:02,065 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: bcprov
2025-07-29 18:06:02,065 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: bcprov
2025-07-29 18:06:02,065 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.auth0.java-jwt
2025-07-29 18:06:02,065 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.auth0.java-jwt
2025-07-29 18:06:02,065 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.fasnote.alm.injection
2025-07-29 18:06:02,065 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.fasnote.alm.injection
2025-07-29 18:06:02,065 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.fasterxml.classmate
2025-07-29 18:06:02,065 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.fasterxml.classmate
2025-07-29 18:06:02,065 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.fasterxml.jackson
2025-07-29 18:06:02,065 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.fasterxml.jackson
2025-07-29 18:06:02,065 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.fasterxml.jackson.dataformat.jackson-dataformat-yaml
2025-07-29 18:06:02,065 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.fasterxml.jackson.dataformat.jackson-dataformat-yaml
2025-07-29 18:06:02,065 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.fasterxml.jackson.jaxrs
2025-07-29 18:06:02,065 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.fasterxml.jackson.jaxrs
2025-07-29 18:06:02,065 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.fasterxml.woodstox
2025-07-29 18:06:02,065 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.fasterxml.woodstox
2025-07-29 18:06:02,065 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.google.gson
2025-07-29 18:06:02,065 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.google.gson
2025-07-29 18:06:02,065 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.google.guava
2025-07-29 18:06:02,065 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.google.guava
2025-07-29 18:06:02,065 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.google.guava.failureaccess
2025-07-29 18:06:02,065 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.google.guava.failureaccess
2025-07-29 18:06:02,065 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.ibm.icu.icu4j
2025-07-29 18:06:02,065 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.ibm.icu.icu4j
2025-07-29 18:06:02,065 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.icl.saxon
2025-07-29 18:06:02,065 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.icl.saxon
2025-07-29 18:06:02,065 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.jayway.jsonpath.json-path
2025-07-29 18:06:02,065 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.jayway.jsonpath.json-path
2025-07-29 18:06:02,065 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.jcraft.jsch
2025-07-29 18:06:02,065 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.jcraft.jsch
2025-07-29 18:06:02,065 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.networknt.json-schema-validator
2025-07-29 18:06:02,065 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.networknt.json-schema-validator
2025-07-29 18:06:02,065 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.nimbusds.content-type
2025-07-29 18:06:02,065 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.nimbusds.content-type
2025-07-29 18:06:02,065 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.nimbusds.nimbus-jose-jwt
2025-07-29 18:06:02,065 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.nimbusds.nimbus-jose-jwt
2025-07-29 18:06:02,065 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.opensymphony.quartz
2025-07-29 18:06:02,065 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.opensymphony.quartz
2025-07-29 18:06:02,065 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.alm.ProjectPlanGantt_new
2025-07-29 18:06:02,065 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.alm.ProjectPlanGantt_new
2025-07-29 18:06:02,065 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.alm.builder
2025-07-29 18:06:02,065 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.alm.builder
2025-07-29 18:06:02,065 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.alm.checker
2025-07-29 18:06:02,065 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.alm.checker
2025-07-29 18:06:02,065 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.alm.impex
2025-07-29 18:06:02,065 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.alm.impex
2025-07-29 18:06:02,065 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.alm.install
2025-07-29 18:06:02,065 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.alm.install
2025-07-29 18:06:02,065 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.alm.oslc
2025-07-29 18:06:02,065 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.alm.oslc
2025-07-29 18:06:02,065 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.alm.projects
2025-07-29 18:06:02,065 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.alm.projects
2025-07-29 18:06:02,065 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.alm.tracker
2025-07-29 18:06:02,066 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.alm.tracker
2025-07-29 18:06:02,066 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.alm.ui
2025-07-29 18:06:02,066 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.alm.ui
2025-07-29 18:06:02,066 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.alm.ui.diagrams.mxgraph
2025-07-29 18:06:02,066 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.alm.ui.diagrams.mxgraph
2025-07-29 18:06:02,066 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.alm.wiki
2025-07-29 18:06:02,066 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.alm.wiki
2025-07-29 18:06:02,066 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.cluster
2025-07-29 18:06:02,066 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.cluster
2025-07-29 18:06:02,066 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.core.boot
2025-07-29 18:06:02,066 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.core.boot
2025-07-29 18:06:02,066 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.core.util
2025-07-29 18:06:02,066 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.core.util
2025-07-29 18:06:02,066 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.fop
2025-07-29 18:06:02,066 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.fop
2025-07-29 18:06:02,066 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.platform
2025-07-29 18:06:02,066 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.platform
2025-07-29 18:06:02,066 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.platform.guice
2025-07-29 18:06:02,066 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.platform.guice
2025-07-29 18:06:02,066 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.platform.hivemind
2025-07-29 18:06:02,066 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.platform.hivemind
2025-07-29 18:06:02,066 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.platform.jobs
2025-07-29 18:06:02,066 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.platform.jobs
2025-07-29 18:06:02,066 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.platform.monitoring
2025-07-29 18:06:02,066 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.platform.monitoring
2025-07-29 18:06:02,066 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.platform.persistence
2025-07-29 18:06:02,066 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.platform.persistence
2025-07-29 18:06:02,066 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.platform.repository
2025-07-29 18:06:02,066 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.platform.repository
2025-07-29 18:06:02,066 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.platform.repository.driver.svn
2025-07-29 18:06:02,066 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.platform.repository.driver.svn
2025-07-29 18:06:02,066 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.platform.repository.external
2025-07-29 18:06:02,066 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.platform.repository.external
2025-07-29 18:06:02,066 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.platform.repository.external.git
2025-07-29 18:06:02,066 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.platform.repository.external.git
2025-07-29 18:06:02,066 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.platform.repository.external.svn
2025-07-29 18:06:02,066 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.platform.repository.external.svn
2025-07-29 18:06:02,066 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.platform.sql
2025-07-29 18:06:02,066 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.platform.sql
2025-07-29 18:06:02,066 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.portal.tomcat
2025-07-29 18:06:02,066 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.portal.tomcat
2025-07-29 18:06:02,066 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.psvn.launcher
2025-07-29 18:06:02,066 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.psvn.launcher
2025-07-29 18:06:02,066 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.psvn.translations.en
2025-07-29 18:06:02,066 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.psvn.translations.en
2025-07-29 18:06:02,066 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.purevariants
2025-07-29 18:06:02,066 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.purevariants
2025-07-29 18:06:02,066 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.qcentre
2025-07-29 18:06:02,066 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.qcentre
2025-07-29 18:06:02,066 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.scripting.servlet
2025-07-29 18:06:02,066 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.scripting.servlet
2025-07-29 18:06:02,066 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.subterra.base
2025-07-29 18:06:02,066 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.subterra.base
2025-07-29 18:06:02,066 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@3e632c01] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:06:02,066 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.subterra.index
2025-07-29 18:06:02,066 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.subterra.index
2025-07-29 18:06:02,066 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.subterra.persistence
2025-07-29 18:06:02,066 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.subterra.persistence
2025-07-29 18:06:02,066 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.subterra.persistence.document
2025-07-29 18:06:02,066 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.subterra.persistence.document
2025-07-29 18:06:02,066 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.synchronizer
2025-07-29 18:06:02,066 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.synchronizer
2025-07-29 18:06:02,066 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.synchronizer.proxy.feishu
2025-07-29 18:06:02,066 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.synchronizer.proxy.feishu
2025-07-29 18:06:02,066 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.synchronizer.proxy.hpalm
2025-07-29 18:06:02,066 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.synchronizer.proxy.hpalm
2025-07-29 18:06:02,066 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.synchronizer.proxy.jira
2025-07-29 18:06:02,066 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.synchronizer.proxy.jira
2025-07-29 18:06:02,067 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.synchronizer.proxy.polarion
2025-07-29 18:06:02,067 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.synchronizer.proxy.polarion
2025-07-29 18:06:02,067 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.synchronizer.proxy.reqif
2025-07-29 18:06:02,067 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.synchronizer.proxy.reqif
2025-07-29 18:06:02,067 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.synchronizer.ui
2025-07-29 18:06:02,067 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.synchronizer.ui
2025-07-29 18:06:02,067 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.usdp.persistence
2025-07-29 18:06:02,067 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.usdp.persistence
2025-07-29 18:06:02,067 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.xray.doc.user
2025-07-29 18:06:02,067 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.polarion.xray.doc.user
2025-07-29 18:06:02,067 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.siemens.des.logger.api
2025-07-29 18:06:02,067 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.siemens.des.logger.api
2025-07-29 18:06:02,067 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.siemens.plm.bitools.analytics
2025-07-29 18:06:02,067 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.siemens.plm.bitools.analytics
2025-07-29 18:06:02,067 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.siemens.polarion.ct.collectors.git
2025-07-29 18:06:02,067 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.siemens.polarion.ct.collectors.git
2025-07-29 18:06:02,067 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.siemens.polarion.eclipse.configurator
2025-07-29 18:06:02,067 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.siemens.polarion.eclipse.configurator
2025-07-29 18:06:02,067 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.siemens.polarion.integration.ci
2025-07-29 18:06:02,067 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.siemens.polarion.integration.ci
2025-07-29 18:06:02,067 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.siemens.polarion.previewer
2025-07-29 18:06:02,067 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.siemens.polarion.previewer
2025-07-29 18:06:02,067 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.siemens.polarion.previewer.external
2025-07-29 18:06:02,067 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.siemens.polarion.previewer.external
2025-07-29 18:06:02,067 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.siemens.polarion.rest
2025-07-29 18:06:02,067 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.siemens.polarion.rest
2025-07-29 18:06:02,067 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.siemens.polarion.rt.api
2025-07-29 18:06:02,067 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.siemens.polarion.rt.api
2025-07-29 18:06:02,067 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.siemens.polarion.rt.collectors.git
2025-07-29 18:06:02,067 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.siemens.polarion.rt.collectors.git
2025-07-29 18:06:02,067 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.siemens.polarion.rt.communication.common
2025-07-29 18:06:02,067 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.siemens.polarion.rt.communication.common
2025-07-29 18:06:02,067 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.siemens.polarion.rt.communication.polarion
2025-07-29 18:06:02,067 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.siemens.polarion.rt.communication.polarion
2025-07-29 18:06:02,067 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.siemens.polarion.rt.communication.rt
2025-07-29 18:06:02,067 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.siemens.polarion.rt.communication.rt
2025-07-29 18:06:02,067 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@157303d9] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:06:02,067 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.siemens.polarion.rt.parsers.c
2025-07-29 18:06:02,068 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.siemens.polarion.rt.parsers.c
2025-07-29 18:06:02,068 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.siemens.polarion.rt.ui
2025-07-29 18:06:02,068 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.siemens.polarion.rt.ui
2025-07-29 18:06:02,068 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.siemens.polarion.synchronizer.proxy.tfs
2025-07-29 18:06:02,068 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.siemens.polarion.synchronizer.proxy.tfs
2025-07-29 18:06:02,068 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.sun.activation.javax.activation
2025-07-29 18:06:02,068 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.sun.activation.javax.activation
2025-07-29 18:06:02,068 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.sun.istack.commons-runtime
2025-07-29 18:06:02,068 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.sun.istack.commons-runtime
2025-07-29 18:06:02,068 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.sun.jna.platform
2025-07-29 18:06:02,068 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.sun.jna.platform
2025-07-29 18:06:02,068 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.sun.xml.bind.jaxb-impl
2025-07-29 18:06:02,068 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.sun.xml.bind.jaxb-impl
2025-07-29 18:06:02,068 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.trilead.ssh2
2025-07-29 18:06:02,068 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.trilead.ssh2
2025-07-29 18:06:02,068 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.zaxxer.hikariCP
2025-07-29 18:06:02,068 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: com.zaxxer.hikariCP
2025-07-29 18:06:02,068 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: des-sdk-core
2025-07-29 18:06:02,068 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: des-sdk-core
2025-07-29 18:06:02,068 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: des-sdk-dss
2025-07-29 18:06:02,068 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: des-sdk-dss
2025-07-29 18:06:02,068 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: io.github.resilience4j.circuitbreaker
2025-07-29 18:06:02,068 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: io.github.resilience4j.circuitbreaker
2025-07-29 18:06:02,068 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: io.github.resilience4j.core
2025-07-29 18:06:02,068 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: io.github.resilience4j.core
2025-07-29 18:06:02,068 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: io.github.resilience4j.retry
2025-07-29 18:06:02,068 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: io.github.resilience4j.retry
2025-07-29 18:06:02,068 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: io.swagger
2025-07-29 18:06:02,068 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: io.swagger
2025-07-29 18:06:02,068 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: io.vavr
2025-07-29 18:06:02,068 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: io.vavr
2025-07-29 18:06:02,068 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: jakaroma
2025-07-29 18:06:02,068 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: jakaroma
2025-07-29 18:06:02,068 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: jakarta.validation.validation-api
2025-07-29 18:06:02,068 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: jakarta.validation.validation-api
2025-07-29 18:06:02,068 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: javassist
2025-07-29 18:06:02,068 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: javassist
2025-07-29 18:06:02,068 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: javax.annotation-api
2025-07-29 18:06:02,068 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: javax.annotation-api
2025-07-29 18:06:02,068 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: javax.cache
2025-07-29 18:06:02,068 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: javax.cache
2025-07-29 18:06:02,068 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: javax.el
2025-07-29 18:06:02,068 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: javax.el
2025-07-29 18:06:02,068 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: javax.inject
2025-07-29 18:06:02,068 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: javax.inject
2025-07-29 18:06:02,068 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: javax.servlet
2025-07-29 18:06:02,068 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: javax.servlet
2025-07-29 18:06:02,068 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: javax.servlet.jsp
2025-07-29 18:06:02,068 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: javax.servlet.jsp
2025-07-29 18:06:02,068 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: javax.transaction
2025-07-29 18:06:02,068 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: javax.transaction
2025-07-29 18:06:02,068 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: jaxb-api
2025-07-29 18:06:02,068 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: jaxb-api
2025-07-29 18:06:02,069 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: jcip-annotations
2025-07-29 18:06:02,069 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: jcip-annotations
2025-07-29 18:06:02,069 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: jcl.over.slf4j
2025-07-29 18:06:02,069 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: jcl.over.slf4j
2025-07-29 18:06:02,069 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: jul.to.slf4j
2025-07-29 18:06:02,069 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: jul.to.slf4j
2025-07-29 18:06:02,069 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: kuromoji-core
2025-07-29 18:06:02,069 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: kuromoji-core
2025-07-29 18:06:02,069 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: kuromoji-ipadic
2025-07-29 18:06:02,069 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: kuromoji-ipadic
2025-07-29 18:06:02,069 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: lang-tag
2025-07-29 18:06:02,069 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: lang-tag
2025-07-29 18:06:02,069 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: net.htmlparser.jericho
2025-07-29 18:06:02,069 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: net.htmlparser.jericho
2025-07-29 18:06:02,069 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: net.java.dev.jna
2025-07-29 18:06:02,069 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: net.java.dev.jna
2025-07-29 18:06:02,069 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: net.minidev.accessors-smart
2025-07-29 18:06:02,069 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: net.minidev.accessors-smart
2025-07-29 18:06:02,069 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: net.minidev.asm
2025-07-29 18:06:02,069 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: net.minidev.asm
2025-07-29 18:06:02,069 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: net.minidev.json-smart
2025-07-29 18:06:02,069 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: net.minidev.json-smart
2025-07-29 18:06:02,069 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: net.n3.nanoxml
2025-07-29 18:06:02,069 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: net.n3.nanoxml
2025-07-29 18:06:02,069 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: net.sourceforge.cssparser
2025-07-29 18:06:02,069 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: net.sourceforge.cssparser
2025-07-29 18:06:02,069 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: nu.xom
2025-07-29 18:06:02,069 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: nu.xom
2025-07-29 18:06:02,069 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: oauth2-oidc-sdk
2025-07-29 18:06:02,069 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: oauth2-oidc-sdk
2025-07-29 18:06:02,069 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.ant
2025-07-29 18:06:02,069 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.ant
2025-07-29 18:06:02,069 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.avro
2025-07-29 18:06:02,069 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.avro
2025-07-29 18:06:02,069 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.axis
2025-07-29 18:06:02,069 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.axis
2025-07-29 18:06:02,070 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.batik
2025-07-29 18:06:02,070 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.batik
2025-07-29 18:06:02,070 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.commons.codec
2025-07-29 18:06:02,070 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.commons.codec
2025-07-29 18:06:02,070 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.commons.collections
2025-07-29 18:06:02,070 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.commons.collections
2025-07-29 18:06:02,070 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.commons.commons-beanutils
2025-07-29 18:06:02,070 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.commons.commons-beanutils
2025-07-29 18:06:02,070 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.commons.commons-collections4
2025-07-29 18:06:02,070 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.commons.commons-collections4
2025-07-29 18:06:02,070 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.commons.commons-compress
2025-07-29 18:06:02,070 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.commons.commons-compress
2025-07-29 18:06:02,070 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.commons.commons-fileupload
2025-07-29 18:06:02,070 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.commons.commons-fileupload
2025-07-29 18:06:02,070 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.commons.digester
2025-07-29 18:06:02,070 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.commons.digester
2025-07-29 18:06:02,070 [Catalina-utility-4] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:06:02,070 [Catalina-utility-6] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:06:02,070 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.commons.exec
2025-07-29 18:06:02,070 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.commons.exec
2025-07-29 18:06:02,070 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.commons.io
2025-07-29 18:06:02,070 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.commons.io
2025-07-29 18:06:02,070 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.commons.lang
2025-07-29 18:06:02,070 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.commons.lang
2025-07-29 18:06:02,070 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.commons.lang3
2025-07-29 18:06:02,071 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.commons.lang3
2025-07-29 18:06:02,071 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.commons.logging
2025-07-29 18:06:02,071 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.commons.logging
2025-07-29 18:06:02,071 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.curator
2025-07-29 18:06:02,071 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.curator
2025-07-29 18:06:02,071 [Catalina-utility-5] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:06:02,071 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.fop
2025-07-29 18:06:02,071 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.fop
2025-07-29 18:06:02,071 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.hivemind
2025-07-29 18:06:02,071 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.hivemind
2025-07-29 18:06:02,071 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.httpcomponents.httpclient
2025-07-29 18:06:02,071 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.httpcomponents.httpclient
2025-07-29 18:06:02,071 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.httpcomponents.httpcore
2025-07-29 18:06:02,071 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.httpcomponents.httpcore
2025-07-29 18:06:02,071 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.jasper.glassfish
2025-07-29 18:06:02,071 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.jasper.glassfish
2025-07-29 18:06:02,071 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.kafka.clients
2025-07-29 18:06:02,071 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.kafka.clients
2025-07-29 18:06:02,071 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.kafka.streams
2025-07-29 18:06:02,071 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.kafka.streams
2025-07-29 18:06:02,071 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.logging.log4j.1.2-api
2025-07-29 18:06:02,071 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.logging.log4j.1.2-api
2025-07-29 18:06:02,071 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.logging.log4j.api
2025-07-29 18:06:02,071 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.logging.log4j.api
2025-07-29 18:06:02,071 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.logging.log4j.apiconf
2025-07-29 18:06:02,071 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.logging.log4j.apiconf
2025-07-29 18:06:02,071 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.logging.log4j.core
2025-07-29 18:06:02,071 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.logging.log4j.core
2025-07-29 18:06:02,071 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.logging.log4j.slf4j-impl
2025-07-29 18:06:02,071 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.logging.log4j.slf4j-impl
2025-07-29 18:06:02,071 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.lucene.analyzers-common
2025-07-29 18:06:02,071 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.lucene.analyzers-common
2025-07-29 18:06:02,071 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.lucene.analyzers-common
2025-07-29 18:06:02,071 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.lucene.analyzers-common
2025-07-29 18:06:02,071 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.lucene.analyzers-smartcn
2025-07-29 18:06:02,071 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.lucene.analyzers-smartcn
2025-07-29 18:06:02,071 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.lucene.core
2025-07-29 18:06:02,071 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.lucene.core
2025-07-29 18:06:02,071 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.lucene.core
2025-07-29 18:06:02,071 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.lucene.core
2025-07-29 18:06:02,071 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.lucene.grouping
2025-07-29 18:06:02,071 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.lucene.grouping
2025-07-29 18:06:02,071 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.lucene.queryparser
2025-07-29 18:06:02,071 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.lucene.queryparser
2025-07-29 18:06:02,071 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.oro
2025-07-29 18:06:02,071 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.oro
2025-07-29 18:06:02,071 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.pdfbox.fontbox
2025-07-29 18:06:02,071 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.pdfbox.fontbox
2025-07-29 18:06:02,071 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.poi
2025-07-29 18:06:02,071 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.poi
2025-07-29 18:06:02,071 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.tika
2025-07-29 18:06:02,071 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.tika
2025-07-29 18:06:02,071 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.xalan
2025-07-29 18:06:02,071 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.xalan
2025-07-29 18:06:02,071 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.xercesImpl
2025-07-29 18:06:02,071 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.xercesImpl
2025-07-29 18:06:02,071 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.xml.serializer
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.xml.serializer
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.xmlgraphics.commons
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.xmlgraphics.commons
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.apache.zookeeper
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.apache.zookeeper
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.codehaus.jettison
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.codehaus.jettison
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.dom4j
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.dom4j
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.eclipse.core.contenttype
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.eclipse.core.contenttype
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.eclipse.core.jobs
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.eclipse.core.jobs
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.eclipse.core.runtime
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.eclipse.core.runtime
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.eclipse.equinox.app
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.eclipse.equinox.app
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.eclipse.equinox.common
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.eclipse.equinox.common
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.eclipse.equinox.http.registry
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.eclipse.equinox.http.registry
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.eclipse.equinox.http.servlet
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.eclipse.equinox.http.servlet
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.eclipse.equinox.launcher
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.eclipse.equinox.launcher
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.eclipse.equinox.preferences
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.eclipse.equinox.preferences
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.eclipse.equinox.registry
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.eclipse.equinox.registry
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.eclipse.jgit
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.eclipse.jgit
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.eclipse.osgi.services
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.eclipse.osgi.services
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.eclipse.osgi.util
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.eclipse.osgi.util
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.ehcache
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.ehcache
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.gitlab.java-gitlab-api
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.gitlab.java-gitlab-api
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.glassfish.jersey
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.glassfish.jersey
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.hibernate.annotations
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.hibernate.annotations
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.hibernate.core
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.hibernate.core
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.hibernate.entitymanager
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.hibernate.entitymanager
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.hibernate.hikaricp
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.hibernate.hikaricp
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.hibernate.jpa.2.1.api
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.hibernate.jpa.2.1.api
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.jboss.logging
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.jboss.logging
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.jvnet.mimepull
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.jvnet.mimepull
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.objectweb.asm
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.objectweb.asm
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.objectweb.jotm
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.objectweb.jotm
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.opensaml
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.opensaml
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.polarion.svncommons
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.polarion.svncommons
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.polarion.svnwebclient
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.polarion.svnwebclient
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.postgesql
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.postgesql
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.rocksdb.rocksdbjni
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.rocksdb.rocksdbjni
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.springframework.data.core
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.springframework.data.core
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.springframework.data.jpa
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.springframework.data.jpa
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.springframework.spring-aop
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.springframework.spring-aop
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.springframework.spring-beans
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.springframework.spring-beans
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.springframework.spring-context
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.springframework.spring-context
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.springframework.spring-core
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.springframework.spring-core
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.springframework.spring-expression
2025-07-29 18:06:02,072 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.springframework.spring-expression
2025-07-29 18:06:02,073 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.springframework.spring-jdbc
2025-07-29 18:06:02,073 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.springframework.spring-jdbc
2025-07-29 18:06:02,073 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.springframework.spring-orm
2025-07-29 18:06:02,073 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.springframework.spring-orm
2025-07-29 18:06:02,073 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.springframework.spring-test
2025-07-29 18:06:02,073 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.springframework.spring-test
2025-07-29 18:06:02,073 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.springframework.spring-tx
2025-07-29 18:06:02,073 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.springframework.spring-tx
2025-07-29 18:06:02,073 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.springframework.spring-web
2025-07-29 18:06:02,073 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.springframework.spring-web
2025-07-29 18:06:02,073 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.springframework.spring-webmvc
2025-07-29 18:06:02,073 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.springframework.spring-webmvc
2025-07-29 18:06:02,073 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.tmatesoft.sqljet
2025-07-29 18:06:02,073 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.tmatesoft.sqljet
2025-07-29 18:06:02,073 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: org.tmatesoft.svnkit
2025-07-29 18:06:02,073 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: org.tmatesoft.svnkit
2025-07-29 18:06:02,073 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: saaj-api
2025-07-29 18:06:02,073 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: saaj-api
2025-07-29 18:06:02,073 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: sdk-lifecycle-collab
2025-07-29 18:06:02,073 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: sdk-lifecycle-collab
2025-07-29 18:06:02,073 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: sdk-lifecycle-docmgmt
2025-07-29 18:06:02,073 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: sdk-lifecycle-docmgmt
2025-07-29 18:06:02,073 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: siemens.des.clientsecurity
2025-07-29 18:06:02,073 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: siemens.des.clientsecurity
2025-07-29 18:06:02,073 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: slf4j.api
2025-07-29 18:06:02,073 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: slf4j.api
2025-07-29 18:06:02,073 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: xml-apis
2025-07-29 18:06:02,073 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: xml-apis
2025-07-29 18:06:02,073 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: xml.apis.ext
2025-07-29 18:06:02,073 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: xml.apis.ext
2025-07-29 18:06:02,073 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: xstream
2025-07-29 18:06:02,073 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 注册Bundle到DI框架: xstream
2025-07-29 18:06:02,073 [Catalina-utility-2] INFO  com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 已注册所有活跃Bundle到DI框架，总数: 249
2025-07-29 18:06:02,073 [Catalina-utility-2] INFO  com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 开始安装许可证模块...
2025-07-29 18:06:02,074 [Catalina-utility-2] INFO  com.fasnote.alm.plugin.manage.injection.module.LicenseManagerModule - LicenseManagerModule创建完成
2025-07-29 18:06:02,075 [Catalina-utility-4] INFO  org.apache.jasper.servlet.TldScanner - At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-07-29 18:06:02,075 [Catalina-utility-1] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:06:02,077 [Catalina-utility-2] INFO  com.fasnote.alm.plugin.manage.injection.module.LicenseManagerModule - 开始配置LicenseManager依赖注入...
2025-07-29 18:06:02,081 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册提供者: com.fasnote.alm.plugin.manage.core.LicenseManager
2025-07-29 18:06:02,081 [Catalina-utility-2] INFO  com.fasnote.alm.plugin.manage.injection.module.LicenseManagerModule - LicenseManager依赖注入配置完成
2025-07-29 18:06:02,081 [Catalina-utility-2] INFO  com.fasnote.alm.injection.impl.DependencyInjector - 已安装模块: LicenseManagerModule (优先级: 100)
2025-07-29 18:06:02,081 [Catalina-utility-2] INFO  com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 已安装LicenseManagerModule到DI框架
2025-07-29 18:06:02,081 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 开始解析服务: com.fasnote.alm.plugin.manage.core.LicenseManager, 服务名: null
2025-07-29 18:06:02,081 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 单例服务缓存中未找到: com.fasnote.alm.plugin.manage.core.LicenseManager, 当前缓存大小: 0
2025-07-29 18:06:02,081 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 未找到实现类: com.fasnote.alm.plugin.manage.core.LicenseManager
2025-07-29 18:06:02,081 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 创建实例: com.fasnote.alm.plugin.manage.core.LicenseManager
2025-07-29 18:06:02,081 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.injection.module.LicenseManagerModule - 创建LicenseManager实例...
2025-07-29 18:06:02,083 [Catalina-utility-2] INFO  com.fasnote.alm.license.crypto.RSAKeyManager - [RSAKeyManager] 使用硬编码公钥，版本: v1
2025-07-29 18:06:02,083 [Catalina-utility-2] INFO  com.fasnote.alm.license.crypto.RSAKeyManager - [RSAKeyManager] 成功加载硬编码公钥
2025-07-29 18:06:02,083 [Catalina-utility-2] INFO  com.fasnote.alm.license.crypto.RSALicenseEncryption - [RSALicenseEncryption] RSA+AES混合加密系统初始化完成，密钥版本: v1
2025-07-29 18:06:02,083 [Catalina-utility-2] INFO  com.fasnote.alm.plugin.manage.core.UnifiedLicenseProcessor - [UnifiedLicenseProcessor] 统一许可证处理器初始化完成
2025-07-29 18:06:02,084 [Catalina-utility-1] INFO  org.apache.tomcat.dbcp.dbcp2.BasicDataSourceFactory - Name = XWikiDS Ignoring unknown property: value of "DB Connection" for "description" property
2025-07-29 18:06:02,084 [Catalina-utility-2] INFO  com.fasnote.alm.plugin.manage.security.SecurityValidator - 初始化安全验证器
2025-07-29 18:06:02,084 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@7f449d0c] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:06:02,084 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.security.SecurityValidator - 成功获取机器码
2025-07-29 18:06:02,084 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.security.SecurityValidator - 未能从任何安全位置加载公钥
2025-07-29 18:06:02,084 [Catalina-utility-2] WARN  com.fasnote.alm.plugin.manage.security.SecurityValidator - 未找到外部公钥配置，使用内置公钥
2025-07-29 18:06:02,084 [Catalina-utility-2] INFO  com.fasnote.alm.license.crypto.RSAKeyManager - [RSAKeyManager] 使用硬编码公钥，版本: v1
2025-07-29 18:06:02,084 [Catalina-utility-2] INFO  com.fasnote.alm.license.crypto.RSAKeyManager - [RSAKeyManager] 成功加载硬编码公钥
2025-07-29 18:06:02,084 [Catalina-utility-2] INFO  com.fasnote.alm.license.crypto.RSALicenseEncryption - [RSALicenseEncryption] RSA+AES混合加密系统初始化完成，密钥版本: v1
2025-07-29 18:06:02,084 [Catalina-utility-2] INFO  com.fasnote.alm.plugin.manage.security.SecurityValidator - 安全验证器初始化完成，机器码: 8AG9****1962
2025-07-29 18:06:02,084 [Catalina-utility-2] INFO  com.fasnote.alm.plugin.manage.core.LicenseManager - LicenseManager创建完成，BundleContext: com.fasnote.alm.plugin.manage
2025-07-29 18:06:02,084 [Catalina-utility-2] INFO  com.fasnote.alm.plugin.manage.core.LicenseManager - 初始化许可证管理器
2025-07-29 18:06:02,084 [Catalina-utility-2] INFO  com.fasnote.alm.plugin.manage.core.LicenseManager - 开始自动扫描许可证文件
2025-07-29 18:06:02,085 [Catalina-utility-2] INFO  com.fasnote.alm.plugin.manage.util.LicenseDirectoryManager - 开始确定许可证目录...
2025-07-29 18:06:02,085 [Catalina-utility-2] INFO  com.fasnote.alm.plugin.manage.util.LicenseDirectoryManager - 未检测到集群环境，使用单机模式
2025-07-29 18:06:02,085 [Catalina-utility-2] INFO  com.fasnote.alm.plugin.manage.util.LicenseDirectoryManager - 通过Configuration获取基础许可证目录: /opt/polarion/polarion/license
2025-07-29 18:06:02,086 [Catalina-utility-2] INFO  com.fasnote.alm.plugin.manage.util.LicenseDirectoryManager - 添加fasnote子目录，最终许可证目录: /opt/polarion/polarion/license/fasnote
2025-07-29 18:06:02,086 [Catalina-utility-2] INFO  com.fasnote.alm.plugin.manage.util.LicenseDirectoryManager - 使用标准许可证目录: /opt/polarion/polarion/license/fasnote
2025-07-29 18:06:02,087 [Catalina-utility-2] INFO  com.fasnote.alm.plugin.manage.core.LicenseScanner - 开始扫描许可证目录: /opt/polarion/polarion/license/fasnote
2025-07-29 18:06:02,087 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.core.LicenseScanner - 映射配置文件不存在: /opt/polarion/polarion/license/fasnote/license-mapping.properties
2025-07-29 18:06:02,087 [Catalina-utility-2] INFO  com.fasnote.alm.plugin.manage.core.LicenseScanner - 使用文件名匹配模式扫描许可证文件
2025-07-29 18:06:02,087 [Catalina-utility-2] INFO  com.fasnote.alm.plugin.manage.core.LicenseScanner - 许可证文件扫描完成，发现 0 个许可证文件
2025-07-29 18:06:02,087 [Catalina-utility-2] INFO  com.fasnote.alm.plugin.manage.core.LicenseManager - 自动扫描许可证文件完成，共预加载 0 个许可证
2025-07-29 18:06:02,087 [Catalina-utility-2] INFO  com.fasnote.alm.plugin.manage.core.LicenseManager - 许可证管理器初始化完成
2025-07-29 18:06:02,087 [Catalina-utility-2] INFO  com.fasnote.alm.plugin.manage.injection.module.LicenseManagerModule - LicenseManager实例创建成功
2025-07-29 18:06:02,087 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过提供者创建实例: com.fasnote.alm.plugin.manage.core.LicenseManager
2025-07-29 18:06:02,087 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 从DI框架获取LicenseManager实例
2025-07-29 18:06:02,089 [Catalina-utility-5] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:06:02,095 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.injection.module.LicenseModule - 从 LicenseManager 获取到 BundleContext
2025-07-29 18:06:02,095 [Catalina-utility-2] INFO  com.fasnote.alm.plugin.manage.injection.module.LicenseModule - LicenseModule通过依赖注入创建
2025-07-29 18:06:02,095 [Catalina-utility-2] INFO  com.fasnote.alm.plugin.manage.injection.module.LicenseModule - 配置LicenseModule...
2025-07-29 18:06:02,097 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册拦截器: LicenseAwareServiceInterceptor (优先级: 10)
2025-07-29 18:06:02,097 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@7a5073f8] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:06:02,098 [Catalina-utility-2] INFO  com.fasnote.alm.plugin.manage.injection.module.LicenseModule - 许可证感知服务拦截器已注册，优先级: 10
2025-07-29 18:06:02,099 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册拦截器: LicenseServiceInterceptor (优先级: 5)
2025-07-29 18:06:02,099 [Catalina-utility-2] INFO  com.fasnote.alm.plugin.manage.injection.module.LicenseModule - 许可证服务拦截器已注册，优先级: 5
2025-07-29 18:06:02,099 [Catalina-utility-2] WARN  com.fasnote.alm.plugin.manage.injection.module.LicenseModule - SecurityValidator未找到，安全验证拦截器将无法正常工作
2025-07-29 18:06:02,099 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册拦截器: SecurityValidationInterceptor (优先级: 15)
2025-07-29 18:06:02,100 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册拦截器: LicenseValidationEnhancedInterceptor (优先级: 10)
2025-07-29 18:06:02,100 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 同时注册为增强拦截器: LicenseValidationEnhancedInterceptor
2025-07-29 18:06:02,100 [Catalina-utility-2] INFO  com.fasnote.alm.plugin.manage.injection.module.LicenseModule - 增强许可证验证拦截器已注册（支持方法级别AOP）
2025-07-29 18:06:02,100 [Catalina-utility-2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册提供者: com.fasnote.alm.plugin.manage.api.LicenseAware
2025-07-29 18:06:02,100 [Catalina-utility-2] INFO  com.fasnote.alm.plugin.manage.injection.module.LicenseModule - LicenseModule配置完成
2025-07-29 18:06:02,100 [Catalina-utility-2] INFO  com.fasnote.alm.injection.impl.DependencyInjector - 已安装模块: LicenseModule (优先级: 10)
2025-07-29 18:06:02,100 [Catalina-utility-2] INFO  com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 已安装LicenseModule到DI框架
2025-07-29 18:06:02,100 [Catalina-utility-2] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - license业务模块不可用，跳过安装: com.fasnote.alm.license.module.LicenseServiceModule cannot be found by com.fasnote.alm.plugin.manage_2.0.0.qualifier
2025-07-29 18:06:02,100 [Catalina-utility-2] INFO  com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - OSGi感知的许可证框架初始化完成
2025-07-29 18:06:02,100 [Catalina-utility-2] INFO  com.fasnote.alm.plugin.manage.Activator - OSGi许可证框架初始化完成 - 许可证拦截器已安装
2025-07-29 18:06:02,100 [Catalina-utility-2] INFO  com.fasnote.alm.plugin.manage.Activator - 许可证管理插件启动成功
2025-07-29 18:06:02,100 [Catalina-utility-2] INFO  com.fasnote.alm.auth.feishu.Activator - Feishu Authentication Plugin starting...
2025-07-29 18:06:02,100 [Catalina-utility-2] INFO  com.fasnote.alm.auth.feishu.Activator - 注册 FeishuPackageScanProvider 为 OSGi 服务...
2025-07-29 18:06:02,101 [Catalina-utility-2] INFO  com.fasnote.alm.auth.feishu.Activator - FeishuPackageScanProvider OSGi 服务注册成功
2025-07-29 18:06:02,101 [Catalina-utility-2] INFO  com.fasnote.alm.auth.feishu.Activator - 扫描包路径: [com.fasnote.alm.auth.feishu]
2025-07-29 18:06:02,101 [Catalina-utility-2] INFO  com.fasnote.alm.auth.feishu.Activator - Feishu Authentication Plugin started successfully
2025-07-29 18:06:02,101 [Catalina-utility-2] INFO  com.fasnote.alm.auth.feishu.Activator - 飞书插件实现类将通过 OSGi 服务自动扫描注册
2025-07-29 18:06:02,101 [Framework Event Dispatcher: Equinox Container: f5015c1e-7286-43a2-ba9f-9fe4c1011674] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - Bundle启动/解析: com.fasnote.alm.plugin.manage
2025-07-29 18:06:02,101 [Framework Event Dispatcher: Equinox Container: f5015c1e-7286-43a2-ba9f-9fe4c1011674] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.fasnote.alm.plugin.manage
2025-07-29 18:06:02,101 [Framework Event Dispatcher: Equinox Container: f5015c1e-7286-43a2-ba9f-9fe4c1011674] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 动态注册Bundle到DI框架: com.fasnote.alm.plugin.manage
2025-07-29 18:06:02,101 [Framework Event Dispatcher: Equinox Container: f5015c1e-7286-43a2-ba9f-9fe4c1011674] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - Bundle启动/解析: com.fasnote.alm.auth.feishu
2025-07-29 18:06:02,101 [Framework Event Dispatcher: Equinox Container: f5015c1e-7286-43a2-ba9f-9fe4c1011674] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.fasnote.alm.auth.feishu
2025-07-29 18:06:02,101 [Framework Event Dispatcher: Equinox Container: f5015c1e-7286-43a2-ba9f-9fe4c1011674] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 动态注册Bundle到DI框架: com.fasnote.alm.auth.feishu
2025-07-29 18:06:02,104 [Catalina-utility-6] INFO  org.apache.jasper.servlet.TldScanner - At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-07-29 18:06:02,105 [Catalina-utility-5] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:06:02,110 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@5e646bfb] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:06:02,112 [Catalina-utility-5] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:06:02,114 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@412311a2] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:06:02,118 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@579612f8] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:06:02,139 [Catalina-utility-5] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:06:02,149 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@5cef3456] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:06:02,153 [Catalina-utility-2] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@316a1241] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:06:02,161 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@304c70b1] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:06:02,162 [Catalina-utility-2] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@21ce7fca] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:06:02,179 [Catalina-utility-2] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@1b08abee] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:06:02,179 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@357afae1] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:06:02,185 [Catalina-utility-6] INFO  com.polarion.portal.velocity.VelocityPathManager - VelocityTemplatesPath=/opt/polarion/polarion/plugins/com.polarion.alm.ui_3.22.1/webapp/authapp/, /opt/polarion/polarion/plugins/com.polarion.alm.ui_3.22.1/webapp/webui/, /opt/polarion/polarion/plugins/com.polarion.alm.wiki_3.22.1/src/main/webapp/, ., /opt/polarion/polarion/plugins/com.polarion.alm.ui_3.22.1/webapp/webui/
2025-07-29 18:06:02,189 [Catalina-utility-2] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:06:02,190 [Framework Event Dispatcher: Equinox Container: f5015c1e-7286-43a2-ba9f-9fe4c1011674] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - Bundle启动/解析: com.siemens.polarion.rt
2025-07-29 18:06:02,191 [Framework Event Dispatcher: Equinox Container: f5015c1e-7286-43a2-ba9f-9fe4c1011674] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.siemens.polarion.rt
2025-07-29 18:06:02,191 [Framework Event Dispatcher: Equinox Container: f5015c1e-7286-43a2-ba9f-9fe4c1011674] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 动态注册Bundle到DI框架: com.siemens.polarion.rt
2025-07-29 18:06:02,191 [Catalina-utility-3] INFO  org.polarion.svncommons.commentscache.CommentsCache - Initializing comments cache. Id: http://localhost/repo, repository: http://localhost/repo/, url: http://localhost/repo/, cache directory: /opt/polarion/data/workspace/polarion-data/log-messages-cache, cache page size: 100
2025-07-29 18:06:02,195 [Catalina-utility-2] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@2b2220c] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:06:02,198 [Catalina-utility-2] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:06:02,201 [Catalina-utility-2] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@2b697117] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:06:02,209 [Catalina-utility-2] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:06:02,214 [Catalina-utility-2] INFO  org.apache.jasper.servlet.TldScanner - At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-07-29 18:06:02,219 [Catalina-utility-2] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@68ebea51] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:06:02,219 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@2e7075d4] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:06:02,223 [Catalina-utility-2] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@4fcbf387] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:06:02,224 [Catalina-utility-1] INFO  org.apache.jasper.servlet.TldScanner - At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-07-29 18:06:02,224 [Catalina-utility-3] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:06:02,229 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@16a1057e] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:06:02,232 [Catalina-utility-2] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@57514114] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:06:02,233 [Catalina-utility-3] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:06:02,243 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@1c543742] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:06:02,245 [Catalina-utility-2] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@1d55df3a] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:06:02,249 [Catalina-utility-2] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:06:02,256 [Catalina-utility-2] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@79eaabdc] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:06:02,257 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@e1affe8] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:06:02,259 [Catalina-utility-2] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:06:02,265 [Catalina-utility-3] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:06:02,285 [Catalina-utility-3] INFO  org.apache.jasper.servlet.TldScanner - At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-07-29 18:06:02,293 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@7eb6889c] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:06:02,299 [Catalina-utility-3] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:06:02,308 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@78fa82c] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:06:02,315 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@58048676] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:06:02,317 [Catalina-utility-3] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-07-29 18:06:02,320 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@5d0c5251] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:06:02,504 [Catalina-utility-5] INFO  com.siemens.polarion.rt.config.RtAppConfig - RT server config is created
2025-07-29 18:06:02,572 [Catalina-utility-5] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Started.
2025-07-29 18:06:03,075 [Catalina-utility-5] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Initialization of RT collectors...
2025-07-29 18:06:03,076 [Catalina-utility-5] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Found collector for subversion (contributed by 'com.siemens.polarion.rt[87]')
2025-07-29 18:06:03,076 [Catalina-utility-5] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Found collector for git (contributed by 'com.siemens.polarion.rt.collectors.git[89]')
2025-07-29 18:06:03,078 [Catalina-utility-5] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Initialization of RT parsers...
2025-07-29 18:06:03,080 [Catalina-utility-5] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.java (contributed by 'com.siemens.polarion.rt[87]')
2025-07-29 18:06:03,081 [Catalina-utility-5] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.xml (contributed by 'com.siemens.polarion.rt[87]')
2025-07-29 18:06:03,082 [Catalina-utility-5] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.c (contributed by 'com.siemens.polarion.rt.parsers.c[93]')
2025-07-29 18:06:03,406 [main] INFO  org.apache.coyote.ajp.AjpNioProtocol - Starting ProtocolHandler ["ajp-nio-127.0.0.1-8889"]
2025-07-29 18:06:03,412 [main] INFO  com.polarion.psvn.launcher.internal.tomcat.TomcatService - Tomcat is listening on port 8889 using AJP/1.3 protocol with 600000 timeout in millis
2025-07-29 18:06:03,413 [main] INFO  com.polarion.psvn.launcher.internal.help.HelpService - Starting Help Service...
2025-07-29 18:06:03,417 [main] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@6c9635e6] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-07-29 18:06:03,421 [main] INFO  com.polarion.psvn.launcher.internal.help.HelpService - Help Service started
2025-07-29 18:06:03,500 [main | u:p] INFO  com.xpn.xwiki.XWiki - xwiki.cfg taken from /WEB-INF/xwiki.cfg because the XWikiConfig variable is not set in the context
2025-07-29 18:06:03,748 [main | u:p | u:p] INFO  TXLOGGER - Tx 6615695d5943f_0_6615695d5943f_0_: finished. Total: 0.126 s, CPU [user: 0.074 s, system: 0.00637 s], Allocated memory: 7.5 MB, GlobalHandler: 0.039 s [98% applyTxChanges (1x)] (4x)
2025-07-29 18:06:04,160 [Thread-33] INFO  class com.polarion.alm.server.util.ChartExporterStartup - Chart renderer says: Server started on 127.0.0.1:34567
2025-07-29 18:06:04,306 [Framework Event Dispatcher: Equinox Container: f5015c1e-7286-43a2-ba9f-9fe4c1011674] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - Bundle启动/解析: com.polarion.alm.qcentre
2025-07-29 18:06:04,307 [Framework Event Dispatcher: Equinox Container: f5015c1e-7286-43a2-ba9f-9fe4c1011674] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.alm.qcentre
2025-07-29 18:06:04,307 [Framework Event Dispatcher: Equinox Container: f5015c1e-7286-43a2-ba9f-9fe4c1011674] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 动态注册Bundle到DI框架: com.polarion.alm.qcentre
2025-07-29 18:06:04,314 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtCommunicationStartup - Rt configuration local mediator is started
2025-07-29 18:06:04,433 [ajp-nio-127.0.0.1-8889-exec-1 | cID:55a57879-c0a8d700-5cecbd49-8fbcffd6] INFO  com.siemens.polarion.rt.dataprovider.controller.RtDataProviderController - RT server is notified to update all configurations.
2025-07-29 18:06:04,454 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtNotifier - RT server was successfully notified of configuration change.
2025-07-29 18:06:04,455 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtCommunicationStartup - Rt notification service is started
2025-07-29 18:06:04,475 [Thread-37] INFO  com.polarion.platform.jobs.info - Job "Attachment Indexer" has id 55a578b2-c0a8d700-5cecbd49-952ac45b
2025-07-29 18:06:04,478 [Thread-37] INFO  com.polarion.platform.jobs.info - State of job "Attachment Indexer" changed to UNSCHEDULED
2025-07-29 18:06:04,479 [Thread-37] INFO  com.polarion.platform.jobs.info - Working directory of root job "Attachment Indexer" is /opt/polarion/data/workspace/polarion-data/jobs/20250729-1806
2025-07-29 18:06:04,480 [Thread-37] INFO  com.polarion.platform.jobs.info - Job "Attachment Indexer" runs as user "polarion"
2025-07-29 18:06:04,481 [Thread-37 | u:p] INFO  com.polarion.platform.jobs.info - State of job "Attachment Indexer" changed to ACTIVATING
2025-07-29 18:06:04,482 [Thread-37 | u:p] INFO  com.polarion.platform.jobs.info - State of job "Attachment Indexer" changed to WAITING
2025-07-29 18:06:04,482 [Thread-36] INFO  com.polarion.core.util.process.JavaRunner - Executing /Users/<USER>/Library/Java/JavaVirtualMachines/ms-11.0.27/Contents/Home/bin/java
  -- args [-jar, /opt/polarion/polarion/plugins/com.polarion.platform_3.22.1/services/notification-service/NotificationService.jar, --server.port=40608, --jwksUrl=http://localhost/polarion/.well-known/jwks.json]
  -- env null
  -- dir /var/folders/z_/shw6wc7d7ps_fjvv781t4gt80000gn/T/polarionSpringServerSubprocess7059463782150663876.tmp
2025-07-29 18:06:04,486 [Thread-37] INFO  com.polarion.platform.jobs.info - Job "DB History Creator" has id 55a578c5-c0a8d700-5cecbd49-be48f320
2025-07-29 18:06:04,487 [Thread-37] INFO  com.polarion.platform.jobs.info - State of job "DB History Creator" changed to UNSCHEDULED
2025-07-29 18:06:04,487 [Worker-0: Attachment Indexer | u:p] INFO  com.polarion.platform.jobs.info - State of job "Attachment Indexer" changed to RUNNING
2025-07-29 18:06:04,488 [main | u:p] INFO  com.polarion.platform.monitoring - Next slow periodic actions will be executed on Wed Jul 30 01:00:04 CST 2025
2025-07-29 18:06:04,488 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.alm.tracker.internal.HttpsConfiguratorStartup successfully initialized
2025-07-29 18:06:04,488 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.alm.server.util.ChartExporterStartup successfully initialized
2025-07-29 18:06:04,488 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.alm.wiki.WikiPlugin successfully initialized
2025-07-29 18:06:04,488 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.alm.qcentre.internal.QCentreStartup successfully initialized
2025-07-29 18:06:04,488 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.siemens.polarion.rt.communication.connection.RtCommunicationStartup successfully initialized
2025-07-29 18:06:04,488 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.platform.internal.startup.NotificationServerStartup successfully initialized
2025-07-29 18:06:04,488 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.subterra.index.impl.IndexingJobsStartup successfully initialized
2025-07-29 18:06:04,488 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.alm.ui.server.ServerStartup successfully initialized
2025-07-29 18:06:04,489 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.alm.server.util.FormulaServerStartup successfully initialized
2025-07-29 18:06:04,489 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.platform.monitoring.internal.MonitoringServiceStart successfully initialized
2025-07-29 18:06:04,489 [Thread-37] INFO  com.polarion.platform.jobs.info - Working directory of root job "DB History Creator" is /opt/polarion/data/workspace/polarion-data/jobs/20250729-1806_0
2025-07-29 18:06:04,489 [main] INFO  com.polarion.platform.monitoring - Executing actions from stage POSTBOOT
2025-07-29 18:06:04,489 [main] INFO  com.polarion.platform.monitoring - Executing action 'polarion.info.cache.statistics'
2025-07-29 18:06:04,490 [Thread-37] INFO  com.polarion.platform.jobs.info - Job "DB History Creator" runs as user "polarion"
2025-07-29 18:06:04,493 [Thread-37 | u:p] INFO  com.polarion.platform.jobs.info - State of job "DB History Creator" changed to ACTIVATING
2025-07-29 18:06:04,495 [Thread-37 | u:p] INFO  com.polarion.platform.jobs.info - State of job "DB History Creator" changed to WAITING
2025-07-29 18:06:04,500 [Worker-1: DB History Creator | u:p] INFO  com.polarion.platform.jobs.info - State of job "DB History Creator" changed to RUNNING
2025-07-29 18:06:04,500 [main] INFO  com.polarion.platform.monitoring - polarion.info.cache.statistics (Statistics of caches) = Statistics of caches 
                         CACHE       HITS     MISSES  HIT_RATIO       GETS       PUTS    REMOVAL   EVICTION 
     attachments-content-cache          0          0          0%          0          0          0          0 
                baseline-cache          0          0          0%          0          0          0          0 
                            bq          0          0          0%          0          0          0          0 
                dao-attachment          0          0          0%          0          0          0          0 
                   dao-comment          0          0          0%          0          0          0          0 
                    dao-global          0          4          0%          4          0          0          0 
                    dao-module          0          0          0%          0          0          0          0 
          dao-moduleattachment          0          0          0%          0          0          0          0 
             dao-modulecomment          0          0          0%          0          0          0          0 
                      dao-plan          0          0          0%          0          0          0          0 
                   dao-project          0          0          0%          0          0          0          0 
              dao-projectgroup          0          6          0%          6          3          0          0 
                  dao-richpage          0          0          0%          0          0          0          0 
        dao-richpageattachment          0          0          0%          0          0          0          0 
                   dao-testrun          0          0          0%          0          0          0          0 
         dao-testrunattachment          0          0          0%          0          0          0          0 
                      dao-user          0          0          0%          0          0          0          0 
                  dao-wikipage          0          0          0%          0          0          0          0 
        dao-wikipageattachment          0          0          0%          0          0          0          0 
                  dao-workitem          0          0          0%          0          0          0          0 
      derived-linked-revisions          0          0          0%          0          0          0          0 
                  formulas-svg          0          0          0%          0          0          0          0 
                github-commits          0          0          0%          0          0          0          0 
            historical-queries          0          0          0%          0          0          0          0 
      historical-queries-sizes          0          0          0%          0          0          0          0 
        oslc-linked-item-cache          0          0          0%          0          0          0          0 
               plan-statistics          0          0          0%          0          0          0          0 
                   rmd-default          3          5         38%          8          6          0          0 
                   ss-combined          0          0          0%          0          0          0          0 
                    ss-context          0          0          0%          0          0          0          0 
                     wiki-page          2          8         20%         10          9          1          0 
              wiki-page-exists          0          0          0%          0          9          1          0 

 [Tue Jul 29 18:06:04 CST 2025]
2025-07-29 18:06:04,504 [main] INFO  com.polarion.platform.monitoring - Finished with actions from stage POSTBOOT: {OK=1}
2025-07-29 18:06:04,504 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.85 s. ]
2025-07-29 18:06:04,504 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.555 s [90% info (158x)] (168x), GlobalHandler: 0.039 s [98% applyTxChanges (1x)] (7x)
2025-07-29 18:06:04,504 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 18:06:04,504 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 10.2 s. ]
2025-07-29 18:06:04,504 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 18:06:04,510 [Worker-0: Attachment Indexer | u:p] INFO  com.polarion.platform.jobs.info - State of job "Attachment Indexer" changed to FINISHED
2025-07-29 18:06:04,512 [PreLoadDataService] INFO  com.polarion.psvn.launcher.internal.data.PreLoadDataService - Preloading data ...
2025-07-29 18:06:04,513 [Worker-0: Attachment Indexer | u:p] INFO  com.polarion.platform.jobs.info - Status of job "Attachment Indexer" is OK
2025-07-29 18:06:04,778 [PreLoadDataService | u:p] DEBUG com.fasnote.alm.queryexpander.queryExpanderInterceptorFactory - Creating SingletonProxy for service com.fasnote.alm.queryexpander.queryExpanderInterceptorFactory
2025-07-29 18:06:04,781 [PreLoadDataService | u:p] DEBUG com.fasnote.alm.queryexpander.queryExpanderInterceptorFactory - Constructing core service implementation for service com.fasnote.alm.queryexpander.queryExpanderInterceptorFactory
2025-07-29 18:06:04,783 [Framework Event Dispatcher: Equinox Container: f5015c1e-7286-43a2-ba9f-9fe4c1011674] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - Bundle启动/解析: com.fasnote.alm.queryexpander
2025-07-29 18:06:04,783 [Framework Event Dispatcher: Equinox Container: f5015c1e-7286-43a2-ba9f-9fe4c1011674] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.fasnote.alm.queryexpander
2025-07-29 18:06:04,783 [Framework Event Dispatcher: Equinox Container: f5015c1e-7286-43a2-ba9f-9fe4c1011674] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 动态注册Bundle到DI框架: com.fasnote.alm.queryexpander
2025-07-29 18:06:04,836 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: TimePoint
2025-07-29 18:06:04,838 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: TimePoint
2025-07-29 18:06:04,849 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Category
2025-07-29 18:06:04,859 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Category
2025-07-29 18:06:04,997 [Thread-43] INFO  class com.polarion.alm.server.util.FormulaServerStartup - Formula renderer says: Server started on 127.0.0.1:34568
2025-07-29 18:06:05,247 [Thread-39] INFO  NotificationService - 
2025-07-29 18:06:05,248 [Thread-39] INFO  NotificationService -   .   ____          _            __ _ _
2025-07-29 18:06:05,248 [Thread-39] INFO  NotificationService -  /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
2025-07-29 18:06:05,248 [Thread-39] INFO  NotificationService - ( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
2025-07-29 18:06:05,248 [Thread-39] INFO  NotificationService -  \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
2025-07-29 18:06:05,248 [Thread-39] INFO  NotificationService -   '  |____| .__|_| |_|_| |_\__, | / / / /
2025-07-29 18:06:05,248 [Thread-39] INFO  NotificationService -  =========|_|==============|___/=/_/_/_/
2025-07-29 18:06:05,248 [Thread-39] INFO  NotificationService -  :: Spring Boot ::                (v2.6.6)
2025-07-29 18:06:05,248 [Thread-39] INFO  NotificationService - 
2025-07-29 18:06:05,332 [Thread-39] INFO  NotificationService - [main] INFO  c.s.polarion.service.notification.Application - Starting Application using Java 11.0.27 on zhangwentiandeMac-mini-2.local with PID 36852 (/opt/polarion/polarion/plugins/com.polarion.platform_3.22.1/services/notification-service/NotificationService.jar started by zhangwentian in /private/var/folders/z_/shw6wc7d7ps_fjvv781t4gt80000gn/T/polarionSpringServerSubprocess7059463782150663876.tmp)
2025-07-29 18:06:05,337 [Thread-39] INFO  NotificationService - [main] INFO  c.s.polarion.service.notification.Application - No active profile set, falling back to 1 default profile: "default"
2025-07-29 18:06:05,430 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  com.polarion.subterra.index.impl.ObjectIndex - Preloading of baselines: 22, for prototypes: WorkItem; with days range: 180d, took  [ TIME 0.831 s. ] 
2025-07-29 18:06:05,432 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.851 s, CPU [user: 0.00874 s, system: 0.00171 s], Allocated memory: 531.6 kB, transactions: 1
2025-07-29 18:06:05,433 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 28, notification worker: 0.0324 s [58% RevisionActivityCreator (2x), 17% PlanActivityCreator (1x), 13% TestRunActivityCreator (1x)] (6x), persistence listener: 0.0242 s [76% indexRefreshPersistenceListener (1x), 14% WorkItemActivityCreator (1x)] (7x), Incremental Baseline: 0.0239 s [100% WorkItem (21x)] (21x), resolve: 0.0164 s [61% User (1x), 39% Revision (2x)] (3x), PullingJob: 0.012 s [100% collectChanges (1x)] (1x), svn: 0.0117 s [65% getLatestRevision (1x), 35% testConnection (1x)] (2x), Lucene: 0.00493 s [100% refresh (1x)] (1x), Full Baseline: 0.00342 s [100% WorkItem (1x)] (1x), DB: 0.0028 s [63% update (1x), 37% commit (1x)] (2x), ObjectMaps: 0.00198 s [100% getPrimaryObjectLocation (1x)] (1x)
2025-07-29 18:06:05,433 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.933 s, CPU [user: 0.18 s, system: 0.0282 s], Allocated memory: 18.3 MB, transactions: 22, svn: 0.764 s [99% getDatedRevision (181x)] (183x)
2025-07-29 18:06:05,434 [Worker-1: DB History Creator | u:p] INFO  com.polarion.platform.jobs.info - State of job "DB History Creator" changed to FINISHED
2025-07-29 18:06:05,435 [Worker-1: DB History Creator | u:p] INFO  com.polarion.platform.jobs.info - Status of job "DB History Creator" is OK
2025-07-29 18:06:05,494 [Activities-Bulk-Publisher] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Activities
2025-07-29 18:06:05,920 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.ObjectIndex - Preloading of baselines: 22, for prototypes: WorkItem; with days range: 180d, took  [ TIME 0.489 s. ] 
2025-07-29 18:06:05,939 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6615695e38c40_0_6615695e38c40_0_: finished. Total: 1.42 s, CPU [user: 0.469 s, system: 0.102 s], Allocated memory: 54.6 MB, svn: 0.834 s [57% getDatedRevision (181x), 28% getDir2 content (25x)] (307x), resolve: 0.501 s [100% Category (96x)] (96x), ObjectMaps: 0.176 s [45% getPrimaryObjectProperty (96x), 34% getPrimaryObjectLocation (96x), 22% getLastPromoted (96x)] (388x)
2025-07-29 18:06:05,943 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: ProjectGroup
2025-07-29 18:06:05,948 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: ProjectGroup
2025-07-29 18:06:05,966 [Thread-39] INFO  NotificationService - [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 40608 (http)
2025-07-29 18:06:05,974 [Thread-39] INFO  NotificationService - [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-40608"]
2025-07-29 18:06:05,974 [Thread-39] INFO  NotificationService - [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-29 18:06:05,974 [Thread-39] INFO  NotificationService - [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-07-29 18:06:05,981 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Project
2025-07-29 18:06:05,987 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Project
2025-07-29 18:06:06,026 [Thread-39] INFO  NotificationService - [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-29 18:06:06,026 [Thread-39] INFO  NotificationService - [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 642 ms
2025-07-29 18:06:06,049 [Thread-39] INFO  NotificationService - [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing AtmosphereFramework
2025-07-29 18:06:06,051 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: User
2025-07-29 18:06:06,054 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: User
2025-07-29 18:06:06,170 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6615695fb8848_0_6615695fb8848_0_: finished. Total: 0.119 s, CPU [user: 0.0571 s, system: 0.00955 s], Allocated memory: 8.4 MB, RepositoryConfigService: 0.0557 s [51% getReadUserConfiguration (10x), 49% getReadConfiguration (162x)] (172x), svn: 0.0538 s [52% info (19x), 40% getFile content (16x)] (37x), resolve: 0.0369 s [100% User (9x)] (9x), ObjectMaps: 0.0196 s [50% getPrimaryObjectProperty (9x), 30% getPrimaryObjectLocation (9x)] (37x)
2025-07-29 18:06:06,231 [Thread-39] INFO  NotificationService - [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-40608"]
2025-07-29 18:06:06,265 [Thread-39] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Atmosphere is using org.atmosphere.cpr.DefaultAnnotationProcessor for processing annotation
2025-07-29 18:06:06,265 [Thread-39] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.DefaultAnnotationProcessor - AnnotationProcessor class org.atmosphere.cpr.DefaultAnnotationProcessor$BytecodeBasedAnnotationProcessor being used
2025-07-29 18:06:06,285 [Thread-39] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AnnotationHandler - Found Annotation in class com.siemens.polarion.service.notification.NotificationService being scanned: interface org.atmosphere.config.service.ManagedService
2025-07-29 18:06:06,289 [Thread-39] INFO  NotificationService - [main] INFO  org.atmosphere.annotation.AnnotationUtil - Adding class org.atmosphere.interceptor.AtmosphereResourceLifecycleInterceptor
2025-07-29 18:06:06,290 [Thread-39] INFO  NotificationService - [main] INFO  org.atmosphere.annotation.AnnotationUtil - Adding class org.atmosphere.client.TrackMessageSizeInterceptor
2025-07-29 18:06:06,291 [Thread-39] INFO  NotificationService - [main] INFO  org.atmosphere.annotation.AnnotationUtil - Adding class org.atmosphere.interceptor.SuspendTrackerInterceptor
2025-07-29 18:06:06,291 [Thread-39] INFO  NotificationService - [main] INFO  org.atmosphere.annotation.AnnotationUtil - Adding class org.atmosphere.config.managed.ManagedServiceInterceptor
2025-07-29 18:06:06,299 [Thread-39] INFO  NotificationService - [main] INFO  org.atmosphere.annotation.AnnotationUtil - Adding class com.siemens.polarion.service.notification.JwtVerificationInterceptor
2025-07-29 18:06:06,305 [Thread-39] INFO  NotificationService - [main] INFO  org.atmosphere.util.ForkJoinPool - Using ForkJoinPool  java.util.concurrent.ForkJoinPool. Set the org.atmosphere.cpr.broadcaster.maxAsyncWriteThreads to -1 to fully use its power.
2025-07-29 18:06:06,308 [Thread-39] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereHandler org.atmosphere.config.managed.ManagedAtmosphereHandler mapped to context-path /notification and Broadcaster Class org.atmosphere.cpr.DefaultBroadcaster
2025-07-29 18:06:06,308 [Thread-39] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor [Atmosphere LifeCycle,  Track Message Size Interceptor using |, UUID Tracking Interceptor, @ManagedService Interceptor, @Service Event Listeners, com.siemens.polarion.service.notification.JwtVerificationInterceptor] mapped to AtmosphereHandler org.atmosphere.config.managed.ManagedAtmosphereHandler
2025-07-29 18:06:06,318 [Thread-39] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Auto detecting WebSocketHandler in /WEB-INF/classes/
2025-07-29 18:06:06,322 [Thread-39] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed WebSocketProtocol org.atmosphere.websocket.protocol.SimpleHttpProtocol 
2025-07-29 18:06:06,325 [Thread-39] INFO  NotificationService - [main] INFO  org.atmosphere.container.JSR356AsyncSupport - JSR 356 Mapping path /notification
2025-07-29 18:06:06,330 [Thread-39] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installing Default AtmosphereInterceptors
2025-07-29 18:06:06,330 [Thread-39] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.CorsInterceptor : CORS Interceptor Support
2025-07-29 18:06:06,330 [Thread-39] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.CacheHeadersInterceptor : Default Response's Headers Interceptor
2025-07-29 18:06:06,330 [Thread-39] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.PaddingAtmosphereInterceptor : Browser Padding Interceptor Support
2025-07-29 18:06:06,331 [Thread-39] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.AndroidAtmosphereInterceptor : Android Interceptor Support
2025-07-29 18:06:06,332 [Thread-39] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.HeartbeatInterceptor : Heartbeat Interceptor Support
2025-07-29 18:06:06,332 [Thread-39] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.SSEAtmosphereInterceptor : SSE Interceptor Support
2025-07-29 18:06:06,333 [Thread-39] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.JSONPAtmosphereInterceptor : JSONP Interceptor Support
2025-07-29 18:06:06,333 [Thread-39] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.JavaScriptProtocol : Atmosphere JavaScript Protocol
2025-07-29 18:06:06,333 [Thread-39] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.WebSocketMessageSuspendInterceptor : org.atmosphere.interceptor.WebSocketMessageSuspendInterceptor
2025-07-29 18:06:06,333 [Thread-39] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.OnDisconnectInterceptor : Browser disconnection detection
2025-07-29 18:06:06,333 [Thread-39] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.IdleResourceInterceptor : org.atmosphere.interceptor.IdleResourceInterceptor
2025-07-29 18:06:06,333 [Thread-39] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Set org.atmosphere.cpr.AtmosphereInterceptor.disableDefaults to disable them.
2025-07-29 18:06:06,333 [Thread-39] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor CORS Interceptor Support with priority FIRST_BEFORE_DEFAULT 
2025-07-29 18:06:06,335 [Thread-39] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor Default Response's Headers Interceptor with priority AFTER_DEFAULT 
2025-07-29 18:06:06,335 [Thread-39] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor Browser Padding Interceptor Support with priority AFTER_DEFAULT 
2025-07-29 18:06:06,335 [Thread-39] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor Android Interceptor Support with priority AFTER_DEFAULT 
2025-07-29 18:06:06,335 [Thread-39] INFO  NotificationService - [main] INFO  org.atmosphere.interceptor.HeartbeatInterceptor - HeartbeatInterceptor configured with padding value 'X', client frequency 30 seconds and server frequency 120 seconds
2025-07-29 18:06:06,335 [Thread-39] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor Heartbeat Interceptor Support with priority AFTER_DEFAULT 
2025-07-29 18:06:06,335 [Thread-39] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor SSE Interceptor Support with priority AFTER_DEFAULT 
2025-07-29 18:06:06,335 [Thread-39] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor JSONP Interceptor Support with priority AFTER_DEFAULT 
2025-07-29 18:06:06,335 [Thread-39] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor Atmosphere JavaScript Protocol with priority AFTER_DEFAULT 
2025-07-29 18:06:06,335 [Thread-39] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor org.atmosphere.interceptor.WebSocketMessageSuspendInterceptor with priority AFTER_DEFAULT 
2025-07-29 18:06:06,335 [Thread-39] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor Browser disconnection detection with priority AFTER_DEFAULT 
2025-07-29 18:06:06,335 [Thread-39] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor org.atmosphere.interceptor.IdleResourceInterceptor with priority BEFORE_DEFAULT 
2025-07-29 18:06:06,336 [Thread-39] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Using EndpointMapper class org.atmosphere.util.DefaultEndpointMapper
2025-07-29 18:06:06,336 [Thread-39] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Using BroadcasterCache: org.atmosphere.cache.UUIDBroadcasterCache
2025-07-29 18:06:06,338 [Thread-39] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Default Broadcaster Class: org.atmosphere.cpr.DefaultBroadcaster
2025-07-29 18:06:06,338 [Thread-39] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Broadcaster Shared List Resources: false
2025-07-29 18:06:06,339 [Thread-39] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Broadcaster Polling Wait Time 100
2025-07-29 18:06:06,339 [Thread-39] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Shared ExecutorService supported: true
2025-07-29 18:06:06,339 [Thread-39] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Messaging ExecutorService Pool Size unavailable - Not instance of ThreadPoolExecutor
2025-07-29 18:06:06,339 [Thread-39] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Async I/O Thread Pool Size: 200
2025-07-29 18:06:06,339 [Thread-39] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Using BroadcasterFactory: org.atmosphere.cpr.DefaultBroadcasterFactory
2025-07-29 18:06:06,339 [Thread-39] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Using AtmosphereResurceFactory: org.atmosphere.cpr.DefaultAtmosphereResourceFactory
2025-07-29 18:06:06,339 [Thread-39] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Using WebSocketProcessor: org.atmosphere.websocket.DefaultWebSocketProcessor
2025-07-29 18:06:06,342 [Thread-39] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Invoke AtmosphereInterceptor on WebSocket message true
2025-07-29 18:06:06,342 [Thread-39] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - HttpSession supported: false
2025-07-29 18:06:06,342 [Thread-39] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Atmosphere is using Spring Web ObjectFactory for dependency injection and object creation
2025-07-29 18:06:06,342 [Thread-39] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Atmosphere is using async support: org.atmosphere.container.JSR356AsyncSupport running under container: Apache Tomcat/9.0.60 using javax.servlet/3.0 and jsr356/WebSocket API
2025-07-29 18:06:06,342 [Thread-39] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Atmosphere Framework 2.6.4 started.
2025-07-29 18:06:06,342 [Thread-39] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 
2025-07-29 18:06:06,342 [Thread-39] INFO  NotificationService - 
2025-07-29 18:06:06,342 [Thread-39] INFO  NotificationService - 	For Atmosphere Framework Commercial Support, visit 
2025-07-29 18:06:06,342 [Thread-39] INFO  NotificationService - 	http://www.async-io.org/ or send an <NAME_EMAIL>
2025-07-29 18:06:06,342 [Thread-39] INFO  NotificationService - 
2025-07-29 18:06:06,345 [Thread-39] INFO  NotificationService - [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 40608 (http) with context path ''
2025-07-29 18:06:06,350 [Thread-39] INFO  NotificationService - [main] INFO  c.s.polarion.service.notification.Application - Started Application in 1.472 seconds (JVM running for 1.852)
2025-07-29 18:06:06,399 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testtype) created
2025-07-29 18:06:06,405 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (subtype) created
2025-07-29 18:06:06,409 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6615695fe004a_0_6615695fe004a_0_: finished. Total: 0.201 s, CPU [user: 0.0669 s, system: 0.00707 s], Allocated memory: 19.9 MB, svn: 0.161 s [76% getDir2 content (17x), 24% getFile content (44x)] (62x), RepositoryConfigService: 0.0619 s [98% getReadConfiguration (170x)] (192x)
2025-07-29 18:06:07,319 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (YesNo) created
2025-07-29 18:06:07,353 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (software_VerificationMethod) created
2025-07-29 18:06:07,372 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (checklist) created
2025-07-29 18:06:07,384 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (commonreqproperty) created
2025-07-29 18:06:07,393 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (objectoriented) created
2025-07-29 18:06:07,396 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (submodule) created
2025-07-29 18:06:07,401 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (yesno) created
2025-07-29 18:06:07,402 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (cICategory) created
2025-07-29 18:06:07,404 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (wpFormat) created
2025-07-29 18:06:07,411 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (trigger) created
2025-07-29 18:06:07,430 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (ASILLevel) created
2025-07-29 18:06:07,433 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (CSRelated) created
2025-07-29 18:06:07,450 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (project_Module) created
2025-07-29 18:06:07,492 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (规格对象类型) created
2025-07-29 18:06:07,499 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (jenkins_job) created
2025-07-29 18:06:07,499 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (truefalse) created
2025-07-29 18:06:07,501 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (takeOnGroups) created
2025-07-29 18:06:07,535 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testcasetype) created
2025-07-29 18:06:07,554 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (processGroup) created
2025-07-29 18:06:07,567 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (changeManagement) created
2025-07-29 18:06:07,581 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (seriousness) created
2025-07-29 18:06:07,595 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (softReqClass) created
2025-07-29 18:06:07,611 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (SWDetailDesign) created
2025-07-29 18:06:07,617 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (PhaseChecklists) created
2025-07-29 18:06:07,623 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (PassNotpass) created
2025-07-29 18:06:07,628 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (baseLineType) created
2025-07-29 18:06:07,639 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (boolYesOrNo) created
2025-07-29 18:06:07,644 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testlevel) created
2025-07-29 18:06:07,652 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (source) created
2025-07-29 18:06:07,669 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (objectType) created
2025-07-29 18:06:07,685 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (atppblversion) created
2025-07-29 18:06:07,692 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (aSIL) created
2025-07-29 18:06:07,705 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (EE) created
2025-07-29 18:06:07,710 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (issueType) created
2025-07-29 18:06:07,719 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (SYS_reqClassification) created
2025-07-29 18:06:07,727 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (oem_2Status) created
2025-07-29 18:06:07,749 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (class) created
2025-07-29 18:06:07,761 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (promotionState) created
2025-07-29 18:06:07,768 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (git_project) created
2025-07-29 18:06:07,789 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (storageType) created
2025-07-29 18:06:07,794 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (issueproperty) created
2025-07-29 18:06:07,810 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (commonissueclass) created
2025-07-29 18:06:07,822 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (AgreeDisagree) created
2025-07-29 18:06:07,834 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (SA_Category) created
2025-07-29 18:06:07,844 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (relevance) created
2025-07-29 18:06:07,860 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (implementationPhase) created
2025-07-29 18:06:07,867 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (supplier_2Status) created
2025-07-29 18:06:07,877 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Testtype) created
2025-07-29 18:06:07,916 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (conf_baselineTime) created
2025-07-29 18:06:07,950 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (levelneed) created
2025-07-29 18:06:07,967 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (finalresult) created
2025-07-29 18:06:07,981 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testability) created
2025-07-29 18:06:08,002 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (solution) created
2025-07-29 18:06:08,027 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Responsible) created
2025-07-29 18:06:08,039 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (verificationstatus) created
2025-07-29 18:06:08,057 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (hsiassigngroup) created
2025-07-29 18:06:08,062 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (reqCategory) created
2025-07-29 18:06:08,068 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (baseLineName) created
2025-07-29 18:06:08,072 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (taskType) created
2025-07-29 18:06:08,088 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (changeReason) created
2025-07-29 18:06:08,092 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (objectmodule) created
2025-07-29 18:06:08,100 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (TestCaseOutputMethod) created
2025-07-29 18:06:08,106 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (SoftwareFeature) created
2025-07-29 18:06:08,134 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (ResponsibleGroup) created
2025-07-29 18:06:08,142 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (hsifunctionmodule) created
2025-07-29 18:06:08,152 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (FwReqSource) created
2025-07-29 18:06:08,160 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (occurPhase) created
2025-07-29 18:06:08,171 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (compiletask) created
2025-07-29 18:06:08,178 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (REQBVerificationMethod) created
2025-07-29 18:06:08,185 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (functionmodule) created
2025-07-29 18:06:08,193 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (variant) created
2025-07-29 18:06:08,202 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Fusatype) created
2025-07-29 18:06:08,209 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (hardwareversion) created
2025-07-29 18:06:08,215 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (appversion) created
2025-07-29 18:06:08,217 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (casefirstmodule) created
2025-07-29 18:06:08,219 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55a58730-c0a8d700-5cecbd49-9a5a80c3] DEBUG com.fasnote.alm.auth.feishu.feishuAuthenticatorManagerInterceptorFactory - Creating SingletonProxy for service com.fasnote.alm.auth.feishu.feishuAuthenticatorManagerInterceptorFactory
2025-07-29 18:06:08,220 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55a58730-c0a8d700-5cecbd49-9a5a80c3] DEBUG com.fasnote.alm.auth.feishu.feishuAuthenticatorManagerInterceptorFactory - Constructing core service implementation for service com.fasnote.alm.auth.feishu.feishuAuthenticatorManagerInterceptorFactory
2025-07-29 18:06:08,220 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (auditType) created
2025-07-29 18:06:08,222 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55a58730-c0a8d700-5cecbd49-9a5a80c3] INFO  com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Creating FeishuAuthenticatorManager interceptor to handle feishu-prefixed authenticators
2025-07-29 18:06:08,222 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55a58730-c0a8d700-5cecbd49-9a5a80c3] INFO  com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - FeishuAuthenticatorManager interceptor created successfully
2025-07-29 18:06:08,223 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55a58730-c0a8d700-5cecbd49-9a5a80c3] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Intercepting getAuthenticators() method call for Feishu enhancement
2025-07-29 18:06:08,223 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55a58730-c0a8d700-5cecbd49-9a5a80c3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 开始解析服务: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer, 服务名: null
2025-07-29 18:06:08,223 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55a58730-c0a8d700-5cecbd49-9a5a80c3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 单例服务缓存中未找到: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer, 当前缓存大小: 0
2025-07-29 18:06:08,223 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55a58730-c0a8d700-5cecbd49-9a5a80c3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 未找到实现类: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer
2025-07-29 18:06:08,223 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55a58730-c0a8d700-5cecbd49-9a5a80c3] WARN  com.fasnote.alm.injection.impl.DependencyInjector - 无法解析服务: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer
2025-07-29 18:06:08,223 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55a58730-c0a8d700-5cecbd49-9a5a80c3] WARN  com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - DI framework returned unexpected type, falling back to default enhancer
2025-07-29 18:06:08,224 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55a58730-c0a8d700-5cecbd49-9a5a80c3] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Using enhancer: Default OAuth2 Enhancer
2025-07-29 18:06:08,224 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55a58730-c0a8d700-5cecbd49-9a5a80c3] INFO  com.fasnote.alm.auth.feishu.FeishuDefaultAuthenticatorEnhancer - Feishu license validation failed or not present, using default OAuth2 authenticators
2025-07-29 18:06:08,224 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55a58730-c0a8d700-5cecbd49-9a5a80c3] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Feishu authenticators registered successfully in getAuthenticators() call
2025-07-29 18:06:08,225 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Samplestage) created
2025-07-29 18:06:08,228 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (casesecondmodule) created
2025-07-29 18:06:08,233 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (issue_source) created
2025-07-29 18:06:08,236 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (ifNeedRegressionTesting) created
2025-07-29 18:06:08,239 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (atpsfsversion) created
2025-07-29 18:06:08,243 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (CustomerAllocation) created
2025-07-29 18:06:08,247 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (issuesubclass) created
2025-07-29 18:06:08,257 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (QANC_importance) created
2025-07-29 18:06:08,262 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (reviewMethod) created
2025-07-29 18:06:08,274 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (QANC_findType) created
2025-07-29 18:06:08,285 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (editType) created
2025-07-29 18:06:08,301 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testingobjects) created
2025-07-29 18:06:08,307 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testcaselevel) created
2025-07-29 18:06:08,316 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (supplierproblem) created
2025-07-29 18:06:08,323 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (reqattribute) created
2025-07-29 18:06:08,331 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (fsigroup) created
2025-07-29 18:06:08,340 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (project_reqsource) created
2025-07-29 18:06:08,355 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (preset) created
2025-07-29 18:06:08,369 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Mechverificationmethod) created
2025-07-29 18:06:08,377 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (CPMToTPM) created
2025-07-29 18:06:08,384 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (REQBType) created
2025-07-29 18:06:08,388 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testcasesign) created
2025-07-29 18:06:08,395 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (verificationphase) created
2025-07-29 18:06:08,411 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (processArea) created
2025-07-29 18:06:08,419 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (artifactType) created
2025-07-29 18:06:08,433 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Classification) created
2025-07-29 18:06:08,440 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (verificationmethod) created
2025-07-29 18:06:08,444 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (changeType) created
2025-07-29 18:06:08,451 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (hardwareAndSoftwareSubType) created
2025-07-29 18:06:08,456 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (SWIntegrationVerificationMethod) created
2025-07-29 18:06:08,460 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (category) created
2025-07-29 18:06:08,492 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (REQBCategory) created
2025-07-29 18:06:08,506 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (softreqclass) created
2025-07-29 18:06:08,511 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (TestMethod) created
2025-07-29 18:06:08,518 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (reType) created
2025-07-29 18:06:08,524 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (VerificationCriteria) created
2025-07-29 18:06:08,528 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (baseLinechecklist) created
2025-07-29 18:06:08,543 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Category) created
2025-07-29 18:06:08,558 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (SWUnitTestDerivingMethods) created
2025-07-29 18:06:08,566 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (firmware_Category) created
2025-07-29 18:06:08,571 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testMethod) created
2025-07-29 18:06:08,571 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55a58730-c0a8d700-5cecbd49-9a5a80c3] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Intercepting getAuthenticators() method call for Feishu enhancement
2025-07-29 18:06:08,571 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55a58730-c0a8d700-5cecbd49-9a5a80c3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 开始解析服务: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer, 服务名: null
2025-07-29 18:06:08,571 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55a58730-c0a8d700-5cecbd49-9a5a80c3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 单例服务缓存中未找到: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer, 当前缓存大小: 0
2025-07-29 18:06:08,571 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55a58730-c0a8d700-5cecbd49-9a5a80c3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 未找到实现类: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer
2025-07-29 18:06:08,571 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55a58730-c0a8d700-5cecbd49-9a5a80c3] WARN  com.fasnote.alm.injection.impl.DependencyInjector - 无法解析服务: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer
2025-07-29 18:06:08,571 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55a58730-c0a8d700-5cecbd49-9a5a80c3] WARN  com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - DI framework returned unexpected type, falling back to default enhancer
2025-07-29 18:06:08,571 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55a58730-c0a8d700-5cecbd49-9a5a80c3] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Using enhancer: Default OAuth2 Enhancer
2025-07-29 18:06:08,571 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55a58730-c0a8d700-5cecbd49-9a5a80c3] INFO  com.fasnote.alm.auth.feishu.FeishuDefaultAuthenticatorEnhancer - Feishu license validation failed or not present, using default OAuth2 authenticators
2025-07-29 18:06:08,571 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55a58730-c0a8d700-5cecbd49-9a5a80c3] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Feishu authenticators registered successfully in getAuthenticators() call
2025-07-29 18:06:08,586 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (QAPorcessAreas) created
2025-07-29 18:06:08,586 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55a58730-c0a8d700-5cecbd49-9a5a80c3] INFO  com.polarion.platform.realm.PolarionRealm - authenticate:admin
2025-07-29 18:06:08,591 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (findSource) created
2025-07-29 18:06:08,600 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661569601244b_0_661569601244b_0_: finished. Total: 2.19 s, CPU [user: 0.728 s, system: 0.174 s], Allocated memory: 1.1 GB, RepositoryConfigService: 1.93 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.961 s [86% getFile content (412x)] (434x)
2025-07-29 18:06:08,611 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55a58730-c0a8d700-5cecbd49-9a5a80c3 | u:p] INFO  com.polarion.platform.internal.security.auth.LoginFlowUserPassword - User admin authenticated from portal/127.0.0.1
2025-07-29 18:06:08,643 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55a58730-c0a8d700-5cecbd49-9a5a80c3 | u:p] INFO  com.polarion.platform.internal.security.auth.LoginFlowUserPassword - User admin logged in from portal/127.0.0.1
2025-07-29 18:06:08,647 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55a58730-c0a8d700-5cecbd49-9a5a80c3] INFO  remember-me-log - Login using remember me for username: admin
2025-07-29 18:06:08,651 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55a58730-c0a8d700-5cecbd49-9a5a80c3] INFO  PolarionLicensing - User 'admin' logged in with named ALM
2025-07-29 18:06:08,687 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55a58730-c0a8d700-5cecbd49-9a5a80c3] INFO  TXLOGGER - Summary for 'servlet /polarion/': Total: 0.51 s, CPU [user: 0.277 s, system: 0.0501 s], Allocated memory: 37.0 MB, transactions: 2, PolarionAuthenticator: 0.474 s [100% authenticate (1x)] (1x)
2025-07-29 18:06:08,807 [ajp-nio-127.0.0.1-8889-exec-3 | cID:55a589a4-c0a8d700-5cecbd49-b81100e4] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Intercepting getAuthenticators() method call for Feishu enhancement
2025-07-29 18:06:08,807 [ajp-nio-127.0.0.1-8889-exec-3 | cID:55a589a4-c0a8d700-5cecbd49-b81100e4] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 开始解析服务: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer, 服务名: null
2025-07-29 18:06:08,808 [ajp-nio-127.0.0.1-8889-exec-3 | cID:55a589a4-c0a8d700-5cecbd49-b81100e4] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 单例服务缓存中未找到: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer, 当前缓存大小: 0
2025-07-29 18:06:08,808 [ajp-nio-127.0.0.1-8889-exec-3 | cID:55a589a4-c0a8d700-5cecbd49-b81100e4] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 未找到实现类: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer
2025-07-29 18:06:08,808 [ajp-nio-127.0.0.1-8889-exec-3 | cID:55a589a4-c0a8d700-5cecbd49-b81100e4] WARN  com.fasnote.alm.injection.impl.DependencyInjector - 无法解析服务: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer
2025-07-29 18:06:08,808 [ajp-nio-127.0.0.1-8889-exec-3 | cID:55a589a4-c0a8d700-5cecbd49-b81100e4] WARN  com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - DI framework returned unexpected type, falling back to default enhancer
2025-07-29 18:06:08,808 [ajp-nio-127.0.0.1-8889-exec-3 | cID:55a589a4-c0a8d700-5cecbd49-b81100e4] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Using enhancer: Default OAuth2 Enhancer
2025-07-29 18:06:08,808 [ajp-nio-127.0.0.1-8889-exec-3 | cID:55a589a4-c0a8d700-5cecbd49-b81100e4] INFO  com.fasnote.alm.auth.feishu.FeishuDefaultAuthenticatorEnhancer - Feishu license validation failed or not present, using default OAuth2 authenticators
2025-07-29 18:06:08,809 [ajp-nio-127.0.0.1-8889-exec-3 | cID:55a589a4-c0a8d700-5cecbd49-b81100e4] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Feishu authenticators registered successfully in getAuthenticators() call
2025-07-29 18:06:08,809 [ajp-nio-127.0.0.1-8889-exec-3 | cID:55a589a4-c0a8d700-5cecbd49-b81100e4] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Intercepting getAuthenticators() method call for Feishu enhancement
2025-07-29 18:06:08,809 [ajp-nio-127.0.0.1-8889-exec-3 | cID:55a589a4-c0a8d700-5cecbd49-b81100e4] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 开始解析服务: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer, 服务名: null
2025-07-29 18:06:08,809 [ajp-nio-127.0.0.1-8889-exec-3 | cID:55a589a4-c0a8d700-5cecbd49-b81100e4] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 单例服务缓存中未找到: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer, 当前缓存大小: 0
2025-07-29 18:06:08,809 [ajp-nio-127.0.0.1-8889-exec-3 | cID:55a589a4-c0a8d700-5cecbd49-b81100e4] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 未找到实现类: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer
2025-07-29 18:06:08,809 [ajp-nio-127.0.0.1-8889-exec-3 | cID:55a589a4-c0a8d700-5cecbd49-b81100e4] WARN  com.fasnote.alm.injection.impl.DependencyInjector - 无法解析服务: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer
2025-07-29 18:06:08,810 [ajp-nio-127.0.0.1-8889-exec-3 | cID:55a589a4-c0a8d700-5cecbd49-b81100e4] WARN  com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - DI framework returned unexpected type, falling back to default enhancer
2025-07-29 18:06:08,810 [ajp-nio-127.0.0.1-8889-exec-3 | cID:55a589a4-c0a8d700-5cecbd49-b81100e4] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Using enhancer: Default OAuth2 Enhancer
2025-07-29 18:06:08,810 [ajp-nio-127.0.0.1-8889-exec-3 | cID:55a589a4-c0a8d700-5cecbd49-b81100e4] INFO  com.fasnote.alm.auth.feishu.FeishuDefaultAuthenticatorEnhancer - Feishu license validation failed or not present, using default OAuth2 authenticators
2025-07-29 18:06:08,810 [ajp-nio-127.0.0.1-8889-exec-3 | cID:55a589a4-c0a8d700-5cecbd49-b81100e4] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Feishu authenticators registered successfully in getAuthenticators() call
2025-07-29 18:06:08,829 [ajp-nio-127.0.0.1-8889-exec-4 | cID:55a589b6-c0a8d700-5cecbd49-a141f098] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Intercepting getAuthenticators() method call for Feishu enhancement
2025-07-29 18:06:08,832 [ajp-nio-127.0.0.1-8889-exec-4 | cID:55a589b6-c0a8d700-5cecbd49-a141f098] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 开始解析服务: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer, 服务名: null
2025-07-29 18:06:08,833 [ajp-nio-127.0.0.1-8889-exec-4 | cID:55a589b6-c0a8d700-5cecbd49-a141f098] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 单例服务缓存中未找到: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer, 当前缓存大小: 0
2025-07-29 18:06:08,833 [ajp-nio-127.0.0.1-8889-exec-4 | cID:55a589b6-c0a8d700-5cecbd49-a141f098] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 未找到实现类: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer
2025-07-29 18:06:08,833 [ajp-nio-127.0.0.1-8889-exec-4 | cID:55a589b6-c0a8d700-5cecbd49-a141f098] WARN  com.fasnote.alm.injection.impl.DependencyInjector - 无法解析服务: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer
2025-07-29 18:06:08,833 [ajp-nio-127.0.0.1-8889-exec-4 | cID:55a589b6-c0a8d700-5cecbd49-a141f098] WARN  com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - DI framework returned unexpected type, falling back to default enhancer
2025-07-29 18:06:08,833 [ajp-nio-127.0.0.1-8889-exec-4 | cID:55a589b6-c0a8d700-5cecbd49-a141f098] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Using enhancer: Default OAuth2 Enhancer
2025-07-29 18:06:08,833 [ajp-nio-127.0.0.1-8889-exec-4 | cID:55a589b6-c0a8d700-5cecbd49-a141f098] INFO  com.fasnote.alm.auth.feishu.FeishuDefaultAuthenticatorEnhancer - Feishu license validation failed or not present, using default OAuth2 authenticators
2025-07-29 18:06:08,838 [ajp-nio-127.0.0.1-8889-exec-4 | cID:55a589b6-c0a8d700-5cecbd49-a141f098] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Feishu authenticators registered successfully in getAuthenticators() call
2025-07-29 18:06:08,839 [ajp-nio-127.0.0.1-8889-exec-4 | cID:55a589b6-c0a8d700-5cecbd49-a141f098] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Intercepting getAuthenticators() method call for Feishu enhancement
2025-07-29 18:06:08,839 [ajp-nio-127.0.0.1-8889-exec-4 | cID:55a589b6-c0a8d700-5cecbd49-a141f098] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 开始解析服务: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer, 服务名: null
2025-07-29 18:06:08,839 [ajp-nio-127.0.0.1-8889-exec-4 | cID:55a589b6-c0a8d700-5cecbd49-a141f098] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 单例服务缓存中未找到: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer, 当前缓存大小: 0
2025-07-29 18:06:08,839 [ajp-nio-127.0.0.1-8889-exec-4 | cID:55a589b6-c0a8d700-5cecbd49-a141f098] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 未找到实现类: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer
2025-07-29 18:06:08,839 [ajp-nio-127.0.0.1-8889-exec-4 | cID:55a589b6-c0a8d700-5cecbd49-a141f098] WARN  com.fasnote.alm.injection.impl.DependencyInjector - 无法解析服务: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer
2025-07-29 18:06:08,839 [ajp-nio-127.0.0.1-8889-exec-4 | cID:55a589b6-c0a8d700-5cecbd49-a141f098] WARN  com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - DI framework returned unexpected type, falling back to default enhancer
2025-07-29 18:06:08,839 [ajp-nio-127.0.0.1-8889-exec-4 | cID:55a589b6-c0a8d700-5cecbd49-a141f098] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Using enhancer: Default OAuth2 Enhancer
2025-07-29 18:06:08,839 [ajp-nio-127.0.0.1-8889-exec-4 | cID:55a589b6-c0a8d700-5cecbd49-a141f098] INFO  com.fasnote.alm.auth.feishu.FeishuDefaultAuthenticatorEnhancer - Feishu license validation failed or not present, using default OAuth2 authenticators
2025-07-29 18:06:08,839 [ajp-nio-127.0.0.1-8889-exec-4 | cID:55a589b6-c0a8d700-5cecbd49-a141f098] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Feishu authenticators registered successfully in getAuthenticators() call
2025-07-29 18:06:08,848 [ajp-nio-127.0.0.1-8889-exec-5 | cID:55a589cb-c0a8d700-5cecbd49-7bf1e9ef] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Intercepting getAuthenticators() method call for Feishu enhancement
2025-07-29 18:06:08,849 [ajp-nio-127.0.0.1-8889-exec-5 | cID:55a589cb-c0a8d700-5cecbd49-7bf1e9ef] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 开始解析服务: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer, 服务名: null
2025-07-29 18:06:08,849 [ajp-nio-127.0.0.1-8889-exec-5 | cID:55a589cb-c0a8d700-5cecbd49-7bf1e9ef] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 单例服务缓存中未找到: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer, 当前缓存大小: 0
2025-07-29 18:06:08,849 [ajp-nio-127.0.0.1-8889-exec-5 | cID:55a589cb-c0a8d700-5cecbd49-7bf1e9ef] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 未找到实现类: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer
2025-07-29 18:06:08,849 [ajp-nio-127.0.0.1-8889-exec-5 | cID:55a589cb-c0a8d700-5cecbd49-7bf1e9ef] WARN  com.fasnote.alm.injection.impl.DependencyInjector - 无法解析服务: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer
2025-07-29 18:06:08,849 [ajp-nio-127.0.0.1-8889-exec-5 | cID:55a589cb-c0a8d700-5cecbd49-7bf1e9ef] WARN  com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - DI framework returned unexpected type, falling back to default enhancer
2025-07-29 18:06:08,849 [ajp-nio-127.0.0.1-8889-exec-5 | cID:55a589cb-c0a8d700-5cecbd49-7bf1e9ef] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Using enhancer: Default OAuth2 Enhancer
2025-07-29 18:06:08,849 [ajp-nio-127.0.0.1-8889-exec-5 | cID:55a589cb-c0a8d700-5cecbd49-7bf1e9ef] INFO  com.fasnote.alm.auth.feishu.FeishuDefaultAuthenticatorEnhancer - Feishu license validation failed or not present, using default OAuth2 authenticators
2025-07-29 18:06:08,849 [ajp-nio-127.0.0.1-8889-exec-5 | cID:55a589cb-c0a8d700-5cecbd49-7bf1e9ef] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Feishu authenticators registered successfully in getAuthenticators() call
2025-07-29 18:06:08,850 [ajp-nio-127.0.0.1-8889-exec-5 | cID:55a589cb-c0a8d700-5cecbd49-7bf1e9ef] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Intercepting getAuthenticators() method call for Feishu enhancement
2025-07-29 18:06:08,850 [ajp-nio-127.0.0.1-8889-exec-5 | cID:55a589cb-c0a8d700-5cecbd49-7bf1e9ef] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 开始解析服务: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer, 服务名: null
2025-07-29 18:06:08,850 [ajp-nio-127.0.0.1-8889-exec-5 | cID:55a589cb-c0a8d700-5cecbd49-7bf1e9ef] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 单例服务缓存中未找到: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer, 当前缓存大小: 0
2025-07-29 18:06:08,850 [ajp-nio-127.0.0.1-8889-exec-5 | cID:55a589cb-c0a8d700-5cecbd49-7bf1e9ef] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 未找到实现类: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer
2025-07-29 18:06:08,850 [ajp-nio-127.0.0.1-8889-exec-5 | cID:55a589cb-c0a8d700-5cecbd49-7bf1e9ef] WARN  com.fasnote.alm.injection.impl.DependencyInjector - 无法解析服务: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer
2025-07-29 18:06:08,850 [ajp-nio-127.0.0.1-8889-exec-5 | cID:55a589cb-c0a8d700-5cecbd49-7bf1e9ef] WARN  com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - DI framework returned unexpected type, falling back to default enhancer
2025-07-29 18:06:08,850 [ajp-nio-127.0.0.1-8889-exec-5 | cID:55a589cb-c0a8d700-5cecbd49-7bf1e9ef] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Using enhancer: Default OAuth2 Enhancer
2025-07-29 18:06:08,850 [ajp-nio-127.0.0.1-8889-exec-5 | cID:55a589cb-c0a8d700-5cecbd49-7bf1e9ef] INFO  com.fasnote.alm.auth.feishu.FeishuDefaultAuthenticatorEnhancer - Feishu license validation failed or not present, using default OAuth2 authenticators
2025-07-29 18:06:08,850 [ajp-nio-127.0.0.1-8889-exec-5 | cID:55a589cb-c0a8d700-5cecbd49-7bf1e9ef] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Feishu authenticators registered successfully in getAuthenticators() call
2025-07-29 18:06:08,991 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (tshirt-sizes) created
2025-07-29 18:06:09,012 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (reqtype) created
2025-07-29 18:06:09,039 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661569623604e_0_661569623604e_0_: finished. Total: 0.439 s, CPU [user: 0.0927 s, system: 0.0136 s], Allocated memory: 18.9 MB, svn: 0.386 s [49% getDir2 content (14x), 32% info (37x)] (81x), RepositoryConfigService: 0.169 s [66% getReadConfiguration (124x), 34% getExistingPrefixes (12x)] (148x)
2025-07-29 18:06:09,108 [ajp-nio-127.0.0.1-8889-exec-6 | cID:55a58ad3-c0a8d700-5cecbd49-365eba7a] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Intercepting getAuthenticators() method call for Feishu enhancement
2025-07-29 18:06:09,108 [ajp-nio-127.0.0.1-8889-exec-6 | cID:55a58ad3-c0a8d700-5cecbd49-365eba7a] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 开始解析服务: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer, 服务名: null
2025-07-29 18:06:09,108 [ajp-nio-127.0.0.1-8889-exec-6 | cID:55a58ad3-c0a8d700-5cecbd49-365eba7a] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 单例服务缓存中未找到: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer, 当前缓存大小: 0
2025-07-29 18:06:09,108 [ajp-nio-127.0.0.1-8889-exec-6 | cID:55a58ad3-c0a8d700-5cecbd49-365eba7a] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 未找到实现类: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer
2025-07-29 18:06:09,108 [ajp-nio-127.0.0.1-8889-exec-6 | cID:55a58ad3-c0a8d700-5cecbd49-365eba7a] WARN  com.fasnote.alm.injection.impl.DependencyInjector - 无法解析服务: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer
2025-07-29 18:06:09,108 [ajp-nio-127.0.0.1-8889-exec-6 | cID:55a58ad3-c0a8d700-5cecbd49-365eba7a] WARN  com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - DI framework returned unexpected type, falling back to default enhancer
2025-07-29 18:06:09,108 [ajp-nio-127.0.0.1-8889-exec-6 | cID:55a58ad3-c0a8d700-5cecbd49-365eba7a] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Using enhancer: Default OAuth2 Enhancer
2025-07-29 18:06:09,108 [ajp-nio-127.0.0.1-8889-exec-6 | cID:55a58ad3-c0a8d700-5cecbd49-365eba7a] INFO  com.fasnote.alm.auth.feishu.FeishuDefaultAuthenticatorEnhancer - Feishu license validation failed or not present, using default OAuth2 authenticators
2025-07-29 18:06:09,108 [ajp-nio-127.0.0.1-8889-exec-6 | cID:55a58ad3-c0a8d700-5cecbd49-365eba7a] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Feishu authenticators registered successfully in getAuthenticators() call
2025-07-29 18:06:09,109 [ajp-nio-127.0.0.1-8889-exec-6 | cID:55a58ad3-c0a8d700-5cecbd49-365eba7a] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Intercepting getAuthenticators() method call for Feishu enhancement
2025-07-29 18:06:09,109 [ajp-nio-127.0.0.1-8889-exec-6 | cID:55a58ad3-c0a8d700-5cecbd49-365eba7a] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 开始解析服务: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer, 服务名: null
2025-07-29 18:06:09,109 [ajp-nio-127.0.0.1-8889-exec-6 | cID:55a58ad3-c0a8d700-5cecbd49-365eba7a] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 单例服务缓存中未找到: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer, 当前缓存大小: 0
2025-07-29 18:06:09,109 [ajp-nio-127.0.0.1-8889-exec-6 | cID:55a58ad3-c0a8d700-5cecbd49-365eba7a] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 未找到实现类: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer
2025-07-29 18:06:09,109 [ajp-nio-127.0.0.1-8889-exec-6 | cID:55a58ad3-c0a8d700-5cecbd49-365eba7a] WARN  com.fasnote.alm.injection.impl.DependencyInjector - 无法解析服务: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer
2025-07-29 18:06:09,109 [ajp-nio-127.0.0.1-8889-exec-6 | cID:55a58ad3-c0a8d700-5cecbd49-365eba7a] WARN  com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - DI framework returned unexpected type, falling back to default enhancer
2025-07-29 18:06:09,109 [ajp-nio-127.0.0.1-8889-exec-6 | cID:55a58ad3-c0a8d700-5cecbd49-365eba7a] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Using enhancer: Default OAuth2 Enhancer
2025-07-29 18:06:09,109 [ajp-nio-127.0.0.1-8889-exec-6 | cID:55a58ad3-c0a8d700-5cecbd49-365eba7a] INFO  com.fasnote.alm.auth.feishu.FeishuDefaultAuthenticatorEnhancer - Feishu license validation failed or not present, using default OAuth2 authenticators
2025-07-29 18:06:09,109 [ajp-nio-127.0.0.1-8889-exec-6 | cID:55a58ad3-c0a8d700-5cecbd49-365eba7a] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Feishu authenticators registered successfully in getAuthenticators() call
2025-07-29 18:06:09,294 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66156962a404f_0_66156962a404f_0_: finished. Total: 0.254 s, CPU [user: 0.0566 s, system: 0.00812 s], Allocated memory: 6.9 MB, svn: 0.231 s [48% info (35x), 42% getDir2 content (8x)] (52x), RepositoryConfigService: 0.0791 s [54% getReadConfiguration (38x), 46% getExistingPrefixes (9x)] (54x)
2025-07-29 18:06:09,636 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Feasibility) created
2025-07-29 18:06:09,641 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (locaMod) created
2025-07-29 18:06:09,647 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (unitTestCaseType) created
2025-07-29 18:06:09,653 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (ControlLevel) created
2025-07-29 18:06:09,655 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (NCitemSev) created
2025-07-29 18:06:09,660 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (signType) created
2025-07-29 18:06:09,677 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (WBSCategory) created
2025-07-29 18:06:09,684 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (swTestCaseEnv) created
2025-07-29 18:06:09,690 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (verifiability) created
2025-07-29 18:06:09,695 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (@ProjectUser) created
2025-07-29 18:06:09,706 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (standardReq) created
2025-07-29 18:06:09,709 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (statusa) created
2025-07-29 18:06:09,713 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (@WorkItems[type:configurationitemversion]) created
2025-07-29 18:06:09,715 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (CIRevisionStatus) created
2025-07-29 18:06:09,721 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (dogTimeout) created
2025-07-29 18:06:09,727 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (REQCategory) created
2025-07-29 18:06:09,734 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (proStage) created
2025-07-29 18:06:09,737 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (BaselineType) created
2025-07-29 18:06:09,740 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (custConfStat) created
2025-07-29 18:06:09,744 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (sofReqVer) created
2025-07-29 18:06:09,753 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Source) created
2025-07-29 18:06:09,757 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (scCategory) created
2025-07-29 18:06:09,762 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (softTestCaseType) created
2025-07-29 18:06:09,765 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (solAdv) created
2025-07-29 18:06:09,770 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (unitTestCaseMet) created
2025-07-29 18:06:09,773 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (sysTestCaseMeth) created
2025-07-29 18:06:09,779 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (softTestCaseMe) created
2025-07-29 18:06:09,785 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (softTestCaseEnv) created
2025-07-29 18:06:09,792 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (auditTarget) created
2025-07-29 18:06:09,796 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (@ReviewForm) created
2025-07-29 18:06:09,802 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (swTestCaseType) created
2025-07-29 18:06:09,806 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (@Collection) created
2025-07-29 18:06:09,812 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (submissionStage) created
2025-07-29 18:06:09,816 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (sysTestCaseMet) created
2025-07-29 18:06:09,820 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (demandType) created
2025-07-29 18:06:09,823 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (swTestCaseMet) created
2025-07-29 18:06:09,826 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (taskUrgen) created
2025-07-29 18:06:09,830 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (solveMethod) created
2025-07-29 18:06:09,838 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (audMethod) created
2025-07-29 18:06:09,845 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (desStat) created
2025-07-29 18:06:09,859 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (scType) created
2025-07-29 18:06:09,870 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (sysTestCaseType) created
2025-07-29 18:06:09,878 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (releaseType) created
2025-07-29 18:06:09,892 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (unitTestCaseEnv) created
2025-07-29 18:06:09,903 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (targetStage) created
2025-07-29 18:06:09,909 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (ClassificationType) created
2025-07-29 18:06:09,916 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testItem) created
2025-07-29 18:06:09,923 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (InfoSecurity) created
2025-07-29 18:06:09,929 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Verification) created
2025-07-29 18:06:09,937 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (triggerMod) created
2025-07-29 18:06:09,942 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (verMethod) created
2025-07-29 18:06:09,950 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (diagramCategory) created
2025-07-29 18:06:09,966 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (assSubsystem) created
2025-07-29 18:06:09,971 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (OccurrenceProbability) created
2025-07-29 18:06:09,975 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (developmentMethod) created
2025-07-29 18:06:09,978 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (portType) created
2025-07-29 18:06:09,985 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (checkType) created
2025-07-29 18:06:09,991 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (demandStatus) created
2025-07-29 18:06:09,995 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (important) created
2025-07-29 18:06:10,012 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (triggerMec) created
2025-07-29 18:06:10,019 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (sysTestCaseTy) created
2025-07-29 18:06:10,022 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (recentPre) created
2025-07-29 18:06:10,026 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (TestCaseDesignMethod) created
2025-07-29 18:06:10,028 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testCasePri) created
2025-07-29 18:06:10,037 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (relObj) created
2025-07-29 18:06:10,041 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (proSer) created
2025-07-29 18:06:10,045 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (TestProblemType) created
2025-07-29 18:06:10,051 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (chipName) created
2025-07-29 18:06:10,054 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (auditTiming) created
2025-07-29 18:06:10,058 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66156962e3850_0_66156962e3850_0_: finished. Total: 0.763 s, CPU [user: 0.255 s, system: 0.0244 s], Allocated memory: 391.1 MB, svn: 0.563 s [45% info (152x), 30% getFile content (185x), 24% getDir2 content (16x)] (354x), RepositoryConfigService: 0.554 s [57% getReadConfiguration (2787x), 43% getExistingPrefixes (89x)] (3025x)
2025-07-29 18:06:10,058 [PreLoadDataService] INFO  com.polarion.psvn.launcher.internal.data.PreLoadDataService - Preloading data FINISHED took  [ TIME 5.55 s. ]
2025-07-29 18:06:10,058 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 5.55 s, CPU [user: 1.8 s, system: 0.351 s], Allocated memory: 1.6 GB, transactions: 10, svn: 3.25 s [40% getFile content (809x), 28% getDir2 content (103x), 16% info (249x)] (1357x), RepositoryConfigService: 2.89 s [86% getReadConfiguration (12019x)] (12691x), resolve: 0.598 s [84% Category (96x)] (117x)
2025-07-29 18:06:10,058 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 56, svn: 4.04 s [32% getFile content (810x), 31% getDatedRevision (362x), 23% getDir2 content (103x)] (1544x), RepositoryConfigService: 2.9 s [86% getReadConfiguration (12020x)] (12692x), resolve: 0.601 s [83% Category (96x)] (119x), PolarionAuthenticator: 0.514 s [100% authenticate (5x)] (5x), ObjectMaps: 0.221 s [47% getPrimaryObjectProperty (110x), 32% getPrimaryObjectLocation (116x), 20% getLastPromoted (110x)] (452x)
2025-07-29 18:06:10,090 [ajp-nio-127.0.0.1-8889-exec-8 | cID:55a58ea9-c0a8d700-5cecbd49-d436000d] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Intercepting getAuthenticators() method call for Feishu enhancement
2025-07-29 18:06:10,090 [ajp-nio-127.0.0.1-8889-exec-8 | cID:55a58ea9-c0a8d700-5cecbd49-d436000d] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 开始解析服务: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer, 服务名: null
2025-07-29 18:06:10,090 [ajp-nio-127.0.0.1-8889-exec-8 | cID:55a58ea9-c0a8d700-5cecbd49-d436000d] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 单例服务缓存中未找到: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer, 当前缓存大小: 0
2025-07-29 18:06:10,090 [ajp-nio-127.0.0.1-8889-exec-8 | cID:55a58ea9-c0a8d700-5cecbd49-d436000d] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 未找到实现类: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer
2025-07-29 18:06:10,090 [ajp-nio-127.0.0.1-8889-exec-8 | cID:55a58ea9-c0a8d700-5cecbd49-d436000d] WARN  com.fasnote.alm.injection.impl.DependencyInjector - 无法解析服务: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer
2025-07-29 18:06:10,090 [ajp-nio-127.0.0.1-8889-exec-8 | cID:55a58ea9-c0a8d700-5cecbd49-d436000d] WARN  com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - DI framework returned unexpected type, falling back to default enhancer
2025-07-29 18:06:10,090 [ajp-nio-127.0.0.1-8889-exec-8 | cID:55a58ea9-c0a8d700-5cecbd49-d436000d] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Using enhancer: Default OAuth2 Enhancer
2025-07-29 18:06:10,090 [ajp-nio-127.0.0.1-8889-exec-8 | cID:55a58ea9-c0a8d700-5cecbd49-d436000d] INFO  com.fasnote.alm.auth.feishu.FeishuDefaultAuthenticatorEnhancer - Feishu license validation failed or not present, using default OAuth2 authenticators
2025-07-29 18:06:10,090 [ajp-nio-127.0.0.1-8889-exec-8 | cID:55a58ea9-c0a8d700-5cecbd49-d436000d] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Feishu authenticators registered successfully in getAuthenticators() call
2025-07-29 18:06:10,091 [ajp-nio-127.0.0.1-8889-exec-8 | cID:55a58ea9-c0a8d700-5cecbd49-d436000d] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Intercepting getAuthenticators() method call for Feishu enhancement
2025-07-29 18:06:10,091 [ajp-nio-127.0.0.1-8889-exec-8 | cID:55a58ea9-c0a8d700-5cecbd49-d436000d] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 开始解析服务: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer, 服务名: null
2025-07-29 18:06:10,091 [ajp-nio-127.0.0.1-8889-exec-8 | cID:55a58ea9-c0a8d700-5cecbd49-d436000d] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 单例服务缓存中未找到: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer, 当前缓存大小: 0
2025-07-29 18:06:10,091 [ajp-nio-127.0.0.1-8889-exec-8 | cID:55a58ea9-c0a8d700-5cecbd49-d436000d] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 未找到实现类: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer
2025-07-29 18:06:10,091 [ajp-nio-127.0.0.1-8889-exec-8 | cID:55a58ea9-c0a8d700-5cecbd49-d436000d] WARN  com.fasnote.alm.injection.impl.DependencyInjector - 无法解析服务: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer
2025-07-29 18:06:10,091 [ajp-nio-127.0.0.1-8889-exec-8 | cID:55a58ea9-c0a8d700-5cecbd49-d436000d] WARN  com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - DI framework returned unexpected type, falling back to default enhancer
2025-07-29 18:06:10,091 [ajp-nio-127.0.0.1-8889-exec-8 | cID:55a58ea9-c0a8d700-5cecbd49-d436000d] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Using enhancer: Default OAuth2 Enhancer
2025-07-29 18:06:10,091 [ajp-nio-127.0.0.1-8889-exec-8 | cID:55a58ea9-c0a8d700-5cecbd49-d436000d] INFO  com.fasnote.alm.auth.feishu.FeishuDefaultAuthenticatorEnhancer - Feishu license validation failed or not present, using default OAuth2 authenticators
2025-07-29 18:06:10,091 [ajp-nio-127.0.0.1-8889-exec-8 | cID:55a58ea9-c0a8d700-5cecbd49-d436000d] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Feishu authenticators registered successfully in getAuthenticators() call
2025-07-29 18:06:10,137 [ajp-nio-127.0.0.1-8889-exec-9 | cID:55a58ed8-c0a8d700-5cecbd49-7312691f] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Intercepting getAuthenticators() method call for Feishu enhancement
2025-07-29 18:06:10,137 [ajp-nio-127.0.0.1-8889-exec-9 | cID:55a58ed8-c0a8d700-5cecbd49-7312691f] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 开始解析服务: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer, 服务名: null
2025-07-29 18:06:10,137 [ajp-nio-127.0.0.1-8889-exec-9 | cID:55a58ed8-c0a8d700-5cecbd49-7312691f] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 单例服务缓存中未找到: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer, 当前缓存大小: 0
2025-07-29 18:06:10,137 [ajp-nio-127.0.0.1-8889-exec-9 | cID:55a58ed8-c0a8d700-5cecbd49-7312691f] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 未找到实现类: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer
2025-07-29 18:06:10,137 [ajp-nio-127.0.0.1-8889-exec-9 | cID:55a58ed8-c0a8d700-5cecbd49-7312691f] WARN  com.fasnote.alm.injection.impl.DependencyInjector - 无法解析服务: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer
2025-07-29 18:06:10,137 [ajp-nio-127.0.0.1-8889-exec-9 | cID:55a58ed8-c0a8d700-5cecbd49-7312691f] WARN  com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - DI framework returned unexpected type, falling back to default enhancer
2025-07-29 18:06:10,137 [ajp-nio-127.0.0.1-8889-exec-9 | cID:55a58ed8-c0a8d700-5cecbd49-7312691f] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Using enhancer: Default OAuth2 Enhancer
2025-07-29 18:06:10,137 [ajp-nio-127.0.0.1-8889-exec-9 | cID:55a58ed8-c0a8d700-5cecbd49-7312691f] INFO  com.fasnote.alm.auth.feishu.FeishuDefaultAuthenticatorEnhancer - Feishu license validation failed or not present, using default OAuth2 authenticators
2025-07-29 18:06:10,137 [ajp-nio-127.0.0.1-8889-exec-9 | cID:55a58ed8-c0a8d700-5cecbd49-7312691f] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Feishu authenticators registered successfully in getAuthenticators() call
2025-07-29 18:06:10,137 [ajp-nio-127.0.0.1-8889-exec-9 | cID:55a58ed8-c0a8d700-5cecbd49-7312691f] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Intercepting getAuthenticators() method call for Feishu enhancement
2025-07-29 18:06:10,137 [ajp-nio-127.0.0.1-8889-exec-9 | cID:55a58ed8-c0a8d700-5cecbd49-7312691f] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 开始解析服务: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer, 服务名: null
2025-07-29 18:06:10,137 [ajp-nio-127.0.0.1-8889-exec-9 | cID:55a58ed8-c0a8d700-5cecbd49-7312691f] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 单例服务缓存中未找到: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer, 当前缓存大小: 0
2025-07-29 18:06:10,137 [ajp-nio-127.0.0.1-8889-exec-9 | cID:55a58ed8-c0a8d700-5cecbd49-7312691f] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 未找到实现类: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer
2025-07-29 18:06:10,137 [ajp-nio-127.0.0.1-8889-exec-9 | cID:55a58ed8-c0a8d700-5cecbd49-7312691f] WARN  com.fasnote.alm.injection.impl.DependencyInjector - 无法解析服务: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer
2025-07-29 18:06:10,137 [ajp-nio-127.0.0.1-8889-exec-9 | cID:55a58ed8-c0a8d700-5cecbd49-7312691f] WARN  com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - DI framework returned unexpected type, falling back to default enhancer
2025-07-29 18:06:10,137 [ajp-nio-127.0.0.1-8889-exec-9 | cID:55a58ed8-c0a8d700-5cecbd49-7312691f] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Using enhancer: Default OAuth2 Enhancer
2025-07-29 18:06:10,137 [ajp-nio-127.0.0.1-8889-exec-9 | cID:55a58ed8-c0a8d700-5cecbd49-7312691f] INFO  com.fasnote.alm.auth.feishu.FeishuDefaultAuthenticatorEnhancer - Feishu license validation failed or not present, using default OAuth2 authenticators
2025-07-29 18:06:10,137 [ajp-nio-127.0.0.1-8889-exec-9 | cID:55a58ed8-c0a8d700-5cecbd49-7312691f] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Feishu authenticators registered successfully in getAuthenticators() call
2025-07-29 18:06:10,290 [ajp-nio-127.0.0.1-8889-exec-9 | cID:55a58ed8-c0a8d700-5cecbd49-7312691f | u:admin] INFO  com.polarion.alm.tracker.internal.XProductsService - Current license type: ALM. License limited prototypes: false. 
2025-07-29 18:06:10,326 [ajp-nio-127.0.0.1-8889-exec-9 | cID:55a58ed8-c0a8d700-5cecbd49-7312691f | u:admin] INFO  com.polarion.alm.ui.server.diagrams.internal.DiagramEditorsRegistry - Registered diagram editor mxg (com.polarion.alm.ui.diagrams.mxgraph.internal.MxGraphDiagramEditor)
2025-07-29 18:06:10,425 [ajp-nio-127.0.0.1-8889-exec-9 | cID:55a58ed8-c0a8d700-5cecbd49-7312691f] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/PortalDataService': Total: 0.289 s, CPU [user: 0.189 s, system: 0.0251 s], Allocated memory: 68.3 MB, transactions: 1, RPC: 0.153 s [40% decodeRequest (1x), 40% encodeResponse (1x)] (4x), PortalDataService: 0.0981 s [100% getInitData (1x)] (1x), svn: 0.0244 s [29% log (1x), 24% getDir2 content (1x), 17% testConnection (1x), 14% info (2x)] (8x), resolve: 0.0206 s [99% Project (4x)] (5x), RepositoryConfigService: 0.0179 s [100% getReadConfiguration (10x)] (15x)
2025-07-29 18:06:10,474 [ajp-nio-127.0.0.1-8889-exec-10 | cID:55a59029-c0a8d700-5cecbd49-99786f5c] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Intercepting getAuthenticators() method call for Feishu enhancement
2025-07-29 18:06:10,474 [ajp-nio-127.0.0.1-8889-exec-10 | cID:55a59029-c0a8d700-5cecbd49-99786f5c] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 开始解析服务: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer, 服务名: null
2025-07-29 18:06:10,474 [ajp-nio-127.0.0.1-8889-exec-10 | cID:55a59029-c0a8d700-5cecbd49-99786f5c] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 单例服务缓存中未找到: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer, 当前缓存大小: 0
2025-07-29 18:06:10,474 [ajp-nio-127.0.0.1-8889-exec-10 | cID:55a59029-c0a8d700-5cecbd49-99786f5c] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 未找到实现类: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer
2025-07-29 18:06:10,474 [ajp-nio-127.0.0.1-8889-exec-10 | cID:55a59029-c0a8d700-5cecbd49-99786f5c] WARN  com.fasnote.alm.injection.impl.DependencyInjector - 无法解析服务: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer
2025-07-29 18:06:10,474 [ajp-nio-127.0.0.1-8889-exec-10 | cID:55a59029-c0a8d700-5cecbd49-99786f5c] WARN  com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - DI framework returned unexpected type, falling back to default enhancer
2025-07-29 18:06:10,474 [ajp-nio-127.0.0.1-8889-exec-10 | cID:55a59029-c0a8d700-5cecbd49-99786f5c] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Using enhancer: Default OAuth2 Enhancer
2025-07-29 18:06:10,474 [ajp-nio-127.0.0.1-8889-exec-10 | cID:55a59029-c0a8d700-5cecbd49-99786f5c] INFO  com.fasnote.alm.auth.feishu.FeishuDefaultAuthenticatorEnhancer - Feishu license validation failed or not present, using default OAuth2 authenticators
2025-07-29 18:06:10,474 [ajp-nio-127.0.0.1-8889-exec-10 | cID:55a59029-c0a8d700-5cecbd49-99786f5c] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Feishu authenticators registered successfully in getAuthenticators() call
2025-07-29 18:06:10,474 [ajp-nio-127.0.0.1-8889-exec-10 | cID:55a59029-c0a8d700-5cecbd49-99786f5c] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Intercepting getAuthenticators() method call for Feishu enhancement
2025-07-29 18:06:10,474 [ajp-nio-127.0.0.1-8889-exec-10 | cID:55a59029-c0a8d700-5cecbd49-99786f5c] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 开始解析服务: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer, 服务名: null
2025-07-29 18:06:10,474 [ajp-nio-127.0.0.1-8889-exec-10 | cID:55a59029-c0a8d700-5cecbd49-99786f5c] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 单例服务缓存中未找到: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer, 当前缓存大小: 0
2025-07-29 18:06:10,474 [ajp-nio-127.0.0.1-8889-exec-10 | cID:55a59029-c0a8d700-5cecbd49-99786f5c] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 未找到实现类: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer
2025-07-29 18:06:10,474 [ajp-nio-127.0.0.1-8889-exec-10 | cID:55a59029-c0a8d700-5cecbd49-99786f5c] WARN  com.fasnote.alm.injection.impl.DependencyInjector - 无法解析服务: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer
2025-07-29 18:06:10,474 [ajp-nio-127.0.0.1-8889-exec-10 | cID:55a59029-c0a8d700-5cecbd49-99786f5c] WARN  com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - DI framework returned unexpected type, falling back to default enhancer
2025-07-29 18:06:10,474 [ajp-nio-127.0.0.1-8889-exec-10 | cID:55a59029-c0a8d700-5cecbd49-99786f5c] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Using enhancer: Default OAuth2 Enhancer
2025-07-29 18:06:10,474 [ajp-nio-127.0.0.1-8889-exec-10 | cID:55a59029-c0a8d700-5cecbd49-99786f5c] INFO  com.fasnote.alm.auth.feishu.FeishuDefaultAuthenticatorEnhancer - Feishu license validation failed or not present, using default OAuth2 authenticators
2025-07-29 18:06:10,474 [ajp-nio-127.0.0.1-8889-exec-10 | cID:55a59029-c0a8d700-5cecbd49-99786f5c] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Feishu authenticators registered successfully in getAuthenticators() call
2025-07-29 18:06:10,630 [Framework Event Dispatcher: Equinox Container: f5015c1e-7286-43a2-ba9f-9fe4c1011674] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - Bundle启动/解析: com.polarion.alm.extension.vcontext
2025-07-29 18:06:10,630 [Framework Event Dispatcher: Equinox Container: f5015c1e-7286-43a2-ba9f-9fe4c1011674] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.polarion.alm.extension.vcontext
2025-07-29 18:06:10,630 [Framework Event Dispatcher: Equinox Container: f5015c1e-7286-43a2-ba9f-9fe4c1011674] DEBUG com.fasnote.alm.plugin.manage.osgi.OSGiLicenseFrameworkInitializer - 动态注册Bundle到DI框架: com.polarion.alm.extension.vcontext
2025-07-29 18:06:10,675 [ajp-nio-127.0.0.1-8889-exec-10 | cID:55a59029-c0a8d700-5cecbd49-99786f5c | u:admin] INFO  TXLOGGER - Tx 661569640cc52_0_661569640cc52_0_: finished. Total: 0.191 s, CPU [user: 0.107 s, system: 0.0192 s], Allocated memory: 16.2 MB, RepositoryConfigService: 0.066 s [92% getReadConfiguration (13x)] (18x), svn: 0.0457 s [42% info (8x), 27% getFile content (7x), 22% getDir2 content (1x)] (18x)
2025-07-29 18:06:10,702 [ajp-nio-127.0.0.1-8889-exec-10 | cID:55a59029-c0a8d700-5cecbd49-99786f5c] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/PortalDataService': Total: 0.228 s, CPU [user: 0.132 s, system: 0.0243 s], Allocated memory: 19.4 MB, transactions: 1, PortalDataService: 0.193 s [100% requestPortalSite (1x)] (1x), RepositoryConfigService: 0.066 s [92% getReadConfiguration (13x)] (18x), svn: 0.0457 s [42% info (8x), 27% getFile content (7x), 22% getDir2 content (1x)] (18x), RPC: 0.0335 s [77% encodeResponse (1x), 20% decodeRequest (1x)] (4x)
2025-07-29 18:06:10,740 [ajp-nio-127.0.0.1-8889-exec-1 | cID:55a59134-c0a8d700-5cecbd49-d47d2122] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Intercepting getAuthenticators() method call for Feishu enhancement
2025-07-29 18:06:10,740 [ajp-nio-127.0.0.1-8889-exec-1 | cID:55a59134-c0a8d700-5cecbd49-d47d2122] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 开始解析服务: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer, 服务名: null
2025-07-29 18:06:10,740 [ajp-nio-127.0.0.1-8889-exec-1 | cID:55a59134-c0a8d700-5cecbd49-d47d2122] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 单例服务缓存中未找到: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer, 当前缓存大小: 0
2025-07-29 18:06:10,740 [ajp-nio-127.0.0.1-8889-exec-1 | cID:55a59134-c0a8d700-5cecbd49-d47d2122] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 未找到实现类: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer
2025-07-29 18:06:10,740 [ajp-nio-127.0.0.1-8889-exec-1 | cID:55a59134-c0a8d700-5cecbd49-d47d2122] WARN  com.fasnote.alm.injection.impl.DependencyInjector - 无法解析服务: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer
2025-07-29 18:06:10,740 [ajp-nio-127.0.0.1-8889-exec-1 | cID:55a59134-c0a8d700-5cecbd49-d47d2122] WARN  com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - DI framework returned unexpected type, falling back to default enhancer
2025-07-29 18:06:10,740 [ajp-nio-127.0.0.1-8889-exec-1 | cID:55a59134-c0a8d700-5cecbd49-d47d2122] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Using enhancer: Default OAuth2 Enhancer
2025-07-29 18:06:10,740 [ajp-nio-127.0.0.1-8889-exec-1 | cID:55a59134-c0a8d700-5cecbd49-d47d2122] INFO  com.fasnote.alm.auth.feishu.FeishuDefaultAuthenticatorEnhancer - Feishu license validation failed or not present, using default OAuth2 authenticators
2025-07-29 18:06:10,740 [ajp-nio-127.0.0.1-8889-exec-1 | cID:55a59134-c0a8d700-5cecbd49-d47d2122] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Feishu authenticators registered successfully in getAuthenticators() call
2025-07-29 18:06:10,741 [ajp-nio-127.0.0.1-8889-exec-1 | cID:55a59134-c0a8d700-5cecbd49-d47d2122] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Intercepting getAuthenticators() method call for Feishu enhancement
2025-07-29 18:06:10,741 [ajp-nio-127.0.0.1-8889-exec-1 | cID:55a59134-c0a8d700-5cecbd49-d47d2122] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 开始解析服务: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer, 服务名: null
2025-07-29 18:06:10,741 [ajp-nio-127.0.0.1-8889-exec-1 | cID:55a59134-c0a8d700-5cecbd49-d47d2122] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 单例服务缓存中未找到: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer, 当前缓存大小: 0
2025-07-29 18:06:10,741 [ajp-nio-127.0.0.1-8889-exec-1 | cID:55a59134-c0a8d700-5cecbd49-d47d2122] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 未找到实现类: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer
2025-07-29 18:06:10,741 [ajp-nio-127.0.0.1-8889-exec-1 | cID:55a59134-c0a8d700-5cecbd49-d47d2122] WARN  com.fasnote.alm.injection.impl.DependencyInjector - 无法解析服务: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer
2025-07-29 18:06:10,741 [ajp-nio-127.0.0.1-8889-exec-1 | cID:55a59134-c0a8d700-5cecbd49-d47d2122] WARN  com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - DI framework returned unexpected type, falling back to default enhancer
2025-07-29 18:06:10,741 [ajp-nio-127.0.0.1-8889-exec-1 | cID:55a59134-c0a8d700-5cecbd49-d47d2122] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Using enhancer: Default OAuth2 Enhancer
2025-07-29 18:06:10,741 [ajp-nio-127.0.0.1-8889-exec-1 | cID:55a59134-c0a8d700-5cecbd49-d47d2122] INFO  com.fasnote.alm.auth.feishu.FeishuDefaultAuthenticatorEnhancer - Feishu license validation failed or not present, using default OAuth2 authenticators
2025-07-29 18:06:10,741 [ajp-nio-127.0.0.1-8889-exec-1 | cID:55a59134-c0a8d700-5cecbd49-d47d2122] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Feishu authenticators registered successfully in getAuthenticators() call
2025-07-29 18:06:10,804 [ajp-nio-127.0.0.1-8889-exec-4 | cID:55a5916d-c0a8d700-5cecbd49-3cea53eb] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Intercepting getAuthenticators() method call for Feishu enhancement
2025-07-29 18:06:10,804 [ajp-nio-127.0.0.1-8889-exec-4 | cID:55a5916d-c0a8d700-5cecbd49-3cea53eb] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 开始解析服务: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer, 服务名: null
2025-07-29 18:06:10,804 [ajp-nio-127.0.0.1-8889-exec-4 | cID:55a5916d-c0a8d700-5cecbd49-3cea53eb] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 单例服务缓存中未找到: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer, 当前缓存大小: 0
2025-07-29 18:06:10,804 [ajp-nio-127.0.0.1-8889-exec-4 | cID:55a5916d-c0a8d700-5cecbd49-3cea53eb] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 未找到实现类: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer
2025-07-29 18:06:10,804 [ajp-nio-127.0.0.1-8889-exec-4 | cID:55a5916d-c0a8d700-5cecbd49-3cea53eb] WARN  com.fasnote.alm.injection.impl.DependencyInjector - 无法解析服务: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer
2025-07-29 18:06:10,804 [ajp-nio-127.0.0.1-8889-exec-4 | cID:55a5916d-c0a8d700-5cecbd49-3cea53eb] WARN  com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - DI framework returned unexpected type, falling back to default enhancer
2025-07-29 18:06:10,804 [ajp-nio-127.0.0.1-8889-exec-4 | cID:55a5916d-c0a8d700-5cecbd49-3cea53eb] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Using enhancer: Default OAuth2 Enhancer
2025-07-29 18:06:10,804 [ajp-nio-127.0.0.1-8889-exec-4 | cID:55a5916d-c0a8d700-5cecbd49-3cea53eb] INFO  com.fasnote.alm.auth.feishu.FeishuDefaultAuthenticatorEnhancer - Feishu license validation failed or not present, using default OAuth2 authenticators
2025-07-29 18:06:10,804 [ajp-nio-127.0.0.1-8889-exec-4 | cID:55a5916d-c0a8d700-5cecbd49-3cea53eb] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Feishu authenticators registered successfully in getAuthenticators() call
2025-07-29 18:06:10,805 [ajp-nio-127.0.0.1-8889-exec-4 | cID:55a5916d-c0a8d700-5cecbd49-3cea53eb] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Intercepting getAuthenticators() method call for Feishu enhancement
2025-07-29 18:06:10,805 [ajp-nio-127.0.0.1-8889-exec-4 | cID:55a5916d-c0a8d700-5cecbd49-3cea53eb] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 开始解析服务: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer, 服务名: null
2025-07-29 18:06:10,805 [ajp-nio-127.0.0.1-8889-exec-4 | cID:55a5916d-c0a8d700-5cecbd49-3cea53eb] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 单例服务缓存中未找到: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer, 当前缓存大小: 0
2025-07-29 18:06:10,805 [ajp-nio-127.0.0.1-8889-exec-4 | cID:55a5916d-c0a8d700-5cecbd49-3cea53eb] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 未找到实现类: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer
2025-07-29 18:06:10,805 [ajp-nio-127.0.0.1-8889-exec-4 | cID:55a5916d-c0a8d700-5cecbd49-3cea53eb] WARN  com.fasnote.alm.injection.impl.DependencyInjector - 无法解析服务: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer
2025-07-29 18:06:10,805 [ajp-nio-127.0.0.1-8889-exec-4 | cID:55a5916d-c0a8d700-5cecbd49-3cea53eb] WARN  com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - DI framework returned unexpected type, falling back to default enhancer
2025-07-29 18:06:10,805 [ajp-nio-127.0.0.1-8889-exec-4 | cID:55a5916d-c0a8d700-5cecbd49-3cea53eb] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Using enhancer: Default OAuth2 Enhancer
2025-07-29 18:06:10,805 [ajp-nio-127.0.0.1-8889-exec-4 | cID:55a5916d-c0a8d700-5cecbd49-3cea53eb] INFO  com.fasnote.alm.auth.feishu.FeishuDefaultAuthenticatorEnhancer - Feishu license validation failed or not present, using default OAuth2 authenticators
2025-07-29 18:06:10,805 [ajp-nio-127.0.0.1-8889-exec-4 | cID:55a5916d-c0a8d700-5cecbd49-3cea53eb] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Feishu authenticators registered successfully in getAuthenticators() call
2025-07-29 18:06:10,805 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55a59174-c0a8d700-5cecbd49-7f1578a2] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Intercepting getAuthenticators() method call for Feishu enhancement
2025-07-29 18:06:10,805 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55a59174-c0a8d700-5cecbd49-7f1578a2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 开始解析服务: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer, 服务名: null
2025-07-29 18:06:10,805 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55a59174-c0a8d700-5cecbd49-7f1578a2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 单例服务缓存中未找到: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer, 当前缓存大小: 0
2025-07-29 18:06:10,805 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55a59174-c0a8d700-5cecbd49-7f1578a2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 未找到实现类: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer
2025-07-29 18:06:10,805 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55a59174-c0a8d700-5cecbd49-7f1578a2] WARN  com.fasnote.alm.injection.impl.DependencyInjector - 无法解析服务: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer
2025-07-29 18:06:10,805 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55a59174-c0a8d700-5cecbd49-7f1578a2] WARN  com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - DI framework returned unexpected type, falling back to default enhancer
2025-07-29 18:06:10,805 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55a59174-c0a8d700-5cecbd49-7f1578a2] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Using enhancer: Default OAuth2 Enhancer
2025-07-29 18:06:10,805 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55a59174-c0a8d700-5cecbd49-7f1578a2] INFO  com.fasnote.alm.auth.feishu.FeishuDefaultAuthenticatorEnhancer - Feishu license validation failed or not present, using default OAuth2 authenticators
2025-07-29 18:06:10,805 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55a59174-c0a8d700-5cecbd49-7f1578a2] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Feishu authenticators registered successfully in getAuthenticators() call
2025-07-29 18:06:10,805 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55a59174-c0a8d700-5cecbd49-7f1578a2] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Intercepting getAuthenticators() method call for Feishu enhancement
2025-07-29 18:06:10,805 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55a59174-c0a8d700-5cecbd49-7f1578a2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 开始解析服务: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer, 服务名: null
2025-07-29 18:06:10,805 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55a59174-c0a8d700-5cecbd49-7f1578a2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 单例服务缓存中未找到: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer, 当前缓存大小: 0
2025-07-29 18:06:10,805 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55a59174-c0a8d700-5cecbd49-7f1578a2] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 未找到实现类: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer
2025-07-29 18:06:10,805 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55a59174-c0a8d700-5cecbd49-7f1578a2] WARN  com.fasnote.alm.injection.impl.DependencyInjector - 无法解析服务: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer
2025-07-29 18:06:10,805 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55a59174-c0a8d700-5cecbd49-7f1578a2] WARN  com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - DI framework returned unexpected type, falling back to default enhancer
2025-07-29 18:06:10,805 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55a59174-c0a8d700-5cecbd49-7f1578a2] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Using enhancer: Default OAuth2 Enhancer
2025-07-29 18:06:10,805 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55a59174-c0a8d700-5cecbd49-7f1578a2] INFO  com.fasnote.alm.auth.feishu.FeishuDefaultAuthenticatorEnhancer - Feishu license validation failed or not present, using default OAuth2 authenticators
2025-07-29 18:06:10,805 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55a59174-c0a8d700-5cecbd49-7f1578a2] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Feishu authenticators registered successfully in getAuthenticators() call
2025-07-29 18:06:10,860 [ajp-nio-127.0.0.1-8889-exec-5 | cID:55a591ac-c0a8d700-5cecbd49-aa6331ef] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Intercepting getAuthenticators() method call for Feishu enhancement
2025-07-29 18:06:10,861 [ajp-nio-127.0.0.1-8889-exec-5 | cID:55a591ac-c0a8d700-5cecbd49-aa6331ef] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 开始解析服务: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer, 服务名: null
2025-07-29 18:06:10,861 [ajp-nio-127.0.0.1-8889-exec-5 | cID:55a591ac-c0a8d700-5cecbd49-aa6331ef] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 单例服务缓存中未找到: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer, 当前缓存大小: 0
2025-07-29 18:06:10,861 [ajp-nio-127.0.0.1-8889-exec-5 | cID:55a591ac-c0a8d700-5cecbd49-aa6331ef] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 未找到实现类: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer
2025-07-29 18:06:10,861 [ajp-nio-127.0.0.1-8889-exec-5 | cID:55a591ac-c0a8d700-5cecbd49-aa6331ef] WARN  com.fasnote.alm.injection.impl.DependencyInjector - 无法解析服务: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer
2025-07-29 18:06:10,861 [ajp-nio-127.0.0.1-8889-exec-5 | cID:55a591ac-c0a8d700-5cecbd49-aa6331ef] WARN  com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - DI framework returned unexpected type, falling back to default enhancer
2025-07-29 18:06:10,862 [ajp-nio-127.0.0.1-8889-exec-5 | cID:55a591ac-c0a8d700-5cecbd49-aa6331ef] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Using enhancer: Default OAuth2 Enhancer
2025-07-29 18:06:10,862 [ajp-nio-127.0.0.1-8889-exec-5 | cID:55a591ac-c0a8d700-5cecbd49-aa6331ef] INFO  com.fasnote.alm.auth.feishu.FeishuDefaultAuthenticatorEnhancer - Feishu license validation failed or not present, using default OAuth2 authenticators
2025-07-29 18:06:10,862 [ajp-nio-127.0.0.1-8889-exec-5 | cID:55a591ac-c0a8d700-5cecbd49-aa6331ef] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Feishu authenticators registered successfully in getAuthenticators() call
2025-07-29 18:06:10,862 [ajp-nio-127.0.0.1-8889-exec-5 | cID:55a591ac-c0a8d700-5cecbd49-aa6331ef] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Intercepting getAuthenticators() method call for Feishu enhancement
2025-07-29 18:06:10,862 [ajp-nio-127.0.0.1-8889-exec-5 | cID:55a591ac-c0a8d700-5cecbd49-aa6331ef] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 开始解析服务: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer, 服务名: null
2025-07-29 18:06:10,864 [ajp-nio-127.0.0.1-8889-exec-5 | cID:55a591ac-c0a8d700-5cecbd49-aa6331ef] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 单例服务缓存中未找到: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer, 当前缓存大小: 0
2025-07-29 18:06:10,865 [ajp-nio-127.0.0.1-8889-exec-5 | cID:55a591ac-c0a8d700-5cecbd49-aa6331ef] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 未找到实现类: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer
2025-07-29 18:06:10,867 [ajp-nio-127.0.0.1-8889-exec-5 | cID:55a591ac-c0a8d700-5cecbd49-aa6331ef] WARN  com.fasnote.alm.injection.impl.DependencyInjector - 无法解析服务: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer
2025-07-29 18:06:10,868 [ajp-nio-127.0.0.1-8889-exec-5 | cID:55a591ac-c0a8d700-5cecbd49-aa6331ef] WARN  com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - DI framework returned unexpected type, falling back to default enhancer
2025-07-29 18:06:10,868 [ajp-nio-127.0.0.1-8889-exec-5 | cID:55a591ac-c0a8d700-5cecbd49-aa6331ef] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Using enhancer: Default OAuth2 Enhancer
2025-07-29 18:06:10,868 [ajp-nio-127.0.0.1-8889-exec-5 | cID:55a591ac-c0a8d700-5cecbd49-aa6331ef] INFO  com.fasnote.alm.auth.feishu.FeishuDefaultAuthenticatorEnhancer - Feishu license validation failed or not present, using default OAuth2 authenticators
2025-07-29 18:06:10,868 [ajp-nio-127.0.0.1-8889-exec-5 | cID:55a591ac-c0a8d700-5cecbd49-aa6331ef] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Feishu authenticators registered successfully in getAuthenticators() call
2025-07-29 18:06:10,995 [ajp-nio-127.0.0.1-8889-exec-8 | cID:55a59232-c0a8d700-5cecbd49-5b270895] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Intercepting getAuthenticators() method call for Feishu enhancement
2025-07-29 18:06:10,995 [ajp-nio-127.0.0.1-8889-exec-8 | cID:55a59232-c0a8d700-5cecbd49-5b270895] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 开始解析服务: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer, 服务名: null
2025-07-29 18:06:10,995 [ajp-nio-127.0.0.1-8889-exec-8 | cID:55a59232-c0a8d700-5cecbd49-5b270895] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 单例服务缓存中未找到: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer, 当前缓存大小: 0
2025-07-29 18:06:10,995 [ajp-nio-127.0.0.1-8889-exec-8 | cID:55a59232-c0a8d700-5cecbd49-5b270895] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 未找到实现类: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer
2025-07-29 18:06:10,995 [ajp-nio-127.0.0.1-8889-exec-8 | cID:55a59232-c0a8d700-5cecbd49-5b270895] WARN  com.fasnote.alm.injection.impl.DependencyInjector - 无法解析服务: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer
2025-07-29 18:06:10,995 [ajp-nio-127.0.0.1-8889-exec-8 | cID:55a59232-c0a8d700-5cecbd49-5b270895] WARN  com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - DI framework returned unexpected type, falling back to default enhancer
2025-07-29 18:06:10,995 [ajp-nio-127.0.0.1-8889-exec-8 | cID:55a59232-c0a8d700-5cecbd49-5b270895] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Using enhancer: Default OAuth2 Enhancer
2025-07-29 18:06:10,995 [ajp-nio-127.0.0.1-8889-exec-8 | cID:55a59232-c0a8d700-5cecbd49-5b270895] INFO  com.fasnote.alm.auth.feishu.FeishuDefaultAuthenticatorEnhancer - Feishu license validation failed or not present, using default OAuth2 authenticators
2025-07-29 18:06:10,995 [ajp-nio-127.0.0.1-8889-exec-8 | cID:55a59232-c0a8d700-5cecbd49-5b270895] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Feishu authenticators registered successfully in getAuthenticators() call
2025-07-29 18:06:10,996 [ajp-nio-127.0.0.1-8889-exec-8 | cID:55a59232-c0a8d700-5cecbd49-5b270895] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Intercepting getAuthenticators() method call for Feishu enhancement
2025-07-29 18:06:10,996 [ajp-nio-127.0.0.1-8889-exec-8 | cID:55a59232-c0a8d700-5cecbd49-5b270895] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 开始解析服务: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer, 服务名: null
2025-07-29 18:06:10,996 [ajp-nio-127.0.0.1-8889-exec-8 | cID:55a59232-c0a8d700-5cecbd49-5b270895] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 单例服务缓存中未找到: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer, 当前缓存大小: 0
2025-07-29 18:06:10,996 [ajp-nio-127.0.0.1-8889-exec-8 | cID:55a59232-c0a8d700-5cecbd49-5b270895] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 未找到实现类: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer
2025-07-29 18:06:10,996 [ajp-nio-127.0.0.1-8889-exec-8 | cID:55a59232-c0a8d700-5cecbd49-5b270895] WARN  com.fasnote.alm.injection.impl.DependencyInjector - 无法解析服务: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer
2025-07-29 18:06:10,996 [ajp-nio-127.0.0.1-8889-exec-8 | cID:55a59232-c0a8d700-5cecbd49-5b270895] WARN  com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - DI framework returned unexpected type, falling back to default enhancer
2025-07-29 18:06:10,996 [ajp-nio-127.0.0.1-8889-exec-8 | cID:55a59232-c0a8d700-5cecbd49-5b270895] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Using enhancer: Default OAuth2 Enhancer
2025-07-29 18:06:10,996 [ajp-nio-127.0.0.1-8889-exec-8 | cID:55a59232-c0a8d700-5cecbd49-5b270895] INFO  com.fasnote.alm.auth.feishu.FeishuDefaultAuthenticatorEnhancer - Feishu license validation failed or not present, using default OAuth2 authenticators
2025-07-29 18:06:10,996 [ajp-nio-127.0.0.1-8889-exec-8 | cID:55a59232-c0a8d700-5cecbd49-5b270895] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Feishu authenticators registered successfully in getAuthenticators() call
2025-07-29 18:06:11,130 [ajp-nio-127.0.0.1-8889-exec-9 | cID:55a592b9-c0a8d700-5cecbd49-698a291f] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Intercepting getAuthenticators() method call for Feishu enhancement
2025-07-29 18:06:11,130 [ajp-nio-127.0.0.1-8889-exec-9 | cID:55a592b9-c0a8d700-5cecbd49-698a291f] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 开始解析服务: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer, 服务名: null
2025-07-29 18:06:11,130 [ajp-nio-127.0.0.1-8889-exec-9 | cID:55a592b9-c0a8d700-5cecbd49-698a291f] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 单例服务缓存中未找到: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer, 当前缓存大小: 0
2025-07-29 18:06:11,130 [ajp-nio-127.0.0.1-8889-exec-9 | cID:55a592b9-c0a8d700-5cecbd49-698a291f] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 未找到实现类: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer
2025-07-29 18:06:11,130 [ajp-nio-127.0.0.1-8889-exec-9 | cID:55a592b9-c0a8d700-5cecbd49-698a291f] WARN  com.fasnote.alm.injection.impl.DependencyInjector - 无法解析服务: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer
2025-07-29 18:06:11,130 [ajp-nio-127.0.0.1-8889-exec-9 | cID:55a592b9-c0a8d700-5cecbd49-698a291f] WARN  com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - DI framework returned unexpected type, falling back to default enhancer
2025-07-29 18:06:11,130 [ajp-nio-127.0.0.1-8889-exec-9 | cID:55a592b9-c0a8d700-5cecbd49-698a291f] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Using enhancer: Default OAuth2 Enhancer
2025-07-29 18:06:11,130 [ajp-nio-127.0.0.1-8889-exec-9 | cID:55a592b9-c0a8d700-5cecbd49-698a291f] INFO  com.fasnote.alm.auth.feishu.FeishuDefaultAuthenticatorEnhancer - Feishu license validation failed or not present, using default OAuth2 authenticators
2025-07-29 18:06:11,130 [ajp-nio-127.0.0.1-8889-exec-9 | cID:55a592b9-c0a8d700-5cecbd49-698a291f] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Feishu authenticators registered successfully in getAuthenticators() call
2025-07-29 18:06:11,130 [ajp-nio-127.0.0.1-8889-exec-9 | cID:55a592b9-c0a8d700-5cecbd49-698a291f] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Intercepting getAuthenticators() method call for Feishu enhancement
2025-07-29 18:06:11,130 [ajp-nio-127.0.0.1-8889-exec-9 | cID:55a592b9-c0a8d700-5cecbd49-698a291f] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 开始解析服务: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer, 服务名: null
2025-07-29 18:06:11,130 [ajp-nio-127.0.0.1-8889-exec-9 | cID:55a592b9-c0a8d700-5cecbd49-698a291f] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 单例服务缓存中未找到: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer, 当前缓存大小: 0
2025-07-29 18:06:11,130 [ajp-nio-127.0.0.1-8889-exec-9 | cID:55a592b9-c0a8d700-5cecbd49-698a291f] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 未找到实现类: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer
2025-07-29 18:06:11,130 [ajp-nio-127.0.0.1-8889-exec-9 | cID:55a592b9-c0a8d700-5cecbd49-698a291f] WARN  com.fasnote.alm.injection.impl.DependencyInjector - 无法解析服务: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer
2025-07-29 18:06:11,130 [ajp-nio-127.0.0.1-8889-exec-9 | cID:55a592b9-c0a8d700-5cecbd49-698a291f] WARN  com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - DI framework returned unexpected type, falling back to default enhancer
2025-07-29 18:06:11,130 [ajp-nio-127.0.0.1-8889-exec-9 | cID:55a592b9-c0a8d700-5cecbd49-698a291f] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Using enhancer: Default OAuth2 Enhancer
2025-07-29 18:06:11,130 [ajp-nio-127.0.0.1-8889-exec-9 | cID:55a592b9-c0a8d700-5cecbd49-698a291f] INFO  com.fasnote.alm.auth.feishu.FeishuDefaultAuthenticatorEnhancer - Feishu license validation failed or not present, using default OAuth2 authenticators
2025-07-29 18:06:11,130 [ajp-nio-127.0.0.1-8889-exec-9 | cID:55a592b9-c0a8d700-5cecbd49-698a291f] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Feishu authenticators registered successfully in getAuthenticators() call
2025-07-29 18:06:11,157 [ajp-nio-127.0.0.1-8889-exec-10 | cID:55a592d4-c0a8d700-5cecbd49-27135899] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Intercepting getAuthenticators() method call for Feishu enhancement
2025-07-29 18:06:11,157 [ajp-nio-127.0.0.1-8889-exec-10 | cID:55a592d4-c0a8d700-5cecbd49-27135899] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 开始解析服务: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer, 服务名: null
2025-07-29 18:06:11,157 [ajp-nio-127.0.0.1-8889-exec-10 | cID:55a592d4-c0a8d700-5cecbd49-27135899] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 单例服务缓存中未找到: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer, 当前缓存大小: 0
2025-07-29 18:06:11,157 [ajp-nio-127.0.0.1-8889-exec-10 | cID:55a592d4-c0a8d700-5cecbd49-27135899] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 未找到实现类: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer
2025-07-29 18:06:11,157 [ajp-nio-127.0.0.1-8889-exec-10 | cID:55a592d4-c0a8d700-5cecbd49-27135899] WARN  com.fasnote.alm.injection.impl.DependencyInjector - 无法解析服务: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer
2025-07-29 18:06:11,157 [ajp-nio-127.0.0.1-8889-exec-10 | cID:55a592d4-c0a8d700-5cecbd49-27135899] WARN  com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - DI framework returned unexpected type, falling back to default enhancer
2025-07-29 18:06:11,157 [ajp-nio-127.0.0.1-8889-exec-10 | cID:55a592d4-c0a8d700-5cecbd49-27135899] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Using enhancer: Default OAuth2 Enhancer
2025-07-29 18:06:11,157 [ajp-nio-127.0.0.1-8889-exec-10 | cID:55a592d4-c0a8d700-5cecbd49-27135899] INFO  com.fasnote.alm.auth.feishu.FeishuDefaultAuthenticatorEnhancer - Feishu license validation failed or not present, using default OAuth2 authenticators
2025-07-29 18:06:11,157 [ajp-nio-127.0.0.1-8889-exec-10 | cID:55a592d4-c0a8d700-5cecbd49-27135899] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Feishu authenticators registered successfully in getAuthenticators() call
2025-07-29 18:06:11,157 [ajp-nio-127.0.0.1-8889-exec-10 | cID:55a592d4-c0a8d700-5cecbd49-27135899] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Intercepting getAuthenticators() method call for Feishu enhancement
2025-07-29 18:06:11,157 [ajp-nio-127.0.0.1-8889-exec-10 | cID:55a592d4-c0a8d700-5cecbd49-27135899] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 开始解析服务: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer, 服务名: null
2025-07-29 18:06:11,157 [ajp-nio-127.0.0.1-8889-exec-10 | cID:55a592d4-c0a8d700-5cecbd49-27135899] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 单例服务缓存中未找到: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer, 当前缓存大小: 0
2025-07-29 18:06:11,157 [ajp-nio-127.0.0.1-8889-exec-10 | cID:55a592d4-c0a8d700-5cecbd49-27135899] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 未找到实现类: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer
2025-07-29 18:06:11,157 [ajp-nio-127.0.0.1-8889-exec-10 | cID:55a592d4-c0a8d700-5cecbd49-27135899] WARN  com.fasnote.alm.injection.impl.DependencyInjector - 无法解析服务: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer
2025-07-29 18:06:11,157 [ajp-nio-127.0.0.1-8889-exec-10 | cID:55a592d4-c0a8d700-5cecbd49-27135899] WARN  com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - DI framework returned unexpected type, falling back to default enhancer
2025-07-29 18:06:11,157 [ajp-nio-127.0.0.1-8889-exec-10 | cID:55a592d4-c0a8d700-5cecbd49-27135899] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Using enhancer: Default OAuth2 Enhancer
2025-07-29 18:06:11,157 [ajp-nio-127.0.0.1-8889-exec-10 | cID:55a592d4-c0a8d700-5cecbd49-27135899] INFO  com.fasnote.alm.auth.feishu.FeishuDefaultAuthenticatorEnhancer - Feishu license validation failed or not present, using default OAuth2 authenticators
2025-07-29 18:06:11,157 [ajp-nio-127.0.0.1-8889-exec-10 | cID:55a592d4-c0a8d700-5cecbd49-27135899] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Feishu authenticators registered successfully in getAuthenticators() call
2025-07-29 18:06:11,160 [ajp-nio-127.0.0.1-8889-exec-1 | cID:55a592d8-c0a8d700-5cecbd49-31cd4e53] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Intercepting getAuthenticators() method call for Feishu enhancement
2025-07-29 18:06:11,160 [ajp-nio-127.0.0.1-8889-exec-1 | cID:55a592d8-c0a8d700-5cecbd49-31cd4e53] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 开始解析服务: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer, 服务名: null
2025-07-29 18:06:11,160 [ajp-nio-127.0.0.1-8889-exec-1 | cID:55a592d8-c0a8d700-5cecbd49-31cd4e53] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 单例服务缓存中未找到: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer, 当前缓存大小: 0
2025-07-29 18:06:11,160 [ajp-nio-127.0.0.1-8889-exec-1 | cID:55a592d8-c0a8d700-5cecbd49-31cd4e53] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 未找到实现类: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer
2025-07-29 18:06:11,160 [ajp-nio-127.0.0.1-8889-exec-1 | cID:55a592d8-c0a8d700-5cecbd49-31cd4e53] WARN  com.fasnote.alm.injection.impl.DependencyInjector - 无法解析服务: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer
2025-07-29 18:06:11,160 [ajp-nio-127.0.0.1-8889-exec-1 | cID:55a592d8-c0a8d700-5cecbd49-31cd4e53] WARN  com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - DI framework returned unexpected type, falling back to default enhancer
2025-07-29 18:06:11,160 [ajp-nio-127.0.0.1-8889-exec-1 | cID:55a592d8-c0a8d700-5cecbd49-31cd4e53] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Using enhancer: Default OAuth2 Enhancer
2025-07-29 18:06:11,160 [ajp-nio-127.0.0.1-8889-exec-1 | cID:55a592d8-c0a8d700-5cecbd49-31cd4e53] INFO  com.fasnote.alm.auth.feishu.FeishuDefaultAuthenticatorEnhancer - Feishu license validation failed or not present, using default OAuth2 authenticators
2025-07-29 18:06:11,160 [ajp-nio-127.0.0.1-8889-exec-1 | cID:55a592d8-c0a8d700-5cecbd49-31cd4e53] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Feishu authenticators registered successfully in getAuthenticators() call
2025-07-29 18:06:11,161 [ajp-nio-127.0.0.1-8889-exec-1 | cID:55a592d8-c0a8d700-5cecbd49-31cd4e53] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Intercepting getAuthenticators() method call for Feishu enhancement
2025-07-29 18:06:11,161 [ajp-nio-127.0.0.1-8889-exec-1 | cID:55a592d8-c0a8d700-5cecbd49-31cd4e53] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 开始解析服务: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer, 服务名: null
2025-07-29 18:06:11,161 [ajp-nio-127.0.0.1-8889-exec-1 | cID:55a592d8-c0a8d700-5cecbd49-31cd4e53] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 单例服务缓存中未找到: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer, 当前缓存大小: 0
2025-07-29 18:06:11,161 [ajp-nio-127.0.0.1-8889-exec-1 | cID:55a592d8-c0a8d700-5cecbd49-31cd4e53] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 未找到实现类: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer
2025-07-29 18:06:11,161 [ajp-nio-127.0.0.1-8889-exec-1 | cID:55a592d8-c0a8d700-5cecbd49-31cd4e53] WARN  com.fasnote.alm.injection.impl.DependencyInjector - 无法解析服务: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer
2025-07-29 18:06:11,161 [ajp-nio-127.0.0.1-8889-exec-1 | cID:55a592d8-c0a8d700-5cecbd49-31cd4e53] WARN  com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - DI framework returned unexpected type, falling back to default enhancer
2025-07-29 18:06:11,161 [ajp-nio-127.0.0.1-8889-exec-1 | cID:55a592d8-c0a8d700-5cecbd49-31cd4e53] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Using enhancer: Default OAuth2 Enhancer
2025-07-29 18:06:11,161 [ajp-nio-127.0.0.1-8889-exec-1 | cID:55a592d8-c0a8d700-5cecbd49-31cd4e53] INFO  com.fasnote.alm.auth.feishu.FeishuDefaultAuthenticatorEnhancer - Feishu license validation failed or not present, using default OAuth2 authenticators
2025-07-29 18:06:11,161 [ajp-nio-127.0.0.1-8889-exec-1 | cID:55a592d8-c0a8d700-5cecbd49-31cd4e53] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Feishu authenticators registered successfully in getAuthenticators() call
2025-07-29 18:06:11,161 [ajp-nio-127.0.0.1-8889-exec-4 | cID:55a592d9-c0a8d700-5cecbd49-cf5e3cfc] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Intercepting getAuthenticators() method call for Feishu enhancement
2025-07-29 18:06:11,161 [ajp-nio-127.0.0.1-8889-exec-4 | cID:55a592d9-c0a8d700-5cecbd49-cf5e3cfc] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 开始解析服务: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer, 服务名: null
2025-07-29 18:06:11,161 [ajp-nio-127.0.0.1-8889-exec-4 | cID:55a592d9-c0a8d700-5cecbd49-cf5e3cfc] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 单例服务缓存中未找到: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer, 当前缓存大小: 0
2025-07-29 18:06:11,161 [ajp-nio-127.0.0.1-8889-exec-4 | cID:55a592d9-c0a8d700-5cecbd49-cf5e3cfc] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 未找到实现类: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer
2025-07-29 18:06:11,161 [ajp-nio-127.0.0.1-8889-exec-4 | cID:55a592d9-c0a8d700-5cecbd49-cf5e3cfc] WARN  com.fasnote.alm.injection.impl.DependencyInjector - 无法解析服务: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer
2025-07-29 18:06:11,161 [ajp-nio-127.0.0.1-8889-exec-4 | cID:55a592d9-c0a8d700-5cecbd49-cf5e3cfc] WARN  com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - DI framework returned unexpected type, falling back to default enhancer
2025-07-29 18:06:11,161 [ajp-nio-127.0.0.1-8889-exec-4 | cID:55a592d9-c0a8d700-5cecbd49-cf5e3cfc] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Using enhancer: Default OAuth2 Enhancer
2025-07-29 18:06:11,162 [ajp-nio-127.0.0.1-8889-exec-4 | cID:55a592d9-c0a8d700-5cecbd49-cf5e3cfc] INFO  com.fasnote.alm.auth.feishu.FeishuDefaultAuthenticatorEnhancer - Feishu license validation failed or not present, using default OAuth2 authenticators
2025-07-29 18:06:11,162 [ajp-nio-127.0.0.1-8889-exec-4 | cID:55a592d9-c0a8d700-5cecbd49-cf5e3cfc] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Feishu authenticators registered successfully in getAuthenticators() call
2025-07-29 18:06:11,162 [ajp-nio-127.0.0.1-8889-exec-4 | cID:55a592d9-c0a8d700-5cecbd49-cf5e3cfc] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Intercepting getAuthenticators() method call for Feishu enhancement
2025-07-29 18:06:11,162 [ajp-nio-127.0.0.1-8889-exec-4 | cID:55a592d9-c0a8d700-5cecbd49-cf5e3cfc] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 开始解析服务: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer, 服务名: null
2025-07-29 18:06:11,162 [ajp-nio-127.0.0.1-8889-exec-4 | cID:55a592d9-c0a8d700-5cecbd49-cf5e3cfc] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 单例服务缓存中未找到: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer, 当前缓存大小: 0
2025-07-29 18:06:11,162 [ajp-nio-127.0.0.1-8889-exec-4 | cID:55a592d9-c0a8d700-5cecbd49-cf5e3cfc] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 未找到实现类: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer
2025-07-29 18:06:11,162 [ajp-nio-127.0.0.1-8889-exec-4 | cID:55a592d9-c0a8d700-5cecbd49-cf5e3cfc] WARN  com.fasnote.alm.injection.impl.DependencyInjector - 无法解析服务: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer
2025-07-29 18:06:11,162 [ajp-nio-127.0.0.1-8889-exec-4 | cID:55a592d9-c0a8d700-5cecbd49-cf5e3cfc] WARN  com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - DI framework returned unexpected type, falling back to default enhancer
2025-07-29 18:06:11,162 [ajp-nio-127.0.0.1-8889-exec-4 | cID:55a592d9-c0a8d700-5cecbd49-cf5e3cfc] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Using enhancer: Default OAuth2 Enhancer
2025-07-29 18:06:11,162 [ajp-nio-127.0.0.1-8889-exec-4 | cID:55a592d9-c0a8d700-5cecbd49-cf5e3cfc] INFO  com.fasnote.alm.auth.feishu.FeishuDefaultAuthenticatorEnhancer - Feishu license validation failed or not present, using default OAuth2 authenticators
2025-07-29 18:06:11,162 [ajp-nio-127.0.0.1-8889-exec-4 | cID:55a592d9-c0a8d700-5cecbd49-cf5e3cfc] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Feishu authenticators registered successfully in getAuthenticators() call
2025-07-29 18:06:11,164 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55a592dc-c0a8d700-5cecbd49-6b803412] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Intercepting getAuthenticators() method call for Feishu enhancement
2025-07-29 18:06:11,164 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55a592dc-c0a8d700-5cecbd49-6b803412] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 开始解析服务: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer, 服务名: null
2025-07-29 18:06:11,164 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55a592dc-c0a8d700-5cecbd49-6b803412] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 单例服务缓存中未找到: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer, 当前缓存大小: 0
2025-07-29 18:06:11,164 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55a592dc-c0a8d700-5cecbd49-6b803412] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 未找到实现类: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer
2025-07-29 18:06:11,164 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55a592dc-c0a8d700-5cecbd49-6b803412] WARN  com.fasnote.alm.injection.impl.DependencyInjector - 无法解析服务: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer
2025-07-29 18:06:11,164 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55a592dc-c0a8d700-5cecbd49-6b803412] WARN  com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - DI framework returned unexpected type, falling back to default enhancer
2025-07-29 18:06:11,164 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55a592dc-c0a8d700-5cecbd49-6b803412] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Using enhancer: Default OAuth2 Enhancer
2025-07-29 18:06:11,164 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55a592dc-c0a8d700-5cecbd49-6b803412] INFO  com.fasnote.alm.auth.feishu.FeishuDefaultAuthenticatorEnhancer - Feishu license validation failed or not present, using default OAuth2 authenticators
2025-07-29 18:06:11,164 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55a592dc-c0a8d700-5cecbd49-6b803412] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Feishu authenticators registered successfully in getAuthenticators() call
2025-07-29 18:06:11,164 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55a592dc-c0a8d700-5cecbd49-6b803412] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Intercepting getAuthenticators() method call for Feishu enhancement
2025-07-29 18:06:11,164 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55a592dc-c0a8d700-5cecbd49-6b803412] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 开始解析服务: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer, 服务名: null
2025-07-29 18:06:11,164 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55a592dc-c0a8d700-5cecbd49-6b803412] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 单例服务缓存中未找到: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer, 当前缓存大小: 0
2025-07-29 18:06:11,164 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55a592dc-c0a8d700-5cecbd49-6b803412] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 未找到实现类: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer
2025-07-29 18:06:11,164 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55a592dc-c0a8d700-5cecbd49-6b803412] WARN  com.fasnote.alm.injection.impl.DependencyInjector - 无法解析服务: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer
2025-07-29 18:06:11,164 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55a592dc-c0a8d700-5cecbd49-6b803412] WARN  com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - DI framework returned unexpected type, falling back to default enhancer
2025-07-29 18:06:11,164 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55a592dc-c0a8d700-5cecbd49-6b803412] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Using enhancer: Default OAuth2 Enhancer
2025-07-29 18:06:11,164 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55a592dc-c0a8d700-5cecbd49-6b803412] INFO  com.fasnote.alm.auth.feishu.FeishuDefaultAuthenticatorEnhancer - Feishu license validation failed or not present, using default OAuth2 authenticators
2025-07-29 18:06:11,164 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55a592dc-c0a8d700-5cecbd49-6b803412] DEBUG com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory - Feishu authenticators registered successfully in getAuthenticators() call
2025-07-29 18:06:11,201 [ajp-nio-127.0.0.1-8889-exec-9 | cID:55a592b9-c0a8d700-5cecbd49-698a291f] WARN  org.glassfish.jersey.internal.Errors - The following warnings have been detected: WARNING: The (sub)resource method createTemporaryProxy in com.polarion.synchronizer.ui.MetaDataResource contains empty path annotation.

2025-07-29 18:06:11,265 [ajp-nio-127.0.0.1-8889-exec-9 | cID:55a592b9-c0a8d700-5cecbd49-698a291f] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/global/canModifyConfiguration': Total: 0.135 s, CPU [user: 0.074 s, system: 0.0109 s], Allocated memory: 9.5 MB, transactions: 0
2025-07-29 18:06:11,265 [ajp-nio-127.0.0.1-8889-exec-10 | cID:55a592d4-c0a8d700-5cecbd49-27135899] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/canModifyConfiguration': Total: 0.108 s, CPU [user: 0.0164 s, system: 0.00447 s], Allocated memory: 1.4 MB, transactions: 0
2025-07-29 18:06:11,289 [ajp-nio-127.0.0.1-8889-exec-1 | cID:55a592d8-c0a8d700-5cecbd49-31cd4e53] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxy_contributions?_=1753783571097': Total: 0.129 s, CPU [user: 0.0409 s, system: 0.00823 s], Allocated memory: 2.0 MB, transactions: 0
2025-07-29 18:06:11,298 [ajp-nio-127.0.0.1-8889-exec-4 | cID:55a592d9-c0a8d700-5cecbd49-cf5e3cfc | u:admin | GET:/polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1753783571098 | u:p] ERROR com.polarion.platform.repository.internal.config.RepositoryConfigService$ConfigProblemCatcher - Failed to work with configuration from location /.polarion/synchronizer/configuration.xml:
[/.polarion/synchronizer/configuration.xml]: 3 counts of IllegalAnnotationExceptions
com.polarion.platform.repository.config.RepositoryConfigurationException: [/.polarion/synchronizer/configuration.xml]: 3 counts of IllegalAnnotationExceptions
	at com.polarion.platform.repository.spi.config.AbstractDataHandler.readLocation(AbstractDataHandler.java:87) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.spi.config.AbstractDataHandler.readLocations(AbstractDataHandler.java:61) ~[platform-repository.jar:?]
	at com.polarion.synchronizer.internal.configuration.ConfigurationDataHandler.readLocations(ConfigurationDataHandler.java:126) ~[synchronizer.jar:?]
	at $IDataHandler_19855a55638.readLocations($IDataHandler_19855a55638.java) ~[?:?]
	at $IDataHandler_19855a55637.readLocations($IDataHandler_19855a55637.java) ~[?:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.readLocations(RepositoryConfigService.java:291) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService$3.runImpl(RepositoryConfigService.java:328) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService$SVNAccessAction$1.runWEx(RepositoryConfigService.java:113) ~[platform-repository.jar:?]
	at com.polarion.core.util.RunnableWEx.runWRet(RunnableWEx.java:61) ~[util.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService$SVNAccessAction.run(RepositoryConfigService.java:123) ~[platform-repository.jar:?]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:?]
	at javax.security.auth.Subject.doAs(Subject.java:361) ~[?:?]
	at com.polarion.platform.internal.security.SubjectNDC.doAs(SubjectNDC.java:58) ~[platform.jar:?]
	at com.polarion.platform.internal.security.SecurityService.doAsUser(SecurityService.java:422) ~[platform.jar:?]
	at com.polarion.platform.internal.security.SecurityService.doAsSystemUser(SecurityService.java:412) ~[platform.jar:?]
	at $ISecurityService_19855a55468.doAsSystemUser($ISecurityService_19855a55468.java) ~[?:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.readConfiguration(RepositoryConfigService.java:324) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.getConfigurationImpl(RepositoryConfigService.java:239) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.getReadConfigurationImpl(RepositoryConfigService.java:199) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.getReadConfiguration(RepositoryConfigService.java:177) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.getReadConfiguration(RepositoryConfigService.java:170) ~[platform-repository.jar:?]
	at $IRepositoryConfigService_19855a55476.getReadConfiguration($IRepositoryConfigService_19855a55476.java) ~[?:?]
	at com.polarion.synchronizer.configuration.ConfigurationHelper.loadConfiguration(ConfigurationHelper.java:55) ~[synchronizer.jar:?]
	at com.polarion.synchronizer.ui.SyncConfigurationResource.getSyncPairs(SyncConfigurationResource.java:68) ~[synchronizer-ui.jar:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:566) ~[?:?]
	at org.glassfish.jersey.server.model.internal.ResourceMethodInvocationHandlerFactory.lambda$static$0(ResourceMethodInvocationHandlerFactory.java:52) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.server.model.internal.AbstractJavaResourceMethodDispatcher$1.run(AbstractJavaResourceMethodDispatcher.java:124) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.server.model.internal.AbstractJavaResourceMethodDispatcher.invoke(AbstractJavaResourceMethodDispatcher.java:167) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.server.model.internal.JavaResourceMethodDispatcherProvider$TypeOutInvoker.doDispatch(JavaResourceMethodDispatcherProvider.java:219) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.server.model.internal.AbstractJavaResourceMethodDispatcher.dispatch(AbstractJavaResourceMethodDispatcher.java:79) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.server.model.ResourceMethodInvoker.invoke(ResourceMethodInvoker.java:469) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.server.model.ResourceMethodInvoker.apply(ResourceMethodInvoker.java:391) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.server.model.ResourceMethodInvoker.apply(ResourceMethodInvoker.java:80) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.server.ServerRuntime$1.run(ServerRuntime.java:253) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.internal.Errors$1.call(Errors.java:248) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.internal.Errors$1.call(Errors.java:244) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.internal.Errors.process(Errors.java:292) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.internal.Errors.process(Errors.java:274) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.internal.Errors.process(Errors.java:244) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.process.internal.RequestScope.runInScope(RequestScope.java:265) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.server.ServerRuntime.process(ServerRuntime.java:232) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.server.ApplicationHandler.handle(ApplicationHandler.java:680) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.servlet.WebComponent.serviceImpl(WebComponent.java:394) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.servlet.WebComponent.service(WebComponent.java:346) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.servlet.ServletContainer.service(ServletContainer.java:366) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.servlet.ServletContainer.service(ServletContainer.java:319) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.servlet.ServletContainer.service(ServletContainer.java:205) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227) ~[catalina.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[catalina.jar:9.0.53]
	at com.polarion.portal.tomcat.servlets.DoAsFilter.doFilterWithUriNDC(DoAsFilter.java:112) ~[platform.jar:?]
	at com.polarion.portal.tomcat.servlets.DoAsFilter.lambda$0(DoAsFilter.java:83) ~[platform.jar:?]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:?]
	at javax.security.auth.Subject.doAs(Subject.java:423) [?:?]
	at com.polarion.platform.internal.security.SubjectNDC.doAs(SubjectNDC.java:69) [platform.jar:?]
	at com.polarion.portal.tomcat.servlets.DoAsFilter.doFilterHttpRequest(DoAsFilter.java:82) [platform.jar:?]
	at com.polarion.portal.tomcat.servlets.DoAsFilter.doFilterRequest(DoAsFilter.java:69) [platform.jar:?]
	at com.polarion.portal.tomcat.servlets.DoAsFilter.doFilter(DoAsFilter.java:59) [platform.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [catalina.jar:9.0.53]
	at com.polarion.portal.tomcat.servlets.SecurityCheckFilter.doFilter(SecurityCheckFilter.java:46) [portal-tomcat.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:659) [catalina.jar:9.0.53]
	at com.polarion.platform.security.auth.PolarionAuthenticator.invokeInternal(PolarionAuthenticator.java:248) [platform.jar:?]
	at com.polarion.platform.security.auth.PolarionAuthenticator.invoke(PolarionAuthenticator.java:242) [platform.jar:?]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) [catalina.jar:9.0.53]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.SingleSignOn.invoke(SingleSignOn.java:312) [catalina.jar:9.0.53]
	at com.polarion.platform.session.PolarionLocalSingleSignOn.invoke(PolarionLocalSingleSignOn.java:164) [platform.jar:?]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) [catalina.jar:9.0.53]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357) [catalina.jar:9.0.53]
	at org.apache.coyote.ajp.AjpProcessor.service(AjpProcessor.java:433) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1726) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-util.jar:9.0.53]
	at java.lang.Thread.run(Thread.java:829) [?:?]
Caused by: com.sun.xml.bind.v2.runtime.IllegalAnnotationsException: 3 counts of IllegalAnnotationExceptions
	at com.sun.xml.bind.v2.runtime.IllegalAnnotationsException$Builder.check(IllegalAnnotationsException.java:106) ~[jaxb-impl-2.4.0-b180830.0438-patched.jar:2.4.0-b180830.0438]
	at com.sun.xml.bind.v2.runtime.JAXBContextImpl.getTypeInfoSet(JAXBContextImpl.java:471) ~[jaxb-impl-2.4.0-b180830.0438-patched.jar:2.4.0-b180830.0438]
	at com.sun.xml.bind.v2.runtime.JAXBContextImpl.<init>(JAXBContextImpl.java:303) ~[jaxb-impl-2.4.0-b180830.0438-patched.jar:2.4.0-b180830.0438]
	at com.sun.xml.bind.v2.runtime.JAXBContextImpl.<init>(JAXBContextImpl.java:139) ~[jaxb-impl-2.4.0-b180830.0438-patched.jar:2.4.0-b180830.0438]
	at com.sun.xml.bind.v2.runtime.JAXBContextImpl$JAXBContextBuilder.build(JAXBContextImpl.java:1156) ~[jaxb-impl-2.4.0-b180830.0438-patched.jar:2.4.0-b180830.0438]
	at com.sun.xml.bind.v2.ContextFactory.createContext(ContextFactory.java:165) ~[jaxb-impl-2.4.0-b180830.0438-patched.jar:2.4.0-b180830.0438]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:566) ~[?:?]
	at javax.xml.bind.ContextFinder.newInstance(ContextFinder.java:288) ~[jaxb-api-2.4.0-b180830.0359-patched.jar:2.3.0]
	at javax.xml.bind.ContextFinder.newInstance(ContextFinder.java:277) ~[jaxb-api-2.4.0-b180830.0359-patched.jar:2.3.0]
	at javax.xml.bind.ContextFinder.find(ContextFinder.java:412) ~[jaxb-api-2.4.0-b180830.0359-patched.jar:2.3.0]
	at javax.xml.bind.JAXBContext.newInstance(JAXBContext.java:721) ~[jaxb-api-2.4.0-b180830.0359-patched.jar:2.3.0]
	at javax.xml.bind.JAXBContext.newInstance(JAXBContext.java:662) ~[jaxb-api-2.4.0-b180830.0359-patched.jar:2.3.0]
	at com.polarion.synchronizer.internal.configuration.ConfigurationDataHandler.loadJaxbContext(ConfigurationDataHandler.java:108) ~[synchronizer.jar:?]
	at com.polarion.synchronizer.internal.configuration.ConfigurationDataHandler.processData(ConfigurationDataHandler.java:94) ~[synchronizer.jar:?]
	at com.polarion.platform.repository.spi.config.AbstractDataHandler.readLocation(AbstractDataHandler.java:83) ~[platform-repository.jar:?]
	... 84 more
2025-07-29 18:06:11,310 [ajp-nio-127.0.0.1-8889-exec-4 | cID:55a592d9-c0a8d700-5cecbd49-cf5e3cfc] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1753783571098': Total: 0.149 s, CPU [user: 0.0542 s, system: 0.01 s], Allocated memory: 7.8 MB, transactions: 1, RepositoryConfigService: 0.0673 s [100% getReadConfiguration (1x)] (1x), svn: 0.0121 s [45% testConnection (1x), 37% getFile content (1x)] (4x)
2025-07-29 18:06:11,322 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55a592dc-c0a8d700-5cecbd49-6b803412 | u:admin | GET:/polarion/synchronizer/rest/projects/WBSdev/connections?_=1753783571099 | u:p] ERROR com.polarion.platform.repository.internal.config.RepositoryConfigService$ConfigProblemCatcher - Failed to work with configuration from location /.polarion/synchronizer/configuration.xml:
[/.polarion/synchronizer/configuration.xml]: 3 counts of IllegalAnnotationExceptions
com.polarion.platform.repository.config.RepositoryConfigurationException: [/.polarion/synchronizer/configuration.xml]: 3 counts of IllegalAnnotationExceptions
	at com.polarion.platform.repository.spi.config.AbstractDataHandler.readLocation(AbstractDataHandler.java:87) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.spi.config.AbstractDataHandler.readLocations(AbstractDataHandler.java:61) ~[platform-repository.jar:?]
	at com.polarion.synchronizer.internal.configuration.ConfigurationDataHandler.readLocations(ConfigurationDataHandler.java:126) ~[synchronizer.jar:?]
	at $IDataHandler_19855a55637.readLocations($IDataHandler_19855a55637.java) ~[?:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.readLocations(RepositoryConfigService.java:291) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService$3.runImpl(RepositoryConfigService.java:328) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService$SVNAccessAction$1.runWEx(RepositoryConfigService.java:113) ~[platform-repository.jar:?]
	at com.polarion.core.util.RunnableWEx.runWRet(RunnableWEx.java:61) ~[util.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService$SVNAccessAction.run(RepositoryConfigService.java:123) ~[platform-repository.jar:?]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:?]
	at javax.security.auth.Subject.doAs(Subject.java:361) ~[?:?]
	at com.polarion.platform.internal.security.SubjectNDC.doAs(SubjectNDC.java:58) ~[platform.jar:?]
	at com.polarion.platform.internal.security.SecurityService.doAsUser(SecurityService.java:422) ~[platform.jar:?]
	at com.polarion.platform.internal.security.SecurityService.doAsSystemUser(SecurityService.java:412) ~[platform.jar:?]
	at $ISecurityService_19855a55468.doAsSystemUser($ISecurityService_19855a55468.java) ~[?:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.readConfiguration(RepositoryConfigService.java:324) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.getConfigurationImpl(RepositoryConfigService.java:239) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.getReadConfigurationImpl(RepositoryConfigService.java:199) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.getReadConfiguration(RepositoryConfigService.java:177) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.getReadConfiguration(RepositoryConfigService.java:170) ~[platform-repository.jar:?]
	at $IRepositoryConfigService_19855a55476.getReadConfiguration($IRepositoryConfigService_19855a55476.java) ~[?:?]
	at com.polarion.synchronizer.configuration.ConfigurationHelper.loadConfiguration(ConfigurationHelper.java:55) ~[synchronizer.jar:?]
	at com.polarion.synchronizer.ui.SyncConfigurationResource.getConnections(SyncConfigurationResource.java:158) ~[synchronizer-ui.jar:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:566) ~[?:?]
	at org.glassfish.jersey.server.model.internal.ResourceMethodInvocationHandlerFactory.lambda$static$0(ResourceMethodInvocationHandlerFactory.java:52) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.server.model.internal.AbstractJavaResourceMethodDispatcher$1.run(AbstractJavaResourceMethodDispatcher.java:124) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.server.model.internal.AbstractJavaResourceMethodDispatcher.invoke(AbstractJavaResourceMethodDispatcher.java:167) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.server.model.internal.JavaResourceMethodDispatcherProvider$TypeOutInvoker.doDispatch(JavaResourceMethodDispatcherProvider.java:219) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.server.model.internal.AbstractJavaResourceMethodDispatcher.dispatch(AbstractJavaResourceMethodDispatcher.java:79) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.server.model.ResourceMethodInvoker.invoke(ResourceMethodInvoker.java:469) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.server.model.ResourceMethodInvoker.apply(ResourceMethodInvoker.java:391) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.server.model.ResourceMethodInvoker.apply(ResourceMethodInvoker.java:80) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.server.ServerRuntime$1.run(ServerRuntime.java:253) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.internal.Errors$1.call(Errors.java:248) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.internal.Errors$1.call(Errors.java:244) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.internal.Errors.process(Errors.java:292) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.internal.Errors.process(Errors.java:274) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.internal.Errors.process(Errors.java:244) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.process.internal.RequestScope.runInScope(RequestScope.java:265) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.server.ServerRuntime.process(ServerRuntime.java:232) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.server.ApplicationHandler.handle(ApplicationHandler.java:680) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.servlet.WebComponent.serviceImpl(WebComponent.java:394) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.servlet.WebComponent.service(WebComponent.java:346) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.servlet.ServletContainer.service(ServletContainer.java:366) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.servlet.ServletContainer.service(ServletContainer.java:319) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.glassfish.jersey.servlet.ServletContainer.service(ServletContainer.java:205) ~[org.glassfish.jersey_2.31.0.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227) ~[catalina.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[catalina.jar:9.0.53]
	at com.polarion.portal.tomcat.servlets.DoAsFilter.doFilterWithUriNDC(DoAsFilter.java:112) ~[platform.jar:?]
	at com.polarion.portal.tomcat.servlets.DoAsFilter.lambda$0(DoAsFilter.java:83) ~[platform.jar:?]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:?]
	at javax.security.auth.Subject.doAs(Subject.java:423) [?:?]
	at com.polarion.platform.internal.security.SubjectNDC.doAs(SubjectNDC.java:69) [platform.jar:?]
	at com.polarion.portal.tomcat.servlets.DoAsFilter.doFilterHttpRequest(DoAsFilter.java:82) [platform.jar:?]
	at com.polarion.portal.tomcat.servlets.DoAsFilter.doFilterRequest(DoAsFilter.java:69) [platform.jar:?]
	at com.polarion.portal.tomcat.servlets.DoAsFilter.doFilter(DoAsFilter.java:59) [platform.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [catalina.jar:9.0.53]
	at com.polarion.portal.tomcat.servlets.SecurityCheckFilter.doFilter(SecurityCheckFilter.java:46) [portal-tomcat.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:659) [catalina.jar:9.0.53]
	at com.polarion.platform.security.auth.PolarionAuthenticator.invokeInternal(PolarionAuthenticator.java:248) [platform.jar:?]
	at com.polarion.platform.security.auth.PolarionAuthenticator.invoke(PolarionAuthenticator.java:242) [platform.jar:?]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) [catalina.jar:9.0.53]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.SingleSignOn.invoke(SingleSignOn.java:312) [catalina.jar:9.0.53]
	at com.polarion.platform.session.PolarionLocalSingleSignOn.invoke(PolarionLocalSingleSignOn.java:164) [platform.jar:?]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) [catalina.jar:9.0.53]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357) [catalina.jar:9.0.53]
	at org.apache.coyote.ajp.AjpProcessor.service(AjpProcessor.java:433) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1726) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-util.jar:9.0.53]
	at java.lang.Thread.run(Thread.java:829) [?:?]
Caused by: com.sun.xml.bind.v2.runtime.IllegalAnnotationsException: 3 counts of IllegalAnnotationExceptions
	at com.sun.xml.bind.v2.runtime.IllegalAnnotationsException$Builder.check(IllegalAnnotationsException.java:106) ~[jaxb-impl-2.4.0-b180830.0438-patched.jar:2.4.0-b180830.0438]
	at com.sun.xml.bind.v2.runtime.JAXBContextImpl.getTypeInfoSet(JAXBContextImpl.java:471) ~[jaxb-impl-2.4.0-b180830.0438-patched.jar:2.4.0-b180830.0438]
	at com.sun.xml.bind.v2.runtime.JAXBContextImpl.<init>(JAXBContextImpl.java:303) ~[jaxb-impl-2.4.0-b180830.0438-patched.jar:2.4.0-b180830.0438]
	at com.sun.xml.bind.v2.runtime.JAXBContextImpl.<init>(JAXBContextImpl.java:139) ~[jaxb-impl-2.4.0-b180830.0438-patched.jar:2.4.0-b180830.0438]
	at com.sun.xml.bind.v2.runtime.JAXBContextImpl$JAXBContextBuilder.build(JAXBContextImpl.java:1156) ~[jaxb-impl-2.4.0-b180830.0438-patched.jar:2.4.0-b180830.0438]
	at com.sun.xml.bind.v2.ContextFactory.createContext(ContextFactory.java:165) ~[jaxb-impl-2.4.0-b180830.0438-patched.jar:2.4.0-b180830.0438]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:566) ~[?:?]
	at javax.xml.bind.ContextFinder.newInstance(ContextFinder.java:288) ~[jaxb-api-2.4.0-b180830.0359-patched.jar:2.3.0]
	at javax.xml.bind.ContextFinder.newInstance(ContextFinder.java:277) ~[jaxb-api-2.4.0-b180830.0359-patched.jar:2.3.0]
	at javax.xml.bind.ContextFinder.find(ContextFinder.java:412) ~[jaxb-api-2.4.0-b180830.0359-patched.jar:2.3.0]
	at javax.xml.bind.JAXBContext.newInstance(JAXBContext.java:721) ~[jaxb-api-2.4.0-b180830.0359-patched.jar:2.3.0]
	at javax.xml.bind.JAXBContext.newInstance(JAXBContext.java:662) ~[jaxb-api-2.4.0-b180830.0359-patched.jar:2.3.0]
	at com.polarion.synchronizer.internal.configuration.ConfigurationDataHandler.loadJaxbContext(ConfigurationDataHandler.java:108) ~[synchronizer.jar:?]
	at com.polarion.synchronizer.internal.configuration.ConfigurationDataHandler.processData(ConfigurationDataHandler.java:94) ~[synchronizer.jar:?]
	at com.polarion.platform.repository.spi.config.AbstractDataHandler.readLocation(AbstractDataHandler.java:83) ~[platform-repository.jar:?]
	... 83 more
2025-07-29 18:06:11,328 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55a592dc-c0a8d700-5cecbd49-6b803412] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections?_=1753783571099': Total: 0.164 s, CPU [user: 0.0266 s, system: 0.00336 s], Allocated memory: 2.9 MB, transactions: 1, RepositoryConfigService: 0.0859 s [100% getReadConfiguration (1x)] (1x)
2025-07-29 18:06:14,492 [Thread-36] INFO  NotificationService - Notification service was started successfully.
