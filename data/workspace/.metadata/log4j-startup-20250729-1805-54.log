2025-07-29 18:05:54,322 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 18:05:54,322 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-29 18:05:54,322 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 18:05:54,322 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-29 18:05:54,322 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-29 18:05:54,322 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:05:54,322 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-29 18:05:58,643 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-29 18:05:58,804 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.16 s. ]
2025-07-29 18:05:58,804 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-29 18:05:58,912 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.108 s. ]
2025-07-29 18:05:58,981 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-29 18:05:59,173 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 12 s. ]
2025-07-29 18:05:59,463 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.15 s. ]
2025-07-29 18:05:59,613 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:05:59,613 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-29 18:05:59,635 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.17 s. ]
2025-07-29 18:05:59,636 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:05:59,636 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-29 18:05:59,643 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (3/9)
2025-07-29 18:05:59,643 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (2/9)
2025-07-29 18:05:59,643 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (4/9)
2025-07-29 18:05:59,643 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (1/9)
2025-07-29 18:05:59,643 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (5/9)
2025-07-29 18:05:59,643 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (6/9)
2025-07-29 18:05:59,653 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-07-29 18:05:59,917 [LowLevelDataService-contextInitializer-4 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-29 18:06:00,004 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-29 18:06:00,538 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.9 s. ]
2025-07-29 18:06:00,552 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:06:00,552 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-29 18:06:00,993 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-29 18:06:01,008 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.47 s. ]
2025-07-29 18:06:01,044 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:06:01,044 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-29 18:06:01,049 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-29 18:06:01,109 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-29 18:06:01,165 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-07-29 18:06:01,194 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (4/9)
2025-07-29 18:06:01,220 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (5/9)
2025-07-29 18:06:01,264 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (6/9)
2025-07-29 18:06:01,299 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-07-29 18:06:01,351 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-29 18:06:01,394 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-29 18:06:01,394 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.39 s. ]
2025-07-29 18:06:01,395 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:06:01,395 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-29 18:06:01,409 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-07-29 18:06:01,409 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:06:01,409 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-29 18:06:01,517 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-29 18:06:01,520 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-29 18:06:01,650 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.24 s. ]
2025-07-29 18:06:01,650 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:06:01,650 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-29 18:06:01,658 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-29 18:06:01,658 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 18:06:01,658 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-29 18:06:04,504 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.85 s. ]
2025-07-29 18:06:04,504 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 18:06:04,504 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 10.2 s. ]
2025-07-29 18:06:04,504 [main] INFO  com.polarion.platform.startup - ****************************************************************
