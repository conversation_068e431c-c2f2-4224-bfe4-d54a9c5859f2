2025-07-28 11:44:31,308 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.048 s [65% update (144x), 35% query (12x)] (221x), svn: 0.0121 s [62% getLatestRevision (2x), 29% testConnection (1x)] (4x)
2025-07-28 11:44:31,415 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0304 s [56% getDir2 content (2x), 33% info (3x)] (6x)
2025-07-28 11:44:32,121 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.703 s, CPU [user: 0.044 s, system: 0.103 s], Allocated memory: 6.8 MB, transactions: 0, ObjectMaps: 0.0594 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-28 11:44:32,121 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.703 s, CPU [user: 0.175 s, system: 0.289 s], Allocated memory: 53.4 MB, transactions: 0, ObjectMaps: 0.107 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-28 11:44:32,121 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.703 s, CPU [user: 0.219 s, system: 0.346 s], Allocated memory: 70.2 MB, transactions: 0, ObjectMaps: 0.124 s [99% getAllPrimaryObjects (1x)] (14x)
2025-07-28 11:44:32,121 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.703 s, CPU [user: 0.097 s, system: 0.22 s], Allocated memory: 23.7 MB, transactions: 0, ObjectMaps: 0.0688 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-28 11:44:32,121 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.703 s, CPU [user: 0.0621 s, system: 0.106 s], Allocated memory: 8.6 MB, transactions: 0, svn: 0.0761 s [83% log2 (10x)] (13x), ObjectMaps: 0.0749 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-28 11:44:32,121 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.703 s, CPU [user: 0.0925 s, system: 0.172 s], Allocated memory: 16.6 MB, transactions: 0, svn: 0.101 s [39% log2 (10x), 21% log (1x), 19% info (5x), 12% getLatestRevision (3x)] (24x), ObjectMaps: 0.0656 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-28 11:44:32,121 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.5 s [100% getAllPrimaryObjects (8x)] (63x), svn: 0.278 s [61% log2 (36x), 13% getLatestRevision (9x), 10% testConnection (6x)] (61x)
2025-07-28 11:44:32,347 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.193 s [100% getReadConfiguration (48x)] (48x), svn: 0.0792 s [83% info (18x)] (38x)
2025-07-28 11:44:32,618 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.204 s [76% info (94x), 16% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.16 s [100% getReadConfiguration (54x)] (54x)
2025-07-28 11:44:32,838 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.117 s, CPU [user: 0.0397 s, system: 0.00999 s], Allocated memory: 11.5 MB
2025-07-28 11:44:32,864 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.233 s [100% doFinishStartup (1x)] (1x), commit: 0.0561 s [100% Revision (1x)] (1x), Lucene: 0.0386 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.0147 s [100% objectsToInv (1x)] (1x), DB: 0.0123 s [41% update (3x), 37% query (1x), 13% execute (1x)] (8x)
2025-07-28 11:45:26,631 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.131494140625
2025-07-28 11:45:36,631 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.063818359375
