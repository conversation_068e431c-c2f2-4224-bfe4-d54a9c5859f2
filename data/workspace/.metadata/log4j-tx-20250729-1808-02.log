2025-07-29 18:08:07,392 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0476 s [58% update (144x), 42% query (12x)] (221x), svn: 0.0382 s [55% getLatestRevision (2x), 38% testConnection (1x)] (4x)
2025-07-29 18:08:07,520 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0284 s [63% getDir2 content (2x), 30% info (3x)] (6x)
2025-07-29 18:08:08,191 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.668 s, CPU [user: 0.185 s, system: 0.257 s], Allocated memory: 52.8 MB, transactions: 0, ObjectMaps: 0.109 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-29 18:08:08,191 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.668 s, CPU [user: 0.218 s, system: 0.315 s], Allocated memory: 68.4 MB, transactions: 0, ObjectMaps: 0.12 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-29 18:08:08,191 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.668 s, CPU [user: 0.114 s, system: 0.19 s], Allocated memory: 24.1 MB, transactions: 0, ObjectMaps: 0.0606 s [100% getAllPrimaryObjects (1x)] (10x)
2025-07-29 18:08:08,191 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.668 s, CPU [user: 0.0692 s, system: 0.0882 s], Allocated memory: 9.4 MB, transactions: 0, svn: 0.0872 s [34% log2 (5x), 21% info (5x), 21% log (1x), 10% getLatestRevision (2x)] (18x), ObjectMaps: 0.0688 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-29 18:08:08,191 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.668 s, CPU [user: 0.0779 s, system: 0.102 s], Allocated memory: 10.7 MB, transactions: 0, svn: 0.0981 s [85% log2 (10x)] (13x), ObjectMaps: 0.0475 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-29 18:08:08,191 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.668 s, CPU [user: 0.0806 s, system: 0.122 s], Allocated memory: 12.4 MB, transactions: 0, ObjectMaps: 0.0912 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.0723 s [80% log2 (10x)] (13x)
2025-07-29 18:08:08,192 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.497 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.317 s [66% log2 (36x), 12% getLatestRevision (9x), 9% testConnection (6x)] (61x)
2025-07-29 18:08:08,421 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.195 s [100% getReadConfiguration (48x)] (48x), svn: 0.0712 s [84% info (18x)] (38x)
2025-07-29 18:08:08,739 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.245 s [77% info (94x), 16% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.185 s [100% getReadConfiguration (54x)] (54x)
2025-07-29 18:08:08,981 [PolarionDocIdCreator-5] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.127 s, CPU [user: 0.0329 s, system: 0.0096 s], Allocated memory: 11.2 MB, GC: 0.018 s [100% G1 Young Generation (1x)] (1x)
2025-07-29 18:08:09,025 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.272 s [100% doFinishStartup (1x)] (1x), commit: 0.0815 s [100% Revision (1x)] (1x), Lucene: 0.0401 s [100% refresh (1x)] (1x), DB: 0.0202 s [39% execute (1x), 30% update (3x), 22% query (1x)] (8x), derivedLinkedRevisionsContributor: 0.0153 s [100% objectsToInv (1x)] (1x)
2025-07-29 18:26:13,319 [PullingJob-default] INFO  TXLOGGER - Summary for 'PullingJob': Total: 0.124 s, CPU [user: 0.0023 s, system: 0.00189 s], Allocated memory: 132.0 kB, transactions: 0, PullingJob: 0.122 s [100% collectChanges (1x)] (1x), svn: 0.122 s [100% getLatestRevision (1x)] (1x), GC: 0.112 s [100% G1 Young Generation (1x)] (1x)
2025-07-29 18:31:16,922 [PullingJob-default] INFO  TXLOGGER - Summary for 'PullingJob': Total: 0.359 s, CPU [user: 0.00281 s, system: 0.0172 s], Allocated memory: 129.7 kB, transactions: 0, PullingJob: 0.121 s [100% collectChanges (1x)] (1x), svn: 0.12 s [100% getLatestRevision (1x)] (1x)
2025-07-29 18:33:40,779 [ajp-nio-127.0.0.1-8889-exec-1 | cID:55bebdf6-c0a8d700-1acc4c5a-87fccb9b] INFO  TXLOGGER - Summary for 'servlet /polarion/svnwebclient/fileDownload.jsp?url=.polarion%2Fsynchronizer%2Fconfiguration.xml&attachment=true': Total: 0.18 s, CPU [user: 0.0541 s, system: 0.0194 s], Allocated memory: 7.7 MB, transactions: 0, PolarionAuthenticator: 0.0675 s [100% authenticate (1x)] (1x)
2025-07-29 18:33:41,172 [main | u:p | u:p] INFO  TXLOGGER - Tx 66156fafef43f_0_66156fafef43f_0_: finished. Total: 0.119 s, CPU [user: 0.0649 s, system: 0.014 s], Allocated memory: 7.6 MB, GlobalHandler: 0.0134 s [97% applyTxChanges (1x)] (4x)
2025-07-29 18:33:41,780 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 17, svn: 5.03 s [93% getLatestRevision (511x)] (685x), PullingJob: 4.76 s [100% collectChanges (510x)] (510x)
2025-07-29 18:33:41,784 [Worker-0: Attachment Indexer | u:p | job: polarion.jobs.attachment.indexer] INFO  TXLOGGER - Summary after AttachmentIndexer: transactions: 0, notification worker: 0.00069 s [100% BuildActivityCreator (1x)] (1x), EHCache: 0.000434 s [100% GET (2x)] (2x)
2025-07-29 18:33:42,670 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.754 s, CPU [user: 0.00514 s, system: 0.00237 s], Allocated memory: 531.2 kB, transactions: 1
2025-07-29 18:33:42,671 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 27, resolve: 0.108 s [92% User (2x)] (4x), persistence listener: 0.0379 s [90% indexRefreshPersistenceListener (1x)] (7x), notification worker: 0.0319 s [76% RevisionActivityCreator (2x), 12% WorkItemActivityCreator (1x)] (5x), Incremental Baseline: 0.0265 s [100% WorkItem (21x)] (21x), ObjectMaps: 0.0188 s [100% getPrimaryObjectLocation (2x)] (2x), Lucene: 0.0122 s [59% add (1x), 41% refresh (1x)] (2x)
2025-07-29 18:33:42,671 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.757 s, CPU [user: 0.158 s, system: 0.0334 s], Allocated memory: 18.5 MB, transactions: 23, svn: 0.665 s [99% getDatedRevision (181x)] (183x), Lucene: 0.0559 s [81% buildBaselineSnapshots (1x)] (23x)
2025-07-29 18:33:43,028 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66156fb0a8045_0_66156fb0a8045_0_: finished. Total: 1.24 s, CPU [user: 0.352 s, system: 0.103 s], Allocated memory: 55.1 MB, svn: 0.668 s [50% getDatedRevision (181x), 32% getDir2 content (25x)] (307x), resolve: 0.411 s [100% Category (96x)] (96x), ObjectMaps: 0.143 s [41% getPrimaryObjectProperty (96x), 36% getPrimaryObjectLocation (96x), 23% getLastPromoted (96x)] (388x)
2025-07-29 18:33:43,221 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66156fb1f2048_0_66156fb1f2048_0_: finished. Total: 0.109 s, CPU [user: 0.0504 s, system: 0.0122 s], Allocated memory: 8.4 MB, RepositoryConfigService: 0.0455 s [58% getReadUserConfiguration (10x), 42% getReadConfiguration (162x)] (172x), svn: 0.0394 s [57% info (19x), 36% getFile content (16x)] (37x), resolve: 0.0346 s [100% User (9x)] (9x), ObjectMaps: 0.0185 s [42% getPrimaryObjectLocation (9x), 41% getPrimaryObjectProperty (9x)] (37x)
2025-07-29 18:33:43,356 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66156fb20d449_0_66156fb20d449_0_: finished. Total: 0.119 s, CPU [user: 0.0307 s, system: 0.00518 s], Allocated memory: 3.8 MB, RepositoryConfigService: 0.0483 s [98% getReadConfiguration (54x)] (77x), svn: 0.0196 s [98% getFile content (12x)] (13x)
2025-07-29 18:33:43,766 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66156fb22f44a_0_66156fb22f44a_0_: finished. Total: 0.407 s, CPU [user: 0.0698 s, system: 0.0163 s], Allocated memory: 19.9 MB, svn: 0.355 s [83% getDir2 content (17x)] (62x), RepositoryConfigService: 0.0928 s [98% getReadConfiguration (170x)] (192x)
2025-07-29 18:33:44,640 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66156fb29584b_0_66156fb29584b_0_: finished. Total: 0.874 s, CPU [user: 0.358 s, system: 0.0337 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.66 s [98% getReadConfiguration (8682x)] (9021x), svn: 0.46 s [65% getFile content (412x), 35% getDir2 content (21x)] (434x), GC: 0.065 s [100% G1 Young Generation (4x)] (4x)
2025-07-29 18:33:44,974 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66156fb38b04e_0_66156fb38b04e_0_: finished. Total: 0.226 s, CPU [user: 0.0939 s, system: 0.00469 s], Allocated memory: 384.1 MB, RepositoryConfigService: 0.143 s [97% getReadConfiguration (2787x)] (3025x), svn: 0.141 s [51% getFile content (185x), 49% getDir2 content (20x)] (206x)
2025-07-29 18:33:44,974 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 3.19 s, CPU [user: 1.01 s, system: 0.186 s], Allocated memory: 1.6 GB, transactions: 10, svn: 1.81 s [46% getDir2 content (114x), 33% getFile content (809x), 18% getDatedRevision (181x)] (1144x), RepositoryConfigService: 1.03 s [95% getReadConfiguration (12019x)] (12691x), resolve: 0.489 s [84% Category (96x)] (117x), ObjectMaps: 0.175 s [43% getPrimaryObjectProperty (110x), 36% getPrimaryObjectLocation (116x), 22% getLastPromoted (110x)] (452x)
2025-07-29 18:33:44,974 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 55, svn: 2.48 s [40% getDatedRevision (362x), 33% getDir2 content (114x), 24% getFile content (809x)] (1328x), RepositoryConfigService: 1.03 s [95% getReadConfiguration (12019x)] (12691x), resolve: 0.489 s [84% Category (96x)] (118x), ObjectMaps: 0.175 s [43% getPrimaryObjectProperty (110x), 36% getPrimaryObjectLocation (116x), 22% getLastPromoted (110x)] (452x)
