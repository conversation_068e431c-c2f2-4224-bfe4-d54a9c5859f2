2025-07-28 15:28:41,245 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0623 s [66% update (144x), 34% query (12x)] (221x), svn: 0.0111 s [53% getLatestRevision (2x), 32% testConnection (1x)] (4x)
2025-07-28 15:28:41,388 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0322 s [56% getDir2 content (2x), 33% info (3x)] (6x)
2025-07-28 15:28:42,220 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.828 s, CPU [user: 0.131 s, system: 0.207 s], Allocated memory: 23.8 MB, transactions: 0, ObjectMaps: 0.086 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-28 15:28:42,220 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.828 s, CPU [user: 0.224 s, system: 0.279 s], Allocated memory: 53.1 MB, transactions: 0, ObjectMaps: 0.104 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-28 15:28:42,220 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.827 s, CPU [user: 0.0615 s, system: 0.0833 s], Allocated memory: 6.8 MB, transactions: 0, svn: 0.0896 s [64% log2 (5x), 21% getLatestRevision (1x)] (7x), ObjectMaps: 0.068 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-28 15:28:42,220 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.828 s, CPU [user: 0.269 s, system: 0.344 s], Allocated memory: 70.2 MB, transactions: 0, ObjectMaps: 0.145 s [99% getAllPrimaryObjects (1x)] (14x)
2025-07-28 15:28:42,220 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.827 s, CPU [user: 0.0808 s, system: 0.102 s], Allocated memory: 8.5 MB, transactions: 0, svn: 0.168 s [84% log2 (10x)] (13x), ObjectMaps: 0.0465 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-28 15:28:42,221 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.828 s, CPU [user: 0.132 s, system: 0.168 s], Allocated memory: 16.9 MB, transactions: 0, svn: 0.176 s [58% log2 (10x), 12% log (1x), 12% getLatestRevision (3x)] (24x), ObjectMaps: 0.106 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-28 15:28:42,221 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.555 s [100% getAllPrimaryObjects (8x)] (63x), svn: 0.513 s [69% log2 (36x), 13% getLatestRevision (9x)] (61x)
2025-07-28 15:28:42,498 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.24 s [100% getReadConfiguration (48x)] (48x), svn: 0.0914 s [84% info (18x)] (38x)
2025-07-28 15:28:43,089 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.439 s [74% info (94x), 18% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.341 s [100% getReadConfiguration (54x)] (54x)
2025-07-28 15:28:43,365 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.131 s, CPU [user: 0.0264 s, system: 0.0106 s], Allocated memory: 10.5 MB, GC: 0.014 s [100% G1 Young Generation (1x)] (1x)
2025-07-28 15:28:43,414 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.306 s [100% doFinishStartup (1x)] (1x), commit: 0.0719 s [100% Revision (1x)] (1x), Lucene: 0.0462 s [100% refresh (1x)] (1x), DB: 0.0194 s [43% query (1x), 26% update (3x), 22% execute (1x)] (8x), derivedLinkedRevisionsContributor: 0.0168 s [100% objectsToInv (1x)] (1x)
2025-07-28 15:28:46,588 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.776 s [91% info (158x)] (170x)
2025-07-28 15:28:47,539 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.869 s, CPU [user: 0.00828 s, system: 0.00237 s], Allocated memory: 552.6 kB, transactions: 1
2025-07-28 15:28:47,540 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 25, resolve: 0.0344 s [91% User (2x)] (4x), persistence listener: 0.0256 s [76% indexRefreshPersistenceListener (1x), 10% WorkItemActivityCreator (1x)] (7x), Incremental Baseline: 0.0238 s [100% WorkItem (19x)] (19x), notification worker: 0.0222 s [54% RevisionActivityCreator (2x), 20% WorkItemActivityCreator (1x), 12% TestRunActivityCreator (1x)] (6x), Lucene: 0.0203 s [64% add (1x), 36% refresh (1x)] (2x), ObjectMaps: 0.00856 s [100% getPrimaryObjectLocation (2x)] (2x), EHCache: 0.00397 s [99% GET (16x)] (35x)
2025-07-28 15:28:47,541 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.872 s, CPU [user: 0.193 s, system: 0.0332 s], Allocated memory: 17.9 MB, transactions: 21, svn: 0.793 s [99% getDatedRevision (181x)] (183x)
2025-07-28 15:28:47,874 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6613fbc683445_0_6613fbc683445_0_: finished. Total: 1.27 s, CPU [user: 0.467 s, system: 0.104 s], Allocated memory: 53.3 MB, svn: 0.69 s [44% getDatedRevision (181x), 33% getDir2 content (25x), 21% getFile content (98x)] (307x), resolve: 0.537 s [100% Category (96x)] (96x), ObjectMaps: 0.191 s [41% getPrimaryObjectProperty (96x), 31% getPrimaryObjectLocation (96x), 27% getLastPromoted (96x)] (388x)
2025-07-28 15:28:48,180 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6613fbc7de848_0_6613fbc7de848_0_: finished. Total: 0.186 s, CPU [user: 0.0802 s, system: 0.0134 s], Allocated memory: 8.4 MB, svn: 0.0815 s [49% info (19x), 36% getFile content (16x)] (37x), RepositoryConfigService: 0.0762 s [57% getReadUserConfiguration (10x), 43% getReadConfiguration (162x)] (172x), resolve: 0.0567 s [100% User (9x)] (9x), ObjectMaps: 0.0283 s [70% getPrimaryObjectProperty (9x), 17% getPrimaryObjectLocation (9x)] (37x)
2025-07-28 15:28:48,291 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6613fbc80d049_0_6613fbc80d049_0_: finished. Total: 0.11 s, CPU [user: 0.0468 s, system: 0.00586 s], Allocated memory: 3.8 MB, RepositoryConfigService: 0.0719 s [96% getReadConfiguration (54x)] (77x), svn: 0.0378 s [100% getFile content (12x)] (13x)
2025-07-28 15:28:48,577 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6613fbc828c4a_0_6613fbc828c4a_0_: finished. Total: 0.286 s, CPU [user: 0.0919 s, system: 0.0112 s], Allocated memory: 19.9 MB, svn: 0.226 s [71% getDir2 content (17x), 29% getFile content (44x)] (62x), RepositoryConfigService: 0.105 s [99% getReadConfiguration (170x)] (192x)
2025-07-28 15:28:49,808 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6613fbc87044b_0_6613fbc87044b_0_: finished. Total: 1.23 s, CPU [user: 0.54 s, system: 0.036 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.975 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.706 s [74% getFile content (412x), 26% getDir2 content (21x)] (434x)
2025-07-28 15:28:49,938 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6613fbc9a404c_0_6613fbc9a404c_0_: finished. Total: 0.13 s, CPU [user: 0.0303 s, system: 0.00362 s], Allocated memory: 17.8 MB, svn: 0.113 s [78% getDir2 content (18x), 22% getFile content (29x)] (48x), RepositoryConfigService: 0.0371 s [98% getReadConfiguration (124x)] (148x)
2025-07-28 15:28:50,446 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6613fbc9ce44e_0_6613fbc9ce44e_0_: finished. Total: 0.469 s, CPU [user: 0.194 s, system: 0.0164 s], Allocated memory: 384.5 MB, RepositoryConfigService: 0.33 s [95% getReadConfiguration (2787x)] (3025x), svn: 0.287 s [61% getFile content (185x), 39% getDir2 content (20x)] (206x)
2025-07-28 15:28:50,447 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 3.85 s, CPU [user: 1.52 s, system: 0.203 s], Allocated memory: 1.6 GB, transactions: 10, svn: 2.23 s [45% getFile content (809x), 37% getDir2 content (114x)] (1144x), RepositoryConfigService: 1.61 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.658 s [82% Category (96x)] (117x), ObjectMaps: 0.239 s [47% getPrimaryObjectProperty (110x), 29% getPrimaryObjectLocation (116x), 24% getLastPromoted (110x)] (452x)
2025-07-28 15:28:50,447 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 51, svn: 3.03 s [36% getDatedRevision (362x), 33% getFile content (809x), 27% getDir2 content (114x)] (1328x), RepositoryConfigService: 1.61 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.658 s [82% Category (96x)] (118x), ObjectMaps: 0.239 s [47% getPrimaryObjectProperty (110x), 29% getPrimaryObjectLocation (116x), 24% getLastPromoted (110x)] (452x)
2025-07-28 15:29:00,442 [ajp-nio-127.0.0.1-8889-exec-2 | cID:4fef4d8a-c0a8d700-27c61944-d078b6ba] INFO  TXLOGGER - Summary for 'servlet /polarion/': Total: 0.655 s, CPU [user: 0.28 s, system: 0.097 s], Allocated memory: 36.5 MB, transactions: 2, PolarionAuthenticator: 0.62 s [100% authenticate (1x)] (1x), resolve: 0.0348 s [100% User (1x)] (1x)
2025-07-28 15:29:01,453 [ajp-nio-127.0.0.1-8889-exec-3 | cID:4fef5390-c0a8d700-27c61944-3bb50ee1] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/3E3564E5AB5ED735B825D709162B29DD.cache.js': Total: 0.124 s, CPU [user: 0.00676 s, system: 0.00886 s], Allocated memory: 135.8 kB, transactions: 0
2025-07-28 15:29:03,029 [ajp-nio-127.0.0.1-8889-exec-7 | cID:4fef591e-c0a8d700-27c61944-bcf7a570 | u:admin] INFO  TXLOGGER - Tx 6613fbd66dc51_0_6613fbd66dc51_0_: finished. Total: 0.126 s, CPU [user: 0.0627 s, system: 0.0163 s], Allocated memory: 6.0 MB, svn: 0.0378 s [30% testConnection (1x), 21% log (1x), 20% getDir2 content (1x), 15% info (2x)] (8x), RepositoryConfigService: 0.0265 s [100% getReadConfiguration (10x)] (15x), resolve: 0.0261 s [99% Project (4x)] (5x), ObjectMaps: 0.0167 s [61% getPrimaryObjectProperty (1x), 16% getPrimaryObjectLocation (1x), 13% getPrimaryObjectLocations (1x)] (6x)
2025-07-28 15:29:03,144 [ajp-nio-127.0.0.1-8889-exec-7 | cID:4fef591e-c0a8d700-27c61944-bcf7a570] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/PortalDataService': Total: 0.389 s, CPU [user: 0.24 s, system: 0.0575 s], Allocated memory: 68.7 MB, transactions: 1, RPC: 0.224 s [50% decodeRequest (1x), 33% encodeResponse (1x)] (4x), PortalDataService: 0.127 s [100% getInitData (1x)] (1x), svn: 0.0378 s [30% testConnection (1x), 21% log (1x), 20% getDir2 content (1x), 15% info (2x)] (8x), RepositoryConfigService: 0.0265 s [100% getReadConfiguration (10x)] (15x), resolve: 0.0261 s [99% Project (4x)] (5x)
2025-07-28 15:29:03,614 [ajp-nio-127.0.0.1-8889-exec-5 | cID:4fef5aed-c0a8d700-27c61944-c8f361dc | u:admin] INFO  TXLOGGER - Tx 6613fbd6c0052_0_6613fbd6c0052_0_: finished. Total: 0.382 s, CPU [user: 0.165 s, system: 0.0328 s], Allocated memory: 15.4 MB, svn: 0.111 s [46% info (14x), 29% getDir2 content (3x), 11% getFile content (5x)] (25x), resolve: 0.0914 s [95% RichPage (5x)] (19x), RepositoryConfigService: 0.0576 s [89% getReadConfiguration (41x)] (79x)
2025-07-28 15:29:03,625 [ajp-nio-127.0.0.1-8889-exec-5 | cID:4fef5aed-c0a8d700-27c61944-c8f361dc] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/PortalDataService': Total: 0.411 s, CPU [user: 0.182 s, system: 0.0369 s], Allocated memory: 18.2 MB, transactions: 1, PortalDataService: 0.384 s [100% requestPortalSite (1x)] (1x), svn: 0.111 s [46% info (14x), 29% getDir2 content (3x), 11% getFile content (5x)] (25x), resolve: 0.0914 s [95% RichPage (5x)] (19x), RepositoryConfigService: 0.0576 s [89% getReadConfiguration (41x)] (79x), RPC: 0.0222 s [51% decodeRequest (1x), 41% encodeResponse (1x)] (4x)
2025-07-28 15:29:04,819 [ajp-nio-127.0.0.1-8889-exec-10 | cID:4fef5e40-c0a8d700-27c61944-d310ff23 | u:admin] INFO  TXLOGGER - Tx 6613fbd80b05a_0_6613fbd80b05a_0_: finished. Total: 0.262 s, CPU [user: 0.0708 s, system: 0.0134 s], Allocated memory: 9.3 MB, resolve: 0.162 s [98% Plan (3x)] (11x), svn: 0.0584 s [54% info (4x), 24% getFile content (3x), 10% getLatestRevision (1x)] (11x), RepositoryConfigService: 0.0395 s [100% getReadConfiguration (44x)] (82x), Lucene: 0.022 s [100% search (2x)] (2x), WeakPObjectList: [secondGetHits: 1 ]
2025-07-28 15:29:04,820 [ajp-nio-127.0.0.1-8889-exec-10 | cID:4fef5e40-c0a8d700-27c61944-d310ff23] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/UniversalService': Total: 0.756 s, CPU [user: 0.188 s, system: 0.0345 s], Allocated memory: 19.5 MB, transactions: 1, RPC: 0.489 s [100% decodeRequest (1x)] (4x), PlansData: 0.263 s [100% initialize (1x)] (1x), resolve: 0.162 s [98% Plan (3x)] (11x), GC: 0.078 s [100% G1 Young Generation (1x)] (1x), svn: 0.0584 s [54% info (4x), 24% getFile content (3x), 10% getLatestRevision (1x)] (11x), RepositoryConfigService: 0.0395 s [100% getReadConfiguration (44x)] (82x), WeakPObjectList: [secondGetHits: 1 ]
2025-07-28 15:29:04,999 [ajp-nio-127.0.0.1-8889-exec-6 | cID:4fef5d82-c0a8d700-27c61944-0dd05346 | u:admin | i:WBSdev/1 Project Management/XiangMuJiHua] INFO  TXLOGGER - Tx 6613fbd81d45b_0_6613fbd81d45b_0_: finished. Total: 0.37 s, CPU [user: 0.154 s, system: 0.0216 s], Allocated memory: 14.6 MB, svn: 0.117 s [78% testConnection (1x), 15% info (4x)] (9x), resolve: 0.0269 s [82% RichPageAttachment (1x)] (14x)
2025-07-28 15:29:05,052 [ajp-nio-127.0.0.1-8889-exec-6 | cID:4fef5d82-c0a8d700-27c61944-0dd05346] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/UniversalService': Total: 1.18 s, CPU [user: 0.463 s, system: 0.0813 s], Allocated memory: 45.4 MB, transactions: 1, RPC: 0.797 s [93% decodeRequest (1x)] (4x), RpeModel: 0.371 s [100% initialize (1x)] (1x), svn: 0.117 s [78% testConnection (1x), 15% info (4x)] (9x), GC: 0.078 s [100% G1 Young Generation (1x)] (1x)
2025-07-28 15:29:06,318 [ajp-nio-127.0.0.1-8889-exec-4 | cID:4fef626e-c0a8d700-27c61944-abd9b3dd | u:admin | i:WBSdev/1 Project Management/XiangMuJiHua e:polarion_client2] INFO  TXLOGGER - Tx 6613fbd89e85c_0_6613fbd89e85c_0_: finished. Total: 1.17 s, CPU [user: 0.426 s, system: 0.0732 s], Allocated memory: 59.1 MB, resolve: 0.74 s [99% WorkItem (24x)] (36x), svn: 0.633 s [50% info (56x), 25% getDir2 content (24x), 19% log (22x)] (130x), ObjectMaps: 0.15 s [95% getPrimaryObjectProperty (25x)] (101x), Lucene: 0.0609 s [100% search (23x)] (23x)
2025-07-28 15:29:06,320 [ajp-nio-127.0.0.1-8889-exec-4 | cID:4fef626e-c0a8d700-27c61944-abd9b3dd] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/UniversalService': Total: 1.19 s, CPU [user: 0.437 s, system: 0.0761 s], Allocated memory: 62.6 MB, transactions: 1, RenderingRequest: 1.17 s [100% initialize (1x)] (1x), resolve: 0.74 s [99% WorkItem (24x)] (36x), svn: 0.633 s [50% info (56x), 25% getDir2 content (24x), 19% log (22x)] (130x), ObjectMaps: 0.15 s [95% getPrimaryObjectProperty (25x)] (101x), Lucene: 0.0609 s [100% search (23x)] (23x)
2025-07-28 15:29:08,233 [ajp-nio-127.0.0.1-8889-exec-5 | cID:4fef5e40-c0a8d700-27c61944-46966b24 | u:admin] INFO  TXLOGGER - Tx 6613fbd791459_0_6613fbd791459_0_: finished. Total: 4.16 s, CPU [user: 1.17 s, system: 0.205 s], Allocated memory: 184.4 MB, resolve: 2.45 s [100% RichPage (179x)] (232x), svn: 2.05 s [71% info (301x), 17% getDir2 content (24x)] (453x), DB: 0.359 s [38% update (161x), 36% query (161x), 25% commit (161x)] (846x)
2025-07-28 15:29:08,258 [ajp-nio-127.0.0.1-8889-exec-5 | cID:4fef5e40-c0a8d700-27c61944-46966b24] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/PortalDataService': Total: 4.19 s, CPU [user: 1.18 s, system: 0.21 s], Allocated memory: 189.3 MB, transactions: 1, PortalDataService: 4.17 s [100% getWikiSpacesAndPages (1x)] (1x), resolve: 2.45 s [100% RichPage (179x)] (232x), svn: 2.05 s [71% info (301x), 17% getDir2 content (24x)] (453x), DB: 0.359 s [38% update (161x), 36% query (161x), 25% commit (161x)] (846x)
